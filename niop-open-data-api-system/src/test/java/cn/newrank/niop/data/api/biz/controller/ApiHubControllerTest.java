package cn.newrank.niop.data.api.biz.controller;

import cn.newrank.niop.data.api.biz.pojo.InterfaceInvokeParams;
import cn.newrank.niop.data.common.ds.Resp;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

class ApiHubControllerTest extends BaseTest{

    @Resource
    private ApiHubController controller;

    @Test
    void invoke() {
        InterfaceInvokeParams invokeParams = new InterfaceInvokeParams();
        invokeParams.put("_page", 1);
        invokeParams.put("_size", 10);
        Resp.DataView dataView = controller.invoke("3CCAA2D6DF", "3CCAA2D6DF", invokeParams);
        System.out.println(JSONObject.toJSONString(dataView));
    }

}