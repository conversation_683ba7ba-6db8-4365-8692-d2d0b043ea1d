package cn.newrank.niop.data.api.biz.trace;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.newrank.niop.data.api.biz.content.ServletContext;
import cn.newrank.niop.data.api.biz.service.DynamicInterfaceService;
import cn.newrank.niop.data.api.config.SystemConfig;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.ds.MutateBuilder;
import cn.newrank.niop.data.common.ds.SlsFactory;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/4 13:40
 */
@Log4j2
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
public class TraceFilter extends OncePerRequestFilter {


    static final int LIMIT_STATUS = 429;
    static final int OK_STATUS = 200;
    static final int ERROR_STATUS = -1;
    private static final String ACCESS_LOGSTORE = "niop-dc-api-access-log";
    private static final String SLS_PROJECT = "nr-niop";
    public static final String DEFAULT_API_ID = "XXXXXXXX";
    private final SlsFactory.Sls sls;
    private final String environment;
    private final DynamicInterfaceService dynamicInterfaceService;

    public TraceFilter(SystemConfig systemConfig, DynamicInterfaceService dynamicInterfaceService) {
        this.environment = systemConfig.getEnvironment().name();
        this.sls = SlsFactory.DEFAULT.create(key -> switch (key) {
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "cn-hangzhou.log.aliyuncs.com";
                } else {
                    yield "cn-hangzhou-intranet.log.aliyuncs.com";
                }
            }
            case DATABASE -> SLS_PROJECT;
            case ALIYUN_AK -> "LTAI5tQfyg6N6xkR7ZTZYPEp";
            case ALIYUN_SK -> "******************************";
            default -> throw new IllegalArgumentException("Unknown key: " + key);
        });
        this.dynamicInterfaceService = dynamicInterfaceService;
    }

    private static void recordResponse(MutateBuilder mutateBuilder, ContentCachingResponseWrapper response) {
        try {
            final byte[] content = response.getContentAsByteArray();
            final Integer status = getStatus(response, content);

            final int contentSize = response.getContentSize();
            mutateBuilder.addParam("response_body_bytes", contentSize)
                    .addParam("status", status);
            mutateBuilder.addParam("query", ServletContext.get().getQuery());
        } finally {
            try {
                response.copyBodyToResponse();
            } catch (IOException e) {
                log.warn("Failed to copy response body to original response", e);
            }
        }
    }

    private static Integer getStatus(ContentCachingResponseWrapper response, byte[] content) {
        final JSONObject resp = JSON.parseObject(content);
        if (resp == null) {
            return response.getStatus();
        }
        final Integer code = resp.getInteger("code");

        if (code == OK_STATUS) {
            return OK_STATUS;
        }

        if (code == BizErr.LIMIT_ERROR.getCode()) {
            return LIMIT_STATUS;
        }

        return response.getStatus();
    }

    private void recordRequest(MutateBuilder mutateBuilder, ContentCachingRequestWrapper request, boolean recordParams) {
        final String appId = request.getHeader("niop-app-id");

        mutateBuilder.addParam("app_id", appId)
                .addParam("http_host", request.getHeader("x-forwarded-host"))
                .addParam("remote_ip", getRemoteIp(request))
                .addParam("request_body_bytes", request.getContentLengthLong());
        if (recordParams) {
            mutateBuilder.addParam("request_params", getRequestBody(request));
        }

        String queryParam = ServletContext.get().getTemplate();
        if (CharSequenceUtil.isNotBlank(queryParam)) {
            mutateBuilder.addParam("template_md5", DigestUtil.md5Hex(queryParam.getBytes()));
        }
    }

    private String getRemoteIp(HttpServletRequest request) {
        final String ip = request.getHeader("x-real-ip");
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        return request.getHeader("remoteip");
    }

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request,
                                    @NotNull HttpServletResponse response,
                                    @NotNull FilterChain filterChain) throws ServletException, IOException {
        final String path = request.getRequestURI().replace(request.getContextPath(), "");
        if (!path.startsWith("/api-hub/")) {
            filterChain.doFilter(request, response);
            return;
        }

        final MutateBuilder mutateBuilder = sls.newMutateBuilder();
        final String[] args = path.split("/");
        final long currentMillis = System.currentTimeMillis();

        boolean recordParams = false;
        if (path.contains("aio")) {
            final String apiId = args[args.length - 1];
            final DynamicInterface dynamicInterface = dynamicInterfaceService.get(apiId);
            if (dynamicInterface != null) {
                mutateBuilder.addParam("dc_id", dynamicInterface.getDcId())
                        .addParam("template_md5", DigestUtil.md5Hex(dynamicInterface.getQuery().getBytes()));
            }
            recordParams = true;
            mutateBuilder.addParam("api_id", apiId);
        } else if (path.contains("query")) {
            final String dcId = args[args.length - 1];
            mutateBuilder.addParam("dc_id", dcId)
                    .addParam("api_id", DEFAULT_API_ID);
        }

        mutateBuilder.collection(ACCESS_LOGSTORE)
                .addParam("environment", environment)
                .addParam("url", path)
                .addParam("time", currentMillis);

        final ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        final ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        try {
            filterChain.doFilter(requestWrapper, responseWrapper);
        } catch (Exception e) {
            log.error("Failed to record request", e);
        } finally {
            mutateBuilder.addParam("timing", System.currentTimeMillis() - currentMillis);
            recordResponse(mutateBuilder, responseWrapper);
            recordRequest(mutateBuilder, requestWrapper, recordParams);

            ServletContext.remove();

            sls.mutate(mutateBuilder);
        }
    }


    /**
     * 从 HttpServletRequest 中获取请求体内容
     *
     * @param request HttpServletRequest 对象
     * @return 请求体内容（字符串）
     */
    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] buf = request.getContentAsByteArray();
        if (buf.length > 0) {
            return new String(buf, StandardCharsets.UTF_8);
        }
        return null;
    }
}
