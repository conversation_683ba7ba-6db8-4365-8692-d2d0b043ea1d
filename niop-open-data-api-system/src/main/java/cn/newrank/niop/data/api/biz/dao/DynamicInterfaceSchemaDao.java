package cn.newrank.niop.data.api.biz.dao;

import cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceSchemaMapper;
import cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfaceSchemaPo;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import com.alibaba.nacos.api.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 14:11
 */
@Component
@RequiredArgsConstructor
public class DynamicInterfaceSchemaDao {

    private final DynamicInterfaceSchemaMapper dynamicInterfaceSchemaMapper;
    private final RedissonClient redissonClient;

    private RBucket<String> getCachedInterface(String interfaceId) {
        return redissonClient.getBucket("dynamic:interface:schema" + interfaceId);
    }


    public DynamicInterfaceSchema getByInterfaceId(String interfaceId) {
        if (StringUtils.isBlank(interfaceId)) {
            return null;
        }

        final RBucket<String> cachedInterface = getCachedInterface(interfaceId);

        if (cachedInterface.isExists()) {
            return DynamicInterfaceSchema.fromJson(cachedInterface.get());
        }

        final DynamicInterfaceSchemaPo interfaceSchemaPo = dynamicInterfaceSchemaMapper.get(interfaceId);
        if (interfaceSchemaPo == null) {
            cachedInterface.set("");
            cachedInterface.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterfaceSchema dynamicInterfaceSchema = interfaceSchemaPo.toDto();

        cachedInterface.set(dynamicInterfaceSchema.toJSONString());
        cachedInterface.expire(Duration.ofDays(15));

        return dynamicInterfaceSchema;
    }

}
