package cn.newrank.niop.data.api.biz.dao;

import cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceMapper;
import cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceTagMapper;
import cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfacePo;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 14:11
 */
@Component
public class DynamicInterfaceDao {

    private final DynamicInterfaceMapper dynamicInterfaceMapper;
    private final RedissonClient redissonClient;
    private final DynamicInterfaceTagMapper dynamicInterfaceTagMapper;

    public DynamicInterfaceDao(DynamicInterfaceMapper dynamicInterfaceMapper,
                               RedissonClient redissonClient,
                               DynamicInterfaceTagMapper dynamicInterfaceTagMapper) {
        this.dynamicInterfaceMapper = dynamicInterfaceMapper;
        this.redissonClient = redissonClient;
        this.dynamicInterfaceTagMapper = dynamicInterfaceTagMapper;
    }

    private RBucket<String> getCachedInterface(String interfaceId) {
        return redissonClient.getBucket("dynamic:interface:" + interfaceId);
    }


    public DynamicInterface get(String interfaceId) {
        final RBucket<String> cachedInterface = getCachedInterface(interfaceId);

        if (cachedInterface.isExists()) {
            return DynamicInterface.fromJson(cachedInterface.get());
        }

        final DynamicInterfacePo interfacePo = dynamicInterfaceMapper.get(interfaceId);
        if (interfacePo == null) {
            cachedInterface.set("");
            cachedInterface.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterface dynamicInterface = interfacePo.toDto();

        dynamicInterface.setTags(dynamicInterfaceTagMapper.getAll(interfaceId));

        cachedInterface.set(dynamicInterface.toJSONString());
        cachedInterface.expire(Duration.ofDays(15));

        return dynamicInterface;
    }
}
