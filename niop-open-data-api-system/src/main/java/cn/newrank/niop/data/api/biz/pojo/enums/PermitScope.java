package cn.newrank.niop.data.api.biz.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/14 9:51
 */
public enum PermitScope implements BizEnum {
    MUTATE("mutate", "变更权限"),
    QUERY("query", "查询权限"),
    ;

    private final String code;
    private final String description;

    PermitScope(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
