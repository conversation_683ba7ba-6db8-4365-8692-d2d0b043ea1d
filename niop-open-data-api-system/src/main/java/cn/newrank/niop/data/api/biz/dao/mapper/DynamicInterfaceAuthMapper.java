package cn.newrank.niop.data.api.biz.dao.mapper;

import cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfaceAuthPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 14:43
 */
@Mapper
public interface DynamicInterfaceAuthMapper {

    DynamicInterfaceAuthPo get(String authId);

    DynamicInterfaceAuthPo getByInterfaceIdAndAppId(@Param("interfaceId") String interfaceId,
                                                    @Param("appId") String appId);
}
