package cn.newrank.niop.data.api.biz.controller;

import cn.newrank.niop.data.api.biz.content.ServletContext;
import cn.newrank.niop.data.api.biz.limiter.DynamicInterfaceLimitFactory;
import cn.newrank.niop.data.api.biz.limiter.QueryLimitFactory;
import cn.newrank.niop.data.api.biz.pojo.InterfaceInvokeParams;
import cn.newrank.niop.data.api.biz.pojo.QueryParam;
import cn.newrank.niop.data.api.biz.service.DatasourceService;
import cn.newrank.niop.data.api.biz.service.DynamicInterfaceService;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.common.limiter.Limitable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 11:17
 */
@Validated
@RestController
@RequestMapping("api-hub")
public class ApiHubController {
    private final DynamicInterfaceService dynamicInterfaceService;
    private final DatasourceService datasourceService;

    public ApiHubController(DynamicInterfaceService dynamicInterfaceService, DatasourceService datasourceService) {
        this.dynamicInterfaceService = dynamicInterfaceService;
        this.datasourceService = datasourceService;
    }


    /**
     * 调用动态接口
     *
     * @param invokeParams 调用参数
     * @param interfaceId  接口ID
     * @return 调用结果
     */
    @PostMapping("aio/{interfaceId}")
    @Limitable(factory = DynamicInterfaceLimitFactory.class)
    public Resp.DataView invoke(@NotBlank(message = "接口ID(interfaceId)不能为空")
                                @PathVariable
                                String interfaceId,
                                @RequestHeader("niop-app-id")
                                @NotBlank(message = "应用ID(niop-app-id)不能为空")
                                String appId,
                                @Valid @RequestBody
                                InterfaceInvokeParams invokeParams) {
        Resp resp = dynamicInterfaceService.invoke(interfaceId, invokeParams);
        ServletContext.cache(context -> context.setQuery(resp.query()));
        return resp.getDataView();
    }

    /**
     * 数据源查询数据
     *
     * @param appId      应用ID
     * @param queryParam 查询参数
     * @return 数据
     */
    @PostMapping("query/{dcId}")
    @Limitable(factory = QueryLimitFactory.class)
    public Resp.DataView query(@RequestHeader("niop-app-id")
                               @NotBlank(message = "应用ID(niop-app-id)不能为空")
                               String appId,
                               @RequestHeader("niop-flow-node")
                               @NotBlank(message = "流程节点ID(niop-flow-node)不能为空")
                               String flowNode,
                               @PathVariable
                               @NotBlank(message = "数据源配置ID不能为空")
                               String dcId,
                               @Valid @RequestBody
                               QueryParam queryParam) {
        Resp resp = datasourceService.execute(dcId, queryParam.getQuery(), queryParam.getArgs(), queryParam.getOperation());

        ServletContext.cache(context -> {
            context.setQuery(resp.query());
            context.setTemplate(queryParam.getQuery());
        });

        return resp.getDataView();
    }

    @PostMapping("monitor-query/{dcId}")
    public Resp queryTest(@RequestHeader("niop-app-id")
                          @NotBlank(message = "应用ID(niop-app-id)不能为空")
                          String appId,
                          @PathVariable
                          @NotBlank(message = "数据源配置ID不能为空")
                          String dcId,
                          @Valid @RequestBody
                          QueryParam queryParam) {
        Resp resp = datasourceService.execute(dcId, queryParam.getQuery(), queryParam.getArgs(), queryParam.getOperation());

//        ServletContext.cache(context -> {
//            context.setQuery(resp.query());
//            context.setTemplate(queryParam.getQuery());
//        });

        return resp;
    }

    /**
     * 数据源执行dml（Data Manipulation Language - 数据操作语言）
     * DML 语句用于查询、插入、更新和删除数据库中的数据。它们操作表中的行记录。
     * SELECT：从数据库中检索数据。
     * INSERT：向表中添加新行。
     * UPDATE：修改表中现有行的值。
     * DELETE：从表中删除行
     *
     * @param appKey     应用Key 鉴权 和 默认限频
     * @param queryParam 查询参数
     * @return 数据
     */
    @PostMapping("dml/{dcId}")
    @Limitable(factory = QueryLimitFactory.class)
    public Resp.DataView dml(@RequestHeader("niop-app-key")
                             @NotBlank(message = "应用ID(niop-app-id)不能为空")
                             String appKey,
                             @PathVariable
                             @NotBlank(message = "数据源配置ID不能为空")
                             String dcId,
                             @Valid @RequestBody
                             QueryParam queryParam) {
        Resp resp = datasourceService.dml(dcId, queryParam.getQuery());

        ServletContext.cache(context -> {
            context.setQuery(resp.query());
            context.setTemplate(queryParam.getQuery());
        });

        return resp.getDataView();
    }

    /**
     * 数据源执行 ddl（Data Definition Language - 数据定义语言）
     *
     * @param appKey     应用Key 鉴权 和 默认限频
     * @param queryParam 查询参数
     * @return 数据
     */
    @PostMapping("ddl/{dcId}")
    @Limitable(factory = QueryLimitFactory.class)
    public Resp.DataView ddl(@RequestHeader("niop-app-key")
                             @NotBlank(message = "应用ID(niop-app-id)不能为空")
                             String appKey,
                             @PathVariable
                             @NotBlank(message = "数据源配置ID不能为空")
                             String dcId,
                             @Valid @RequestBody
                             QueryParam queryParam) {
        Resp resp = datasourceService.ddl(dcId, queryParam.getQuery());

        ServletContext.cache(context -> {
            context.setQuery(resp.query());
            context.setTemplate(queryParam.getQuery());
        });

        return resp.getDataView();
    }
}
