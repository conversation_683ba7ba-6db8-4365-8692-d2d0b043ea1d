package cn.newrank.niop.data.api.biz.dao;

import cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceAuthMapper;
import cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfaceAuthPo;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:48
 */
@Component
public class DynamicInterfaceAuthDao {

    private final DynamicInterfaceAuthMapper dynamicInterfaceAuthMapper;
    private final RedissonClient redissonClient;

    public DynamicInterfaceAuthDao(DynamicInterfaceAuthMapper dynamicInterfaceAuthMapper,
                                   RedissonClient redissonClient) {
        this.dynamicInterfaceAuthMapper = dynamicInterfaceAuthMapper;
        this.redissonClient = redissonClient;
    }

    public DynamicInterfaceAuth get(String authId) {
        final DynamicInterfaceAuthPo authPo = dynamicInterfaceAuthMapper.get(authId);
        if (authPo == null) {
            return null;
        }

        return authPo.toDto();
    }

    public DynamicInterfaceAuth get(String interfaceId, String appId) {
        final RBucket<String> cachedInterfaceAuth = getCachedInterfaceAuth(interfaceId, appId);
        if (cachedInterfaceAuth.isExists()) {
            return DynamicInterfaceAuth.ofJSONString(cachedInterfaceAuth.get());
        }

        final DynamicInterfaceAuthPo authPo = dynamicInterfaceAuthMapper.getByInterfaceIdAndAppId(interfaceId, appId);
        if (authPo == null) {
            cachedInterfaceAuth.set("");
            cachedInterfaceAuth.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterfaceAuth interfaceAuth = authPo.toDto();

        cachedInterfaceAuth.set(interfaceAuth.toJSONString());
        cachedInterfaceAuth.expire(Duration.ofDays(15));

        return interfaceAuth;
    }

    private RBucket<String> getCachedInterfaceAuth(String interfaceId, String appId) {
        return redissonClient.getBucket("dynamic:interface:auth:" + interfaceId + ":" + appId);
    }
}
