package cn.newrank.niop.data.api.biz.pojo;


import cn.newrank.niop.data.common.enums.DsType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:42
 */
@Data
public class DatasourceConfig {
    private Integer id;

    /**
     * 数据源配置id
     */
    private String dcId;

    /**
     * 数据源类型
     */
    private DsType type;

    /**
     * 数据源配置名称
     */
    private String name;

    /**
     * 数据源配置
     */
    private String config;

    /**
     * 任务并发数
     */
    private Integer concurrencyPermit;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

}