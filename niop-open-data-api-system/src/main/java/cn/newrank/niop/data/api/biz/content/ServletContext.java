package cn.newrank.niop.data.api.biz.content;

import lombok.Data;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ServletContext {
    private String query;
    private String template;

    private static final ThreadLocal<ServletContext> HOLDER = new ThreadLocal<>();


    public static void remove() {
        HOLDER.remove();
    }

    public static ServletContext get() {
        final ServletContext servletContext = HOLDER.get();
        if (servletContext == null) {
            return new ServletContext();
        }
        return servletContext;
    }

    public static void cache(Consumer<ServletContext> contextConsumer) {
        ServletContext context = HOLDER.get();
        if (context == null) {
            context = new ServletContext();
            HOLDER.set(context);
        }

        contextConsumer.accept(context);
    }

}