package cn.newrank.niop.data.api.config;

import cn.newrank.niop.data.common.limiter.LimitFactory;
import cn.newrank.niop.data.common.limiter.LimitInterceptor;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * web 配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/12/28 9:55
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final RedissonClient redissonClient;
    private final List<LimitFactory> limitFactories;

    public WebConfig(RedissonClient redissonClient, List<LimitFactory> limitFactories) {
        this.redissonClient = redissonClient;
        this.limitFactories = limitFactories;
    }

    @Override
    public void addInterceptors(@NotNull InterceptorRegistry registry) {
        registry.addInterceptor(new LimitInterceptor(redissonClient, limitFactories));
    }

}
