package cn.newrank.niop.data.api.biz.pojo;

import cn.newrank.niop.data.api.biz.pojo.enums.PermitScope;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/20 9:33
 */
@Data
public class QueryParam {
    /**
     * 查询语句
     */
    @NotBlank(message = "查询语句(query)不能为空")
    String query;

    /**
     * 权限
     */
    PermitScope operation = PermitScope.QUERY;

    /**
     * 参数
     */
    Map<String, Object> args;
}
