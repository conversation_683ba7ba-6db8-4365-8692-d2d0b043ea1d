package cn.newrank.niop.data.api.biz.dao;

import cn.newrank.niop.data.api.biz.dao.mapper.DatasourceMapper;
import cn.newrank.niop.data.api.biz.pojo.DatasourceConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 13:56
 */
@Component
public class DatasourceDao {

    private final DatasourceMapper datasourceMapper;

    public DatasourceDao(DatasourceMapper datasourceMapper) {
        this.datasourceMapper = datasourceMapper;
    }

    public DatasourceConfig getConfig(String dcId) {
        return datasourceMapper.get(dcId);
    }

}
