package cn.newrank.niop.data.api.biz.service;

import cn.newrank.niop.data.api.biz.pojo.enums.PermitScope;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.Resp;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 13:40
 */
public interface DatasourceService {

    Datasource getDatasource(String dcId);

    /**
     * 查询数据
     *
     * @param dcId  数据源配置ID
     * @param query 查询语句
     * @param args  参数
     * @return {@link Resp}
     */
    Resp query(String dcId, String query, Map<String, Object> args);

    /**
     * 执行语句
     *
     * @param dcId     数据源配置ID
     * @param template 语句
     * @param args     参数
     * @param permit   访问权限
     * @return {@link Resp}
     */
    Resp execute(String dcId, String template, Map<String, Object> args, PermitScope permit);

    /**
     * 执行查询语句
     *
     * @param dcId     数据源ID
     * @param template sql
     * @return {@link Resp}
     */
    Resp dml(String dcId, String template);

    /**
     * 执行语句
     *
     * @param dcId     数据源配置ID
     * @param template 语句
     * @return {@link Resp}
     */
    Resp ddl(String dcId, String template);
}
