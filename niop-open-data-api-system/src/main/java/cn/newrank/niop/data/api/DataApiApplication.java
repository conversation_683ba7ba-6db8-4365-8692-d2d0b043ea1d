package cn.newrank.niop.data.api;


import cn.newrank.niop.web.config.WebAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 10:38
 */
@EnableDiscoveryClient
@SpringBootApplication
@ImportAutoConfiguration(WebAutoConfiguration.class)
public class DataApiApplication {


    public static void main(String[] args) {
        SpringApplication.run(DataApiApplication.class, args);
    }
}