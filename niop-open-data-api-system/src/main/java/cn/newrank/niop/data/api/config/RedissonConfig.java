package cn.newrank.niop.data.api.config;


import cn.newrank.niop.data.api.config.property.RedissonProperties;
import cn.newrank.nrcore.redis.NrRedis;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19 14:11
 */
@Configuration
@EnableConfigurationProperties(RedissonProperties.class)
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient(RedissonProperties redissonProperties) {
        redissonProperties.setBaseKey("open:data:");
        return NrRedis.createClient(redissonProperties);
    }

}
