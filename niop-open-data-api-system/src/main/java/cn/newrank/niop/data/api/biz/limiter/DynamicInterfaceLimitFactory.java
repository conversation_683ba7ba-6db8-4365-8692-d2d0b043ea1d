package cn.newrank.niop.data.api.biz.limiter;


import cn.newrank.niop.data.api.biz.service.DynamicInterfaceService;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import cn.newrank.niop.data.common.limiter.LimitFactory;
import cn.newrank.niop.web.exception.BizExceptions;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/7 10:42
 */
@Component
public class DynamicInterfaceLimitFactory implements LimitFactory {

    public static final String NIOP_APP_ID = "niop-app-id";
    private final RedissonClient redissonClient;
    private final DynamicInterfaceService dynamicInterfaceService;

    public DynamicInterfaceLimitFactory(RedissonClient redissonClient,
                                        DynamicInterfaceService dynamicInterfaceService) {
        this.redissonClient = redissonClient;
        this.dynamicInterfaceService = dynamicInterfaceService;
    }

    @Override
    public boolean tryAcquire(HttpServletRequest request, HandlerMethod handlerMethod) {

        final DynamicInterface dynamicInterface = getDynamicInterface(request);
        if (dynamicInterface == null) {
            // 跳过, 由下面执行参数校验
            return true;
        }


        final String appId = request.getHeader(NIOP_APP_ID);
        if (StringUtils.isBlank(appId)) {
            return true;
        }

        final String interfaceId = dynamicInterface.getInterfaceId();
        // 接口限流
        if (!tryAcquireInterfaceLimit(interfaceId, dynamicInterface)) {
            return false;
        }

        // 授权限流
        return tryAcquireAuthLimit(interfaceId, appId);
    }

    private DynamicInterface getDynamicInterface(HttpServletRequest request) {
        final String uri = request.getRequestURI();
        final int i = uri.lastIndexOf("/");
        if (i < 1) {
            // 跳过, 由下面执行参数校验
            return null;
        }


        final String interfaceId = uri.substring(i + 1);
        if (StringUtils.isBlank(interfaceId)) {
            // 跳过, 由下面执行参数校验
            return null;
        }


        return dynamicInterfaceService.get(interfaceId);
    }

    private boolean tryAcquireAuthLimit(String interfaceId, String appId) {
        final DynamicInterfaceAuth interfaceAuth = dynamicInterfaceService.getAuth(interfaceId, appId);
        if (interfaceAuth == null) {
            throw BizExceptions.createBizException(BizErr.AUTH_ERROR, "应用(ID: {})未授权", appId);
        }

        final String authKey = "dynamic:interface:auth:limiter:" + interfaceAuth.getAuthId();
        final RRateLimiter interfaceAuthLimiter = LimitFactory.createRedisRateLimiter(redissonClient, authKey,
                interfaceAuth.getRefreshPermits(),
                interfaceAuth.getRefreshSeconds());

        return interfaceAuthLimiter.tryAcquire();
    }

    private boolean tryAcquireInterfaceLimit(String interfaceId, DynamicInterface dynamicInterface) {
        final String key = "dynamic:interface:limiter:" + interfaceId;
        final RRateLimiter interfaceRateLimiter = LimitFactory.createRedisRateLimiter(redissonClient, key,
                dynamicInterface.getRefreshPermits(),
                dynamicInterface.getRefreshSeconds());

        return interfaceRateLimiter.tryAcquire();
    }


    @Nullable
    @Override
    public RAtomicLong getCounterIfAccess(HttpServletRequest request, HandlerMethod handlerMethod) {
        final DynamicInterface dynamicInterface = getDynamicInterface(request);
        if (dynamicInterface == null) {
            return null;
        }

        final RAtomicLong throughputCounter = LimitFactory.createThroughputCounter(redissonClient,
                "dynamic:interface:" + dynamicInterface.getInterfaceId());
        if (throughputCounter.incrementAndGet() < dynamicInterface.getRefreshPermits() * 1.5) {
            return throughputCounter;
        } else {
            throughputCounter.decrementAndGet();
            return null;
        }
    }
}
