package cn.newrank.niop.data.api.biz.service.impl;

import cn.newrank.niop.data.api.biz.dao.DatasourceDao;
import cn.newrank.niop.data.api.biz.pojo.DatasourceConfig;
import cn.newrank.niop.data.api.biz.pojo.enums.PermitScope;
import cn.newrank.niop.data.api.biz.service.DatasourceService;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.RedisEnableManager;
import cn.newrank.niop.data.common.ds.*;
import cn.newrank.niop.data.common.event.DatasourceConfigRefreshTopic;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/21 13:48
 */
@Service
@Log4j2
public class DatasourceServiceImpl implements DatasourceService {
    protected final ConcurrentMap<String/*dcId*/, Datasource> datasourceCache;
    private final RedisEnableManager redisEnableManager;
    private final DatasourceDao datasourceDao;

    public DatasourceServiceImpl(RedissonClient redissonClient, DatasourceDao datasourceDao,
                                 DatasourceConfigRefreshTopic datasourceConfigRefreshTopic) {
        this.datasourceDao = datasourceDao;
        this.datasourceCache = new ConcurrentHashMap<>(256);
        this.redisEnableManager = new RedisEnableManager(redissonClient, "ds");
        datasourceConfigRefreshTopic.addListener((channel, msg) -> {
            log.info("数据源配置(dcId: {})被修改，将关闭连接", msg.getDcId());
            final Datasource datasource = datasourceCache.remove(msg.getDcId());
            if (datasource != null) {
                datasource.close();
            }
        });
    }


    @Override
    public Datasource getDatasource(String dcId) {
        final Datasource datasource = datasourceCache.get(dcId);
        if (datasource != null) {
            if (datasource.isActive()) {
                return datasource;
            }

            datasource.close();
            datasourceCache.remove(dcId, datasource);
        }

        return datasourceCache.computeIfAbsent(dcId, id -> {
            if (redisEnableManager.isEnable(dcId)) {
                final Datasource ds = createDatasource(dcId);

                if (ds.isActive()) {
                    return ds;
                } else {
                    ds.close();
                    log.warn("数据源配置(dcId: {})连接失败, e: ", dcId, ds.getCause());
                }

                redisEnableManager.disable(dcId, Duration.ofMinutes(1));
            }


            throw createParamError("数据源(id: {})连接失败", dcId);
        });
    }

    private Datasource createDatasource(String dcId) {
        final DatasourceConfig config = datasourceDao.getConfig(dcId);
        if (config == null) {
            throw createParamError("数据源配置(dcId: {})不存在", dcId);
        }

        final DatasourceFactory factory = config.getType().getFactory();
        final ConfigProperties configProperties = ConfigProperties.of(config.getConfig());

        return factory.create(configProperties);
    }

    @Override
    public Resp query(String dcId, String query, Map<String, Object> args) {
        final Datasource datasource = getDatasource(dcId);
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(query);

        if (args != null) {
            args.forEach(queryBuilder::addParam);
        }

        return datasource.query(queryBuilder);
    }


    @Override
    public Resp execute(String dcId, String template, Map<String, Object> args, PermitScope permit) {
        final Datasource datasource = getDatasource(dcId);

        final ExecuteBuilder executeBuilder = datasource.newExecuteBuilder()
                .template(template);

        if (args != null) {
            args.forEach(executeBuilder::addParam);
        }

        try {
            final Operations operations = executeBuilder.operations();
            if (operations == Operations.MUTATE) {
                if (permit == PermitScope.MUTATE) {
                    return datasource.mutate(executeBuilder.ofMutateBuilder());
                }

                throw createParamError("暂无变更操作权限");
            }

            return datasource.query(executeBuilder.ofQueryBuilder());
        } catch (Exception e) {
            String msg = e.getMessage();
            if (e.getCause() != null) {
                msg += "\n" + e.getCause().getMessage();
            }

            throw createDbError(msg);
        }


    }

    @Override
    public Resp dml(String dcId, String template) {
        final Datasource datasource = getDatasource(dcId);
        try {
            final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                    .template(template)
                    .skipValidate();
            return datasource.query(queryBuilder);
        } catch (Exception e) {
            String msg = e.getMessage();
            if (e.getCause() != null) {
                msg += "\n" + e.getCause().getMessage();
            }

            throw createDbError(msg);
        }
    }

    @Override
    public Resp ddl(String dcId, String template) {

        final Datasource datasource = getDatasource(dcId);

        final ExecuteBuilder executeBuilder = datasource.newExecuteBuilder()
                .template(template);
        try {
            if (executeBuilder.isDdl()) {
                final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                        .template(template)
                        .skipValidate();
                return datasource.query(queryBuilder);
            }
            throw createParamError("不支持该语句:{}", template);
        } catch (Exception e) {
            String msg = e.getMessage();
            if (e.getCause() != null) {
                msg += "\n" + e.getCause().getMessage();
            }

            throw createDbError(msg);
        }
    }
}
