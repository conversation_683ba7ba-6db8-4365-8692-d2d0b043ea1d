<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceAuthMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfaceAuthPo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="authId" column="auth_id" jdbcType="VARCHAR"/>
        <result property="interfaceId" column="interface_id" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="refreshPermits" column="refresh_permits" jdbcType="INTEGER"/>
        <result property="refreshSeconds" column="refresh_seconds" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        auth_id,interface_id,app_id,
        refresh_permits,refresh_seconds
    </sql>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_auth
        where auth_id = #{authId}
    </select>

    <select id="getByInterfaceIdAndAppId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_auth
        where interface_id = #{interfaceId}
        and app_id = #{appId}
    </select>
</mapper>
