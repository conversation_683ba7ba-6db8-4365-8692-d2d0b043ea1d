<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfacePo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="interfaceId" column="interface_id" jdbcType="VARCHAR"/>
        <result property="dcId" column="dc_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="query" column="query" jdbcType="VARCHAR"/>
        <result property="args" column="args" jdbcType="VARCHAR"/>
        <result property="refreshPermits" column="refresh_permits" jdbcType="INTEGER"/>
        <result property="refreshSeconds" column="refresh_seconds" jdbcType="INTEGER"/>
        <result property="maintainerId" column="maintainer_id" jdbcType="VARCHAR"/>
        <result property="maintainerName" column="maintainer_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        interface_id,dc_id,name,
        query,args,refresh_permits,
        refresh_seconds,maintainer_id,maintainer_name,description
    </sql>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface
        where interface_id = #{interfaceId}
    </select>


</mapper>
