<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceSchemaMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.api.biz.pojo.po.DynamicInterfaceSchemaPo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="interfaceId" column="interface_id" jdbcType="VARCHAR"/>
        <result property="dataSchema" column="data_schema" jdbcType="VARCHAR"/>
        <result property="dataExample" column="data_example" jdbcType="VARCHAR"/>
        <result property="enableMapping" column="enable_mapping" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        interface_schema_id, interface_id, data_schema, data_example, enable_mapping
    </sql>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_schema
        where interface_id = #{interfaceId}
    </select>

</mapper>
