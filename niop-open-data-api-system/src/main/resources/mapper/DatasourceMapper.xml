<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.api.biz.dao.mapper.DatasourceMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.api.biz.pojo.DatasourceConfig">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="dcId" column="dc_id" jdbcType="CHAR"/>
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="config" column="config" jdbcType="VARCHAR"/>
        <result property="concurrencyPermit" column="concurrency_permit" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,dc_id,type,
        name,config,concurrency_permit,
        gmt_create,gmt_modified
    </sql>


    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        where dc_id = #{dcId}
    </select>

</mapper>
