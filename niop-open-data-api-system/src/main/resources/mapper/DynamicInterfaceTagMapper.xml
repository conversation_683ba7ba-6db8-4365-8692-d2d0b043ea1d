<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.api.biz.dao.mapper.DynamicInterfaceTagMapper">


    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        interface_id,name
    </sql>


    <select id="getAll" resultType="java.lang.String">
        select name
        from niop_data_dynamic_interface_tag
        where interface_id = #{interfaceId}
    </select>
</mapper>
