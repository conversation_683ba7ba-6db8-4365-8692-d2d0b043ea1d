<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>

    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p})  %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %line %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!--收集所有日志-->
    <appender name="file.info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/home/<USER>/logs/console/${springAppName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>/home/<USER>/logs/console/${springAppName}.log_%d{yyyy-MM-dd}</FileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level ${springAppName} [%thread] %logger{56}.%method:%L -
                %msg%n
            </pattern>
        </layout>
    </appender>

    <!--收集错误日志-->
    <appender name="file.error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/home/<USER>/logs/error/${springAppName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>/home/<USER>/logs/error/${springAppName}.log_%d{yyyy-MM-dd}</FileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level ${springAppName} [%thread] %logger{56}.%method:%L -
                %msg%n
            </pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="file.error"/>
        <appender-ref ref="file.info"/>
    </root>
</configuration>