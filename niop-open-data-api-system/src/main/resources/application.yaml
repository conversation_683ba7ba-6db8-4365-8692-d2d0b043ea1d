server:
  tomcat:
    accept-count: 10240
    threads:
      max: 1600
  port: 9278
  servlet:
    context-path: /open/data
spring:
  application:
    name: niop-open-data-api
  cloud:
    nacos:
      server-addr: @spring.cloud.nacos.server-addr@
      discovery:
        namespace: @spring.cloud.nacos.discovery.namespace@
        group: @spring.cloud.nacos.config.group@
      config:
        namespace: @spring.cloud.nacos.config.namespace@
        group: @spring.cloud.nacos.config.group@
  config:
    import:
      - nacos:niop-open-data.yaml
newrank:
  jackson:
    base-enum-package: cn.newrank.niop.data

mybatis-plus:
  configuration:
    default-enum-type-handler: cn.newrank.nrcore.dao.mybatis.NrDbEnumTypeHandler

---
spring:
  config:
    activate:
      on-profile: product
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: dev
    import:
      - optional:classpath:application-dev.yml
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@


