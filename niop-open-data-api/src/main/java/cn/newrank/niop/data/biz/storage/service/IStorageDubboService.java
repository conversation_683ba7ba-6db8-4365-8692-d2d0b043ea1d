package cn.newrank.niop.data.biz.storage.service;

import cn.newrank.niop.data.biz.storage.pojo.dto.Storage;
import cn.newrank.niop.data.biz.storage.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.storage.pojo.params.StorageGetHistoryRequest;
import cn.newrank.niop.data.biz.storage.pojo.params.StorageGetRequest;


/**
 * dubbo服务接口-数据中心存储数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/08/14 15:01:38
 */
public interface IStorageDubboService {
    /**
     * 获取数据
     *
     * @param request 请求参数
     * @return {@link Storage }
     */
    Storage get(StorageGetRequest request);

    /**
     * 获取历史数据列表
     *
     * @param request 请求
     * @return {@link StorageHistory }
     */
    StorageHistory getHistory(StorageGetHistoryRequest request);
}
