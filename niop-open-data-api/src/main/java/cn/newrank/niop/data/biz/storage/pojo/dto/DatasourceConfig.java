package cn.newrank.niop.data.biz.storage.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/8/4 16:34
 */
@Data
public class DatasourceConfig implements Serializable {

    /**
     * 数据源配置id
     */
    private String dcId;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * 数据源配置名称
     */
    private String name;

    /**
     * 数据源配置
     */
    private String config;

    /**
     * 任务并发数
     */
    private Integer concurrencyPermit;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;
}
