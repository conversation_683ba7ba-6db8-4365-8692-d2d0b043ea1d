package cn.newrank.niop.data.biz.storage.pojo.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/19 13:45
 */
@Data
public class StorageHistory implements Serializable {
    @Serial
    private static final long serialVersionUID = 5269085094496750047L;

    /**
     * 标识符
     */
    private String identifier;

    /**
     * 历史版本
     */
    private List<History> histories;

    /**
     * 总页数
     */
    private long pages;

    /**
     * 总条数
     */
    private long total;

    @Data
    public static class History implements Serializable {
        @Serial
        private static final long serialVersionUID = 5269085095596750047L;

        /**
         * 版本
         */
        private Integer version;

        /**
         * 更新时间
         */
        private Long updateTime;

        /**
         * 数据
         */
        private JSONObject data;
    }
}
