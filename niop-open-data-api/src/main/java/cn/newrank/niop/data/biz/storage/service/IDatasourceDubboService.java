package cn.newrank.niop.data.biz.storage.service;

import cn.newrank.niop.data.biz.storage.pojo.dto.DatasourceConfig;

import java.util.List;

/**
 * dubbo 服务-数据源信息查询
 *
 * <AUTHOR>
 * @since 2025/8/4 15:21
 */
public interface IDatasourceDubboService {
    /**
     * 数据源配置信息获取
     *
     * @param dcId 数据源id
     * @return 数据源配置信息
     */
    DatasourceConfig getDatasourceConfig(String dcId);

    /**
     * 数据源配置信息获取
     *
     * @param dcIds 数据源id
     * @return 数据源配置信息
     */
    List<DatasourceConfig> listDatasourceConfig(List<String> dcIds);

    /**
     * 查询当前支持的所有数据源类型
     *
     * @return 数据源类型
     */
    List<String> listDatasourceType();

    /**
     * 查询数据源
     *
     * @param keyword 查询关键字
     * @param type    数据源类型
     * @return 数据源
     */
    List<DatasourceConfig> listDatasourceByType(String keyword, String type);

}
