package cn.newrank.niop.data.biz.storage.service;

import cn.newrank.niop.data.biz.storage.pojo.dto.DatasourceConfig;

import java.util.List;

/**
 * dubbo 服务-数据源信息查询
 *
 * <AUTHOR>
 * @since 2025/8/4 15:21
 */
public interface IDatasourceDubboService {
    /**
     * 数据源配置信息获取
     *
     * @param dcId 数据源id
     * @return 数据源配置信息
     */
    DatasourceConfig getDatasourceConfig(String dcId);
    /**
     * 数据源配置信息获取
     *
     * @param dcIds 数据源id
     * @return 数据源配置信息
     */
    List<DatasourceConfig> listDatasourceConfig(List<String> dcIds);
}
