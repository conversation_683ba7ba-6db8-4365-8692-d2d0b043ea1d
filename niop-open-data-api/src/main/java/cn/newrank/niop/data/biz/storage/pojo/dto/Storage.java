package cn.newrank.niop.data.biz.storage.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 存储结果
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/08/14 15:03:12
 */
@Data
public class Storage implements Serializable {
    @Serial
    private static final long serialVersionUID = 5169085094496750047L;

    /**
     * 标识符
     */
    private String identifier;

    /**
     * 版本
     */
    private int version;

    /**
     * 更新时间
     */
    private long updateTime;

    /**
     * 数据
     */
    private String data;
}
