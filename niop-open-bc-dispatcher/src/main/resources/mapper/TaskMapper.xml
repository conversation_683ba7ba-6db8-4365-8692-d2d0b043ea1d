<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.bc.biz.mapper.TaskMapper">
    <insert id="save">
        insert into bc_task(job_id, task_id, state)
            value (
                   #{task.jobId},
                   #{task.taskId},
                   #{task.state}
            )
    </insert>

    <update id="update">
        update bc_task
        <set>
            <if test="task.state != null">
                state = #{task.state},
            </if>
            <if test="task.startTime != null">
                start_time = #{task.startTime},
            </if>
            <if test="task.endTime != null">
                end_time = #{task.endTime}
            </if>
        </set>
        where task_id = #{task.taskId}
    </update>

    <select id="getLastestTask" resultType="cn.newrank.niop.bc.biz.pojo.Task">
        select *
        from bc_task
        where job_id = #{jobId}
        and (params is null or params = '')
        order by id desc
        limit 1
    </select>

</mapper>
