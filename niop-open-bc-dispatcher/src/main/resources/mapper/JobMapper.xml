<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.bc.biz.mapper.JobMapper">

    <update id="updateNextTime">
        update bc_job
        set next_time = #{nextTime},
            last_time = now()
        where job_id = #{jobId}
    </update>

    <update id="updateLastTime">
        update bc_job
        set last_time = now()
        where job_id = #{jobId}
    </update>

    <select id="find" resultType="cn.newrank.niop.bc.biz.pojo.Job">
        select *
        from bc_job
        where status = 1
          and type = 1
          and next_time &lt;= #{now}
        for update
    </select>

    <select id="getJob" resultType="cn.newrank.niop.bc.biz.pojo.Job">
        select *
        from bc_job
        where job_id = #{jobId}
    </select>

</mapper>
