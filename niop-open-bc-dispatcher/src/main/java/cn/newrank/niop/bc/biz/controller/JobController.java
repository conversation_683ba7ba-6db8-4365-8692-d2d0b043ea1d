package cn.newrank.niop.bc.biz.controller;

import cn.newrank.niop.bc.biz.component.TaskSender;
import cn.newrank.niop.bc.biz.pojo.dto.JobExecDTO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/8/8 11:37:20
 * @version: 1.0.0
 * @description:
 */
@RestController
@RequestMapping("/bc/job")
public class JobController {

    @Autowired
    private TaskSender taskSender;

    @PostMapping("/exec")
    public boolean exec(@RequestBody @Valid JobExecDTO jobExec) {
        return taskSender.send(jobExec.getJobId(), jobExec.getParams());
    }

}
