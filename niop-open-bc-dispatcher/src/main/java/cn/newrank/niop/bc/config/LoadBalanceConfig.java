package cn.newrank.niop.bc.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 17:26:23
 * @version: 1.0.0
 * @description:
 */
@Configuration
public class LoadBalanceConfig {

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

}
