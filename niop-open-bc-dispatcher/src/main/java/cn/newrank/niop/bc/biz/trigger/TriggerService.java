package cn.newrank.niop.bc.biz.trigger;

import cn.newrank.niop.bc.biz.component.TaskSender;
import cn.newrank.niop.bc.biz.exception.BcBizError;
import cn.newrank.niop.bc.biz.mapper.JobMapper;
import cn.newrank.niop.bc.biz.pojo.Job;
import cn.newrank.nrcore.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/8/13 10:19:36
 * @version: 1.0.0
 * @description:
 */
@Service
public class TriggerService {

    @Autowired
    private JobMapper jobMapper;
    @Autowired
    private TaskSender taskSender;

    public boolean trigger(String jobId, JSONObject params) {
        Job job = jobMapper.getJob(jobId);
        if (job.getStatus() == 0) {
            throw new BizException(BcBizError.JOB_BANNED);
        }
        if (job.getType() != 2) {
            throw new BizException(BcBizError.JOB_TYPE_NOT_SUPPORT);
        }

        boolean success = taskSender.send(jobId, params);
        if (success) {
            jobMapper.updateLastTime(jobId, LocalDateTime.now());
        }
        return success;
    }

}
