package cn.newrank.niop.bc.biz.mapper;

import cn.newrank.niop.bc.biz.pojo.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 19:43:32
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface TaskMapper {

    /**
     * 查询job下面最新的一个任务
     *
     * @param jobId jobId
     * @return 任务信息
     */
    Task getLastestTask(@Param("jobId") String jobId);

    /**
     * 保存任务
     *
     * @param task 任务信息
     */
    void save(@Param("task") Task task);

    /**
     * 更新任务信息
     * @param task  任务信息
     */
    void update(@Param("task") Task task);

}
