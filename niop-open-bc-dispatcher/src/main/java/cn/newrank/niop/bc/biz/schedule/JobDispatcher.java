package cn.newrank.niop.bc.biz.schedule;

import cn.newrank.niop.bc.biz.component.TaskSender;
import cn.newrank.niop.bc.biz.mapper.JobMapper;
import cn.newrank.niop.bc.biz.mapper.TaskMapper;
import cn.newrank.niop.bc.biz.pojo.Job;
import cn.newrank.niop.bc.biz.pojo.Task;
import cn.newrank.niop.bc.biz.pojo.TaskState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: xuguangjie
 * @date: 2025/7/30 16:32:47
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Component
public class JobDispatcher {

    @Autowired
    private JobMapper jobMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskSender taskSender;

    @Scheduled(cron = "0/10 * * * * ?")
    public void dispatcher() {
        List<Job> jobs = jobMapper.find(LocalDateTime.now());

        for (Job job : jobs) {
            String jobId = job.getJobId();
            Task task = taskMapper.getLastestTask(jobId);
            if (task != null && (task.getState() == TaskState.PENDING || task.getState() == TaskState.RUNNING)) {
                nextTime(job);
                continue;
            }

            boolean success = taskSender.send(job.getJobId(), null);
            if (success) {
                nextTime(job);
            }
        }
    }

    private void nextTime(Job job) {
        String cron = job.getCron();
        CronExpression cronExpression = CronExpression.parse(cron);
        LocalDateTime nextTime = cronExpression.next(LocalDateTime.now());
        jobMapper.updateNextTime(job.getJobId(), nextTime);
    }

}
