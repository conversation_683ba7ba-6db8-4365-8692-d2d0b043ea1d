package cn.newrank.niop.bc.biz.mapper;

import cn.newrank.niop.bc.biz.pojo.Job;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/29 11:55:18
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface JobMapper {

    /**
     * 查询当前需要被执行的job
     *
     * @return job列表
     */
    List<Job> find(@Param("now") LocalDateTime now);

    /**
     * 修改下一次执行时间
     *
     * @param jobId    jobId
     * @param nextTime 下次执行时间
     */
    void updateNextTime(@Param("jobId") String jobId, @Param("nextTime") LocalDateTime nextTime);

    /**
     * 更新最近执行时间
     *
     * @param jobId    工作id
     * @param lastTime 执行时间
     */
    void updateLastTime(@Param("jobId") String jobId, @Param("lastTime") LocalDateTime lastTime);

    /**
     * 查询job
     *
     * @param jobId jobId
     * @return 工作信息
     */
    Job getJob(@Param("jobId") String jobId);

}
