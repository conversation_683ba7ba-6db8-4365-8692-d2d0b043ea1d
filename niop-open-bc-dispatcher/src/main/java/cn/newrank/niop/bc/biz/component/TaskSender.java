package cn.newrank.niop.bc.biz.component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xuguang<PERSON>e
 * @date: 2025/8/8 13:57:33
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Component
public class TaskSender {

    private final HttpHeaders headers = new HttpHeaders();

    {
        headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    }

    @Autowired
    private RestTemplate restTemplate;

    public boolean send(String jobId, JSONObject params) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("jobId", jobId);
        httpParams.put("params", params);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(httpParams, headers);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity("http://niop-open-bc-worker/open/bc/worker/job/exec", entity, String.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("【百川】发送任务失败, http status: {}", responseEntity.getStatusCode());
            return false;
        }
        return true;
    }

}
