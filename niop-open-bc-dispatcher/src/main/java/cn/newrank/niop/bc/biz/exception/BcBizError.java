package cn.newrank.niop.bc.biz.exception;

import cn.newrank.nrcore.exception.BizError;

/**
 * @author: xug<PERSON><PERSON><PERSON>
 * @date: 2025/8/13 10:23:14
 * @version: 1.0.0
 * @description:
 */
public enum BcBizError implements BizError {

    JOB_BANNED(10001, "工作已被禁用"),

    JOB_TYPE_NOT_SUPPORT(20001, "工作类型不支持"),

    ;


    final int code;
    final String desc;

    BcBizError(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
