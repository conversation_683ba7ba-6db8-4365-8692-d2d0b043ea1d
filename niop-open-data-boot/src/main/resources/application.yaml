server:
  port: 9302
  servlet:
    context-path: /open/data/boot
spring:
  application:
    name: niop-open-data-boot
  cloud:
    nacos:
      server-addr: @spring.cloud.nacos.server-addr@
      discovery:
        namespace: @spring.cloud.nacos.discovery.namespace@
        group: @spring.cloud.nacos.config.group@
      config:
        namespace: @spring.cloud.nacos.config.namespace@
        group: @spring.cloud.nacos.config.group@
  config:
    import:
      - nacos:niop-open-data-boot.yaml

---
spring:
  config:
    activate:
      on-profile: product
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: dev
    import:
      - optional:classpath:application-dev.yml
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
