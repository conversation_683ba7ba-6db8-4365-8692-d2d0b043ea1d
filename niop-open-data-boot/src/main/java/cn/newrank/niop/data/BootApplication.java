package cn.newrank.niop.data;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/8/1 15:37:13
 * @version: 1.0.0
 * @description:
 */
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"cn.newrank.niop.bc", "cn.newrank.niop.data"})
@MapperScan(basePackages = {"cn.newrank.niop.bc"})
public class BootApplication {

    public static void main(String[] args) {
        SpringApplication.run(BootApplication.class, args);
    }

}
