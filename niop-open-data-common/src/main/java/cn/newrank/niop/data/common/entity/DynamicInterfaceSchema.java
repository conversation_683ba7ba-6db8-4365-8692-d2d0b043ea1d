package cn.newrank.niop.data.common.entity;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceSchema {

    public static final int ID_LENGTH = 10;
    /**
     * 数据样例
     */
    String dataExample;
    /**
     * 是否开始映射
     */
    Boolean enableMapping;
    /**
     *
     */
    private LocalDateTime gmtModified;
    /**
     *
     */
    private LocalDateTime gmtCreate;
    /**
     * 接口SchemaID
     */
    private String interfaceSchemaId;
    /**
     * 接口ID
     */
    private String interfaceId;
    /**
     * 数据解析Schema
     */
    private String dataSchema;

    public static DynamicInterfaceSchema fromJson(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return JSON.parseObject(json, DynamicInterfaceSchema.class);
    }

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}