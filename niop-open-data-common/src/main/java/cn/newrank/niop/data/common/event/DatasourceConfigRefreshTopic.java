package cn.newrank.niop.data.common.event;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;

/**
 * 数据源刷新事件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/20 9:40
 */
@Log4j2
public class DatasourceConfigRefreshTopic implements GlobalTopic<DatasourceConfigRefreshEvent> {

    private final RTopic topic;

    public DatasourceConfigRefreshTopic(RedissonClient redissonClient) {
        this.topic = redissonClient.getTopic("topic:ds:refresh");
    }

    @Override
    public void emitEvent(DatasourceConfigRefreshEvent event) {
        topic.publish(JSON.toJSONString(event));
    }

    @Override
    public void addListener(MessageListener<DatasourceConfigRefreshEvent> messageListener) {
        topic.addListener(String.class, (channel, msg) -> {
                    try {
                        messageListener.onMessage(channel, JSON.parseObject(msg, DatasourceConfigRefreshEvent.class));
                    } catch (Exception e) {
                        log.error("数据源刷新事件监听异常", e);
                    }
                }
        );
    }


}
