package cn.newrank.niop.data.common.ds;

import lombok.Data;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

/**
 * 字段定义
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/1 15:36
 */
@Data
public class Column {
    public static final BeanPropertyRowMapper<Column> MAPPER = BeanPropertyRowMapper.newInstance(Column.class);

    /**
     * 列名
     */
    String name;
    /**
     * 列描述
     */
    String description;
    /**
     * 是否可空
     */
    Boolean nullable;
    /**
     * 类型
     */
    String type;
}
