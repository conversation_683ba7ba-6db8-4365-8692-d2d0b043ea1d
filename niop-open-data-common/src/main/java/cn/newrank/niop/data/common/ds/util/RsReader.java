package cn.newrank.niop.data.common.ds.util;

import cn.newrank.niop.data.common.ds.ObjectMapper;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 查询结果定义
 */
public class RsReader {
    private final AtomicBoolean firstRead = new AtomicBoolean(true);
    @Getter
    private final JSONArray records = new JSONArray();
    @Getter
    private final List<String> columns = new ArrayList<>();

    private final ObjectMapper objectMapper;

    public RsReader(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public void read(ResultSet rs) throws SQLException {
        final ResultSetMetaData metaData = rs.getMetaData();

        if (firstRead.getAndSet(false)) {
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                columns.add(metaData.getColumnName(i));
            }
        }

        final JSONObject object = new JSONObject();
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            final Object item = rs.getObject(i);

            object.put(columns.get(i - 1), objectMapper.map(item));
        }

        records.add(object);
    }

}
