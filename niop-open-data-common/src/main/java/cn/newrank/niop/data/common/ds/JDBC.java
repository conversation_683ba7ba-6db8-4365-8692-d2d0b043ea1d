package cn.newrank.niop.data.common.ds;


import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.builder.AbstractExecuteBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractMutateBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractQueryBuilder;
import cn.newrank.niop.data.common.ds.util.*;
import com.alibaba.druid.pool.DruidDataSource;
import lombok.Data;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Limit;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.EmptySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;

import static cn.newrank.niop.web.exception.BizExceptions.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 19:40
 */
@Log4j2
public abstract class JDBC extends AbstractDatasource {

    public static final String STR_SIGN = "'";
    public static final String ESCAPE_STR_SIGN = "''";
    /**
     * 语言驱动
     */
    private static final LanguageDriver LANGUAGE_DRIVER = new LanguageDriver(value -> {
        if (value == null) {
            return "NULL";
        }

        if (value instanceof CharSequence) {
            return STR_SIGN + value.toString().replace(STR_SIGN, ESCAPE_STR_SIGN) + STR_SIGN;
        }

        return value;
    }, () -> new LanguageDriver.PlaceholderDiscriminator() {
        private static final char ESCAPE = '\\';
        private boolean value = false;
        private boolean lastEscape = false;
        private char startCh = '0';

        @Override
        public boolean discriminate(char ch) {
            boolean escape = lastEscape;
            lastEscape = ch == ESCAPE;

            // 不是转义字符", 那么就是key和值的开始或结束
            switch (ch) {
                case '"', '\'' -> {
                    if (!escape && (startCh == '0' || startCh == ch)) {
                        value = !value;
                        if (startCh == '0') {
                            startCh = ch;
                        } else {
                            startCh = '0';
                        }
                    }

                    return false;
                }
                case PLACEHOLDER -> {
                    return !value;
                }

                default -> {
                    return false;
                }
            }
        }
    });
    @Getter
    protected final NamedParameterJdbcTemplate jdbcTemplate;
    protected final DruidDataSource dataSource = new DruidDataSource();
    protected final String database;
    protected final ObjectMapper objectMapper;
    protected final AtomicReference<PlatformTransactionManager> transactionManager = new AtomicReference<>();

    protected JDBC(ConfigProperties configProperties, String schema, String... args) {
        super(configProperties);
        String username = configProperties.getCheckedString(ConfigKey.USERNAME);
        String password = configProperties.getCheckedString(ConfigKey.PASSWORD);
        this.database = configProperties.getCheckedString(ConfigKey.DATABASE);
        final String address = configProperties.getCheckedString(ConfigKey.ADDRESS);

        this.jdbcTemplate = createJdbcTemplate(getUrl(schema, address, database, args), username, password);
        this.objectMapper = valueMapper();
    }

    private static String getUrl(String schema, String address, String database, String... args) {
        final String dbUrl = "jdbc:" + schema + "://" + address + "/" + database;

        if (args == null || args.length == 0) {
            return dbUrl;
        }

        return dbUrl + "?" + String.join("&", args);
    }

    private NamedParameterJdbcTemplate createJdbcTemplate(String url, String username, String password) {
        DriverManager.setLoginTimeout(5);
        try (Connection ignored = DriverManager.getConnection(url, username, password)) {
            dataSource.setUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);
            dataSource.setBreakAfterAcquireFailure(true);
            dataSource.setTestWhileIdle(true);
            dataSource.setTestOnBorrow(true);
            dataSource.setValidationQuery("SELECT 1");
            dataSource.setTestOnReturn(false);
            dataSource.setConnectionErrorRetryAttempts(3);
            dataSource.setKeepAlive(true);
            dataSource.setKeepAliveBetweenTimeMillis(900000);
            dataSource.setQueryTimeout(50);
            dataSource.setMaxWait(60000);
            dataSource.setMinEvictableIdleTimeMillis(600000);
            dataSource.setMaxEvictableIdleTimeMillis(900000);
            dataSource.setTimeBetweenEvictionRunsMillis(60000);
            // 连接池配置
            dataSource.setMaxActive(20);
            dataSource.setMinIdle(10);
            dataSource.setInitialSize(5);
            dataSource.init();

            return new NamedParameterJdbcTemplate(dataSource);
        } catch (Exception exception) {
            unhealthy(createDbError(exception.getMessage()));
            return null;
        }
    }

    public NamedParameterJdbcTemplate availableJdbcTemplate() {
        checkHealth();

        return jdbcTemplate;
    }

    /**
     * 获取事务管理器
     *
     * @return 事务管理器
     */
    public PlatformTransactionManager getTransactionManager() {
        return transactionManager.getAndUpdate(x -> {
            if (x == null) {
                return new DataSourceTransactionManager(dataSource);
            }

            return x;
        });
    }

    /**
     * 值映射
     *
     * @return 值映射
     */
    public ObjectMapper valueMapper() {
        return ObjectMapper.self();
    }

    @Override
    public QueryBuilder newQueryBuilder() {
        return new JDBCQueryBuilder(this);
    }

    @Override
    public Resp query(QueryBuilder queryBuilder) {
        checkHealth();

        if (queryBuilder instanceof JDBCQueryBuilder qb) {
            try {
                if (qb.isPageQuery()) {
                    // 统计总数
                    final String countQuery = qb.getCountQuery();
                    final long total = queryCount(countQuery);

                    // 分页查询
                    return query(qb.getQuery(), (query, rsReader) ->
                            QueryResp.ofPage(rsReader, query, total, qb.getPageSize())
                    );
                } else {
                    return query(qb.getQuery(), (query, rsReader) -> QueryResp.of(rsReader, query));
                }
            } catch (Exception e) {
                String msg = e.getMessage();
                if (e.getCause() != null) {
                    msg += "\n" + e.getCause().getMessage();
                }

                throw createDbError(msg);
            }
        }

        throw createParamError("QueryBuilder({}) 类型错误", queryBuilder.getClass().getName());
    }

    /**
     * 执行查询
     *
     * @param query   查询语句
     * @param mapping 结果映射
     * @return 查询结果
     */
    private Resp query(String query, BiFunction<String, RsReader, Resp> mapping) {
        final RsReader rsReader = new RsReader(valueMapper());

        availableJdbcTemplate().query(query, rsReader::read);

        return mapping.apply(query, rsReader);
    }

    private long queryCount(String query) {
        final Map<String, Object> resp = availableJdbcTemplate().queryForMap(query, new HashMap<>());

        final Object total = resp.get("total");
        if (total == null) {
            return 0;
        }
        if (total instanceof Long num) {
            return num;
        }

        if (total instanceof Number num) {
            return num.longValue();
        }

        return Long.parseLong(total.toString());
    }

    @Override
    public Resp mutate(MutateBuilder mutateBuilder) {
        if (mutateBuilder instanceof JDBCMutateBuilder jdbcMutateBuilder) {
            try {
                final String sql = jdbcMutateBuilder.getSQL();

                final int effectedRows = availableJdbcTemplate().update(sql, EmptySqlParameterSource.INSTANCE);
                return new MutateResp(effectedRows, sql);
            } catch (Exception e) {
                String msg = e.getMessage();
                if (e.getCause() != null) {
                    msg += "\n" + e.getCause().getMessage();
                }

                throw createDbError(msg);
            }
        }

        throw createParamError("MutateBuilder({}) 类型错误", mutateBuilder.getClass().getName());
    }

    @Override
    public MutateBuilder newMutateBuilder() {
        return new JDBCMutateBuilder(this);
    }

    @Override
    public ExecuteBuilder newExecuteBuilder() {
        return new JDBCExecuteBuilder(this);
    }

    @Override
    public void close() {
        try {
            dataSource.close();
        } catch (Exception e) {
            log.warn("dcId {} close datasource error", getConfig().getValue(ConfigKey.DATABASE), e);
        }
    }

    /**
     * 执行构建器
     */
    public static class JDBCExecuteBuilder extends AbstractExecuteBuilder<JDBC> {
        static final List<String> DDL_WORD = List.of("DESC", "DESCRIBE", "EXPLAIN", "SHOW",
                "desc", "describe", "explain", "show");

        private final AtomicBoolean init = new AtomicBoolean(false);
        private String statement;

        public JDBCExecuteBuilder(JDBC jdbc) {
            super(jdbc);
        }

        @Override
        public final Operations operations() {
            if (init.compareAndSet(false, true)) {
                this.statement = LANGUAGE_DRIVER.process(template, dynamicParams).trim();
            }

            if (statement.startsWith("SELECT") || statement.startsWith("select")) {
                return Operations.QUERY;
            } else {
                return Operations.MUTATE;
            }
        }

        @Override
        public boolean isDdl() {
            return DDL_WORD.stream().anyMatch(template::startsWith);
        }

        @Override
        public final MutateBuilder ofMutateBuilder() {
            if (operations() == Operations.MUTATE) {
                return new JDBCMutateBuilder(datasource, statement, dynamicParams);
            }

            throw createParamError("当前语句非变更操作类型");
        }

        @Override
        public final QueryBuilder ofQueryBuilder() {
            if (operations() == Operations.QUERY) {
                return createQueryBuilder(statement, dynamicParams);
            }

            throw createParamError("当前语句非查询操作类型");
        }

        protected QueryBuilder createQueryBuilder(String statement, Map<String, Object> params) {
            return new JDBCQueryBuilder(datasource, statement, params);
        }
    }

    /**
     * 变更语句构造器
     */
    public static class JDBCMutateBuilder extends AbstractMutateBuilder<JDBC> {
        private String statement;

        public JDBCMutateBuilder(JDBC jdbc, String statement, Map<String, Object> params) {
            super(jdbc);
            this.statement = statement;
            this.dynamicParams.putAll(params);
        }

        public JDBCMutateBuilder(JDBC jdbc) {
            super(jdbc);
        }


        public String getSQL() {
            if (StringUtils.isNotBlank(statement)) {
                return statement;
            }

            if (template == null) {
                throw createParamError("参数template 不能为空");
            }

            return LANGUAGE_DRIVER.process(template, dynamicParams);
        }
    }


    /**
     * 变更结果定义
     */
    static class MutateResp implements Resp {
        private final long affectedRows;
        private final String query;

        public MutateResp(long affectedRows, String query) {
            this.affectedRows = affectedRows;
            this.query = query;
        }


        @Override
        public DataView getDataView() {
            return new DataViewImpl(affectedRows);
        }

        @Override
        public PageView getPageView() {
            return new PageViewImpl(affectedRows, query);
        }

        @Override
        public String query() {
            return query;
        }

        @Override
        @SuppressWarnings("unchecked")
        public Long data() {
            return affectedRows;
        }


        public record DataViewImpl(long affectedRows) implements DataView {
            @Override
            public DataView mapping(String schema) {
                throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
            }
        }

        public record PageViewImpl(long affectedRows, String query) implements PageView {
            @Override
            public PageView mapping(String schema) {
                throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
            }
        }
    }

    /**
     * 查询参数定义
     */
    public static class JDBCQueryBuilder extends AbstractQueryBuilder<JDBC> {
        /**
         * 默认查询模板
         */
        private static final String DEFAULT_QUERY_TEMPLATE = "SELECT * FROM %s LIMIT 20";
        String builtQuery;
        /**
         * 解析语句对象
         */
        Statement statement;


        public JDBCQueryBuilder(JDBC jdbc, String statement, Map<String, Object> params) {
            super(jdbc);
            this.statement = SQL.parse(statement);
            this.dynamicParams.putAll(params);
        }

        public JDBCQueryBuilder(JDBC jdbc) {
            super(jdbc);
        }

        /**
         * 获取查询语句
         *
         * @return 查询语句
         */
        public String getQuery() {
            if (builtQuery != null) {
                return builtQuery;
            }
            if (skipValidate) {
                return template;
            }

            final PlainSelect select = (PlainSelect) getSelect().getSelectBody();
            builtQuery = enablePreview ? formatPreviewQuery(select) : formatQuery(select);

            return builtQuery;
        }


        /**
         * 格式化查询语句
         *
         * @return 查询语句
         */
        protected String formatQuery(PlainSelect select) {
            if (isPageQuery()) {
                setPageLimitParam(select);
            }

            final Limit limit = select.getLimit();
            if (limit == null) {
                final Limit newLimit = new Limit();
                newLimit.setRowCount(new LongValue(1000));
                select.setLimit(newLimit);
            } else {
                final Expression rowCount = limit.getRowCount();
                if (Long.parseLong(rowCount.toString()) > 1000) {
                    throw createParamError("LIMIT条数不能超过 1000");
                }
            }

            return select.toString();
        }


        /**
         * 格式化预览语句
         *
         * @return 预览语句
         */
        public String formatPreviewQuery(PlainSelect select) {
            select.setWhere(null);

            final Limit limit = new Limit();
            limit.setRowCount(new LongValue(20));
            select.setLimit(limit);

            select.setOrderByElements(null);

            return select.toString();
        }

        protected void setPageLimitParam(PlainSelect select) {
            final long pageSize = getPageSize();
            final long page = getPage();

            final Limit newLimit = new Limit();
            newLimit.setRowCount(new LongValue(pageSize));
            newLimit.setOffset(new LongValue(pageSize * (page - 1)));
            select.setLimit(newLimit);
        }

        /**
         * 获取查询语句对象
         *
         * @return 查询语句对象
         */
        private Select getSelect() {
            if (statement == null) {
                if (template == null || template.isBlank()) {
                    // 使用集合参数初始化语句
                    if (collection == null) {
                        throw createParamError("collection or template is null");
                    }

                    template = String.format(DEFAULT_QUERY_TEMPLATE, collection);
                }

                final String sql = LANGUAGE_DRIVER.process(template, dynamicParams);
                statement = SQL.parse(sql);
            }

            if (statement instanceof Select select) {
                return select;
            }

            throw createParamError("仅支持查询语句");
        }

        /**
         * 获取统计查询语句
         *
         * @return 统计查询语句
         */
        public String getCountQuery() {
            return SQL.getCountSql(getSelect().toString());
        }

        /**
         * 获取每页数量
         *
         * @return 每页数量
         */
        public long getPageSize() {
            Object size = dynamicParams.get(PAGE_QUERY_PARAM_SIZE);

            // 预览默认1
            if (enablePreview) {
                size = 1;
            }

            if (size == null) {
                throw createParamError("分页查询必须指定 _size");
            }

            if (size instanceof Number number) {
                return number.longValue();
            }

            return Long.parseLong(size.toString());
        }

        /**
         * 获取页码
         *
         * @return 页码
         */
        public long getPage() {
            Object page = dynamicParams.get(PAGE_QUERY_PARAM_PAGE);

            // 预览默认20
            if (enablePreview) {
                page = 20;
            }

            if (page == null) {
                throw createParamError("分页查询必须指定 _page");
            }

            if (page instanceof Number number) {
                return number.longValue();
            }

            return Long.parseLong(page.toString());
        }
    }


    /**
     * 查询结果定义
     */
    @Data
    public static class QueryResp implements Resp {
        List<String> columns;
        List<Object> records;
        String query;
        Long total;
        Long size;

        private QueryResp(List<String> columns, List<Object> records, String query) {
            this.columns = columns;
            this.records = records;
            this.query = query;
        }

        private QueryResp(List<String> columns, List<Object> records, String query, Long total, Long size) {
            this.columns = columns;
            this.records = records;
            this.query = query;
            this.total = total;
            this.size = size;
        }

        public static Resp of(RsReader rsReader, String query) {
            return new QueryResp(rsReader.getColumns(), rsReader.getRecords(), query);
        }

        /**
         * 分页查询
         *
         * @param rsReader 数据
         * @param query    查询语句
         * @param total    总数
         * @param size     页大小
         * @return QueryResp
         */
        public static Resp ofPage(RsReader rsReader, String query, Long total, Long size) {
            return new QueryResp(rsReader.getColumns(), rsReader.getRecords(), query, total, size);
        }

        @Override
        public String query() {
            return query;
        }

        @Override
        @SuppressWarnings("unchecked")
        public <T> T data() {
            return (T) records;
        }

        @Override
        public DataView getDataView() {
            if (size != null) {
                return new DataPageViewImpl(records, total, size);
            } else {
                return new DataViewImpl(records);
            }
        }

        @Override
        public PageView getPageView() {
            return new PageViewImpl(query, columns, records);
        }


        @Data
        public static class PageViewImpl implements PageView {
            final String query;
            final List<String> columns;
            final List<Object> records;

            public PageViewImpl(String query, List<String> columns, List<Object> records) {
                this.query = query;
                this.columns = columns;
                this.records = records;
            }

            @Override
            public String query() {
                return query;
            }

            @Override
            public PageView mapping(String schema) {
                // Schema解析
                Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                // Schema校验
                String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                if (StrUtil.isNotBlank(validMessage)) {
                    throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                }

                List<String> columnList = SchemaColumnParser.parseBySchema(schema, columns);
                return new ParsedPageViewImpl(query, columnList, parsedData);
            }
        }

        /**
         * 数据视图
         *
         * <AUTHOR>
         * @version 1.0.0
         * @since 2024/3/21 15:26
         */
        public record DataViewImpl(List<Object> records) implements DataView {
            @Override
            public DataView mapping(String schema) {
                // Schema解析
                Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                // Schema校验
                String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                if (StrUtil.isNotBlank(validMessage)) {
                    throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                }

                return new ParsedDataViewImpl(parsedData);
            }
        }

        /**
         * 数据分页视图
         *
         * <AUTHOR>
         * @version 1.0.0
         * @since 2024/3/21 15:26
         */
        @Data
        public static class DataPageViewImpl implements DataView {
            final Long total;
            final Long pages;
            final List<Object> records;

            public DataPageViewImpl(List<Object> records, Long total, Long size) {
                this.records = records;
                this.total = total;
                this.pages = total % size == 0 ? total / size : total / size + 1;
            }

            @Override
            public DataView mapping(String schema) {
                // Schema解析
                Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                // Schema校验
                String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                if (StrUtil.isNotBlank(validMessage)) {
                    throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                }

                return new ParsedDataPageViewImpl(total, pages, parsedData);
            }
        }

        @Data
        public static class ParsedDataViewImpl implements DataView {
            final Object records;

            public ParsedDataViewImpl(Object records) {
                this.records = records;
            }

            @Override
            public DataView mapping(String schema) {
                throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
            }
        }

        @Data
        public static class ParsedDataPageViewImpl implements DataView {
            final Long total;
            final Long pages;
            final Object records;

            public ParsedDataPageViewImpl(Long total, Long pages, Object records) {
                this.total = total;
                this.pages = pages;
                this.records = records;
            }

            @Override
            public DataView mapping(String schema) {
                throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
            }
        }

        @Data
        public static class ParsedPageViewImpl implements PageView {
            final String query;
            final List<String> columns;
            final Object records;

            public ParsedPageViewImpl(String query, List<String> columns, Object records) {
                this.query = query;
                this.columns = columns;
                this.records = records;
            }

            @Override
            public String query() {
                return query;
            }

            @Override
            public PageView mapping(String schema) {
                throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
            }
        }

    }

}
