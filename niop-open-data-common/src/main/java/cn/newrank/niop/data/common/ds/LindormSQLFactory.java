package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigProperties;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

import java.sql.Types;
import java.util.List;

/**
 * lindorm
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/12 10:19
 */
public class LindormSQLFactory implements DatasourceFactory {
    public static final LindormSQLFactory DEFAULT = new LindormSQLFactory();


    @Override
    public LindormSQL create(ConfigProperties configProperties) {
        return new LindormSQL(configProperties);
    }

    public static final class LindormSQL extends JDBC {
        private static final String QUERY_TABLE_INFO = """
                SELECT
                    table_name AS name,
                    table_comment AS description
                FROM
                    information_schema.tables
                WHERE
                    table_schema = :tableSchema;
                """;
        private static final String QUERY_COLUMN_INFO = """
                SELECT
                    column_name AS name,
                    data_type AS type,
                    is_nullable AS nullable,
                    column_comment AS description
                FROM
                    information_schema.columns
                WHERE
                    table_schema = :tableSchema
                    AND table_name = :tableName;
                """;

        private LindormSQL(ConfigProperties configProperties) {
            super(configProperties, "mysql", "sslMode=disabled", "allowPublicKeyRetrieval=true",
                    "useServerPrepStmts=true", "useLocalSessionState=true", "rewriteBatchedStatements=true",
                    "prepStmtCacheSize=100", "prepStmtCacheSqlLimit=500000");
        }

        @Override
        public List<Collection> getCollections() {
            checkHealth();
            final MapSqlParameterSource source = new MapSqlParameterSource()
                    .addValue("tableSchema", database, Types.VARCHAR);

            return availableJdbcTemplate().query(QUERY_TABLE_INFO, source, Collection.MAPPER);
        }

        @Override
        public List<Collection> getChildren(String collection) {
            return List.of();
        }

        @Override
        public List<Column> getColumns(String collection) {
            checkHealth();
            final MapSqlParameterSource source = new MapSqlParameterSource()
                    .addValue("tableSchema", database, Types.VARCHAR)
                    .addValue("tableName", collection, Types.VARCHAR);

            return availableJdbcTemplate().query(QUERY_COLUMN_INFO, source, Column.MAPPER);
        }
    }
}
