package cn.newrank.niop.data.common.ds;


import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.builder.AbstractExecuteBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractMutateBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractQueryBuilder;
import cn.newrank.niop.data.common.ds.util.BuilderSnack3SchemaDataParser;
import cn.newrank.niop.data.common.ds.util.SchemaDataValidator;
import cn.newrank.nrcore.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.Data;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.newrank.niop.web.exception.BizExceptions.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/20 14:06
 */
@Log4j2
public class EsFactory implements DatasourceFactory {

    public static final EsFactory DEFAULT = new EsFactory();

    @Override
    public Es create(ConfigProperties configProperties) {
        return new Es(configProperties);
    }


    public Es create(RestClient restClient) {
        return new Es(restClient);
    }


    public static class Es extends AbstractDatasource {
        final RestClient client;

        public Es(RestClient restClient) {
            super(null);
            this.client = restClient;
            tryConnect();
        }

        public Es(ConfigProperties configProperties) {
            super(configProperties);
            final CredentialsProvider credentialsProvider = createCredentialsProvider(configProperties);
            this.client = RestClient.builder(createHttpHost(configProperties))
                    .setHttpClientConfigCallback(httpAsyncClientBuilder ->
                            httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                                    .setConnectionTimeToLive(60, TimeUnit.SECONDS)
                                    .setMaxConnTotal(20)
                    )
                    .setRequestConfigCallback(
                            requestConfigBuilder ->
                                    requestConfigBuilder
                                            .setConnectTimeout(5000)
                                            .setSocketTimeout(30000)
                    )
                    .build();

            tryConnect();
        }

        private static void startParseColumn(String preCol, JSONObject props, List<Column> columns) {
            if (props == null || props.isEmpty()) {
                return;
            }

            props.forEach((field, setting) -> {
                final Column column = new Column();

                final String fullField = preCol == null ? field : preCol + "." + field;

                column.setName(fullField);
                if (setting instanceof JSONObject jsonSetting) {
                    column.setType(jsonSetting.getString("type"));

                    final JSONObject subProps = jsonSetting.getJSONObject("properties");
                    startParseColumn(fullField, subProps, columns);
                }

                columns.add(column);
            });
        }

        @Override
        public List<Collection> getCollections() {
            final Request request = new Request("GET", "/_alias");
            final String content = performRequest(request);

            final JSONObject resp = JSONObject.parseObject(content);

            final Map<String, Collection> indices = new HashMap<>();
            resp.forEach((index, value) -> {
                if (indices.containsKey(index) || index.startsWith(".")) {
                    return;
                }
                if (value instanceof JSONObject json) {
                    final JSONObject aliases = json.getJSONObject("aliases");

                    final Collection collection = new Collection();
                    if (aliases.isEmpty()) {
                        collection.setName(index);
                    } else {
                        aliases.keySet().stream().findFirst().ifPresent(collection::setName);
                        collection.setParent(true);
                    }

                    indices.put(collection.getName(), collection);
                }
            });


            return indices.values().stream().toList();
        }

        @Override
        public List<Collection> getChildren(String collection) {
            final Request request = new Request("GET", "/_alias/" + collection);
            final String content = performRequest(request);

            return JSONObject.parseObject(content).keySet()
                    .stream()
                    .sorted()
                    .map(index -> {
                        final Collection c = new Collection();
                        c.setName(index);
                        return c;
                    })
                    .toList();
        }

        @Override
        public List<Column> getColumns(String collection) {
            final Request request = new Request("GET", "/%s/_mapping".formatted(collection));
            final String content = performRequest(request);

            final JSONObject mappings = JSON.parseObject(content)
                    .values()
                    .stream()
                    .findFirst()
                    .map(m -> (JSONObject) m)
                    .orElse(new JSONObject())
                    .getJSONObject("mappings");
            if (mappings == null || mappings.isEmpty()) {
                return new ArrayList<>();
            }

            final JSONObject props;
            if (mappings.containsKey("_doc")) {
                props = mappings.getJSONObject("_doc").getJSONObject("properties");
            } else {
                props = mappings.getJSONObject("properties");
            }

            final List<Column> columns = new ArrayList<>();

            startParseColumn(null, props, columns);

            return columns;
        }

        @Override
        public Resp query(QueryBuilder queryBuilder) {
            if (queryBuilder instanceof EsQueryBuilder esQueryBuilder) {
                try {
                    final Request request = esQueryBuilder.getQueryRequest();

                    return new RespImpl(performRequest(request), esQueryBuilder.getRenderedQuery());
                } catch (BizException e) {
                    throw e;
                } catch (Exception e) {
                    throw createParamError(e.getMessage());
                }
            }

            throw createParamError("QueryBuilder({}) is not matched", queryBuilder);
        }

        @Override
        public ExecuteBuilder newExecuteBuilder() {
            return new EsExecuteBuilder(this);
        }


        @Override
        public MutateBuilder newMutateBuilder() {
            return new EsMutateBuilder(this);
        }

        @Override
        public Resp mutate(MutateBuilder mutateBuilder) {
            if (mutateBuilder instanceof EsMutateBuilder esMutateBuilder) {
                try {
                    final Request request = esMutateBuilder.getMutateRequest();
                    final String content = performRequest(request);

                    return new RespImpl(content, esMutateBuilder.getCommand());
                } catch (BizException e) {
                    throw e;
                } catch (Exception e) {
                    throw createParamError(e.getLocalizedMessage());
                }
            }
            throw createParamError("MutateBuilder({}) is not matched", mutateBuilder);
        }

        private void tryConnect() {
            // 创建一个请求来获取集群健康状态
            try {
                final Request healthRequest = new Request("GET", "/_cluster/health");
                final Response response = client.performRequest(healthRequest);

                if (response.getStatusLine().getStatusCode() != 200) {
                    log.warn("es health check error, {}", response.getWarnings());
                }
            } catch (IOException e) {
                unhealthy(createDbError(e.getLocalizedMessage()));
            }
        }

        private HttpHost createHttpHost(ConfigProperties configProperties) {
            final String address = configProperties.getCheckedString(ConfigKey.ADDRESS);
            final String[] split = address.split(":");
            try {
                if (split.length == 2) {
                    final String host = split[0].trim();
                    final String port = split[1].trim();

                    return new HttpHost(host.trim(), Integer.parseInt(port.trim()), "http");
                }
            } catch (Exception e) {
                log.warn("address({}) format error", address);
            }

            throw createParamError("address({}) format error", address);
        }

        private CredentialsProvider createCredentialsProvider(ConfigProperties configProperties) {
            // 创建CredentialsProvider并设置用户名和密码
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    AuthScope.ANY,
                    new UsernamePasswordCredentials(
                            configProperties.getCheckedString(ConfigKey.USERNAME),
                            configProperties.getCheckedString(ConfigKey.PASSWORD))
            );


            return credentialsProvider;
        }

        protected String performRequest(Request request) {
            checkHealth();

            try {
                final Response response = client.performRequest(request);
                try (final InputStream content = response.getEntity().getContent()) {
                    return new String(content.readAllBytes(), StandardCharsets.UTF_8);
                }
            } catch (IOException e) {
                log.warn("es performRequest error", e);
                throw createParamError("{}", e.getMessage());
            }
        }

        @Override
        public void close() {
            try {
                client.close();
            } catch (Exception e) {
                log.error("es close error", e);
            }
        }

        @Override
        public QueryBuilder newQueryBuilder() {
            return new EsQueryBuilder(this);
        }

        public static class EsMutateBuilder extends AbstractMutateBuilder<Es> {
            DslBuilder dslBuilder;
            @Getter
            String command;


            public EsMutateBuilder(Es es, DslBuilder dslBuilder) {
                super(es);
                this.dslBuilder = dslBuilder;
            }

            public EsMutateBuilder(Es es) {
                super(es);

            }

            public Request getMutateRequest() {
                if (dslBuilder == null) {
                    dslBuilder = new DslBuilder(template, false, dynamicParams);
                }
                command = dslBuilder.getQuery();

                return dslBuilder.toEsRequest();
            }
        }


        public static class EsExecuteBuilder extends AbstractExecuteBuilder<Es> {
            private DslBuilder dslBuilder;

            public EsExecuteBuilder(Es es) {
                super(es);
            }

            @Override
            public Operations operations() {
                if (dslBuilder == null) {
                    dslBuilder = new DslBuilder(template, false, dynamicParams);
                }

                if (dslBuilder.isQueryMethod()) {
                    return Operations.QUERY;
                }

                return Operations.MUTATE;
            }

            @Override
            public boolean isDdl() {
                final String keyword = "_mapping";
                return template.contains(keyword);
            }

            @Override
            public MutateBuilder ofMutateBuilder() {
                if (operations() != Operations.MUTATE) {
                    throw createParamError("当前语句非变更操作类型");
                }
                return new EsMutateBuilder(datasource, dslBuilder);
            }

            @Override
            public QueryBuilder ofQueryBuilder() {
                if (operations() != Operations.QUERY) {
                    throw createParamError("当前语句非查询操作类型");
                }
                return new EsQueryBuilder(datasource, dslBuilder);
            }
        }

        /**
         * es查询构建器
         */
        public static class EsQueryBuilder extends AbstractQueryBuilder<Es> {
            private static final String PREVIEW_QUERY = """
                    GET %s/_search
                    {
                      "query": {
                      "match_all": {}
                      }
                    }
                    """;
            @Getter
            String renderedQuery;
            DslBuilder dslBuilder;

            public EsQueryBuilder(Es es, DslBuilder dslBuilder) {
                super(es);
                this.dslBuilder = dslBuilder;
            }

            public EsQueryBuilder(Es es) {
                super(es);
            }


            private DslBuilder getQuery() {
                if (dslBuilder == null) {
                    dslBuilder = new DslBuilder(template, enablePreview, dynamicParams);
                }
                return dslBuilder;
            }


            @Override
            public QueryBuilder collection(String collection) {
                // 集合预览
                enablePreview();
                this.template = PREVIEW_QUERY.formatted(collection);
                return this;
            }

            public Request getQueryRequest() {
                final DslBuilder builder = getQuery();

                if (!builder.isQueryMethod()) {
                    throw createParamError("仅支持查询语句");
                }

                this.renderedQuery = builder.getQuery();
                return builder.toEsRequest();
            }
        }

        public static class RespImpl implements Resp {
            private final JSONObject resp;
            private final String query;

            public RespImpl(String content, String query) {
                this.resp = JSONObject.parseObject(content);
                this.query = query;
            }

            @Override
            public DataView getDataView() {
                return new EsDataView(resp);
            }

            @Override
            public String query() {
                return query;
            }

            @Override
            @SuppressWarnings("unchecked")
            public <T> T data() {
                return (T) resp;
            }

            @Override
            public PageView getPageView() {
                return new EsPageView(query, resp);
            }


            public record EsDataView(JSONObject resp) implements DataView {
                @Override
                public DataView mapping(String schema) {
                    // Schema解析
                    Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(resp, schema);

                    // Schema校验
                    String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                    if (StrUtil.isNotBlank(validMessage)) {
                        throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                    }
                    return new ParsedDataViewImpl(parsedData);
                }
            }

            public record EsPageView(String query, JSONObject resp) implements PageView {
                @Override
                public PageView mapping(String schema) {
                    // Schema解析
                    Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(resp, schema);

                    // Schema校验
                    String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                    if (StringUtils.isNotBlank(validMessage)) {
                        throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                    }
                    return new ParsedPageViewImpl(query, parsedData);
                }
            }

            @Data
            public static class ParsedDataViewImpl implements DataView {
                final Object resp;

                public ParsedDataViewImpl(Object resp) {
                    this.resp = resp;
                }

                @Override
                public DataView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }

            @Data
            public static class ParsedPageViewImpl implements PageView {
                final String query;
                final Object resp;

                public ParsedPageViewImpl(String query, Object resp) {
                    this.query = query;
                    this.resp = resp;
                }

                @Override
                public String query() {
                    return query;
                }

                @Override
                public PageView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }

        }
    }

    @Data
    public static class DslBuilder {
        protected static final String MATCH_ALL = """
                {
                  "size": 20,
                  "query": {
                    "match_all": {}
                  }
                }
                """;
        private static final LanguageDriver LANGUAGE_DRIVER = new LanguageDriver(JSON::toJSONString, JSONPlaceholderDiscriminator::new);
        private final String method;
        private final String command;
        private final String endpoint;
        private final String body;


        public DslBuilder(String dsl, boolean enablePreview, Map<String, Object> params) {
            this.command = getCommand(dsl);
            final List<String> args = getArgs(command);
            if (args.size() != 2) {
                throw createParamError("query({}) format error", dsl);
            }

            this.method = getMethod(args);
            this.endpoint = getEndpoint(args);
            this.body = getBody(enablePreview, dsl, params);
        }

        @NotNull
        private static String getMethod(List<String> args) {
            return args.get(0).trim().toUpperCase();
        }

        @NotNull
        private static List<String> getArgs(String command) {
            return Arrays.stream(command.split(" "))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .toList();
        }

        @NotNull
        private static String getCommand(String dsl) {
            if (StrUtil.isBlank(dsl)) {
                throw createParamError("query dsl is null", dsl);
            }
            int start = -1;
            for (int i = 0; i < dsl.length(); i++) {
                char ch = dsl.charAt(i);
                if (Character.isLetter(ch)) {
                    start = i;
                    break;
                }
            }
            if (start == -1) {
                throw createParamError("query({}) format error, not found letter", dsl);
            }
            int end = dsl.indexOf(StrPool.C_LF, start);
            if (end == -1) {
                end = dsl.length();
            }

            return dsl.substring(start, end).trim();
        }

        @NotNull
        private static String getEndpoint(List<String> args) {
            String endpoint = args.get(1).trim();
            if (!endpoint.startsWith("/")) {
                endpoint = "/" + endpoint;
            }

            if (!endpoint.contains("?") && !endpoint.endsWith("/")) {
                endpoint += "/";
            }

            return endpoint;
        }

        private String getBody(boolean enablePreview, String dsl, Map<String, Object> params) {
            if (enablePreview) {
                return MATCH_ALL;
            }

            final String bodyStr = dsl.trim().replace(command, "");
            return LANGUAGE_DRIVER.process(bodyStr, params);
        }

        public boolean isQueryMethod() {
            return "GET".equals(method);
        }

        public Request toEsRequest() {
            final Request request = new Request(method, endpoint);
            if (StringUtils.isNotBlank(body)) {
                request.setJsonEntity(body);
            }

            return request;
        }

        public String getQuery() {
            return method + " " + endpoint + "\n" + getPrettyBody();
        }

        private String getPrettyBody() {
            JSONObject pb = JSON.parseObject(body);
            return Objects.nonNull(pb) ? pb.toJSONString(JSONWriter.Feature.PrettyFormat)
                    : StrUtil.EMPTY;
        }

        /**
         * JSON占位符
         */
        public static class JSONPlaceholderDiscriminator implements LanguageDriver.PlaceholderDiscriminator {
            private static final char ESCAPE = '\\';
            private boolean jsonValue = false;
            private boolean lastEscape = false;

            @Override
            public boolean discriminate(char ch) {
                boolean escape = lastEscape;
                lastEscape = ch == ESCAPE;

                // 不是转义字符", 那么就是key和值的开始或结束
                switch (ch) {
                    case '"' -> {
                        if (!escape) {
                            jsonValue = !jsonValue;
                        }
                        return false;
                    }

                    case PLACEHOLDER -> {
                        return !jsonValue;
                    }

                    default -> {
                        return false;
                    }
                }
            }
        }
    }


}
