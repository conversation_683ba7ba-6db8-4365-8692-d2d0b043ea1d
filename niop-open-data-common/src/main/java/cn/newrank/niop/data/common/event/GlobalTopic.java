package cn.newrank.niop.data.common.event;

import org.redisson.api.listener.MessageListener;

/**
 * 全局事件主题
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/20 9:41
 */
public interface GlobalTopic<E> {

    /**
     * 发送事件
     *
     * @param event 事件
     */
    void emitEvent(E event);

    /**
     * 添加监听器
     *
     * @param messageListener 监听器
     */
    void addListener(MessageListener<E> messageListener);
}
