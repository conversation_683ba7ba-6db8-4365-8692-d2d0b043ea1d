package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.client.Request;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/12 10:43
 */
@Log4j2
public class LindormEsFactory extends EsFactory {
    public static final LindormEsFactory DEFAULT = new LindormEsFactory();

    @Override
    public LindormEs create(ConfigProperties configProperties) {
        return new LindormEs(configProperties);
    }

    public static class LindormEs extends Es {
        public LindormEs(ConfigProperties configProperties) {
            super(configProperties);
        }

        protected static String getAliasName(String index) {
            if (index.startsWith(".ds-")) {
                return index.split("-")[1];
            }
            return index;
        }

        @Override
        public List<Collection> getCollections() {
            final Request request = new Request("GET", "_alias");
            final String content = performRequest(request);

            final JSONObject resp = JSONObject.parseObject(content);
            return resp.keySet()
                    .stream()
                    .map(LindormEs::getAliasName)
                    .filter(alias -> !alias.startsWith("."))
                    .distinct()
                    .map(alias -> {
                        final Collection collection = new Collection();
                        collection.setName(alias);
                        collection.setParent(true);
                        return collection;
                    }).toList();
        }

        @Override
        public List<Collection> getChildren(String collection) {
            final Request request = new Request("GET", "_alias");
            final String content = performRequest(request);

            final JSONObject resp = JSONObject.parseObject(content);
            return resp.keySet()
                    .stream()
                    .filter(index -> index.startsWith(".ds-") && getAliasName(index).equals(collection))
                    .distinct()
                    .map(alias -> {
                        final Collection child = new Collection();
                        child.setName(alias);
                        child.setParent(false);
                        return child;
                    }).toList();
        }

        @Override
        public List<Column> getColumns(String collection) {
            final Request request = new Request("GET", "_alias");
            final String content = performRequest(request);

            final JSONObject resp = JSONObject.parseObject(content);
            final Optional<String> firstIndex = resp.keySet()
                    .stream()
                    .filter(index -> index.startsWith(".ds-") && getAliasName(index).equals(collection))
                    .findFirst();
            if (firstIndex.isEmpty()) {
                return List.of();
            }

            return super.getColumns(firstIndex.get());
        }
    }

}
