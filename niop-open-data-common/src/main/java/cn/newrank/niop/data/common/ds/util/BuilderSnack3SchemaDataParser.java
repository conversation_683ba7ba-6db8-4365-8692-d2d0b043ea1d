package cn.newrank.niop.data.common.ds.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.common.BizErr;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.noear.snack.ONode;

import java.util.Optional;
import java.util.Set;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

@Slf4j
public class BuilderSnack3SchemaDataParser {

    /**
     * 是否使用标准模式解析
     */
    private static final boolean STANDARD = true;

    public static Object parseBySchema(Object data, String schemaJson) {
        if (StrUtil.isBlank(schemaJson)) {
            return data;
        }

        ONode node = ONode.load(data);

        return parseBySchema(node, schemaJson);
    }

    public static Object parseBySchema(ONode ctx, String schemaJson) {
        // schema
        JSONObject schema = JSONObject.parseObject(schemaJson);

        // type
        String type = schema.getString("type");
        if (StrUtil.isBlank(type)) {
            throw new IllegalArgumentException("type不能为空");
        }

        // expression
        String expressionValue = schema.getString("expression");

        // expression不能为空
        if (StrUtil.isBlank(expressionValue)) {
            throw new IllegalArgumentException("expression不能为空");
        }

        // 忽略$后面的.
        if (expressionValue.endsWith(".")) {
            expressionValue = expressionValue.substring(0, expressionValue.length() - 1);
        }

        StringBuilder expression = new StringBuilder(expressionValue);

        // type是对象
        if ("object".equals(type)) {
            // 根对象
            JSONObject rootObject = new JSONObject();

            // 根对象的properties
            JSONObject rootProperties = schema.getJSONObject("properties");
            // 对象属性为空返回空对象
            if (rootProperties == null) {
                return rootObject;
            }

            // 通过properties递归的解析对象
            recursionParseObjectByProperties(rootObject, rootProperties, expression, ctx);

            return rootObject;
        }

        // type是数组
        if ("array".equals(type)) {
            JSONArray targetArray = new JSONArray();
            JSONObject items = schema.getJSONObject("items");
            if (items == null) {
                throw new IllegalArgumentException("items不能为空");
            }

            Integer size = readPathSize(expression, ctx);
            size = Optional.ofNullable(size).orElse(0);

            if (size != 0) {
                String itemsType = items.getString("type");
                if (StrUtil.isBlank(itemsType)) {
                    throw new IllegalArgumentException("type不能为空");
                }
                JSONObject itemsProperties = items.getJSONObject("properties");
                if ("object".equals(itemsType)) {

                    // 数组的对象属性为空返回空数组
                    if (itemsProperties == null) {
                        return targetArray;
                    }

                    for (int i = 0; i < size; i++) {
                        JSONObject targetObject = new JSONObject();

                        // 通过properties递归的解析对象
                        // 前进
                        expression.append("[").append(i).append("]");
                        int count = getArrayIndexLength(i);
                        recursionParseObjectByProperties(targetObject, itemsProperties, expression, ctx);

                        targetArray.add(targetObject);

                        // 回退
                        expression.setLength(expression.length() - count);
                    }
                }

                // 基本数据类型
                forEachBasicParseArrayBasicValue(itemsType, targetArray, size, expression, ctx);
            }

            return targetArray;
        }

        return new Object();
    }

    private static void recursionParseObjectByProperties(Object parentObject, JSONObject parentProperties, StringBuilder parentExpression, ONode ctx) {
        // 遍历properties
        Set<String> keySet = parentProperties.keySet();
        for (String key : keySet) {
            // property
            JSONObject property = parentProperties.getJSONObject(key);

            // property的type
            String type = property.getString("type");
            if (StrUtil.isBlank(type)) {
                throw new IllegalArgumentException("type不能为空");
            }

            // property的expression
            String expression = property.getString("expression");
            if (StrUtil.isBlank(expression)) {
                throw new IllegalArgumentException("expression不能为空");
            }

            // 类型是数组 对象嵌套数组
            if ("array".equals(type)) {

                JSONObject items = property.getJSONObject("items");
                if (items == null) {
                    throw new IllegalArgumentException("items不能为空");
                }

                // 数组嵌套数组
                if (parentObject instanceof JSONArray parentArray) {
                    String itemsType = items.getString("type");
                    if (StrUtil.isBlank(itemsType)) {
                        throw new IllegalArgumentException("type不能为空");
                    }
                    for (int i = 0; i < parentArray.size(); i++) {

                        JSONArray array = new JSONArray();

                        // 前进
                        parentExpression.append("[").append(i).append("]");
                        int count = "[".length() + Integer.toString(i).length() + "]".length();
                        count += spliceExpression(parentExpression, expression);

                        Integer size = readPathSize(parentExpression, ctx);
                        size = Optional.ofNullable(size).orElse(0);

                        if (size != 0) {
                            for (int j = 0; j < size; j++) {

                                // 前进
                                parentExpression.append("[").append(j).append("]");
                                int itemCount = "[".length() + Integer.toString(j).length() + "]".length();

                                // 数组嵌套对象
                                if ("object".equals(itemsType)) {
                                    JSONObject targetObject = new JSONObject();

                                    // 通过properties递归的解析对象
                                    JSONObject itemsProperties = items.getJSONObject("properties");

                                    // 数组下的对象属性为空返回空数组
                                    if (itemsProperties != null) {
                                        recursionParseObjectByProperties(targetObject, itemsProperties, parentExpression, ctx);
                                        array.add(targetObject);
                                    }

                                }

                                if ("string".equals(itemsType)) {
                                    Object sourceBasic = readPath(parentExpression, String.class, ctx);
                                    if (sourceBasic != null) {
                                        array.add(sourceBasic);
                                    }
                                }

                                if ("integer".equals(itemsType)) {
                                    Object sourceBasic = readPath(parentExpression, Integer.class, ctx);
                                    if (sourceBasic != null) {
                                        array.add(sourceBasic);
                                    }
                                }

                                if ("boolean".equals(itemsType)) {
                                    Object sourceBasic = readPath(parentExpression, Boolean.class, ctx);
                                    if (sourceBasic != null) {
                                        array.add(sourceBasic);
                                    }
                                }

                                if ("number".equals(itemsType)) {
                                    Object sourceBasic = readPath(parentExpression, Number.class, ctx);
                                    if (sourceBasic != null) {
                                        array.add(sourceBasic);
                                    }
                                }

                                // 回退
                                parentExpression.setLength(parentExpression.length() - itemCount);
                            }

                            // 目标数组
                            JSONObject parentObjectItem = parentArray.getJSONObject(i);
                            parentObjectItem.put(key, array);
                        }

                        // 回退
                        parentExpression.setLength(parentExpression.length() - count);
                    }
                    continue;
                } else {
                    JSONArray targetArray = new JSONArray();

                    // 前进
                    int count = spliceExpression(parentExpression, expression);

                    Integer size = readPathSize(parentExpression, ctx);
                    size = Optional.ofNullable(size).orElse(0);

                    if (size != 0) {
                        String itemsType = items.getString("type");
                        if (StrUtil.isBlank(itemsType)) {
                            throw new IllegalArgumentException("type不能为空");
                        }

                        // 数组嵌套对象
                        if ("object".equals(itemsType)) {
                            for (int i = 0; i < size; i++) {
                                targetArray.add(new JSONObject());
                            }
                            JSONObject itemsProperties = items.getJSONObject("properties");

                            // 数组下的对象属性为空返回空数组
                            if (itemsProperties != null) {
                                // 通过properties递归的解析数组
                                recursionParseObjectByProperties(targetArray, itemsProperties, parentExpression, ctx);
                            }

                        }

                        // 基本数据类型
                        forEachBasicParseArrayBasicValue(itemsType, targetArray, size, parentExpression, ctx);

                        JSONObject parentTargetObj = (JSONObject) parentObject;
                        parentTargetObj.put(key, targetArray);
                    }

                    // 回退
                    parentExpression.setLength(parentExpression.length() - count);
                }

            }

            // 类型是对象
            if ("object".equals(type)) {

                // 根对象是数组
                if (parentObject instanceof JSONArray parentArray) {
                    // 解析根数组
                    parseParentArray(parentArray, parentExpression, key, property, expression, ctx);
                }

                // 根对象是对象
                if (parentObject instanceof JSONObject parentObjectInstance) {
                    // 前进
                    int count = spliceExpression(parentExpression, expression);

                    // 对象不存在
                    try {
                        readPath(parentExpression, JSONObject.class, ctx);
                        Integer size = readPathSize(parentExpression, ctx);
                        size = Optional.ofNullable(size).orElse(0);
                        if (size == 0) {
                            return;
                        }
                    } catch (PathNotFoundException ignore) {
                        return;
                    }

                    // 对象的properties
                    JSONObject properties = property.getJSONObject("properties");

                    // 对象
                    JSONObject object = new JSONObject();

                    // 对象属性为空返回空对象
                    if (properties != null) {
                        // 通过properties递归的解析对象
                        recursionParseObjectByProperties(object, properties, parentExpression, ctx);
                    }

                    parentObjectInstance.put(key, object);

                    // 回退
                    parentExpression.setLength(parentExpression.length() - count);
                }

            }

            // 基本数据类型
            if ("string".equals(type)) {
                parseObjectBasicValue(parentObject, parentExpression, key, expression, String.class, ctx);
            }
            if ("integer".equals(type)) {
                parseObjectBasicValue(parentObject, parentExpression, key, expression, Integer.class, ctx);
            }
            if ("boolean".equals(type)) {
                parseObjectBasicValue(parentObject, parentExpression, key, expression, Boolean.class, ctx);
            }
            if ("number".equals(type)) {
                parseObjectBasicValue(parentObject, parentExpression, key, expression, Number.class, ctx);
            }

        }
    }

    private static <T> void forEachBasicParseArrayBasicValue(String itemsType, JSONArray targetArray, Integer size, StringBuilder parentExpression, ONode ctx) {
        // 基本数据类型
        if ("string".equals(itemsType)) {
            parseArrayBasicValue(targetArray, size, parentExpression, String.class, ctx);
        }
        if ("integer".equals(itemsType)) {
            parseArrayBasicValue(targetArray, size, parentExpression, Integer.class, ctx);
        }
        if ("boolean".equals(itemsType)) {
            parseArrayBasicValue(targetArray, size, parentExpression, Boolean.class, ctx);
        }
        if ("number".equals(itemsType)) {
            parseArrayBasicValue(targetArray, size, parentExpression, Number.class, ctx);
        }
    }

    private static <T> void parseArrayBasicValue(JSONArray targetArray, Integer size, StringBuilder parentExpression, Class<T> type, ONode ctx) {
        for (int i = 0; i < size; i++) {
            // 前进
            parentExpression.append("[").append(i).append("]");
            int count = getArrayIndexLength(i);
            Object value = readPath(parentExpression, type, ctx);

            // 数组的对象为空
            if (value != null) {
                targetArray.add(value);
            }

            // 回退
            parentExpression.setLength(parentExpression.length() - count);
        }
    }

    private static <T> void parseObjectBasicValue(Object parentObject, StringBuilder parentExpression, String key, String expression, Class<T> type, ONode ctx) {
        if (parentObject instanceof JSONArray parentArray) {
            for (int i = 0; i < parentArray.size(); i++) {
                JSONObject parentObjectItem = parentArray.getJSONObject(i);

                // 前进
                parentExpression.append("[").append(i).append("]");
                int count = getArrayIndexLength(i);
                count += spliceExpression(parentExpression, expression);

                Object value = readPath(parentExpression, type, ctx);

                // value为空
                if (value != null) {
                    parentObjectItem.put(key, value);
                }

                // 回退
                parentExpression.setLength(parentExpression.length() - count);
            }
        }
        if (parentObject instanceof JSONObject parentObjectInstance) {
            // 前进
            int count = spliceExpression(parentExpression, expression);
            Object object = readPath(parentExpression, type, ctx);

            // value为空
            if (object != null) {
                parentObjectInstance.put(key, object);
            }

            // 回退
            parentExpression.setLength(parentExpression.length() - count);
        }
    }

    private static void parseParentArray(JSONArray parentArray, StringBuilder parentExpression, String key, JSONObject property, String expression, ONode ctx) {
        for (int i = 0; i < parentArray.size(); i++) {
            JSONObject parentObject = parentArray.getJSONObject(i);

            JSONObject object = new JSONObject();

            // 前进
            parentExpression.append("[").append(i).append("]");
            int count = getArrayIndexLength(i);
            count += spliceExpression(parentExpression, expression);
            JSONObject properties = property.getJSONObject("properties");

            // 对象属性为空返回空对象
            if (properties != null) {
                recursionParseObjectByProperties(object, properties, parentExpression, ctx);
            }

            parentObject.put(key, object);

            // 回退
            parentExpression.setLength(parentExpression.length() - count);
        }
    }

    private static int spliceExpression(StringBuilder parentExpression, String expression) {
        if (expression.equals("$")) {
            return 0;
        }
        if (expression.startsWith("$.")) {
            // 去除开头的"$."
            expression = expression.substring(2);
        }

        if (StrUtil.isBlank(expression)) {
            return 0;
        }

        parentExpression.append(".").append(expression);
        return ".".length() + expression.length();
    }


    private static Integer readPathSize(StringBuilder path, ONode ctx) {
        try {
            // 前进
            path.append(".size()");
            ONode tmp = ctx.select(path.toString(), STANDARD);
            // 回退
            path.setLength(path.length() - ".size()".length());
            return tmp.getInt();
        } catch (PathNotFoundException ignore) {
            return null;
        } catch (Exception e) {
            throw createBizException(BizErr.SCHEMA_PARSE_ERROR, "数据映射配置错误" + " 路径: " + path + " 异常: " + e.getMessage());
        }
    }

    private static <T> Object readPath(StringBuilder path, Class<T> type, ONode ctx) {
        try {
            ONode tmp = ctx.select(path.toString(), STANDARD);
            String json = tmp.toJson();
            Object data = JSON.parse(json);
            return TypeConvertUtil.convertType(data, type, json);
        } catch (PathNotFoundException ignore) {
            return null;
        } catch (Exception e) {
            throw createBizException(BizErr.SCHEMA_PARSE_ERROR, "数据映射配置错误" + " 路径: " + path + " 异常: " + e.getMessage());
        }
    }

    private static int getArrayIndexLength(int i) {
        return "[".length() + Integer.toString(i).length() + "]".length();
    }

    public static String parseToSchema(String data) {
        Object parsedData = JSON.parse(data);
        if (parsedData instanceof JSONObject) {

            JSONObject schema = new JSONObject();
            schema.put("title", "");
            schema.put("description", "");
            schema.put("type", "object");

            JSONObject propList = new JSONObject();
            JSONObject propArr = new JSONObject();

            recursionParseToProperties(parsedData, schema, propList, "$", propArr);

            schema.put("expression", "$.");
            schema.put("properties", propList);
            return schema.toJSONString();
        }

        if (parsedData instanceof JSONArray objArrValue) {

            JSONObject schema = new JSONObject();
            schema.put("title", "");
            schema.put("description", "");
            schema.put("type", "array");

            schema.put("expression", "$.");

            JSONArray objArrRequired = new JSONArray();
            objArrRequired.add("items");
            schema.put("required", objArrRequired);

            if (CollUtil.isNotEmpty(objArrValue)) {

                JSONObject arrObjProp = new JSONObject();
                arrObjProp.put("type", "object");
                arrObjProp.put("need", "是");
                arrObjProp.put("expression", "items");

                JSONObject arrObjPropArr = new JSONObject();
                JSONObject arrObjPropList = arrObjPropArr.getJSONObject("$");
                arrObjPropList = Optional.ofNullable(arrObjPropList).orElse(new JSONObject());


                for (Object arrValue : objArrValue) {
                    if (arrValue instanceof JSONObject arrObjValue) {

                        recursionParseToProperties(arrObjValue, arrObjProp, arrObjPropList, "$", arrObjPropArr);
                        arrObjProp.put("properties", arrObjPropList);

                        schema.put("items", arrObjProp);
                    }
                }

                Object arrValue = objArrValue.get(0);

                if (arrValue instanceof JSONObject) {
                    JSONArray arrObjRequired = new JSONArray();
                    arrObjRequired.addAll(arrObjPropList.keySet());
                    arrObjProp.put("required", arrObjRequired);
                }

                if (arrValue instanceof String) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "string");
                    prop.put("need", "是");
                    prop.put("expression", "items");
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    schema.put("items", prop);
                }

                if (arrValue instanceof Integer) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "integer");
                    prop.put("need", "是");
                    prop.put("expression", "items");
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    schema.put("items", prop);
                }

                if (arrValue instanceof Boolean) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "boolean");
                    prop.put("need", "是");
                    prop.put("expression", "items");
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    schema.put("items", prop);
                }

                if (arrValue instanceof Number) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "number");
                    prop.put("need", "是");
                    prop.put("expression", "items");
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    schema.put("items", prop);
                }

            } else {
                // 默认items
                JSONObject prop = new JSONObject();
                prop.put("type", "string");
                prop.put("need", "是");
                prop.put("expression", "items");
                prop.put("remark", "");

                JSONArray required = new JSONArray();
                prop.put("required", required);
                schema.put("items", prop);
            }

            return schema.toJSONString();
        }

        return new JSONObject().toJSONString();
    }

    private static void recursionParseToProperties(Object data, JSONObject objProp, JSONObject propList, String rootExpression, JSONObject propArr) {
        if (data instanceof JSONObject object) {

            JSONArray objRequired = new JSONArray();
            objRequired.addAll(object.keySet());
            objProp.put("required", objRequired);

            for (String key : object.keySet()) {
                Object value = object.get(key);
                if (value instanceof JSONArray objArrValue) {
                    JSONObject arrProp = new JSONObject();
                    arrProp.put("type", "array");
                    arrProp.put("expression", "$." + key);

                    JSONArray objArrRequired = new JSONArray();
                    objArrRequired.add("items");
                    arrProp.put("required", objArrRequired);

                    if (CollUtil.isNotEmpty(objArrValue)) {
                        JSONObject arrObjProp = new JSONObject();
                        arrObjProp.put("type", "object");
                        arrObjProp.put("need", "是");
                        arrObjProp.put("expression", "items");

                        // JSONObject arrObjPropList = new JSONObject();
                        JSONObject arrObjPropList = propArr.getJSONObject(rootExpression + "." + key);
                        arrObjPropList = Optional.ofNullable(arrObjPropList).orElse(new JSONObject());

                        for (Object arrValue : objArrValue) {
                            if (arrValue instanceof JSONObject arrObjValue) {
                                recursionParseToProperties(arrObjValue, arrObjProp, arrObjPropList,
                                        rootExpression + "." + key, propArr);

                                // 为了把数组中对象的字段都拿到
                                arrObjProp.put("properties", arrObjPropList);
                                arrProp.put("items", arrObjProp);
                            }
                        }

                        // 给兄弟节点一个prop
                        propArr.put(rootExpression + "." + key, arrObjPropList);

                        Object arrValue = objArrValue.get(0);

                        if (arrValue instanceof JSONObject) {
                            JSONArray arrObjRequired = new JSONArray();
                            arrObjRequired.addAll(arrObjPropList.keySet());
                            arrObjProp.put("required", arrObjRequired);
                        }

                        if (arrValue instanceof String) {
                            JSONObject prop = new JSONObject();
                            prop.put("type", "string");
                            prop.put("need", "是");
                            prop.put("expression", "items");
                            prop.put("remark", "");

                            JSONArray required = new JSONArray();
                            prop.put("required", required);
                            arrProp.put("items", prop);
                        }

                        if (arrValue instanceof Integer) {
                            JSONObject prop = new JSONObject();
                            prop.put("type", "integer");
                            prop.put("need", "是");
                            prop.put("expression", "items");
                            prop.put("remark", "");

                            JSONArray required = new JSONArray();
                            prop.put("required", required);
                            arrProp.put("items", prop);
                        }

                        if (arrValue instanceof Boolean) {
                            JSONObject prop = new JSONObject();
                            prop.put("type", "boolean");
                            prop.put("need", "是");
                            prop.put("expression", "items");
                            prop.put("remark", "");

                            JSONArray required = new JSONArray();
                            prop.put("required", required);
                            arrProp.put("items", prop);
                        }

                        if (arrValue instanceof Number) {
                            JSONObject prop = new JSONObject();
                            prop.put("type", "number");
                            prop.put("need", "是");
                            prop.put("expression", "items");
                            prop.put("remark", "");

                            JSONArray required = new JSONArray();
                            prop.put("required", required);
                            arrProp.put("items", prop);
                        }

                        propList.put(key, arrProp);
                    } else {
                        // JSONObject arrObjPropList = new JSONObject();
                        JSONObject arrObjPropList = propArr.getJSONObject(rootExpression + "." + key);
                        if (arrObjPropList == null) {
                            // 默认items
                            JSONObject prop = new JSONObject();
                            prop.put("type", "string");
                            prop.put("need", "是");
                            prop.put("expression", "items");
                            prop.put("remark", "");

                            JSONArray required = new JSONArray();
                            prop.put("required", required);
                            arrProp.put("items", prop);
                            propList.put(key, arrProp);
                        }
                    }
                }

                if (value instanceof JSONObject objObject) {
                    JSONObject objObjProp = new JSONObject();
                    objObjProp.put("type", "object");
                    objObjProp.put("need", "是");
                    objObjProp.put("expression", "$." + key);

                    JSONArray arrObjRequired = new JSONArray();
                    arrObjRequired.addAll(objObject.keySet());
                    objObjProp.put("required", arrObjRequired);

                    // JSONObject arrObjPropList = new JSONObject();
                    JSONObject arrObjPropList = propArr.getJSONObject(rootExpression + "." + key);
                    arrObjPropList = Optional.ofNullable(arrObjPropList).orElse(new JSONObject());
                    recursionParseToProperties(objObject, objObjProp, arrObjPropList, rootExpression + "." + key, propArr);

                    // 给兄弟节点一个prop
                    propArr.put(rootExpression + "." + key, arrObjPropList);

                    objObjProp.put("properties", arrObjPropList);

                    propList.put(key, objObjProp);
                }

                if (value instanceof String) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "string");
                    prop.put("need", "是");
                    prop.put("expression", "$." + key);
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    propList.put(key, prop);
                }

                if (value instanceof Integer) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "integer");
                    prop.put("need", "是");
                    prop.put("expression", "$." + key);
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    propList.put(key, prop);
                }

                if (value instanceof Boolean) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "boolean");
                    prop.put("need", "是");
                    prop.put("expression", "$." + key);
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    propList.put(key, prop);
                }

                if (value instanceof Number) {
                    JSONObject prop = new JSONObject();
                    prop.put("type", "number");
                    prop.put("need", "是");
                    prop.put("expression", "$." + key);
                    prop.put("remark", "");

                    JSONArray required = new JSONArray();
                    prop.put("required", required);
                    propList.put(key, prop);
                }
            }
        }
    }

}
