package cn.newrank.niop.data.common.ds;

/**
 * 通用构建
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/12 15:05
 */
public interface ExecuteBuilder {

    /**
     * 执行语句模板
     *
     * @param template 语句模板
     * @return this
     */
    ExecuteBuilder template(String template);

    /**
     * 添加参数
     *
     * @param name  参数名称
     * @param value 参数值
     * @return this
     */
    ExecuteBuilder addParam(String name, Object value);

    /**
     * 当前操作类型
     *
     * @return 操作类型
     */
    Operations operations();

    /**
     * 当前语句是否为ddl
     *
     * @return 是否
     */
    default boolean isDdl() {
        return false;
    }

    /**
     * 构建变更操作构建器
     *
     * @return 变更操作构建器
     */
    MutateBuilder ofMutateBuilder();

    /**
     * 构建查询构建器
     *
     * @return 查询构建器
     */
    QueryBuilder ofQueryBuilder();

    /**
     * 执行
     *
     * @return 执行结果
     */
    Resp execute();
}
