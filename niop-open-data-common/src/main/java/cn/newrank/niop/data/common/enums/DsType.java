package cn.newrank.niop.data.common.enums;


import cn.newrank.niop.data.common.ds.*;
import cn.newrank.niop.web.model.BizEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/20 11:19
 */
@Getter
public enum DsType implements BizEnum {
    //数据源
    MYSQL("MySQL", "MySQL", MySQLFactory.DEFAULT),
    ELASTICSEARCH("Elasticsearch", "Elasticsearch", EsFactory.DEFAULT),
    POSTGRES("Postgres", "Postgres", PostgresFactory.DEFAULT),
    HOLO("Hologres", "Hologres", HoloFactory.DEFAULT),
    SLS("Sls", "Sls", SlsFactory.DEFAULT),
    LINDORM_SQL("LindormSQL", "LindormSQL", LindormSQLFactory.DEFAULT),
    LINDORM_ES("LindormES", "LindormES", LindormEsFactory.DEFAULT),
    KAFKA("Kafka", "Kafka", KafkaFactory.DEFAULT),
    ;

    final String code;
    final String description;
    final DatasourceFactory factory;

    DsType(
            String code,
            String description,
            DatasourceFactory factory) {
        this.code = code;
        this.description = description;
        this.factory = factory;
    }


    @Override
    public String getDbCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }
}
