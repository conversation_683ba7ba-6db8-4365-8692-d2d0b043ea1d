package cn.newrank.niop.data.common;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 活跃管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/25 14:21
 */
public class RedisEnableManager {

    private static final String ACTIVATION_KEY = "activation";
    private static final String ACTIVATION_VALUE = "disabled";
    private final RedissonClient redissonClient;
    private final String key;


    public RedisEnableManager(RedissonClient redissonClient, String key) {
        this.redissonClient = redissonClient;
        this.key = key;
    }

    /**
     * 禁用
     *
     * @param id       id
     * @param duration 禁用时长
     */
    public void disable(String id, Duration duration) {
        activationBucket(id).set(ACTIVATION_VALUE, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * 是否可用
     *
     * @param id id
     */
    public boolean isEnable(String id) {
        return !isDisable(id);
    }

    /**
     * 是否禁用
     *
     * @param id id
     */
    public boolean isDisable(String id) {
        return ACTIVATION_VALUE.equals(activationBucket(id).get());
    }

    private RBucket<Object> activationBucket(String id) {
        return redissonClient.getBucket(String.join(":", ACTIVATION_KEY, key, id));
    }
}
