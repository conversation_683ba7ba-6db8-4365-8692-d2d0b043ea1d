package cn.newrank.niop.data.common.ds.builder;

import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.ExecuteBuilder;
import cn.newrank.niop.data.common.ds.Resp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/15 15:33
 */
public abstract class AbstractExecuteBuilder<T extends Datasource> extends AbstractBuilder<T> implements ExecuteBuilder {

    protected AbstractExecuteBuilder(T datasource) {
        super(datasource);
    }

    @Override
    public ExecuteBuilder template(String template) {
        this.template = template;
        return this;
    }

    @Override
    public ExecuteBuilder addParam(String name, Object value) {
        this.dynamicParams.put(name, value);
        return this;
    }


    @Override
    public Resp execute() {
        return datasource.execute(this);
    }

}
