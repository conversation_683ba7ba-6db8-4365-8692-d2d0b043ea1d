package cn.newrank.niop.data.common.ds.builder;

import cn.newrank.niop.data.common.ds.Datasource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/15 15:24
 */
public abstract class AbstractBuilder<T extends Datasource> {
    protected final T datasource;
    protected final Map<String, Object> dynamicParams = new HashMap<>();
    protected String template;
    protected String collection;


    protected AbstractBuilder(T datasource) {
        this.datasource = datasource;
    }
}
