package cn.newrank.niop.data.common.ds;

/**
 * 数据变更构造器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/10 11:24
 */
public interface MutateBuilder {

    /**
     * 设置操作集合
     *
     * @return QueryBuilder
     */
    MutateBuilder collection(String collection);

    /**
     * 设置语句模板
     *
     * @param template 查询语句模板
     * @return QueryBuilder
     */
    MutateBuilder template(String template);

    /**
     * 参数设置
     *
     * @param name  参数名
     * @param value 参数值
     * @return QueryBuilder
     */
    MutateBuilder addParam(String name, Object value);

    /**
     * 执行变更
     *
     * @return Resp
     */
    Resp mutate();
}
