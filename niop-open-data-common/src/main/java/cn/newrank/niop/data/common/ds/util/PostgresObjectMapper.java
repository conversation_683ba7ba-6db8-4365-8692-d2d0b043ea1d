package cn.newrank.niop.data.common.ds.util;

import cn.newrank.niop.data.common.ds.ObjectMapper;
import com.alibaba.fastjson.JSON;
import org.postgresql.jdbc.PgArray;
import org.postgresql.util.PGobject;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/14 11:59
 */
public class PostgresObjectMapper implements ObjectMapper {

    @Override
    public Object map(Object value) {
        if (value instanceof PGobject pGobject) {
            final String type = pGobject.getType();
            if ("jsonb".equals(type)) {
                return JSON.parse(pGobject.getValue());
            }
        }

        if (value instanceof PgArray pgArray) {
            try {
                return pgArray.getArray();
            } catch (Exception e) {
                throw createDbError(e, "字段解析错误， {}", e.getMessage());
            }
        }
        return value;
    }
}
