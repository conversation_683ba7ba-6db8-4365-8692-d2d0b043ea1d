package cn.newrank.niop.data.common.ds.builder;

import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.common.ds.Resp;
import lombok.Getter;

/**
 * query 抽象实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/15 15:10
 */
public abstract class AbstractQueryBuilder<T extends Datasource> extends AbstractBuilder<T> implements QueryBuilder {
    protected boolean enablePreview;
    protected boolean skipValidate;

    protected AbstractQueryBuilder(T datasource) {
        super(datasource);
    }

    @Override
    public QueryBuilder template(String query) {
        this.template = query;
        return this;
    }

    @Override
    public QueryBuilder addParam(String name, Object value) {
        this.dynamicParams.put(name, value);
        return this;
    }

    @Override
    public QueryBuilder enablePreview() {
        this.enablePreview = true;
        return this;
    }

    @Override
    public QueryBuilder skipValidate() {
        this.skipValidate = true;
        return this;
    }

    @Override
    public QueryBuilder collection(String collection) {
        this.collection = collection;
        return this;
    }

    @Override
    public boolean isPageQuery() {
        return this.dynamicParams.containsKey(PAGE_QUERY_PARAM_SIZE);
    }

    @Override
    public Resp query() {
        return this.datasource.query(this);
    }

}
