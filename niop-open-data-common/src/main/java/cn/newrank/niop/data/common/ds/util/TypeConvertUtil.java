package cn.newrank.niop.data.common.ds.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;

public class TypeConvertUtil {

    // 将Object转换为integer类型
    public static int toInteger(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        } else {
            throw new IllegalArgumentException("Cannot convert to integer: " + obj);
        }
    }

    // 将Object转换为boolean类型
    public static boolean toBoolean(Object obj) {
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        } else {
            throw new IllegalArgumentException("Cannot convert to boolean: " + obj);
        }
    }


    // 将Object转换为number类型
    public static Number toNumber(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj);
        } else {
            throw new IllegalArgumentException("Cannot convert to number: " + obj);
        }
    }

    // 将Object转换为string类型
    public static String toString(Object obj) {
        if (obj instanceof String) {
            return (String) obj;
        } else {
            throw new IllegalArgumentException("cannot convert to string: " + obj);
        }
    }

    public static <T> Object convertType(Object obj, Class<T> type, String json) {
        String message = StrUtil.format("cannot convert to {}: {}",
                type.getSimpleName().toLowerCase(), obj);
        if (obj instanceof Integer) {
            if (Integer.class.equals(type)) {
                return toInteger(obj);
            } else if (Number.class.equals(type)) {
                return toNumber(obj);
            } else {
                throw new IllegalArgumentException(message);
            }
        }
        if (obj instanceof Boolean) {
            if (Boolean.class.equals(type)) {
                return toBoolean(obj);
            } else {
                throw new IllegalArgumentException(message);
            }
        }
        if (obj instanceof Number) {
            if (Number.class.equals(type)) {
                return toNumber(obj);
            } else if (Integer.class.equals(type)) {
                return toInteger(obj);
            } else {
                throw new IllegalArgumentException(message);
            }
        }
        if (obj instanceof String) {
            if (String.class.equals(type)) {
                return toString(obj);
            } else {
                throw new IllegalArgumentException(message);
            }
        }
        return JSON.parseObject(json, type);
    }

}