package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.builder.AbstractMutateBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractQueryBuilder;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.Admin;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.common.config.SaslConfigs;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 *
 * <AUTHOR>
 * @since 2025/9/4 15:37
 */
public class KafkaFactory implements DatasourceFactory {

    public static final KafkaFactory DEFAULT = new KafkaFactory();

    @Override
    public Datasource create(ConfigProperties configProperties) {
        return new Kafka(configProperties);
    }

    public static class Kafka extends AbstractDatasource {

        private static final Set<String> PROTOCOL_SET = Set.of("SASL_PLAINTEXT", "PLAINTEXT");

        protected Kafka(ConfigProperties configProperties) {
            super(configProperties);
            Properties props = adminProperties(configProperties);
            try (Admin adminClient = Admin.create(props)) {
                adminClient.listTopics();
            } catch (Exception e) {
                unhealthy(createDbError(e.getMessage()));
            }
        }

        /**
         * 创建配置信息
         *
         * @param configProperties 配置信息
         * @return admin properties
         */
        private Properties adminProperties(ConfigProperties configProperties) {
            String protocol = configProperties.getString(ConfigKey.PROTOCOL);
            if (!PROTOCOL_SET.contains(protocol)) {
                throw createParamError("不支持协议{}", protocol);
            }
            Properties properties = new Properties();
            properties.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, configProperties.getString(ConfigKey.KAFKA_BOOTSTRAP_SERVERS));
            properties.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, protocol);

            if (!CommonClientConfigs.DEFAULT_SECURITY_PROTOCOL.equals(protocol)) {
                properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
                properties.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
                String username = configProperties.getString(ConfigKey.USERNAME);
                String password = configProperties.getString(ConfigKey.PASSWORD);
                String prefix = "org.apache.kafka.common.security.plain.PlainLoginModule";
                String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";"
                        , prefix, username, password);
                properties.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
            }

            properties.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, (int) Duration.ofSeconds(10).toMillis());
            properties.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, (int) Duration.ofSeconds(10).toMillis());
            return properties;
        }

        @Override
        public List<Collection> getCollections() {
            return Collections.emptyList();
        }

        @Override
        public List<Collection> getChildren(String collection) {
            return Collections.emptyList();
        }

        @Override
        public List<Column> getColumns(String collection) {
            return Collections.emptyList();
        }

        @Override
        public void close() {
            //do nothing
        }

        @Override
        public QueryBuilder newQueryBuilder() {
            return new KafkaQueryBuilder(this);
        }

        @Override
        public Resp query(QueryBuilder queryBuilder) {
            throw createParamError("该数据源不支持查询");
        }

        @Override
        public MutateBuilder newMutateBuilder() {
            return new KafkaMutateBuilder(this);
        }

        @Override
        public Resp mutate(MutateBuilder mutateBuilder) {
            return null;
        }

        @Override
        public ExecuteBuilder newExecuteBuilder() {
            throw createParamError("该数据源不支持变更");
        }
    }

    public static class KafkaQueryBuilder extends AbstractQueryBuilder<Kafka> {

        protected KafkaQueryBuilder(Kafka kafka) {
            super(kafka);
        }
    }

    public static class KafkaMutateBuilder extends AbstractMutateBuilder<Kafka> {
        protected KafkaMutateBuilder(Kafka kafka) {
            super(kafka);
        }
    }
}
