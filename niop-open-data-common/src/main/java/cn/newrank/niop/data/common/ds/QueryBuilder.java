package cn.newrank.niop.data.common.ds;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 9:03
 */
public interface QueryBuilder {
    /**
     * 分页查询参数-每页数量
     */
    String PAGE_QUERY_PARAM_SIZE = "_size";
    /**
     * 分页查询参数-页码
     */
    String PAGE_QUERY_PARAM_PAGE = "_page";

    /**
     * 设置查询语句模板
     *
     * @param query 查询语句模板
     * @return QueryBuilder
     */
    QueryBuilder template(String query);

    /**
     * 设置查询参数
     *
     * @param name  参数名
     * @param value 参数值
     * @return QueryBuilder
     */
    QueryBuilder addParam(String name, Object value);

    /**
     * 启用预览
     *
     * @return QueryBuilder
     */
    QueryBuilder enablePreview();

    /**
     * 跳过验证
     * @return QueryBuilder
     */
    QueryBuilder skipValidate();

    /**
     * 设置操作集合
     *
     * @return QueryBuilder
     */
    QueryBuilder collection(String collection);

    /**
     * 设置分页参数
     *
     * @param page 页码
     * @param size 页大小
     * @return QueryBuilder
     */
    default QueryBuilder page(int page, int size) {
        addParam(PAGE_QUERY_PARAM_PAGE, page);
        addParam(PAGE_QUERY_PARAM_SIZE, size);
        return this;
    }

    /**
     * 是否为分页查询
     *
     * @return boolean
     */
    boolean isPageQuery();

    /**
     * 执行查询
     *
     * @return Resp
     */
    Resp query();
}
