package cn.newrank.niop.data.common.ds;


import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.nrcore.exception.BizException;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 数据源基类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 10:06
 */
public abstract class AbstractDatasource implements Datasource {
    private final ConfigProperties configProperties;
    private final AtomicBoolean active = new AtomicBoolean(true);
    private BizException dsException;

    protected AbstractDatasource(ConfigProperties configProperties) {
        this.configProperties = configProperties;
    }


    protected void unhealthy(BizException dsException) {
        active.set(false);
        this.dsException = dsException;
    }

    @Override
    public ConfigProperties getConfig() {
        return configProperties;
    }

    @Override
    public boolean isActive() {
        return active.get();
    }

    @Override
    public BizException getCause() {
        return dsException;
    }

    @Override
    public void checkHealth() throws BizException {
        if (isActive()) {
            return;
        }

        throw dsException;
    }
}
