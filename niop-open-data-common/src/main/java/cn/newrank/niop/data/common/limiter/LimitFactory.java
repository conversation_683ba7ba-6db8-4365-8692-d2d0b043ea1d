package cn.newrank.niop.data.common.limiter;

import jakarta.servlet.http.HttpServletRequest;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.*;
import org.springframework.web.method.HandlerMethod;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/7 10:23
 */
public interface LimitFactory {

    /**
     * 创建限流器
     *
     * @param redissonClient redisson客户端
     * @param limitKey       限流key
     * @param refreshPermits QPS 次数
     * @param refreshSeconds QPS 时间间隔
     * @return 限流器
     */
    static RRateLimiter createRedisRateLimiter(RedissonClient redissonClient, String limitKey,
                                               int refreshPermits, int refreshSeconds) {
        final RRateLimiter rateLimiter = redissonClient.getRateLimiter(limitKey);

        if (rateLimiter.isExists()) {
            final RateLimiterConfig rateLimiterConfig = rateLimiter.getConfig();
            final Long rateInterval = rateLimiterConfig.getRateInterval();
            final Long rate = rateLimiterConfig.getRate();

            // 是否修改配置
            if (refreshSeconds != TimeUnit.MILLISECONDS.toSeconds(rateInterval) || refreshPermits != rate) {
                rateLimiter.delete();
                rateLimiter.trySetRate(RateType.OVERALL, refreshPermits, refreshSeconds, RateIntervalUnit.SECONDS);
            }

            return rateLimiter;
        }


        rateLimiter.trySetRate(RateType.OVERALL, refreshPermits, refreshSeconds, RateIntervalUnit.SECONDS);

        return rateLimiter;
    }

    /**
     * 创建吞吐量计数器
     *
     * @param redissonClient redisson客户端
     * @param limitKey       限流key
     * @return 吞吐量计数器
     */
    static RAtomicLong createThroughputCounter(RedissonClient redissonClient, String limitKey) {
        final RAtomicLong counter = redissonClient.getAtomicLong("limiter:throughput:" + limitKey);
        if (counter.isExists()) {
            return counter;
        }
        counter.expire(Duration.ofDays(15));

        return counter;
    }

    /**
     * 验证请求是否通过限流器
     *
     * @param request       请求
     * @param handlerMethod 处理器方法
     * @return true: 通过限流器, false: 未通过限流器
     */
    boolean tryAcquire(HttpServletRequest request, HandlerMethod handlerMethod);


    /**
     * 获取吞吐量计数器, 如果验证通过返回当前计数器, 否则返回null
     *
     * @param request       请求
     * @param handlerMethod 处理器方法
     * @return 吞吐量计数器 or null
     */
    @Nullable RAtomicLong getCounterIfAccess(HttpServletRequest request, HandlerMethod handlerMethod);
}
