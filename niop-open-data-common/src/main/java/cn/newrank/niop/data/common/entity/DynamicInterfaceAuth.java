package cn.newrank.niop.data.common.entity;


import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:38
 */
@Data
public class DynamicInterfaceAuth {
    public static final int ID_LENGTH = 12;
    /**
     * 授权ID
     */
    private String authId;

    /**
     * 接口ID
     */
    private String interfaceId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * QPS 时间间隔
     */
    private Integer refreshPermits;

    /**
     * QPS 次数
     */
    private Integer refreshSeconds;


    private LocalDateTime gmtModified;

    private LocalDateTime gmtCreate;

    public static DynamicInterfaceAuth ofJSONString(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return JSON.parseObject(json, DynamicInterfaceAuth.class);
    }


    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}