package cn.newrank.niop.data.common.ds.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.networknt.schema.*;

import java.util.Optional;
import java.util.Set;

public class SchemaDataValidator {

    private static final JsonSchemaFactory jsonSchemaFactory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012);

    private static final SchemaValidatorsConfig config = SchemaValidatorsConfig.builder()
            .failFast(true)
            .pathType(PathType.JSON_PATH)
            .build();

    public static String validBySchema(Object data, String schema) {
        JsonSchema jsonSchema = jsonSchemaFactory.getSchema(schema, config);
        Set<ValidationMessage> assertions = jsonSchema.validate(JSON.toJSONString(data), InputFormat.JSON);
        if (CollUtil.isEmpty(assertions)) {
            return "";
        }
        for (ValidationMessage assertion : assertions) {
            String path = assertion.getInstanceLocation().toString();
            path = Optional.ofNullable(path).orElse("");
            String property = assertion.getProperty();
            property = Optional.ofNullable(property).orElse("");
            if (StrUtil.isAllNotBlank(path, property)) {
                path = path + "." + property;
            }
            String error = assertion.getError();
            error = Optional.ofNullable(error).orElse("");
            return "数据映射校验错误" + " 路径: " + path + " 异常: " + error;
        }
        return "";
    }

}
