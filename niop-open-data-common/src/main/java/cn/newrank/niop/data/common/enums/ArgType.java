package cn.newrank.niop.data.common.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:28
 */
@Getter
public enum ArgType implements BizEnum {

    STRING("string", "字符串"),

    NUMBER("number", "数字"),

    BOOLEAN("boolean", "布尔值"),

    ARRAY("array", "数组"),
    ;

    final String value;
    final String description;

    ArgType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getJsonValue() {
        return value;
    }

    @Override
    public String getDbCode() {
        return value;
    }
}
