package cn.newrank.niop.data.common.ds.builder;

import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.MutateBuilder;
import cn.newrank.niop.data.common.ds.Resp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/15 15:28
 */
public abstract class AbstractMutateBuilder<T extends Datasource> extends AbstractBuilder<T> implements MutateBuilder {


    protected AbstractMutateBuilder(T datasource) {
        super(datasource);
    }

    @Override
    public MutateBuilder collection(String collection) {
        this.collection = collection;
        return this;
    }

    @Override
    public MutateBuilder template(String template) {
        this.template = template;
        return this;
    }

    @Override
    public MutateBuilder addParam(String name, Object value) {
        this.dynamicParams.put(name, value);
        return this;
    }

    @Override
    public Resp mutate() {
        return datasource.mutate(this);
    }
}
