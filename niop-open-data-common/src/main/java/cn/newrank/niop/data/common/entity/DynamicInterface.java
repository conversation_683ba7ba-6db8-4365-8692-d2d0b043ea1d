package cn.newrank.niop.data.common.entity;

import cn.newrank.niop.data.common.enums.DsType;
import cn.newrank.niop.data.common.model.Arg;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterface {

    public static final int ID_LENGTH = 10;

    /**
     * 接口ID
     */
    private String interfaceId;

    private DsType dsType;
    /**
     * 数据源ID
     */
    private String dcId;
    /**
     * 数据源名称
     */
    private String dsName;

    /**
     * 接口名称
     */
    private String name;

    /**
     * 查询语句
     */
    private String query;

    /**
     * 接口路径
     */
    private String path;

    /**
     * 查询参数
     */
    private List<Arg> args;


    /**
     * QPS 时间间隔
     */
    private Integer refreshPermits;

    /**
     *
     */
    private Integer refreshSeconds;

    /**
     * 负责人ID
     */
    private String maintainerId;

    /**
     *
     */
    private String maintainerName;

    private String description;

    private List<String> tags;

    /**
     *
     */
    private LocalDateTime gmtModified;

    /**
     *
     */
    private LocalDateTime gmtCreate;


    public static DynamicInterface fromJson(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return JSON.parseObject(json, DynamicInterface.class);
    }

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}