package cn.newrank.niop.data.common.ds;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.builder.AbstractExecuteBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractMutateBuilder;
import cn.newrank.niop.data.common.ds.builder.AbstractQueryBuilder;
import cn.newrank.niop.data.common.ds.util.BuilderSnack3SchemaDataParser;
import cn.newrank.niop.data.common.ds.util.SchemaColumnParser;
import cn.newrank.niop.data.common.ds.util.SchemaDataValidator;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.openservices.aliyun.log.producer.LogProducer;
import com.aliyun.openservices.aliyun.log.producer.ProducerConfig;
import com.aliyun.openservices.aliyun.log.producer.ProjectConfig;
import com.aliyun.openservices.aliyun.log.producer.errors.ProducerException;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.Index;
import com.aliyun.openservices.log.common.IndexKey;
import com.aliyun.openservices.log.common.IndexKeys;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetHistogramsResponse;
import com.aliyun.openservices.log.response.GetIndexResponse;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.openservices.log.response.ListLogStoresResponse;
import lombok.Data;
import lombok.extern.log4j.Log4j2;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.newrank.niop.web.exception.BizExceptions.*;

/**
 * 日志服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/10 10:14
 */
@Log4j2
public class SlsFactory implements DatasourceFactory {
    public static final SlsFactory DEFAULT = new SlsFactory();

    @Override
    public Sls create(ConfigProperties configProperties) {
        return new Sls(configProperties);
    }


    public static class Sls extends AbstractDatasource {
        /**
         * sls参数-日志库
         */
        public static final String SLS_PARAM_LOGSTORE = "_sls_logstore";
        public static final String SLS_PARAM_START = "_sls_start_time";
        public static final String SLS_PARAM_END = "_sls_end_time";
        public static final String SLS_PARAM_ORDER_REVERSE = "_SLS_ORDER_REVERSE";
        public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        /**
         * 语言驱动
         */
        private static final LanguageDriver LANGUAGE_DRIVER = new LanguageDriver(value -> value,
                () -> new LanguageDriver.PlaceholderDiscriminator() {
                    private static final char ESCAPE = '\\';
                    private boolean value = false;
                    private boolean lastEscape = false;
                    private char startCh = '0';

                    @Override
                    public boolean discriminate(char ch) {
                        boolean escape = lastEscape;
                        lastEscape = ch == ESCAPE;

                        // 不是转义字符", 那么就是key和值的开始或结束
                        switch (ch) {
                            case '"', '\'' -> {
                                if (!escape && (startCh == '0' || startCh == ch)) {
                                    value = !value;
                                    if (startCh == '0') {
                                        startCh = ch;
                                    } else {
                                        startCh = '0';
                                    }
                                }

                                return false;
                            }
                            case PLACEHOLDER -> {
                                return !value;
                            }

                            default -> {
                                return false;
                            }
                        }
                    }
                });
        private static final LogProducer WRITER;

        static {
            final ProducerConfig producerConfig = new ProducerConfig();
            producerConfig.setIoThreadCount(32);
            producerConfig.setBatchCountThreshold(50);
            producerConfig.setMaxBlockMs(1000);
            WRITER = new LogProducer(producerConfig);

            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                try {
                    WRITER.close();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (ProducerException ignore) {
                    // ignore
                }
            }));
        }

        final String project;
        ProjectConfig config;
        Client reader;


        public Sls(ConfigProperties configProperties) {
            super(configProperties);

            final String host = configProperties.getCheckedString(ConfigKey.ADDRESS);
            this.project = configProperties.getCheckedString(ConfigKey.DATABASE);
            final String ak = configProperties.getCheckedString(ConfigKey.ALIYUN_AK);
            final String sk = configProperties.getCheckedString(ConfigKey.ALIYUN_SK);

            try {
                this.reader = new Client(host, ak, sk);
                reader.ListConfig(project);

                this.config = new ProjectConfig(project, host, ak, sk);
                WRITER.putProjectConfig(config);
            } catch (LogException e) {
                unhealthy(createDbError(e.getMessage()));
            }
        }

        public static int toSeconds(String dateTime) {
            final LocalDateTime time;
            try {
                time = LocalDateTime.parse(dateTime, DATE_TIME_FORMATTER);
            } catch (Exception e) {
                throw createParamError("{} 不符合日期格式(yyyy-MM-dd HH:mm:ss)", dateTime);
            }

            return Math.toIntExact(time.toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000);
        }

        @Override
        public List<Collection> getCollections() {
            try {
                final ListLogStoresResponse resp = reader.ListLogStores(project, 0, 1000);

                return resp.GetLogStores().stream().map(logstore -> {
                    final Collection collection = new Collection();
                    collection.setName(logstore);
                    return collection;
                }).toList();
            } catch (LogException e) {
                throw createDbError("查询日志库失败: {}", e.getMessage());
            }
        }


        @Override
        public List<Collection> getChildren(String collection) {
            return List.of();
        }

        @Override
        public List<Column> getColumns(String collection) {
            try {
                final GetIndexResponse resp = reader.GetIndex(project, collection);

                final Map<String, IndexKey> keyMap = Optional.ofNullable(resp.GetIndex())
                        .map(Index::GetKeys)
                        .map(IndexKeys::GetKeys)
                        .orElse(null);
                if (keyMap == null) {
                    return List.of();
                }

                return keyMap.entrySet()
                        .stream()
                        .map(entry -> {
                            final Column column = new Column();
                            column.setName(entry.getKey());
                            column.setDescription(entry.getValue().getAlias());
                            column.setType(entry.getValue().GetType());
                            column.setNullable(true);
                            return column;
                        }).toList();
            } catch (LogException e) {
                return List.of();
                //throw createParamError(e.getMessage());
            }
        }

        @Override
        public void close() {
            // noop
        }

        @Override
        public QueryBuilder newQueryBuilder() {
            return new SlsQueryBuilder(this);
        }

        @Override
        public MutateBuilder newMutateBuilder() {
            return new SlsMutateBuilder(this);
        }

        @Override
        public Resp mutate(MutateBuilder mutateBuilder) {
            checkHealth();
            if (mutateBuilder instanceof SlsMutateBuilder slsMutateBuilder) {
                try {
                    final List<LogItem> items = slsMutateBuilder.getItems();
                    WRITER.send(project, slsMutateBuilder.getCollection(), items, result -> {
                        if (result.isSuccessful()) {
                            return;
                        }

                        log.error("{} send log error: {}", project, result.getErrorMessage());
                    });

                    return new MutateResp(items.size(), "");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    throw createDbError(e, "send log error");
                }
            }

            throw createParamError("MutateBuilder({}) 类型错误", mutateBuilder.getClass().getName());
        }

        @Override
        public ExecuteBuilder newExecuteBuilder() {
            return new SlsExecuteBuilder(this);
        }

        @Override
        public QueryResp query(QueryBuilder queryBuilder) {
            checkHealth();
            if (queryBuilder instanceof SlsQueryBuilder slsQueryBuilder) {
                final String logstore = slsQueryBuilder.getLogstore();

                final int startSeconds = slsQueryBuilder.getStartSeconds();
                final int endSeconds = slsQueryBuilder.getEndSeconds();
                final String query = slsQueryBuilder.getQuery();
                try {
                    if (slsQueryBuilder.isPageQuery()) {
                        final GetHistogramsResponse histogramsResponse = reader.GetHistograms(project, logstore,
                                startSeconds, endSeconds, "", query);
                        final long total = histogramsResponse.GetTotalCount();

                        final int pageSize = slsQueryBuilder.getPageSize();
                        final GetLogsResponse resp = reader.GetLogs(project, logstore,
                                startSeconds,
                                endSeconds,
                                "",
                                query,
                                pageSize,
                                (Math.max(slsQueryBuilder.getPage(), 1) - 1) * pageSize,
                                slsQueryBuilder.getOrderReverse());

                        return QueryResp.ofPage(resp, query, total, pageSize);
                    } else {
                        final GetLogsResponse resp = reader.GetLogs(project, logstore,
                                startSeconds, endSeconds, "", query
                                , 1000, 0
                                , slsQueryBuilder.getOrderReverse());
                        return QueryResp.of(resp, query);
                    }
                } catch (LogException e) {
                    String msg = e.getMessage();
                    throw createDbError(msg);
                }
            }
            throw createParamError("QueryBuilder({}) 类型错误", queryBuilder.getClass().getName());
        }

        public static class SlsExecuteBuilder extends AbstractExecuteBuilder<Sls> {

            public SlsExecuteBuilder(Sls sls) {
                super(sls);
            }

            @Override
            public Operations operations() {
                return Operations.QUERY;
            }

            @Override
            public MutateBuilder ofMutateBuilder() {
                return null;
            }

            @Override
            public QueryBuilder ofQueryBuilder() {
                return new SlsQueryBuilder(datasource, template, dynamicParams);
            }
        }

        /**
         * 变更结果定义
         */
        static class MutateResp implements Resp {
            private final long affectedRows;
            private final String query;

            public MutateResp(long affectedRows, String query) {
                this.affectedRows = affectedRows;
                this.query = query;
            }


            @Override
            public DataView getDataView() {
                return new DataViewImpl(affectedRows);
            }

            @Override
            public PageView getPageView() {
                return new PageViewImpl(affectedRows, query);
            }

            @Override
            public String query() {
                return query;
            }

            @Override
            public Long data() {
                return affectedRows;
            }


            public record DataViewImpl(long affectedRows) implements DataView {
                @Override
                public DataView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }

            }

            public record PageViewImpl(long affectedRows, String query) implements PageView {
                @Override
                public PageView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }
        }

        public static class SlsMutateBuilder extends AbstractMutateBuilder<Sls> {


            protected SlsMutateBuilder(Sls datasource) {
                super(datasource);
            }

            public String getCollection() {
                if (collection == null) {
                    throw createParamError("logstore is null");
                }
                return collection;
            }


            public List<LogItem> getItems() {
                if (!dynamicParams.isEmpty()) {
                    final LogItem logItem = new LogItem();

                    dynamicParams.forEach((k, v) -> {
                        if (v != null) {
                            logItem.PushBack(k, String.valueOf(v));
                        }
                    });

                    return List.of(logItem);
                }

                throw createParamError("entities or param is null");
            }
        }

        public static class SlsQueryBuilder extends AbstractQueryBuilder<Sls> {


            public SlsQueryBuilder(Sls sls) {
                super(sls);
            }

            public SlsQueryBuilder(Sls datasource, String template, Map<String, Object> dynamicParams) {
                super(datasource);
                this.template = template;
                this.dynamicParams.putAll(dynamicParams);
            }

            @Override
            public QueryBuilder collection(String collection) {
                dynamicParams.put(SLS_PARAM_LOGSTORE, collection);
                return this;
            }

            public int getStartSeconds() {
                if (enablePreview) {
                    return (int) (System.currentTimeMillis() / 1000 - 60 * 60 * 24 * 30);
                }
                final Object start = dynamicParams.get(SLS_PARAM_START);
                if (start == null) {
                    throw createParamError("开始时间参数未设置");
                }

                return toSeconds(start.toString());
            }

            public int getEndSeconds() {
                if (enablePreview) {
                    return (int) (System.currentTimeMillis() / 1000);
                }
                final Object end = dynamicParams.get(SLS_PARAM_END);
                if (end == null) {
                    throw createParamError("结束时间参数未设置");
                }
                return toSeconds(end.toString());
            }

            public String getLogstore() {
                final Object logstore = dynamicParams.get(SLS_PARAM_LOGSTORE);
                if (logstore == null) {
                    throw createParamError("logstore 参数未设置");
                }

                return logstore.toString();
            }


            public String getQuery() {
                if (!enablePreview) {
                    return LANGUAGE_DRIVER.process(template, dynamicParams);
                }

                return "*";
            }

            /**
             * 获取每页数量
             *
             * @return 每页数量
             */
            public int getPageSize() {
                final Object size = dynamicParams.get(PAGE_QUERY_PARAM_SIZE);
                if (size == null) {
                    throw createParamError("分页查询必须指定 _size");
                }

                return Integer.parseInt(size.toString());
            }

            /**
             * 获取页码
             *
             * @return 页码
             */
            public int getPage() {
                final Object page = dynamicParams.get(PAGE_QUERY_PARAM_PAGE);
                if (page == null) {
                    throw createParamError("分页查询必须指定 _page");
                }
                return Integer.parseInt(page.toString());
            }

            public boolean getOrderReverse() {
                final Object orderBy = dynamicParams.get(SLS_PARAM_ORDER_REVERSE);
                if (orderBy == null) {
                    return true;
                }

                return Boolean.TRUE.equals(orderBy);
            }
        }

        /**
         * 查询结果定义
         */
        @Data
        public static class QueryResp implements Resp {
            List<String> columns;
            List<JSONObject> records;
            String query;
            Long total;
            Long size;

            private QueryResp(List<JSONObject> records, List<String> columns, String query) {
                this.columns = columns;
                this.records = records;
                this.query = query;
            }

            private QueryResp(List<JSONObject> records, List<String> columns, String query, Long total, Long size) {
                this.records = records;
                this.query = query;
                this.total = total;
                this.size = size;
                this.columns = columns;
            }

            public static QueryResp ofPage(GetLogsResponse resp, String query, Long total, Integer size) {
                return new QueryResp(getRecords(resp), resp.getKeys(), query, total, size.longValue());
            }

            public static QueryResp of(GetLogsResponse resp, String query) {
                return new QueryResp(getRecords(resp), resp.getKeys(), query);
            }


            static List<JSONObject> getRecords(GetLogsResponse resp) {
                return resp.getLogs().stream()
                        .map(item -> item.GetLogItem().ToJsonString())
                        .map(JSONObject::parseObject)
                        .toList();
            }

            @Override
            public String query() {
                return query;
            }

            @Override
            @SuppressWarnings("unchecked")
            public <T> T data() {
                return (T) records;
            }

            @Override
            public DataView getDataView() {
                if (size != null) {
                    return new DataPageViewImpl(records, total, size);
                } else {
                    return new DataViewImpl(records);
                }
            }

            @Override
            public PageView getPageView() {
                return new PageViewImpl(query, columns, records);
            }


            @Data
            public static class PageViewImpl implements PageView {
                final String query;
                final List<String> columns;
                final List<JSONObject> records;

                public PageViewImpl(String query, List<String> columns, List<JSONObject> records) {
                    this.query = query;
                    this.columns = columns;
                    this.records = records;
                }

                @Override
                public String query() {
                    return query;
                }

                @Override
                public PageView mapping(String schema) {
                    // Schema解析
                    Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                    // Schema校验
                    String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                    if (StrUtil.isNotBlank(validMessage)) {
                        throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                    }

                    List<String> columnList = SchemaColumnParser.parseBySchema(schema, columns);
                    return new ParsedPageViewImpl(query, columnList, parsedData);
                }
            }

            /**
             * 数据视图
             *
             * <AUTHOR>
             * @version 1.0.0
             * @since 2024/3/21 15:26
             */
            public record DataViewImpl(List<JSONObject> records) implements DataView {
                @Override
                public DataView mapping(String schema) {
                    // Schema解析
                    Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                    // Schema校验
                    String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                    if (StrUtil.isNotBlank(validMessage)) {
                        throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                    }

                    return new ParsedDataViewImpl(parsedData);
                }
            }

            /**
             * 数据分页视图
             *
             * <AUTHOR>
             * @version 1.0.0
             * @since 2024/3/21 15:26
             */
            @Data
            public static class DataPageViewImpl implements DataView {
                final Long total;
                final Long pages;
                final List<JSONObject> records;

                public DataPageViewImpl(List<JSONObject> records, Long total, Long size) {
                    this.records = records;
                    this.total = total;
                    this.pages = total % size == 0 ? total / size : total / size + 1;
                }

                @Override
                public DataView mapping(String schema) {
                    // Schema解析
                    Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(records, schema);

                    // Schema校验
                    String validMessage = SchemaDataValidator.validBySchema(parsedData, schema);
                    if (StrUtil.isNotBlank(validMessage)) {
                        throw createBizException(BizErr.SCHEMA_VALID_ERROR, validMessage);
                    }

                    return new ParsedDataPageViewImpl(total, pages, parsedData);
                }
            }

            @Data
            public static class ParsedDataViewImpl implements DataView {
                final Object records;

                public ParsedDataViewImpl(Object records) {
                    this.records = records;
                }

                @Override
                public DataView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }

            @Data
            public static class ParsedDataPageViewImpl implements DataView {
                final Long total;
                final Long pages;
                final Object records;

                public ParsedDataPageViewImpl(Long total, Long pages, Object records) {
                    this.total = total;
                    this.pages = pages;
                    this.records = records;
                }

                @Override
                public DataView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }

            @Data
            public static class ParsedPageViewImpl implements PageView {
                final String query;
                final List<String> columns;
                final Object records;

                public ParsedPageViewImpl(String query, List<String> columns, Object records) {
                    this.query = query;
                    this.columns = columns;
                    this.records = records;
                }

                @Override
                public String query() {
                    return query;
                }

                @Override
                public PageView mapping(String schema) {
                    throw createBizException(BizErr.NOT_SUPPORT_SCHEMA_PARSE_ERROR, "不支持Schema解析的视图");
                }
            }

        }
    }
}
