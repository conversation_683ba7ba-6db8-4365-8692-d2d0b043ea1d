package cn.newrank.niop.data.common.ds.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.experimental.UtilityClass;
import lombok.extern.log4j.Log4j2;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.ParseException;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/21 15:26
 */
@Log4j2
@UtilityClass
public class SQL {
    private static final List<SelectItem> COUNT_SELECT_ITEM = Collections.singletonList(
            new SelectExpressionItem(new net.sf.jsqlparser.schema.Column().withColumnName("COUNT(*)"))
                    .withAlias(new Alias("TOTAL"))
    );

    /**
     * 解析SQL
     *
     * @param sql sql
     * @return statement
     */
    public static Statement parse(String sql) {
        try {
            return tryParse(sql);
        } catch (ParseException e) {
            throw createDbError("SQL({})解析失败, {}", sql, e.getMessage());
        }
    }
    private Statement tryParse(String sql) throws ParseException {
        try {
           return CCJSqlParserUtil.newParser(sql)
                   .withAllowComplexParsing(false)
                   .Statement();
        } catch (ParseException ex) {
            return CCJSqlParserUtil.newParser(sql)
                    .withAllowComplexParsing(true)
                    .Statement();
        }
    }

    public static String getCountSql(String sql) {
        try {
            Select select = (Select) SQL.parse(sql);
            SelectBody selectBody = select.getSelectBody();

            if (selectBody instanceof PlainSelect plainSelect) {
                plainSelect.setLimit(null);
                plainSelect.setOffset(null);
            }

            // https://github.com/baomidou/mybatis-plus/issues/3920  分页增加union语法支持
            if (selectBody instanceof SetOperationList) {
                return getOriginalCountSql(select.toString());
            }
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
            Distinct distinct = plainSelect.getDistinct();
            GroupByElement groupBy = plainSelect.getGroupBy();
            List<OrderByElement> orderBy = plainSelect.getOrderByElements();

            if (CollectionUtils.isNotEmpty(orderBy)) {
                boolean canClean = groupBy == null;
                // 包含groupBy 不去除orderBy
                if (canClean) {
                    for (OrderByElement order : orderBy) {
                        // order by 里带参数,不去除order by
                        Expression expression = order.getExpression();
                        if (!(expression instanceof Column) && expression.toString().contains(StringPool.QUESTION_MARK)) {
                            canClean = false;
                            break;
                        }
                    }
                }
                if (canClean) {
                    plainSelect.setOrderByElements(null);
                }
            }
            //#95 Github, selectItems contains #{} ${}, which will be translated to ?, and it may be in a function: power(#{myInt},2)
            for (SelectItem item : plainSelect.getSelectItems()) {
                if (item.toString().contains(StringPool.QUESTION_MARK)) {
                    return getOriginalCountSql(select.toString());
                }
            }
            // 包含 distinct、groupBy不优化
            if (distinct != null || null != groupBy) {
                return getOriginalCountSql(select.toString());
            }
            // 包含 join 连表,进行判断是否移除 join 连表

            List<Join> joins = plainSelect.getJoins();
            if (CollectionUtils.isNotEmpty(joins)) {
                boolean canRemoveJoin = true;
                String whereS = Optional.ofNullable(plainSelect.getWhere()).map(Expression::toString).orElse(StringPool.EMPTY);
                // 不区分大小写
                whereS = whereS.toLowerCase();
                for (Join join : joins) {
                    if (!join.isLeft()) {
                        canRemoveJoin = false;
                        break;
                    }
                    FromItem rightItem = join.getRightItem();
                    String str = "";
                    if (rightItem instanceof Table table) {
                        str = Optional.ofNullable(table.getAlias()).map(Alias::getName).orElse(table.getName()) + StringPool.DOT;
                    } else if (rightItem instanceof SubSelect subSelect) {
                        /* 如果 left join 是子查询，并且子查询里包含 ?(代表有入参) 或者 where 条件里包含使用 join 的表的字段作条件,就不移除 join */
                        if (subSelect.toString().contains(StringPool.QUESTION_MARK)) {
                            canRemoveJoin = false;
                            break;
                        }
                        str = subSelect.getAlias().getName() + StringPool.DOT;
                    }
                    // 不区分大小写
                    str = str.toLowerCase();

                    if (whereS.contains(str)) {
                        /* 如果 where 条件里包含使用 join 的表的字段作条件,就不移除 join */
                        canRemoveJoin = false;
                        break;
                    }

                    for (Expression expression : join.getOnExpressions()) {
                        if (expression.toString().contains(StringPool.QUESTION_MARK)) {
                            /* 如果 join 里包含 ?(代表有入参) 就不移除 join */
                            canRemoveJoin = false;
                            break;
                        }
                    }
                }

                if (canRemoveJoin) {
                    plainSelect.setJoins(null);
                }
            }
            // 优化 SQL
            plainSelect.setSelectItems(COUNT_SELECT_ITEM);

            return select.toString();
        } catch (Exception e) {
            log.error("optimize this sql to a count sql has error, sql:\"" + sql + "\", exception:\n" + e);
        }
        return getOriginalCountSql(sql);
    }

    /**
     * 获取 COUNT 原生 SQL 包装
     *
     * @param originalSql ignore
     * @return ignore
     */
    public static String getOriginalCountSql(String originalSql) {
        return String.format("SELECT COUNT(*) FROM (%s) TOTAL", originalSql);
    }
}
