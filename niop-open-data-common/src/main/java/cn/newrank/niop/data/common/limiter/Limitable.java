package cn.newrank.niop.data.common.limiter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 14:44
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Limitable {

    /**
     * 刷新许可
     *
     * @return 刷新许可
     */
    int refreshPermits() default 200;

    /**
     * 刷新秒数
     *
     * @return 刷新秒数
     */
    int refreshSeconds() default 1;

    /**
     * 限流工厂
     *
     * @return 限流工厂
     */
    Class<? extends LimitFactory> factory() default LimitFactory.class;
}
