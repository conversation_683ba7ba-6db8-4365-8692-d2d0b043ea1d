package cn.newrank.niop.data.common.limiter;


import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.util.U;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 14:45
 */
@Log4j2
public class LimitInterceptor implements HandlerInterceptor {

    private static final ThreadLocal<RAtomicLong> WAIT_RELEASE_COUNTER = new ThreadLocal<>();
    private final Map<Class<? extends LimitFactory>, LimitFactory> factoryMap;
    private final RedissonClient redissonClient;


    public LimitInterceptor(RedissonClient redissonClient, List<LimitFactory> limitFactories) {
        this.redissonClient = redissonClient;
        this.factoryMap = U.toMap(limitFactories, LimitFactory::getClass, Function.identity());
    }

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) {
        if (handler instanceof HandlerMethod handlerMethod) {
            final Method method = handlerMethod.getMethod();
            final Limitable limitable = method.getAnnotation(Limitable.class);

            if (limitable == null) {
                return true;
            }

            final Class<? extends LimitFactory> factory = limitable.factory();
            if (factory.equals(LimitFactory.class)) {
                final String methodString = handlerMethod.toString();
                final RRateLimiter rateLimiter = LimitFactory.createRedisRateLimiter(redissonClient
                        , methodString
                        , limitable.refreshPermits()
                        , limitable.refreshSeconds());

                if (rateLimiter.tryAcquire()) {
                    if (accessThroughout(methodString, limitable.refreshPermits())) {
                        return true;
                    }

                    throw createBizException(BizErr.LIMIT_ERROR, "接口请求已达最高吞吐量，请优化执行速度");
                }
            } else {
                final LimitFactory limitFactory = factoryMap.get(factory);
                if (limitFactory == null) {
                    log.error("未找到限流工厂：{}", factory);
                    return true;
                }

                if (limitFactory.tryAcquire(request, handlerMethod)) {
                    final RAtomicLong throughputCounter = limitFactory.getCounterIfAccess(request, handlerMethod);
                    if (throughputCounter != null) {
                        WAIT_RELEASE_COUNTER.set(throughputCounter);
                        return true;
                    }

                    throw createBizException(BizErr.LIMIT_ERROR, "接口请求已达最高吞吐量，请优化执行速度");
                }
            }

            response.setStatus(429);
            throw createBizException(BizErr.LIMIT_ERROR, "接口请求太频繁，请稍后再试");
        }

        return true;
    }

    private boolean accessThroughout(String throughoutKey, int limitPermits) {
        final RAtomicLong throughputCounter = LimitFactory.createThroughputCounter(redissonClient, throughoutKey);

        if (throughputCounter.incrementAndGet() < limitPermits * 1.5) {
            WAIT_RELEASE_COUNTER.set(throughputCounter);
            return true;
        } else {
            throughputCounter.decrementAndGet();
            return false;
        }
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request,
                                @NotNull HttpServletResponse response,
                                @NotNull Object handler, Exception ex) throws Exception {
        final RAtomicLong throughputCounter = WAIT_RELEASE_COUNTER.get();
        if (throughputCounter != null) {
            throughputCounter.decrementAndGet();
            WAIT_RELEASE_COUNTER.remove();
        }
    }
}
