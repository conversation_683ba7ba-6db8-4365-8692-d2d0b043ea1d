package cn.newrank.niop.data.common.ds;

/**
 * 查询结果定义
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 17:58
 */
public interface Resp {

    /**
     * 返回数据视图
     *
     * @return 视图
     */
    DataView getDataView();

    /**
     * 返回页面视图
     *
     * @return 页面
     */
    PageView getPageView();

    /**
     * 返回语句
     *
     * @return 语句
     */
    String query();

    /**
     * 获取数据
     *
     * @return 数据
     */
    <T> T data();

    /**
     * 通过Schema解析,返回的数据视图
     */
    default Resp mapping(String schema) {
        return new Resp() {

            // 通过 Class.this 访问外部类的实例
            private final Resp resp = Resp.this;

            @Override
            public DataView getDataView() {
                return resp.getDataView().mapping(schema);
            }

            @Override
            public PageView getPageView() {
                return resp.getPageView().mapping(schema);
            }

            @Override
            public String query() {
                return resp.query();
            }

            @Override
            public <T> T data() {
                return resp.data();
            }
        };
    }

    /**
     * 用于取数接口, 返回的数据视图
     */
    interface DataView {

        /**
         * 通过Schema解析,返回的数据视图
         */
        DataView mapping(String schema);

    }

    /**
     * 用于前端展示接口, 返回的页面视图
     */
    interface PageView {
        /**
         * 获取执行语句
         *
         * @return 执行语句
         */
        String query();

        /**
         * 通过Schema解析,返回的页面视图
         */
        PageView mapping(String schema);
    }

}
