package cn.newrank.niop.data.common.ds.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class SchemaColumnParser {

    public static List<String> parseBySchema(String schemaJson, List<String> defaultColumnList) {
        if (StrUtil.isBlank(schemaJson)) {
            return defaultColumnList;
        }
        // schema
        JSONObject schema = JSONObject.parseObject(schemaJson);

        // type
        String type = schema.getString("type");
        if (StrUtil.isBlank(type)) {
            throw new IllegalArgumentException("type不能为空");
        }

        // type是数组
        if ("array".equals(type)) {

            List<String> targetArray = new ArrayList<>();
            JSONObject items = schema.getJSONObject("items");

            String itemsType = items.getString("type");
            if (StrUtil.isBlank(itemsType)) {
                throw new IllegalArgumentException("type不能为空");
            }
            if ("object".equals(itemsType)) {
                JSONObject itemsProperties = items.getJSONObject("properties");
                // 数组的对象属性为空返回空数组
                if (itemsProperties == null) {
                    return targetArray;
                }
                Set<String> keySet = itemsProperties.keySet();
                targetArray.addAll(keySet);
            }

            return targetArray;
        }

        return Collections.emptyList();
    }

}
