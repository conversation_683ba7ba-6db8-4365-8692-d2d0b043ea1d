package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.nrcore.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import org.elasticsearch.client.RestClient;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;

class EsFactoryTest {

    static EsFactory.Es create7_x_Es() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "youdu_api";
            case PASSWORD -> "di&^23saiSDFI*SDFIW7";
            case ADDRESS -> "es-cn-2r42p7jaa000cg6ng.public.elasticsearch.aliyuncs.com:9200";
            default -> null;
        };

        return new EsFactory.Es(config);
    }

    static EsFactory.Es create6_x_Es() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "elastic_youdu_api";
            case PASSWORD -> "eR4H3lx3Y6";
            case ADDRESS -> "xhs-search.public.elasticsearch.newrank.cn:9200";
            default -> null;
        };

        return new EsFactory.Es(config);
    }

    @Test
    void test() {
        try (final EsFactory.Es es = create7_x_Es()) {
            final QueryBuilder queryBuilder = es.newQueryBuilder();

            queryBuilder.collection("niop_dc_dictionary_test");

            final Resp resp = es.query(queryBuilder);
            final Resp resp1 = queryBuilder.query();
            System.out.println(JSON.toJSONString(resp1.getPageView(), JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_dic() {
        try (final EsFactory.Es es = create7_x_Es()) {
            final List<Column> columns = es.getColumns("niop_dc_dictionary");

            System.out.println(JSON.toJSONString(columns, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test2() throws IOException {
        try (final EsFactory.Es es = create6_x_Es()) {
            es.checkHealth();
            if (!es.isActive()) {
                throw es.getCause();
            }
            //final List<Collection> collections = es.getCollections();

            final RestClient client = es.client;
            //final Response response = client.performRequest(new Request("GET", "_alias"));
            //final Response response = client.performRequest(new Request("GET", "/search_xhs_user/_mappings"));
            //final Response response = client.performRequest(new Request("GET", "search_xhs_user/_mappings"));
            //System.out.println(response.toString());
            final QueryBuilder queryBuilder = es.newQueryBuilder()
                    .template("""
                            GET /search_xhs_user_all/_search
                            {
                              "query": {
                                "match_all": {}
                              }
                            }
                            """);

            final Resp resp = queryBuilder.query();
            System.out.println(JSON.toJSONString(resp.getPageView(), JSONWriter.Feature.PrettyFormat));
        }
    }


    @Test
    void test3() {
        final EsFactory.Es es = create7_x_Es();
        final QueryBuilder queryBuilder = es.newQueryBuilder();

        queryBuilder.template("""
                GET niop_dc_dictionary_test/_search
                {
                  "query": {
                    "match_all": {
                    }
                  }
                }
                """);
        final Resp resp = queryBuilder.query();

        final JSONObject data = resp.data();

        System.out.println(data.toJSONString(JSONWriter.Feature.PrettyFormat));
    }

    @Test
    void test31() {
        final EsFactory.Es es = create7_x_Es();
        final ExecuteBuilder executeBuilder = es.newExecuteBuilder();

        executeBuilder.template("""
                        GET niop_dc_dictionary_test/_search?scroll=1m
                        {
                          "query": {
                            "term": {
                              "_id": {
                                 "value": #{id}
                              }
                            }
                          }
                        }
                        """)
                .addParam("id", "2B71A01AA90C3A12");
        final Resp resp = executeBuilder.execute();

        final JSONObject data = resp.data();
        final String scrollId = data.getString("_scroll_id");

        final ExecuteBuilder executeBuilder1 = es.newExecuteBuilder()
                .template("""
                        GET /_search/scroll
                        {
                          "scroll": "1m",
                          "scroll_id": #{ID}
                        }
                        """)
                .addParam("ID", scrollId);
        final Resp resp1 = executeBuilder1.execute();

        System.out.println(data.toJSONString(JSONWriter.Feature.PrettyFormat));
    }

    @Test
    void test_template() {
        final EsFactory.Es es = create7_x_Es();
        final QueryBuilder queryBuilder = es.newQueryBuilder();

        queryBuilder.template("""
                        
                        GET niop_dc_dictionary_test/_search
                        ${query}
                        """)
                .addParam("query", """
                        {
                          "query": {
                            "term": {
                              "_id": {
                                 "value": #{ab}
                              }
                            }
                          }
                        }
                        """).addParam("ab", false);
        final Resp resp = queryBuilder.query();


        System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
    }

    @Test
    void test_command() {
        String dsl1 = null;
        String dsl2 = """
                GET table/_mapping
                """;
        String dsl3 = """
                GET table/_search
                {
                  "query": {
                  "match_all": {}
                  }
                }
                """;

        Assert.assertThrows(BizException.class, () -> {
            var es1 = new EsFactory.DslBuilder(dsl1, false, null);
        });
        var es2 = new EsFactory.DslBuilder(dsl2, false, null);
        Assertions.assertEquals("GET table/_mapping", es2.getCommand());

        var es3 = new EsFactory.DslBuilder(dsl3, false, null);
        Assertions.assertEquals("GET table/_search", es3.getCommand());


    }

    public static void main(String[] args) {
        System.out.println(JSON.toJSONString("""
                Witsbb·"无敏"维生素ad婴幼新生儿敏宝ad高质量育儿90粒/瓶
                """));
        System.out.println(JSON.toJSONString(null));
        System.out.println(JSON.toJSONString(1));
    }
}