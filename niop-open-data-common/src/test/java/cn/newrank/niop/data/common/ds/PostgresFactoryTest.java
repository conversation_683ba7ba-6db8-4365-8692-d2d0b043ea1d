package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;

import java.util.List;

@Log4j2
class PostgresFactoryTest {


    private JDBC getInstance() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "open_test";
            case PASSWORD -> "6Ytvyu8RorUaNYjQ";
            case ADDRESS -> "pgm-bp1qtj200jqeuh5dho.pg.rds.aliyuncs.com:5432";
            case DATABASE -> "open-data";
            default -> null;
        };

        return HoloFactory.DEFAULT.create(config);
    }

    @Test
    void testQuery() {
        try (JDBC instance = getInstance()) {
            final List<Collection> collections = instance.getCollections();
            collections.forEach(System.out::println);

        }
    }

    @Test
    void test_get_children() {
        try (JDBC instance = getInstance()) {
            final List<Collection> children = instance.getChildren("niop_data_task");

            System.out.println(JSON.toJSONString(children, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_get_columns() {
        try (JDBC instance = getInstance()) {
            final QueryBuilder queryBuilder = instance.newQueryBuilder()
                    .enablePreview()
                    .collection("niop_data_biz_dy_buyin_room");
            final Resp query = instance.query(queryBuilder);

            System.out.println(JSON.toJSONString(query));
        }
    }

    @Test
    void test_delete() {
        try (JDBC instance = getInstance()) {
            final MutateBuilder builder = instance.newMutateBuilder()
                    .template("delete from xxx where id = 1");

            final Resp resp = instance.mutate(builder);
            System.out.println(resp);
        }

    }

}