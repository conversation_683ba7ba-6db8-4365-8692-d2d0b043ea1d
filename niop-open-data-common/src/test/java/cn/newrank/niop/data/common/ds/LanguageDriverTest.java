package cn.newrank.niop.data.common.ds;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

class LanguageDriverTest {

    private static final LanguageDriver LANGUAGE_DRIVER = new LanguageDriver(value -> {
        if (value == null) {
            return "null";
        }

        if (value instanceof CharSequence) {
            return "\"" + value + "\"";
        }

        return value;
    }, EsFactory.DslBuilder.JSONPlaceholderDiscriminator::new);
    String ds = """
            GET search_nr_account_20231010/_search
            {
              "size": #{size},
              "query": {
                "bool": {
                  "should": [
                    {
                      "terms": {
                        "account_id": #{entities,jdbcType=VARCHAR},
                        "boost": 1
                      }
                    }
                  ],
                  "adjust_pure_negative": true,
                  "boost": 1
                }
              }
              <if test="sort != null and sort != ''">
              ,
              "sort": [
                {
                  #{sort}: {
                    "order": #{sortType}
                  }
                }
              ]
              </if>
            }
            """;

    @Test
    void test1() {
        String text = """
                {
                "name": "\"xxx"
                }
                """;

        final char[] chars = text.toCharArray();

        for (char ch : chars) {
            System.out.println(ch);
        }
    }

    @Test
    void test() {
        String query = """
                GET niop_dc_dictionary_test/_search
                {
                  "query": {
                    "term": {
                      "_id": {
                        "value": "\\"?\\"",
                        "value1": "?"
                      }
                    }
                  }
                }
                """;

        System.out.println(LANGUAGE_DRIVER.process(query, Map.of("id", "123")));
    }

    @Test
    void testEsDsl() {
        final LanguageDriver languageDriver = new LanguageDriver(value -> {
            if (value == null) {
                return "null";
            }

            if (value instanceof CharSequence) {
                return "\"" + value + "\"";
            }

            return value;
        });
        // 动态生成 SQL 语句
        final String dsl = """
                   {
                     "query": {
                       "bool": {
                         "filter": {
                           "range": {
                             "time": {
                               "gte": #{startTime},
                               "lte": #{endTime}
                             }
                           }
                         },
                         "minimum_should_match": 1,
                         "should": [
                          <if test="display != null">
                               ${display}
                          </if>
                           {
                             "term": {
                               "awemeAcqDataType": {
                                 "value": "photo_detail"
                               }
                             }
                           }
                         ]
                       }
                     }
                   }
                """;

        final Map<String, Object> params = new HashMap<>();
        params.put("display", """
                {
                 "term": {
                   "awemeAcqDataType": {
                     "value": "photo_list"
                   }
                 }
                },
                """);
        params.put("startTime", "2024-08-16 00:00:00");
        final String query = languageDriver.process(dsl, params);

        final String prettyQuery = JSON.parseObject(query).toJSONString(JSONWriter.Feature.PrettyFormat);
        System.out.println(prettyQuery);
    }

    @Test
    void testSQL() {
        // 动态生成 SQL 语句
        final String sql = """
                    select * from ds_dwd_bz_e_user
                       <where>
                         mid = #{mid}
                            <if test="mids != null and mids.size > 0">
                                and id in
                                     <foreach collection="mids" item="id" open="(" separator="," close=")">
                                            #{id}
                                        </foreach>
                            </if>
                       </where>
                       limit :offset
                """;
        final Map<String, Object> params = new HashMap<>();
        //params.put("mid", "John");
        params.put("mids", List.of(1, 2, 3));
        final LanguageDriver languageDriver = new LanguageDriver(value -> {
            if (value == null) {
                return "NULL";
            }

            if (value instanceof CharSequence) {
                return "'" + value + "'";
            }

            return value;
        });


        final String query = languageDriver.process(sql, params);
        System.out.println(query);
    }

    @Test
    void testEs() {
        final HashMap<String, Object> map = new HashMap<>();

        map.put("entities", List.of("1", "2", "3"));
        map.put("size", 10);
        map.put("sort", "account_id");
        map.put("sortType", "asc");
        final String process = LANGUAGE_DRIVER.process(ds, map);
        System.out.println(process);
    }
}