package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;
import org.postgresql.util.PGobject;
import org.springframework.jdbc.core.namedparam.EmptySqlParameterSource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

class HoloFactoryTest {


    public static final String INSERT = """
            INSERT INTO dc_dim_ks_opus
            (original_photo_id, photo_id, user_id, user_info, publish_time,
            m_type, duration, stream_manifest, cover_type, cover, ana_tag, view_num,
             like_num, comment_num, share_num, is_promotion, sound_track, ext_params,
             create_time, update_time, ds, caption)
            VALUES ('123456', 'photo_id_21', 12345, '{"user_key":"user_value"}',
            '2024-09-05 10:00:00', 1, 60, '{"stream_key":"stream_value"}', 'type_1',
            'cover_url_1', '{"tag_key":"tag_value"}', 100, 50, 20, 10, false, '{"sound_key":"sound_value"}',
            '{"ext_key":"ext_value"}', '2024-09-05 10:01:00', '2024-09-05 10:02:00', '202408', '呜呜呜不要取关我 #开箱𝐕𝐥𝐨𝐠 #张极 #黄旭熙梦女')
            ON CONFLICT (original_photo_id,user_id,ds)    DO UPDATE SET
                                                               publish_time=excluded.publish_time
            """;

    @Test
    void create() {
        final JDBC datasource = getJdbc();
        datasource.getJdbcTemplate().queryForList("select * from ds_dwd_bz_e_user", new MapSqlParameterSource());


        System.out.println(datasource.isActive());

        datasource.close();
    }


    @Test
    void query() {
        try (final JDBC jdbc = getJdbc()) {
            final QueryBuilder queryBuilder = jdbc.newQueryBuilder();
            //queryBuilder.enablePreview();
            queryBuilder.template("select * from ds_dwd_bz_e_user limit 1");
            //queryBuilder.addParam("mid", "2024-08-04 23:59:36", ArgType.STRING);

            final Resp resp = jdbc.query(queryBuilder);
            final JSONArray data = resp.data();
            System.out.println(data.toJSONString(JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void query_in() {
        try (final JDBC jdbc = getJdbc()) {
            final QueryBuilder queryBuilder = jdbc.newQueryBuilder();
            //queryBuilder.enablePreview();
            queryBuilder.template("select * from ds_dwd_bz_e_user  where mid in (:ids)");
            queryBuilder.addParam("ids", Arrays.asList(1, 2));

            final Resp query = jdbc.query(queryBuilder);

        }
    }

    @Test
    void query_mybatis_sql() {
        try (final JDBC jdbc = getJdbc()) {
            final QueryBuilder queryBuilder = jdbc.newQueryBuilder();
            //queryBuilder.enablePreview();
            queryBuilder.template("""
                    SELECT * FROM dc_dim_ks_opus
                    <where>
                        <if test="opusIds != null and opusIds.size > 0">
                           original_photo_id IN
                                <foreach item="opusId" collection="opusIds" open="(" separator="," close=")">
                                  #{opusId}
                                </foreach>
                        </if>
                        <if test="publishStart != null and publishEnd != null">
                           AND publish_time BETWEEN #{publishStart, jdbcType= TIMESTAMP}
                           AND #{publishEnd, jdbcType= TIMESTAMP}
                        </if>
                    </where>
                    ORDER BY publish_time ASC
                    LIMIT 1000
                    """);
            queryBuilder.enablePreview();
            //queryBuilder.addParam("mids", List.of(10002221, 1001579179))
            //        .addParam("offset", 10);

            final Resp query = queryBuilder.query();
            System.out.println(query);

        }
    }

    @Test
    void query_mybatis_sql2() {
        try (final JDBC jdbc = getJdbc()) {
            final QueryBuilder queryBuilder = jdbc.newQueryBuilder();
            //queryBuilder.enablePreview();
            queryBuilder.template("""
                    select * from ds_dwd_ks_e_opus limit ${offset}
                    """);
            queryBuilder
                    .addParam("offset", 10);

            final Resp query = jdbc.query(queryBuilder);
            System.out.println(query.getPageView());
        }
    }


    @Test
    void query_page() {
        try (final JDBC jdbc = getJdbc()) {
            final QueryBuilder queryBuilder = jdbc.newQueryBuilder();
            queryBuilder.template("select * from ds_dwd_bz_e_user");
            queryBuilder.addParam("_page", 1);
            queryBuilder.addParam("_size", 10)
                    .enablePreview();

            final Resp query = queryBuilder.query();

            System.out.println(JSON.toJSONString(query.getDataView(), JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void query_page_with_execute_builder() {
        try (final JDBC jdbc = getJdbc()) {
            final ExecuteBuilder executeBuilder = jdbc.newExecuteBuilder()
                    .template("select * from ds_dwd_bz_e_user")
                    .addParam("_page", 1)
                    .addParam("_size", 10);

            final Resp query = jdbc.execute(executeBuilder);

            System.out.println(JSON.toJSONString(query.getDataView(), JSONWriter.Feature.PrettyFormat));
        }
    }



    private JDBC getJdbc() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "BASIC$niop_test";
            case PASSWORD -> "xppFZzX8mx6YN509";
            case ADDRESS -> "hgprecn-cn-n6w22gd3w007-cn-hangzhou.hologres.aliyuncs.com:80";
            case DATABASE -> "yd_api_test";
            default -> null;
        };

        return HoloFactory.DEFAULT.create(config);
    }


    @Test
    void query_preview() {
        try (final JDBC jdbc = getJdbc()) {
            final List<Collection> collections = jdbc.getCollections();

            for (Collection collection : collections) {
                System.out.println(collection);
            }
        }
    }

    @Test
    void get_children() {
        try (final JDBC jdbc = getJdbc()) {
            final List<Collection> children = jdbc.getChildren("dc_dim_ks_opus");
            System.out.println(JSON.toJSONString(children, JSONWriter.Feature.PrettyFormat));
        }
    }


    @Test
    void query_preview2() {
        try (final JDBC jdbc = getJdbc()) {
            final MutateBuilder mutateBuilder = jdbc.newMutateBuilder().template(INSERT);
            final Resp resp = jdbc.mutate(mutateBuilder);

            System.out.println(resp);
        }
    }

    @Test
    void delete() {
        try (final JDBC jdbc = getJdbc()){
            final MutateBuilder mutateBuilder = jdbc.newMutateBuilder()
                    .template("DELETE FROM dc_dim_ks_opus_202411 WHERE original_photo_id = '123321'");

            final Resp mutate = jdbc.mutate(mutateBuilder);
            System.out.println(mutate);

        }
    }

    @Test
    void query_preview3() {

        try (final JDBC jdbc = getJdbc()) {
            final DruidDataSource dataSource = jdbc.dataSource;
            final DruidPooledConnection connection = dataSource.getConnection();
            try (PreparedStatement stmt = connection.prepareStatement("""
                        INSERT INTO dc_dim_ks_opus
                        (original_photo_id, photo_id, user_id, user_info, publish_time,
                        m_type, duration, stream_manifest, cover_type, cover, ana_tag, view_num,
                         like_num, comment_num, share_num, is_promotion, sound_track, ext_params,
                         create_time, update_time, ds, caption)
                        VALUES (?, ?, ?, ?,?, ?, ?, ?,?, ?,?,?, ?, ?, ?, ?, ?, ?,?,?,?,?);
                    """)) {
                stmt.setString(1, "123321");
                stmt.setString(2, "photo_id_21");
                stmt.setInt(3, 12345);
                final PGobject A1 = new PGobject();
                A1.setType("jsonb");
                A1.setValue("{\"user_key\":\"user_value\"}");

                stmt.setObject(4, A1);
                stmt.setTimestamp(5, Timestamp.valueOf("2024-09-05 10:00:00"));
                stmt.setInt(6, 1);
                stmt.setInt(7, 60);

                final PGobject A2 = new PGobject();
                A2.setType("jsonb");
                A2.setValue("{\"stream_key\":\"stream_value\"}");
                stmt.setObject(8, A2);
                stmt.setString(9, "type_1");
                stmt.setString(10, "cover_url_1");
                final PGobject A3 = new PGobject();
                A3.setType("jsonb");
                A3.setValue("{\"caption_key\":\"caption_value\"}");
                stmt.setObject(11, A3);
                stmt.setInt(12, 100);
                stmt.setInt(13, 50);
                stmt.setInt(14, 20);
                stmt.setInt(15, 10);
                stmt.setBoolean(16, false);
                final PGobject A4 = new PGobject();
                A4.setType("jsonb");
                A4.setValue("{\"ext_key\":\"ext_value\"}");
                stmt.setObject(17, A4);
                final PGobject A5 = new PGobject();
                A5.setType("jsonb");
                A5.setValue("{\"caption_key\":\"caption_value\"}");
                stmt.setObject(18, A5);
                stmt.setTimestamp(19, Timestamp.valueOf("2024-09-05 10:01:00"));
                stmt.setTimestamp(20, Timestamp.valueOf("2024-09-05 10:02:00"));
                stmt.setString(21, "202408");

                stmt.setString(22, "呜呜呜不要取关我\\u0000‍🔥 #开箱𝐕𝐥𝐨𝐠 #张极 #黄旭熙梦女");


                int affected_rows = stmt.executeUpdate();
                System.out.println("affected rows => " + affected_rows);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void query_preview4() {
        try (final JDBC jdbc = getJdbc()) {
            jdbc.jdbcTemplate.update(INSERT, new MapSqlParameterSource());
        }
    }

    @Test
    void update() {
        final String INSERT = """
                UPDATE
                    dc_dim_ability_task_202409
                SET
                    task_status = 0
                WHERE
                    task_id = '3s3fty3q8ikwk5mj66d92a92000000000000001b'
                """;
        try (final JDBC jdbc = getJdbc()) {
            final int update = jdbc.jdbcTemplate.update(INSERT, EmptySqlParameterSource.INSTANCE);
            System.out.println(update);
        }
    }

    private static final LanguageDriver LANGUAGE_DRIVER = new LanguageDriver(value -> {
        if (value == null) {
            return "NULL";
        }

        if (value instanceof CharSequence) {
            return "'" + value + "'";
        }

        return value;
    });

    @Test
    void update_arg() {
        final String INSERT = """
                UPDATE
                    dc_dim_ability_task_202409
                SET
                    task_status = 0
                WHERE
                    task_id = #{taskId}
                """;

        final String process = LANGUAGE_DRIVER.process(INSERT, Collections.singletonMap("taskId", "3s3fty3q8ikwk5mj66d92a92000000000000001b"));

        try (final JDBC jdbc = getJdbc()) {
            final int update = jdbc.jdbcTemplate.update(process, EmptySqlParameterSource.INSTANCE);
            System.out.println(update);
        }
    }


    @Test
    void insert_arg() {
        final String sql = """
                  INSERT INTO dc_dim_ability_task_${ds} (task_id,ability_id,ds,
                   app_id,priority,scene_ids,
                   biz_code,biz_msg,params_md5,
                   task_status,gmt_create,finish_time,ability_version)
                   VALUES
                   <foreach collection="tasks" item="item" separator=",">
                       (#{item.taskId},#{item.abilityId},#{item.ds},
                       #{item.appId},#{item.priority},#{item.sceneIds,jdbcType=ARRAY,typeHandler=org.apache.ibatis.type.ArrayTypeHandler},
                       #{item.bizCode},#{item.bizMsg},#{item.paramsMd5},
                       #{item.taskStatus},#{item.gmtCreate},#{item.finishTime},#{item.abilityVersion})
                   </foreach>
                """;
        final HashMap<String,Object> task = new HashMap<>();
        task.put("taskId", "3s3fty3q8ikwk5mj66d92a92000000000000004");
        task.put("abilityId", "1");
        task.put("ds", "202409");
        task.put("appId", "1");
        task.put("priority", 1);
        task.put("bizCode", "1");
        task.put("bizMsg", "'1");
        task.put("paramsMd5", "1");
        task.put("taskStatus", 1);
        task.put("gmtCreate", "2024-09-05 10:01:00");
        task.put("finishTime", "2024-09-05 10:02:00");


        try (final Datasource datasource = getJdbc()) {
            final ExecuteBuilder executeBuilder = datasource.newExecuteBuilder()
                    .template(sql)
                    .addParam("ds", "202409")
                    .addParam("tasks", List.of(task));

            final Resp resp = datasource.execute(executeBuilder);
            final Long data = resp.data();
            System.out.println(data);

            final Resp query = datasource.query(datasource.newQueryBuilder().template("""
                    select * from dc_dim_ability_task_202409 where task_id='3s3fty3q8ikwk5mj66d92a92000000000000004'
                    """));
            System.out.println(query);
        }
    }
}