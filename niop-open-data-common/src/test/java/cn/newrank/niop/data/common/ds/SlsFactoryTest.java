package cn.newrank.niop.data.common.ds;

import cn.newrank.nrcore.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;

import java.util.List;

class SlsFactoryTest {


    @Test
    void create() {
        try (final SlsFactory.Sls sls = getSls();) {
            final QueryBuilder template = sls.newQueryBuilder()
                    .template("* and api_id: F877368578")
                    .addParam(SlsFactory.Sls.SLS_PARAM_LOGSTORE, "niop-dc-api-access-log")
                    .addParam(SlsFactory.Sls.SLS_PARAM_START, "2024-10-01 00:00:00")
                    .addParam(SlsFactory.Sls.SLS_PARAM_END, "2024-11-01 23:59:59")
                    .addParam(SlsFactory.Sls.SLS_PARAM_ORDER_REVERSE, false);

            final Resp resp = sls.query(template);

            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_error_create() {
        final SlsFactory.Sls sls = SlsFactory.DEFAULT.create(key -> switch (key) {
            case ADDRESS -> "cn-hangzhou.log.aliyuncs.com";
            case DATABASE -> "nr-niop";
            case ALIYUN_AK -> "LTAI5tQfyg6N6xkR7ZTZYPEp";
            case ALIYUN_SK -> "7Zsj9aqvwd0Nz8QasqFfJCHJUzVog";
            default -> throw new IllegalArgumentException("Unknown key: " + key);
        });


        if (!sls.isActive()) {
            final BizException cause = sls.getCause();
            System.out.println(cause);
        }

    }

    @Test
    void test_execute_builder() {
        try (final SlsFactory.Sls sls = getSls();) {
            final ExecuteBuilder executeBuilder = sls.newExecuteBuilder()
                    .template("* and api_id: F877368578")
                    .addParam(SlsFactory.Sls.SLS_PARAM_LOGSTORE, "niop-dc-api-access-log")
                    .addParam(SlsFactory.Sls.SLS_PARAM_START, "2024-10-01 00:00:00")
                    .addParam(SlsFactory.Sls.SLS_PARAM_END, "2024-11-01 23:59:59");

            final Resp resp = executeBuilder.execute();

            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_arg_builder() {
        try (final SlsFactory.Sls sls = getSls();) {
            final ExecuteBuilder executeBuilder = sls.newExecuteBuilder()
                    .template("* and api_id: #{appId} | select count(*), api_id  from log where http_host = #{host} group by #{condition}")
                    .addParam(SlsFactory.Sls.SLS_PARAM_LOGSTORE, "niop-dc-api-access-log")
                    .addParam(SlsFactory.Sls.SLS_PARAM_START, "2024-10-01 00:00:00")
                    .addParam(SlsFactory.Sls.SLS_PARAM_END, "2024-11-01 23:59:59")
                    .addParam("appId", "F877368578")
                    .addParam("condition", "api_id")
                    .addParam("host", "'open.newrank.cn'");

            final Resp resp = sls.execute(executeBuilder);

            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_enablePer_builder() {
        try (final SlsFactory.Sls sls = getSls();) {
            final QueryBuilder queryBuilder = sls.newQueryBuilder()
                    .template("* and api_id: #{appId} | select count(*), api_id  from log where http_host = #{host} group by #{condition}")
                    .addParam(SlsFactory.Sls.SLS_PARAM_LOGSTORE, "niop-dc-api-access-log")
                    .addParam(SlsFactory.Sls.SLS_PARAM_START, "2024-10-01 00:00:00")
                    .addParam(SlsFactory.Sls.SLS_PARAM_END, "2024-11-01 23:59:59")
                    .addParam("appId", "F877368578")
                    .addParam("condition", "api_id")
                    .addParam("host", "'open.newrank.cn'")
                    .enablePreview();

            final Resp resp = sls.query(queryBuilder);

            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void test_metadata() {
        try (final SlsFactory.Sls sls = getSls();) {
            final List<Collection> collections = sls.getCollections();
            System.out.println(collections);

            final List<Column> columns = sls.getColumns("niop-dc-api-access-log");
            System.out.println(columns);
        }
    }



    private static SlsFactory.Sls getSls() {
        return SlsFactory.DEFAULT.create(key -> switch (key) {
            case ADDRESS -> "cn-hangzhou.log.aliyuncs.com";
            case DATABASE -> "nr-niop";
            case ALIYUN_AK -> "LTAI5tQfyg6N6xkR7ZTZYPEp";
            case ALIYUN_SK -> "******************************";
            default -> throw new IllegalArgumentException("Unknown key: " + key);
        });
    }
}