package cn.newrank.niop.data.common.ds;

import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.List;

class MySQLFactoryTest {

    static MySQLFactory.MySQL createMySQL() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "dawn";
            case PASSWORD -> "mhXNvhbgqM5^@Iig";
            case ADDRESS -> "rm-bp124x1wn7kgowg4b2o.mysql.rds.aliyuncs.com:3306";
            case DATABASE -> "niop_dev";
            default -> null;
        };

        return new MySQLFactory.MySQL(config);
    }

    @Test
    void test() {
        try (final MySQLFactory.MySQL mySQL = createMySQL()) {
            final NamedParameterJdbcTemplate template = mySQL.availableJdbcTemplate();

            final QueryBuilder queryBuilder = mySQL.newQueryBuilder();

            queryBuilder.collection("miam_user");

            final Resp resp = queryBuilder.query();
            System.out.println(JSON.toJSONString(resp.getPageView(), JSONWriter.Feature.PrettyFormat));
        }
    }


    @Test
    void test3() {
        try (final MySQLFactory.MySQL mySQL = createMySQL()) {
            final QueryBuilder queryBuilder = mySQL.newQueryBuilder();

            queryBuilder.template(
                    """
                            SELECT * FROM miam_user LIMIT 20
                            """
            );

            final Resp query = mySQL.query(queryBuilder);

            System.out.println(query.getDataView());
        }
    }

    @Test
    void test2() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "yd_api_dev";
            case PASSWORD -> "fkrkapr$eXv#Sy0V";
            case ADDRESS -> "*************:33060";
            case DATABASE -> "niop_dc_test";
            default -> null;
        };

        try (final MySQLFactory.MySQL mySQL = MySQLFactory.DEFAULT.create(config)) {
            final List<Collection> collections = mySQL.getCollections();

            System.out.println(collections);
        }
    }
}