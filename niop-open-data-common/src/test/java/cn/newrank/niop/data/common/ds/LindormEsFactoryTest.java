package cn.newrank.niop.data.common.ds;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;

import java.util.List;

class LindormEsFactoryTest {


    Datasource createInstance() {
        return LindormEsFactory.DEFAULT.create(key -> switch (key) {
            case ADDRESS -> "ld-bp1e25h77kx2tl94o-proxy-search-pub.lindorm.aliyuncs.com:30070";
            case USERNAME -> "root";
            case PASSWORD -> "UYdgnUkZwIiw";
            default -> throw new IllegalArgumentException("unknown key: " + key);
        });
    }
    @Test
    void query() {
        try (Datasource datasource = createInstance();) {
            final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                    .template("""
                            GET ks.dim_opus.search_index/_search
                            {
                              "query": {
                                "bool": {
                                  "must": [
                                    {
                                      "range": {
                                        "time": {
                                          "gte": 1717171200000,
                                          "lt": 1717257600000
                                        }
                                      }
                                    }
                                  ]
                                }
                              },
                              "track_total_hits": true
                            }
                            """);
            final Resp resp = datasource.query(queryBuilder);
            System.out.println("resp: " + JSON.toJSONString(resp.data(), JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void getCollections() {
        try (Datasource datasource = createInstance();) {
            final List<Collection> collections = datasource.getCollections();
            System.out.println("collections: " + JSON.toJSONString(collections, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void getChildren() {
        try (Datasource datasource = createInstance();) {
            final List<Collection> children = datasource.getChildren("ks.dim_opus.search_index");
            System.out.println("children: " + JSON.toJSONString(children, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void getColumns() {
        try (Datasource datasource = createInstance();) {
            final List<Column> columns = datasource.getColumns("ks.dim_opus.search_index");
            System.out.println("columns: " + JSON.toJSONString(columns, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void mutate() {
        try (Datasource datasource = createInstance();) {
            final List<Collection> collections = datasource.getCollections();
            for (Collection collection : collections) {
                final List<Column> columns = datasource.getColumns(collection.getName());

                System.out.println("collection: " + collection.getName() + ", columns: " + JSON.toJSONString(columns, JSONWriter.Feature.PrettyFormat));
            }
        }
    }
}