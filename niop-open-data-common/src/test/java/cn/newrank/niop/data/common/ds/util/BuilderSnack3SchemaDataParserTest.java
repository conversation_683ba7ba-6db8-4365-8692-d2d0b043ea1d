package cn.newrank.niop.data.common.ds.util;

import cn.newrank.niop.data.common.exception.ExceptionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.networknt.schema.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class BuilderSnack3SchemaDataParserTest {

    @Test
    void parseBySchema() {
        String dataJson = readResourceFile("util/JsonData.json");
        String schemaJson = readResourceFile("util/JsonSchema.json");
        JSONObject data = JSON.parseObject(dataJson);

        // 解析
        Object parsedData = BuilderSnack3SchemaDataParser.parseBySchema(data, schemaJson);

        // 校验
        String message = SchemaDataValidator.validBySchema(parsedData, schemaJson);

        System.out.println(message);
    }

    @NonNull
    public static String readResourceFile(String fileName) {
        StringBuilder contentBuilder = new StringBuilder();

        try (InputStream inputStream = BuilderSnack3SchemaDataParserTest.class.getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream == null) {
                log.error("找不到对应的文件: {}", fileName);
                return "";
            }
            try (InputStreamReader streamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                 BufferedReader reader = new BufferedReader(streamReader)) {

                String line;
                while ((line = reader.readLine()) != null) {
                    contentBuilder.append(line).append(System.lineSeparator());
                }
            }
        } catch (IOException e) {
            log.error(ExceptionUtil.getStackTrace(e));
            return "";
        }

        return contentBuilder.toString();
    }

}