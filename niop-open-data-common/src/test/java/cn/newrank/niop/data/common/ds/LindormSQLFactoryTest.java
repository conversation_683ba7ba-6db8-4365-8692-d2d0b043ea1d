package cn.newrank.niop.data.common.ds;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;

import java.util.List;

class LindormSQLFactoryTest {

    Datasource createInstance() {
        return LindormSQLFactory.DEFAULT.create(key -> switch (key) {
            case ADDRESS -> "*************:33060";
            case USERNAME -> "yd_api_dev";
            case PASSWORD -> "7ZzJ2s8F@DLF$%Kc";
            case DATABASE -> "niop_slr_test";
            default -> throw new IllegalStateException("Unexpected value: " + key);
        });
    }
    @Test
    void getCollections() {
        try (Datasource datasource = createInstance()) {
            final List<Collection> collections = datasource.getCollections();
            System.out.println("col: " + JSON.toJSONString(collections, JSONWriter.Feature.PrettyFormat));
        }
    }


    @Test
    void getColumns() {
        try (Datasource datasource = createInstance()) {
            final List<Column> columns = datasource.getColumns("ability_task_info");
            System.out.println("col: " + JSON.toJSONString(columns, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void query() {
        try (Datasource datasource = createInstance()) {
            final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                    .template("select * from ability_task_info limit 10");
            final Resp resp = datasource.query(queryBuilder);
            System.out.println("data: " + JSON.toJSONString(resp.data(), JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void mutate() {
        try (Datasource datasource = createInstance()) {
            for (int i = 0; i < 100; i++) {
                final Resp mutate = datasource.newMutateBuilder()
                        .template("DELETE FROM ability_task_info where task_id = 'r633bcc3j9lxmll9672b264e000000000000000b' and ds ='20241111' ")
                        .mutate();
                final Object data = mutate.data();
                System.out.println("DELETE" + data);

                final MutateBuilder mutateBuilder = datasource.newMutateBuilder()
                        .template("""
                            UPSERT INTO ability_task_info(task_id,ds,ability_id,update_time)
                            VALUES(#{task_id},#{ds},#{ability_id},#{update_time})
                            """)
                        .addParam("task_id", "r633bcc3j9lxmll9672b264e000000000000000b")
                        .addParam("ds", "20241111")
                        .addParam("ability_id", "R633BCC4")
                        .addParam("update_time", "2024-11-11 16:18:29");
                final Resp resp = datasource.mutate(mutateBuilder);
                System.out.println("IN: " + JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));

                final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                        .template("select * from ability_task_info where task_id = #{taskId}")
                        .addParam("taskId", "r633bcc3j9lxmll9672b264e000000000000000b");
                final Resp resp1 = datasource.query(queryBuilder);
                System.out.println("QUERY: " + JSON.toJSONString(resp1.data(), JSONWriter.Feature.PrettyFormat));
            }

        }
    }
}