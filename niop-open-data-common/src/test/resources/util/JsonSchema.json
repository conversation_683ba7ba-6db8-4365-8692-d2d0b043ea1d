{"type": "array", "required": ["items"], "items": {"type": "object", "need": "是", "expression": "items", "required": ["_index", "_type", "_id", "_score", "_source"], "properties": {"_index": {"type": "string", "need": "是", "expression": "$._index", "required": []}, "_type": {"type": "string", "need": "是", "expression": "$._type", "required": []}, "_id": {"type": "string", "need": "是", "expression": "$._id", "required": []}, "_score": {"type": "number", "need": "是", "expression": "$._score", "required": []}, "_source": {"type": "object", "need": "是", "expression": "$._source", "required": ["_class", "account", "nickname", "avatar", "status", "gender", "area", "province", "city", "level", "attribute", "verified", "verify", "verifyType", "introduction", "homeUrl", "following_num", "fans_num", "like_num", "collect_num", "like_collect_num", "opus_num", "banner_image", "first_category", "second_category", "tags", "mcn_name", "pgy", "normal_opus_price", "normal_opus_cpe", "video_opus_price", "video_opus_cpe", "collected_time", "newrank_weekly_index", "uid", "update_time", "gmt_create", "sync_date", "id"], "properties": {"_class": {"type": "string", "need": "是", "expression": "$._class", "required": []}, "account": {"type": "string", "need": "是", "expression": "$.account", "required": []}, "nickname": {"type": "string", "need": "是", "expression": "$.nickname", "required": []}, "avatar": {"type": "string", "need": "是", "expression": "$.avatar", "required": []}, "status": {"type": "number", "need": "是", "expression": "$.status", "required": []}, "gender": {"type": "string", "need": "是", "expression": "$.gender", "required": []}, "area": {"type": "string", "need": "是", "expression": "$.area", "required": []}, "province": {"type": "string", "need": "是", "expression": "$.province", "required": []}, "city": {"type": "string", "need": "是", "expression": "$.city", "required": []}, "level": {"type": "string", "need": "是", "expression": "$.level", "required": []}, "attribute": {"type": "string", "need": "是", "expression": "$.attribute", "required": []}, "verified": {"type": "boolean", "need": "是", "expression": "$.verified", "required": []}, "verify": {"type": "string", "need": "是", "expression": "$.verify", "required": []}, "verifyType": {"type": "string", "need": "是", "expression": "$.verifyType", "required": []}, "introduction": {"type": "string", "need": "是", "expression": "$.introduction", "required": []}, "homeUrl": {"type": "string", "need": "是", "expression": "$.homeUrl", "required": []}, "following_num": {"type": "number", "need": "是", "expression": "$.following_num", "required": []}, "fans_num": {"type": "number", "need": "是", "expression": "$.fans_num", "required": []}, "like_num": {"type": "number", "need": "是", "expression": "$.like_num", "required": []}, "collect_num": {"type": "number", "need": "是", "expression": "$.collect_num", "required": []}, "like_collect_num": {"type": "number", "need": "是", "expression": "$.like_collect_num", "required": []}, "opus_num": {"type": "number", "need": "是", "expression": "$.opus_num", "required": []}, "banner_image": {"type": "string", "need": "是", "expression": "$.banner_image", "required": []}, "first_category": {"type": "string", "need": "是", "expression": "$.first_category", "required": []}, "second_category": {"type": "string", "need": "是", "expression": "$.second_category", "required": []}, "tags": {"type": "string", "need": "是", "expression": "$.tags", "required": []}, "mcn_name": {"type": "string", "need": "是", "expression": "$.mcn_name", "required": []}, "pgy": {"type": "boolean", "need": "是", "expression": "$.pgy", "required": []}, "normal_opus_price": {"type": "number", "need": "是", "expression": "$.normal_opus_price", "required": []}, "normal_opus_cpe": {"type": "number", "need": "是", "expression": "$.normal_opus_cpe", "required": []}, "video_opus_price": {"type": "number", "need": "是", "expression": "$.video_opus_price", "required": []}, "video_opus_cpe": {"type": "number", "need": "是", "expression": "$.video_opus_cpe", "required": []}, "collected_time": {"type": "string", "need": "是", "expression": "$.collected_time", "required": []}, "newrank_weekly_index": {"type": "string", "need": "是", "expression": "$.newrank_weekly_index", "required": []}, "uid": {"type": "string", "need": "是", "expression": "$.uid", "required": []}, "update_time": {"type": "string", "need": "是", "expression": "$.update_time", "required": []}, "gmt_create": {"type": "string", "need": "是", "expression": "$.gmt_create", "required": []}, "sync_date": {"type": "string", "need": "是", "expression": "$.sync_date", "required": []}, "id": {"type": "string", "need": "是", "expression": "$.id", "required": []}}}}}, "expression": "$.hits.hits"}