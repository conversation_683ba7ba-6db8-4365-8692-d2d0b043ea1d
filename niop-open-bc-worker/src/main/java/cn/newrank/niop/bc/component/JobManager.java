package cn.newrank.niop.bc.component;

import cn.newrank.niop.bc.web.pojo.TaskState;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/31 16:16:54
 * @version: 1.0.0
 * @description:
 */
@Data
public class JobManager {

    private final String jobId;
    private final String taskId;
    private TaskState state;
    private String reason;

    private AtomicBoolean stopped = new AtomicBoolean(false);
    private List<Thread> workers = new ArrayList<>();

    public JobManager(String jobId, String taskId) {
        this.jobId = jobId;
        this.taskId = taskId;
        // 状态默认为完成, 如果遇到特殊情况, 状态将会被更新
        this.state = TaskState.COMPLETED;
    }

    public synchronized void registerWorker(Thread worker) {
        workers.add(worker);
    }

    public void failure(String reason) {
        if (stopped.compareAndSet(false, true)) {

            this.state = TaskState.FAILED;
            if (this.reason == null || this.reason.isEmpty()) {
                this.reason = reason;
            } else {
                this.reason = this.reason + "\n" + reason;
            }
            workers.forEach(Thread::interrupt);
        }
    }

    public void cancel() {
        if (stopped.compareAndSet(false, true)) {
            this.state = TaskState.CANCELED;
            this.reason = "取消执行";
            workers.forEach(Thread::interrupt);
        }
    }

}
