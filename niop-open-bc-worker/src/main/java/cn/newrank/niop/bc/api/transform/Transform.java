package cn.newrank.niop.bc.api.transform;

import cn.newrank.niop.bc.api.Record;
import com.typesafe.config.Config;

import java.util.List;

/**
 * @author: xuguangjie
 * @date: 2025/7/22 17:02:29
 * @version: 1.0.0
 * @description:
 */
public interface Transform extends TransformPlugin {

    /**
     * 初始化 transform 组件
     *
     * @param config hocon transform 配置信息
     */
    void init(Config config);

    /**
     * 处理输入的数据, 并将处理后的数据返回
     *
     * @param inputRecord 输入的数据
     * @return 处理后的数据
     */
    List<Record> transform(Record inputRecord);

    /**
     * 释放 transform 持有的所有资源
     */
    void close();

}
