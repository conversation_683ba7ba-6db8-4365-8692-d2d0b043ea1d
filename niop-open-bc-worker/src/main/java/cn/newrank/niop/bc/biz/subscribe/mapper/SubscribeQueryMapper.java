package cn.newrank.niop.bc.biz.subscribe.mapper;

import cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 14:10:09
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface SubscribeQueryMapper {

    /**
     * 查询指定组别下的所有查询信息
     *
     * @param groupId 组别id
     * @return 查询信息
     */
    List<SubscribeQuery> listByGroupId(@Param("groupId") String groupId);

}
