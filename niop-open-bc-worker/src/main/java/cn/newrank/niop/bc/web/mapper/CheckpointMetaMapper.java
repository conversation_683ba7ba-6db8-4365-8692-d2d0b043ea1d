package cn.newrank.niop.bc.web.mapper;

import cn.newrank.niop.bc.web.pojo.CheckpointMeta;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/4 15:50:43
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface CheckpointMetaMapper {

    /**
     * 保存检查点元数据
     *
     * @param checkpointMeta 检查点元数据
     */
    void save(@Param("checkpointMeta") CheckpointMeta checkpointMeta);

    /**
     * 根据检查点ID和插件ID获取检查点元数据
     *
     * @param checkpointId 检查点id
     * @param pluginId     插件id
     * @return 检查点元数据
     */
    CheckpointMeta getCheckpointMeta(@Param("checkpointId") String checkpointId, @Param("pluginId") String pluginId);

}
