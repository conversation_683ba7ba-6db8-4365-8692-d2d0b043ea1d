package cn.newrank.niop.bc.web;

import cn.newrank.niop.bc.web.pojo.JobDTO;
import cn.newrank.niop.bc.web.service.JobExecService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 19:15:03
 * @version: 1.0.0
 * @description:
 */
@RestController
@RequestMapping("/job")
public class JobController {

    @Autowired
    private JobExecService jobExecService;

    @PostMapping("/exec")
    public boolean execute(@RequestBody JobDTO job) {
        return jobExecService.execute(job);
    }

}
