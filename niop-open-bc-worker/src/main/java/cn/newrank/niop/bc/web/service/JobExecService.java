package cn.newrank.niop.bc.web.service;

import cn.newrank.niop.bc.job.JobExecutor;
import cn.newrank.niop.bc.web.pojo.JobDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 20:48:03
 * @version: 1.0.0
 * @description:
 */
@Service
public class JobExecService {

    @Autowired
    private JobExecutor jobExecutor;

    public boolean execute(JobDTO job) {
        return jobExecutor.execute(job.getJobId(), job.getParams());
    }

}
