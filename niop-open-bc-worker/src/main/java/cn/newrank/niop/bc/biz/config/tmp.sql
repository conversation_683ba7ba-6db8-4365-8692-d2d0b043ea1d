INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *星尘智能* )) OR desc:(( *星尘智能* ))) AND time:[2025-09-07 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *petstar* OR *天元* ) AND ( *宠物* )) OR desc:(( *petstar* OR *天元* ) AND ( *宠物* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *高昆轮* OR *低昆轮* OR *考研数学高昆轮* OR *考研高昆轮* ) AND NOT ( *高老师* )) OR desc:(( *高昆轮* OR *低昆轮* OR *考研数学高昆轮* OR *考研高昆轮* ) AND NOT ( *高老师* ))) AND time:[2025-05-31 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *聚保管家* )) OR desc:(( *聚保管家* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *QD-MINI* AND *LED* AND *PRO* )) OR desc:(( *QD-MINI* AND *LED* AND *PRO* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL* AND *RGB* AND *Mini* AND *LED* )) OR desc:(( *TCL* AND *RGB* AND *Mini* AND *LED* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL* *X11L* OR *TCLX11L* )) OR desc:(( *TCL* *X11L* OR *TCLX11L* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *T6L* *Pro* OR *T6LPro* ) AND ( *TCL* )) OR desc:(( *T6L* *Pro* OR *T6LPro* ) AND ( *TCL* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *创富港* )) OR desc:(( *创富港* ))) AND time:[2025-09-09 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *wework* )) OR desc:(( *wework* ))) AND time:[2025-09-09 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *T7L* *Pro* OR *T7LPro* ) AND ( *TCL* )) OR desc:(( *T7L* *Pro* OR *T7LPro* ) AND ( *TCL* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *BEEPLUS* )) OR desc:(( *BEEPLUS* ))) AND time:[2025-09-09 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *Q9L* *Pro* OR *Q9LPro* ) AND ( *TCL* )) OR desc:(( *Q9L* *Pro* OR *Q9LPro* ) AND ( *TCL* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *优客工场* )) OR desc:(( *优客工场* ))) AND time:[2025-09-09 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *办公* OR *写字楼* ) AND ( *MFG* )) OR desc:(( *办公* OR *写字楼* ) AND ( *MFG* ))) AND time:[2025-09-09 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *T7L* *ultra* OR *T7Lultra* )) OR desc:(( *T7L* *ultra* OR *T7Lultra* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *T7L* *ultra* OR *T7Lultra* ) AND ( *TCL* )) OR desc:(( *T7L* *ultra* OR *T7Lultra* ) AND ( *TCL* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL* AND *Q9L* )) OR desc:(( *TCL* AND *Q9L* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL* AND *T7L* )) OR desc:(( *TCL* AND *T7L* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *Q10L* *Pro* OR *Q10LPro* ) AND ( *TCL* )) OR desc:(( *Q10L* *Pro* OR *Q10LPro* ) AND ( *TCL* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL* AND *Q10L* )) OR desc:(( *TCL* AND *Q10L* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *美的空调* )) OR desc:(( *美的空调* ))) AND time:[2025-09-07 16:00:00 TO *] AND uid:(null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( ( *句句真研* OR *讲真题* OR *英语* ) AND ( *静姐* AND *考研* ) ) OR ( ( *田静* ) AND ( *考研* OR *句句真研* OR *田静讲真题* OR *英语* ) )) OR desc:(( ( *句句真研* OR *讲真题* OR *英语* ) AND ( *静姐* AND *考研* ) ) OR ( ( *田静* ) AND ( *考研* OR *句句真研* OR *田静讲真题* OR *英语* ) ))) AND time:[2025-05-31 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *豆包爱学* ) AND ( *开学* ) AND NOT ( *头图* OR *图片* OR *图集* OR *豆包ai生成* OR *技术指导* OR *内容由豆包提供* OR *钦豆包* OR *水豚* OR *钦柠味* OR *粘豆包* OR *吃豆包* OR *孙颖莎* OR *王楚钦* OR *大头* OR *红豆包* OR *大豆包* OR *莎莎* OR *大头* OR *大模型* OR *小豆包* OR *菜包* OR *干粮* OR *封面* OR *图源* OR *配图来源* OR *链接* OR *U盘* OR *一审一校* OR *图文* )) OR desc:(( *豆包爱学* ) AND ( *开学* ) AND NOT ( *头图* OR *图片* OR *图集* OR *豆包ai生成* OR *技术指导* OR *内容由豆包提供* OR *钦豆包* OR *水豚* OR *钦柠味* OR *粘豆包* OR *吃豆包* OR *孙颖莎* OR *王楚钦* OR *大头* OR *红豆包* OR *大豆包* OR *莎莎* OR *大头* OR *大模型* OR *小豆包* OR *菜包* OR *干粮* OR *封面* OR *图源* OR *配图来源* OR *链接* OR *U盘* OR *一审一校* OR *图文* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* ) AND ( *法米娜* )) OR desc:(( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* ) AND ( *法米娜* ))) AND time:[2025-09-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *ng* OR *ng兔* OR *恩萃* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* )) OR desc:(( *ng* OR *ng兔* OR *恩萃* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* ))) AND time:[2025-09-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *福摩* OR *fromm* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* )) OR desc:(( *福摩* OR *fromm* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *宠粮* OR *干粮* ))) AND time:[2025-09-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *皇家ROYAL* *CANIN* OR *皇家* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *干粮* OR *宠粮* )) OR desc:(( *皇家ROYAL* *CANIN* OR *皇家* ) AND ( *猫粮* OR *狗粮* OR *罐头* OR *猫饭* OR *宠物* OR *萌宠* OR *干粮* OR *宠粮* ))) AND time:[2025-09-06 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *halo自然光环* OR *自然光环* ) AND NOT ( *自然光环境* )) OR desc:(( *halo自然光环* OR *自然光环* ) AND NOT ( *自然光环境* ))) AND time:[2025-09-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *a豆智能无线鼠标* )) OR desc:(( *a豆智能无线鼠标* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *a豆氮化镓快速充电器* )) OR desc:(( *a豆氮化镓快速充电器* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *a豆香氛机械键盘* )) OR desc:(( *a豆香氛机械键盘* ))) AND time:[2025-07-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *迎驾贡酒* )) OR desc:(( *迎驾贡酒* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *口子窖* )) OR desc:(( *口子窖* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *酒* ) AND ( *今世缘* )) OR desc:(( *酒* ) AND ( *今世缘* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *橙啦* OR *橙啦公考* OR *橙啦考研* OR *橙啦原力英语* OR *橙啦雅思* )) OR desc:(( *橙啦* OR *橙啦公考* OR *橙啦考研* OR *橙啦原力英语* OR *橙啦雅思* ))) AND time:[2025-09-07 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *开学* ) AND ( *作业帮* ) AND NOT ( *作业帮助* OR *做家务* OR *书包* OR *链接* OR *帮孩子* OR *招聘* OR *负责人* OR *帮着孩子* OR *免费贴膜* OR *网盘* OR *帮忙* OR *帮帮团* )) OR desc:(( *开学* ) AND ( *作业帮* ) AND NOT ( *作业帮助* OR *做家务* OR *书包* OR *链接* OR *帮孩子* OR *招聘* OR *负责人* OR *帮着孩子* OR *免费贴膜* OR *网盘* OR *帮忙* OR *帮帮团* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *情感反诈模拟器* )) OR desc:(( *情感反诈模拟器* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *人宠共居* )) OR desc:(( *人宠共居* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *棉花公寓* )) OR desc:(( *棉花公寓* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *Airclass* )) OR desc:(( *Airclass* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *Lingoace* )) OR desc:(( *Lingoace* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *Polly* )) OR desc:(( *Polly* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *英语* ) AND ( *流利说* )) OR desc:(( *英语* ) AND ( *流利说* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *英语* )) OR desc:(( *英语* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *理财* OR *金融理财* OR *债* OR *债基* OR *混债* OR *固收* OR *固收加* OR *固收+* OR *存钱理财* OR *债券基金* OR *利率* OR *存款利息* OR *稳健理财* OR *稳健* OR *支付宝* OR *zfb* ) AND ( *基金* AND *理财* ) AND NOT ( *养老险* )) OR desc:(( *理财* OR *金融理财* OR *债* OR *债基* OR *混债* OR *固收* OR *固收加* OR *固收+* OR *存钱理财* OR *债券基金* OR *利率* OR *存款利息* OR *稳健理财* OR *稳健* OR *支付宝* OR *zfb* ) AND ( *基金* AND *理财* ) AND NOT ( *养老险* ))) AND time:[2025-09-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *口腔* OR *牙* ) AND ( *诺保科* )) OR desc:(( *口腔* OR *牙* ) AND ( *诺保科* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *牙* OR *口腔* OR *正畸* OR *矫正* ) AND ( *隐适美* )) OR desc:(( *牙* OR *口腔* OR *正畸* OR *矫正* ) AND ( *隐适美* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *诺贝尔种植牙* )) OR desc:(( *诺贝尔种植牙* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *ITI种植牙* )) OR desc:(( *ITI种植牙* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *士卓曼* )) OR desc:(( *士卓曼* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( ( *盲盒* ) AND ( *美团* OR *某团* OR *拼好饭* OR *外卖* OR *开学* ) ) OR ( ( *美团* OR *拼好饭* ) AND ( *孙颖莎周边* OR *孙颖莎透卡* OR *孙颖莎亲签* OR *莎莎周边* OR *莎莎透卡* OR *莎莎亲签* OR *饭卡* OR *卡套* ) )) OR desc:(( ( *盲盒* ) AND ( *美团* OR *某团* OR *拼好饭* OR *外卖* OR *开学* ) ) OR ( ( *美团* OR *拼好饭* ) AND ( *孙颖莎周边* OR *孙颖莎透卡* OR *孙颖莎亲签* OR *莎莎周边* OR *莎莎透卡* OR *莎莎亲签* OR *饭卡* OR *卡套* ) ))) AND time:[2025-07-29 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *狮马秒容* )) OR desc:(( *狮马秒容* ))) AND time:[2025-08-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *雅苒肥料* )) OR desc:(( *雅苒肥料* ))) AND time:[2025-08-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *恩泰克复合肥* )) OR desc:(( *恩泰克复合肥* ))) AND time:[2025-08-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *狮马牌复合肥* )) OR desc:(( *狮马牌复合肥* ))) AND time:[2025-08-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *海露* OR *润洁* OR *珍视明* ) AND ( *滴眼液* )) OR desc:(( *海露* OR *润洁* OR *珍视明* ) AND ( *滴眼液* ))) AND time:[2025-09-05 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *优垦复合肥* )) OR desc:(( *优垦复合肥* ))) AND time:[2025-08-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *艺卡* OR *YEECAR* ) AND ( *膜* OR *汽车* )) OR desc:(( *艺卡* OR *YEECAR* ) AND ( *膜* OR *汽车* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *汉乐* OR *HANLE* ) AND ( *汽车* OR *膜* )) OR desc:(( *汉乐* OR *HANLE* ) AND ( *汽车* OR *膜* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *车艺尚* )) OR desc:(( *车艺尚* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *穿搭* ) AND ( *西北环线* )) OR desc:(( *穿搭* ) AND ( *西北环线* ))) AND time:[2025-09-08 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *包车游* ) AND ( *西北* )) OR desc:(( *包车游* ) AND ( *西北* ))) AND time:[2025-09-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *小郎酒* )) OR desc:(( *小郎酒* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *老村长酒* )) OR desc:(( *老村长酒* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *牛碧桶* ) AND ( *牛栏山* )) OR desc:(( *牛碧桶* ) AND ( *牛栏山* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *养生* ) AND ( *劲酒* )) OR desc:(( *养生* ) AND ( *劲酒* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *中国太平* OR *中國太平* ) AND ( *保险* ) AND NOT ( *太平洋* )) OR desc:(( *中国太平* OR *中國太平* ) AND ( *保险* ) AND NOT ( *太平洋* ))) AND time:[2024-09-30 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *AXA* OR *安盛* ) AND ( *保险* ) AND NOT ( *平安盛世* )) OR desc:(( *AXA* OR *安盛* ) AND ( *保险* ) AND NOT ( *平安盛世* ))) AND time:[2024-09-30 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *专业显示器* ) AND ( ( *创梦* OR *创梦Pro* OR *设计* OR *剪辑* OR *后期* OR *调色* OR *ProArt* ) OR ( *华硕* AND *显示器* ) ) AND ( *华硕* AND *ProArt* ) AND NOT ( *维修* OR *二手* OR *回收* OR *修电脑* OR *闲置* OR *远程* OR *咸鱼* OR *租赁* OR *出租* OR *转转* OR *快修* OR *官翻* OR *翻新* OR *电竞* OR *游戏* )) OR desc:(( *专业显示器* ) AND ( ( *创梦* OR *创梦Pro* OR *设计* OR *剪辑* OR *后期* OR *调色* OR *ProArt* ) OR ( *华硕* AND *显示器* ) ) AND ( *华硕* AND *ProArt* ) AND NOT ( *维修* OR *二手* OR *回收* OR *修电脑* OR *闲置* OR *远程* OR *咸鱼* OR *租赁* OR *出租* OR *转转* OR *快修* OR *官翻* OR *翻新* OR *电竞* OR *游戏* ))) AND time:[2025-06-16 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *麻六记酸辣粉* )) OR desc:(( *麻六记酸辣粉* ))) AND time:[2025-08-19 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *百岁山* )) OR desc:(( *百岁山* ))) AND time:[2025-09-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *抽卡* OR *卡池* OR *抽* ) AND ( *发条总动员* )) OR desc:(( *抽卡* OR *卡池* OR *抽* ) AND ( *发条总动员* ))) AND time:[2025-07-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *中国银行* OR *工商银行* OR *农业银行* OR *建设银行* OR *交通银行* OR *邮储银行* OR *中信银行* OR *光大银行* OR *华夏银行* OR *兴业银行* OR *广发银行* OR *平安银行* OR *浦发银行* OR *浙商银行* OR *渤海银行* OR *中行* OR *农行* OR *工行* OR *建行* OR *交行* ) AND NOT ( *助农* OR *农行动* OR *工行业* OR *义工行* OR *浪中行* OR *建行业* OR *中行人* OR *中行稳* OR *雨中行* OR *雪中行* )) OR desc:(( *中国银行* OR *工商银行* OR *农业银行* OR *建设银行* OR *交通银行* OR *邮储银行* OR *中信银行* OR *光大银行* OR *华夏银行* OR *兴业银行* OR *广发银行* OR *平安银行* OR *浦发银行* OR *浙商银行* OR *渤海银行* OR *中行* OR *农行* OR *工行* OR *建行* OR *交行* ) AND NOT ( *助农* OR *农行动* OR *工行业* OR *义工行* OR *浪中行* OR *建行业* OR *中行人* OR *中行稳* OR *雨中行* OR *雪中行* ))) AND time:[2025-09-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *招商银行* OR *招行* ) AND NOT ( *这招* OR *招行云流水* )) OR desc:(( *招商银行* OR *招行* ) AND NOT ( *这招* OR *招行云流水* ))) AND time:[2024-10-11 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *海信电视* )) OR desc:(( *海信电视* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *海信E8Q* )) OR desc:(( *海信E8Q* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *海信E5Q* )) OR desc:(( *海信E5Q* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL Q10L* AND *海信E8Q* AND *怎么选* )) OR desc:(( *TCL Q10L* AND *海信E8Q* AND *怎么选* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *海信* OR *索尼* ) AND ( *TCL* AND *电视* AND *哪个好* )) OR desc:(( *海信* OR *索尼* ) AND ( *TCL* AND *电视* AND *哪个好* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *2025* AND *电视推荐* )) OR desc:(( *2025* AND *电视推荐* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *双11* AND *电视推荐* )) OR desc:(( *双11* AND *电视推荐* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *电视品牌哪个好* )) OR desc:(( *电视品牌哪个好* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *电视怎么选* )) OR desc:(( *电视怎么选* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL电视质量* )) OR desc:(( *TCL电视质量* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL电视测评* )) OR desc:(( *TCL电视测评* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL电视怎么样* )) OR desc:(( *TCL电视怎么样* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *TCL电视* )) OR desc:(( *TCL电视* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *开学AI第一课* OR *夸克AI会员* ) OR ( ( *夸克* ) AND ( *夸克新学期* OR *夸克老师* OR *教育计划* OR *大学生发会员* OR *邪修学习* OR *邪修必备* OR *发会员* OR *送会员* OR *老师发会员* OR *必备AI神器* OR *大学生出现* ) AND NOT ( *网盘* OR *小说* OR *网文* OR *扫描* OR *提取码* OR *案例* OR *链接转自* OR *转载* OR *地址* OR *粉丝福利* OR *物理* OR *课程* OR *自取* OR *学院* OR *点赞* ) )) OR desc:(( *开学AI第一课* OR *夸克AI会员* ) OR ( ( *夸克* ) AND ( *夸克新学期* OR *夸克老师* OR *教育计划* OR *大学生发会员* OR *邪修学习* OR *邪修必备* OR *发会员* OR *送会员* OR *老师发会员* OR *必备AI神器* OR *大学生出现* ) AND NOT ( *网盘* OR *小说* OR *网文* OR *扫描* OR *提取码* OR *案例* OR *链接转自* OR *转载* OR *地址* OR *粉丝福利* OR *物理* OR *课程* OR *自取* OR *学院* OR *点赞* ) ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *知问教育* OR *知问公考* )) OR desc:(( *知问教育* OR *知问公考* ))) AND time:[2025-08-05 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *#新一代鲜活营养在飞鹤* )) OR desc:(( *#新一代鲜活营养在飞鹤* ))) AND time:[2025-07-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *美的空调* )) OR desc:(( *美的空调* ))) AND time:[2025-09-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *美的厨房空调* )) OR desc:(( *美的厨房空调* ))) AND time:[2025-09-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *AOC显示器* )) OR desc:(( *AOC显示器* ))) AND time:[2024-12-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *久心* ) AND ( *AED* )) OR desc:(( *久心* ) AND ( *AED* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *联鹿* ) AND ( *AED* )) OR desc:(( *联鹿* ) AND ( *AED* ))) AND time:[2025-08-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *洛克兄弟* AND *坑兄弟* )) OR desc:(( *洛克兄弟* AND *坑兄弟* ))) AND time:[2025-08-19 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *粉笔教育* AND *事业单位* AND *面试班* )) OR desc:(( *粉笔教育* AND *事业单位* AND *面试班* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *#神奇的敕勒川* )) OR desc:(( *#神奇的敕勒川* ))) AND time:[2025-07-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *波斯王子：rogue* OR *rouge* OR *肉鸽* OR *波斯王子rogue* OR *育碧游戏* OR *育碧* ) AND ( *波斯王子* )) OR desc:(( *波斯王子：rogue* OR *rouge* OR *肉鸽* OR *波斯王子rogue* OR *育碧游戏* OR *育碧* ) AND ( *波斯王子* ))) AND time:[2025-07-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *聚瑞云控* )) OR desc:(( *聚瑞云控* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *张宇* OR *宇哥* OR *张宇考研数学* OR *考研数学张宇* OR *宇哥考研* OR *张宇考研* OR *考研张宇* ) AND ( *考研* OR *数学* OR *30讲* OR *36讲* OR *8套卷* OR *4套卷* OR *临门一脚* ) AND NOT ( *宇宙* OR *台球* OR *图文* OR *编辑* OR *演唱* OR *歌曲* OR *唱歌* OR *明星* )) OR desc:(( *张宇* OR *宇哥* OR *张宇考研数学* OR *考研数学张宇* OR *宇哥考研* OR *张宇考研* OR *考研张宇* ) AND ( *考研* OR *数学* OR *30讲* OR *36讲* OR *8套卷* OR *4套卷* OR *临门一脚* ) AND NOT ( *宇宙* OR *台球* OR *图文* OR *编辑* OR *演唱* OR *歌曲* OR *唱歌* OR *明星* ))) AND time:[2025-05-31 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *notability* )) OR desc:(( *notability* ))) AND time:[2025-08-10 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *goodnotes* )) OR desc:(( *goodnotes* ))) AND time:[2025-08-10 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *豆包ai* OR *豆包app* ) AND ( *开学* ) AND NOT ( *头图* OR *图片* OR *图集* OR *豆包ai生成* OR *技术指导* OR *内容由豆包提供* OR *钦豆包* OR *水豚* OR *钦柠味* OR *粘豆包* OR *吃豆包* OR *孙颖莎* OR *王楚钦* OR *大头* OR *红豆包* OR *大豆包* OR *莎莎* OR *大头* OR *大模型* OR *小豆包* OR *菜包* OR *干粮* OR *封面* OR *图源* OR *配图来源* OR *链接* OR *U盘* OR *一审一校* OR *图文* )) OR desc:(( *豆包ai* OR *豆包app* ) AND ( *开学* ) AND NOT ( *头图* OR *图片* OR *图集* OR *豆包ai生成* OR *技术指导* OR *内容由豆包提供* OR *钦豆包* OR *水豚* OR *钦柠味* OR *粘豆包* OR *吃豆包* OR *孙颖莎* OR *王楚钦* OR *大头* OR *红豆包* OR *大豆包* OR *莎莎* OR *大头* OR *大模型* OR *小豆包* OR *菜包* OR *干粮* OR *封面* OR *图源* OR *配图来源* OR *链接* OR *U盘* OR *一审一校* OR *图文* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *200题* OR *8套卷* OR *教育学* OR *考研* OR *小紫书* ) AND ( *洪哥* ) AND NOT ( *雷语* OR *女儿* )) OR desc:(( *200题* OR *8套卷* OR *教育学* OR *考研* OR *小紫书* ) AND ( *洪哥* ) AND NOT ( *雷语* OR *女儿* ))) AND time:[2025-05-14 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *武忠祥* OR *武老* OR *武佬* ) AND ( *数学* OR *考研* OR *660* OR *高数* OR *强化* OR *辅导讲义* )) OR desc:(( *武忠祥* OR *武老* OR *武佬* ) AND ( *数学* OR *考研* OR *660* OR *高数* OR *强化* OR *辅导讲义* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *200题* ) AND ( *冬青* )) OR desc:(( *200题* ) AND ( *冬青* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *数学* OR *考研* OR *1800* OR *强化* OR *基础* ) AND ( *汤家凤* )) OR desc:(( *数学* OR *考研* OR *1800* OR *强化* OR *基础* ) AND ( *汤家凤* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *200题* OR *必背200题* OR *必备200题* ) AND ( *洪哥* )) OR desc:(( *200题* OR *必背200题* OR *必备200题* ) AND ( *洪哥* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *英语* OR *考研* OR *真题* ) AND ( *考研真相* )) OR desc:(( *英语* OR *考研* OR *真题* ) AND ( *考研真相* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *徐涛* OR *涛涛* ) AND ( *考研* OR *考研政治* OR *核心考案* OR *优题库* OR *强化班* OR *强化课* OR *押题* OR *背诵笔记* OR *背诵手册* )) OR desc:(( *徐涛* OR *涛涛* ) AND ( *考研* OR *考研政治* OR *核心考案* OR *优题库* OR *强化班* OR *强化课* OR *押题* OR *背诵笔记* OR *背诵手册* ))) AND time:[2025-08-03 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *政治* OR *肖秀荣* ) AND ( *精讲精练* )) OR desc:(( *政治* OR *肖秀荣* ) AND ( *精讲精练* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *管综* OR *经综* OR *母题800* OR *33篇* OR *逻辑* OR *写作* ) AND ( *老吕* )) OR desc:(( *管综* OR *经综* OR *母题800* OR *33篇* OR *逻辑* OR *写作* ) AND ( *老吕* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *腿姐* OR *陆寓丰* ) AND ( *政治* OR *考研* OR *考点清单* OR *九页纸* OR *背诵手册* )) OR desc:(( *腿姐* OR *陆寓丰* ) AND ( *政治* OR *考研* OR *考点清单* OR *九页纸* OR *背诵手册* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *肖秀荣* OR *肖爷爷* OR *肖大大* ) AND ( *考研* OR *考研政治* OR *政治* )) OR desc:(( *肖秀荣* OR *肖爷爷* OR *肖大大* ) AND ( *考研* OR *考研政治* OR *政治* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *陈锦斌* OR *锦斌* OR *考研英语陈锦斌* OR *考研陈锦斌* OR *陈锦斌斌哥* ) AND NOT ( *斌斌* OR *斌哥* OR *主持人斌哥* )) OR desc:(( *陈锦斌* OR *锦斌* OR *考研英语陈锦斌* OR *考研陈锦斌* OR *陈锦斌斌哥* ) AND NOT ( *斌斌* OR *斌哥* OR *主持人斌哥* ))) AND time:[2025-07-31 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *周洋鑫* )) OR desc:(( *周洋鑫* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *马天艺* OR *考研英语马天艺* OR *考研英语艺哥* ) AND NOT ( *天艺吖* OR *马天亿麻麻* )) OR desc:(( *马天艺* OR *考研英语马天艺* OR *考研英语艺哥* ) AND NOT ( *天艺吖* OR *马天亿麻麻* ))) AND time:[2025-07-31 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *招商证券* )) OR desc:(( *招商证券* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *背诵手册* OR *背诵笔记* ) AND ( *肖秀荣* )) OR desc:(( *背诵手册* OR *背诵笔记* ) AND ( *肖秀荣* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *六套卷* OR *6套卷* OR *四套卷* OR *4套卷* OR *六+四* OR *6+4* OR *六加四* OR *6加4* OR *李六* OR *李6* OR *李四* OR *李4* ) AND ( *李林* )) OR desc:(( *六套卷* OR *6套卷* OR *四套卷* OR *4套卷* OR *六+四* OR *6+4* OR *六加四* OR *6加4* OR *李六* OR *李6* OR *李四* OR *李4* ) AND ( *李林* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *超越* OR *5套卷* OR *五套卷* OR *5+5* OR *五加五* OR *五+五* OR *5加5* ) AND ( *合工大* )) OR desc:(( *超越* OR *5套卷* OR *五套卷* OR *5+5* OR *五加五* OR *五+五* OR *5加5* ) AND ( *合工大* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *启航* ) AND ( *真题详解* AND *英语* )) OR desc:(( *启航* ) AND ( *真题详解* AND *英语* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *考研* ) AND ( *有道* ) AND NOT ( *词典* OR *翻译* OR *磁场* OR *道理* OR *有道题* OR *四级* OR *六级* OR *四六级* OR *可循* OR *app* OR *欧包* OR *启航考研* OR *狸猫夜* OR *美术* OR *高中* OR *美国* OR *公职* OR *道士* OR *307* OR *文具* OR *外刊* OR *华为* OR *景观* )) OR desc:(( *考研* ) AND ( *有道* ) AND NOT ( *词典* OR *翻译* OR *磁场* OR *道理* OR *有道题* OR *四级* OR *六级* OR *四六级* OR *可循* OR *app* OR *欧包* OR *启航考研* OR *狸猫夜* OR *美术* OR *高中* OR *美国* OR *公职* OR *道士* OR *307* OR *文具* OR *外刊* OR *华为* OR *景观* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *考研* OR *马天艺* ) AND ( *单词之间* )) OR desc:(( *考研* OR *马天艺* ) AND ( *单词之间* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *启航* ) AND ( *红宝书* )) OR desc:(( *启航* ) AND ( *红宝书* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *开学* ) AND ( *小猿AI* )) OR desc:(( *开学* ) AND ( *小猿AI* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *开学* ) AND ( *deepseek* ) AND NOT ( *副业* OR *神券* OR *技巧* OR *福利* OR *幼儿园* OR *原唱* OR *翻唱* OR *洛天依* OR *文章* OR *报名人数* OR *人生* OR *价值观* OR *教研* OR *电影* OR *A股* OR *图文* OR *链接* OR *图片* )) OR desc:(( *开学* ) AND ( *deepseek* ) AND NOT ( *副业* OR *神券* OR *技巧* OR *福利* OR *幼儿园* OR *原唱* OR *翻唱* OR *洛天依* OR *文章* OR *报名人数* OR *人生* OR *价值观* OR *教研* OR *电影* OR *A股* OR *图文* OR *链接* OR *图片* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *考点狂背* AND *王吉背诵* )) OR desc:(( *考点狂背* AND *王吉背诵* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *背诵笔记* OR *背诵手册* ) AND ( *徐涛* )) OR desc:(( *背诵笔记* OR *背诵手册* ) AND ( *徐涛* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *徐涛* OR *政治* ) AND ( *优题库* )) OR desc:(( *徐涛* OR *政治* ) AND ( *优题库* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *徐涛* OR *考研* OR *政治* ) AND ( *核心考案* )) OR desc:(( *徐涛* OR *考研* OR *政治* ) AND ( *核心考案* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *写作锦囊* OR *陈锦斌36句* OR *陈锦斌写作* OR *陈锦斌作文* )) OR desc:(( *写作锦囊* OR *陈锦斌36句* OR *陈锦斌写作* OR *陈锦斌作文* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *考研* OR *张宇* ) AND ( *临门一脚* )) OR desc:(( *考研* OR *张宇* ) AND ( *临门一脚* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *开学* ) AND ( *腾讯元宝* )) OR desc:(( *开学* ) AND ( *腾讯元宝* ))) AND time:[2025-08-26 16:00:00 TO 2025-09-30 15:59:00]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *八套卷* OR *8套卷* OR *四套卷* OR *4套卷* OR *张八* OR *张8* OR *张四* OR *张4* OR *八加四* OR *8+4* OR *八+四* ) AND ( *张宇* )) OR desc:(( *八套卷* OR *8套卷* OR *四套卷* OR *4套卷* OR *张八* OR *张8* OR *张四* OR *张4* OR *八加四* OR *8+4* OR *八+四* ) AND ( *张宇* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *张宇* OR *考研* OR *数学* ) AND ( *30讲* )) OR desc:(( *张宇* OR *考研* OR *数学* ) AND ( *30讲* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *张宇* OR *数学* OR *考研* ) AND ( *36讲* )) OR desc:(( *张宇* OR *数学* OR *考研* ) AND ( *36讲* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *启航教育* OR *启航考研* ) AND NOT ( *成人自考* OR *成人教育* OR *电视剧* OR *启航留学* OR *二等奖* OR *分享会* OR *校庆* OR *小学* OR *驾校* OR *文明* OR *二手车* OR *小升初* OR *托管* OR *高二* OR *高中* OR *高三* OR *宝贝* OR *艺考* OR *孩子* OR *幼儿* OR *汇演* OR *全托* OR *半托* OR *专升本* OR *游学* OR *音乐* OR *碧桂园* OR *铂翼* OR *爱心助学* OR *教育学院* OR *大学新生* OR *集团* OR *开学典礼* OR *赛事* OR *叉车* )) OR desc:(( *启航教育* OR *启航考研* ) AND NOT ( *成人自考* OR *成人教育* OR *电视剧* OR *启航留学* OR *二等奖* OR *分享会* OR *校庆* OR *小学* OR *驾校* OR *文明* OR *二手车* OR *小升初* OR *托管* OR *高二* OR *高中* OR *高三* OR *宝贝* OR *艺考* OR *孩子* OR *幼儿* OR *汇演* OR *全托* OR *半托* OR *专升本* OR *游学* OR *音乐* OR *碧桂园* OR *铂翼* OR *爱心助学* OR *教育学院* OR *大学新生* OR *集团* OR *开学典礼* OR *赛事* OR *叉车* ))) AND time:[2025-05-05 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *打新* OR *上市* OR *ipo* ) AND ( *figrue* )) OR desc:(( *打新* OR *上市* OR *ipo* ) AND ( *figrue* ))) AND time:[2025-08-29 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *gemini* AND *ipo* )) OR desc:(( *gemini* AND *ipo* ))) AND time:[2025-08-29 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *支付宝* OR *zfb* OR *支付宝基金* OR *支付宝理财* OR *基金* OR *新能源* OR *cpo* OR *创新药* OR *纳斯达克* OR *红利* OR *消费* OR *金融理财* OR *吃肉* OR *收益更新* OR *赚* OR *行情* OR *大A* OR *A股* OR *理财小白* OR *投资* OR *操作* OR *加仓* OR *收益* OR *ETF* OR *指数* OR *配置* OR *混合* OR *定投* OR *持仓* OR *理财日记* ) AND ( *基金* AND *理财* ) AND NOT ( *稳健* OR *跌* OR *大跌* OR *绿* )) OR desc:(( *支付宝* OR *zfb* OR *支付宝基金* OR *支付宝理财* OR *基金* OR *新能源* OR *cpo* OR *创新药* OR *纳斯达克* OR *红利* OR *消费* OR *金融理财* OR *吃肉* OR *收益更新* OR *赚* OR *行情* OR *大A* OR *A股* OR *理财小白* OR *投资* OR *操作* OR *加仓* OR *收益* OR *ETF* OR *指数* OR *配置* OR *混合* OR *定投* OR *持仓* OR *理财日记* ) AND ( *基金* AND *理财* ) AND NOT ( *稳健* OR *跌* OR *大跌* OR *绿* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *影驰显卡* )) OR desc:(( *影驰显卡* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *技嘉显卡* )) OR desc:(( *技嘉显卡* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *微星显卡* )) OR desc:(( *微星显卡* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *ROG显卡* ) AND ( *华硕显卡* )) OR desc:(( *ROG显卡* ) AND ( *华硕显卡* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *七彩虹显卡* )) OR desc:(( *七彩虹显卡* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *存量证明* OR *开户* OR *海外证明* ) AND ( *ibkr* AND *盈透* ) AND NOT ( *轻盈* )) OR desc:(( *存量证明* OR *开户* OR *海外证明* ) AND ( *ibkr* AND *盈透* ) AND NOT ( *轻盈* ))) AND time:[2025-08-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* )) OR desc:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* ))) AND time:[2025-07-11 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* )) OR desc:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* ))) AND time:[2025-07-11 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* )) OR desc:(( *微泰泵* OR *微泰equil* OR *微泰胰岛素泵* OR *微泰贴泵* OR *微泰贴敷式胰岛素泵* OR *微泰胰岛素* OR *微泰医疗泵* OR *微泰科研泵* ))) AND time:[2025-07-11 16:00:00 TO *] AND uid:(null OR null OR null OR null OR null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *LISTENEER* ) AND ( *listeneer* ) AND ( *倾听者* )) OR desc:(( *LISTENEER* ) AND ( *listeneer* ) AND ( *倾听者* ))) AND time:[2025-03-17 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *外卖* ) AND ( *拼好饭* )) OR desc:(( *外卖* ) AND ( *拼好饭* ))) AND time:[2025-05-12 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *美团餐饮团购* ) OR ( *美团美食团购* ) OR ( *美团团购* )) OR desc:(( *美团餐饮团购* ) OR ( *美团美食团购* ) OR ( *美团团购* ))) AND time:[2025-06-30 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *神抢手* )) OR desc:(( *神抢手* ))) AND time:[2025-06-30 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *FFALCON雷鸟* OR *TCL雷鸟* OR *雷鸟电视* OR *FFALCON* ) AND NOT ( *眼镜* OR *周深* OR *造型* OR *LT* OR *凯乐石* OR *盾* OR *图腾* OR *雷鸟Air3sPro* OR *雷鸟显示器* OR *岩雷鸟* OR *雷鸟口袋电视* OR *雷鸟x3Pro* OR *雷鸟V3* OR *梦幻西游* OR *美国空军雷鸟飞行表演队* OR *柳雷鸟* OR *人机* )) OR desc:(( *FFALCON雷鸟* OR *TCL雷鸟* OR *雷鸟电视* OR *FFALCON* ) AND NOT ( *眼镜* OR *周深* OR *造型* OR *LT* OR *凯乐石* OR *盾* OR *图腾* OR *雷鸟Air3sPro* OR *雷鸟显示器* OR *岩雷鸟* OR *雷鸟口袋电视* OR *雷鸟x3Pro* OR *雷鸟V3* OR *梦幻西游* OR *美国空军雷鸟飞行表演队* OR *柳雷鸟* OR *人机* ))) AND time:[2024-12-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *瑞幸* OR *原产地* OR *苹* OR *阿克苏* OR *苹果C* OR *苹C* OR *生椰* OR *蜜柚* OR *柠檬* OR *茉莉* OR *咖啡* OR *苹果拿铁* OR *苹果美式* ) AND ( *汤唯* )) OR desc:(( *瑞幸* OR *原产地* OR *苹* OR *阿克苏* OR *苹果C* OR *苹C* OR *生椰* OR *蜜柚* OR *柠檬* OR *茉莉* OR *咖啡* OR *苹果拿铁* OR *苹果美式* ) AND ( *汤唯* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *瑞幸* OR *瑞幸咖啡* OR *瑞子* OR *luckin* OR *luckin* *coffee* ) AND ( *阿克苏* OR *苹果C* OR *苹C* OR *在地化* OR *苹果拿铁* OR *苹果美式* )) OR desc:(( *瑞幸* OR *瑞幸咖啡* OR *瑞子* OR *luckin* OR *luckin* *coffee* ) AND ( *阿克苏* OR *苹果C* OR *苹C* OR *在地化* OR *苹果拿铁* OR *苹果美式* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( ( *澳门* OR *澳門* ) AND ( *澳门银河* OR *澳门星际* OR *澳門銀河* OR *澳門星際* OR *百老汇* OR *悦榕庄* OR *大仓* OR *万豪* OR *JW* OR *丽思* OR *RC酒店* OR *安达仕* OR *萊佛士* OR *嘉佩乐* ) AND ( *餐饮* OR *餐厅* OR *美食* OR *酒吧* OR *酒廊* OR *cafe* ) ) OR ( ( *澳门* OR *澳門* ) AND ( *BOMBANA* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) )) OR desc:(( ( *澳门* OR *澳門* ) AND ( *澳门银河* OR *澳门星际* OR *澳門銀河* OR *澳門星際* OR *百老汇* OR *悦榕庄* OR *大仓* OR *万豪* OR *JW* OR *丽思* OR *RC酒店* OR *安达仕* OR *萊佛士* OR *嘉佩乐* ) AND ( *餐饮* OR *餐厅* OR *美食* OR *酒吧* OR *酒廊* OR *cafe* ) ) OR ( ( *澳门* OR *澳門* ) AND ( *BOMBANA* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *银河会议中心* OR *银河综艺馆* OR *银河娱乐集团* OR *银娱集团* ) OR ( ( *澳门* OR *澳門* ) AND ( *银河* OR *星际* OR *百老汇* OR *大仓* OR *悦榕庄* OR *万豪* OR *丽思* OR *安达仕* OR *莱佛士* OR *嘉佩乐* OR *天浪淘园* OR *巨钻秀* OR *时尚汇* OR *优越会* OR *童乐坊* OR *宝卡* OR *BOMBANA* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) )) OR desc:(( *银河会议中心* OR *银河综艺馆* OR *银河娱乐集团* OR *银娱集团* ) OR ( ( *澳门* OR *澳門* ) AND ( *银河* OR *星际* OR *百老汇* OR *大仓* OR *悦榕庄* OR *万豪* OR *丽思* OR *安达仕* OR *莱佛士* OR *嘉佩乐* OR *天浪淘园* OR *巨钻秀* OR *时尚汇* OR *优越会* OR *童乐坊* OR *宝卡* OR *BOMBANA* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门* OR *澳門* ) AND ( *永利皇宫* OR *永利* OR *谭卉* OR *永翠宫* OR *永利扒房* OR *鮨泓* OR *永利轩* OR *九鲲* OR *发财树* OR *馥乐庭* )) OR desc:(( *澳门* OR *澳門* ) AND ( *永利皇宫* OR *永利* OR *谭卉* OR *永翠宫* OR *永利扒房* OR *鮨泓* OR *永利轩* OR *九鲲* OR *发财树* OR *馥乐庭* ))) AND time:[2025-09-02 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *考研* OR *图书* OR *时代* OR *旗舰店* ) AND ( *云图* ) AND NOT ( *词云* OR *卫星* OR *巨量* OR *美术馆* OR *图书馆* OR *房子* OR *热力* OR *粉丝* OR *装修* OR *公园* OR *分辨率* OR *税务* OR *二手房* OR *楼层* OR *房产* OR *车位* OR *好房* OR *风口* OR *公寓* OR *龙湖* OR *特效* OR *四川* OR *保利* OR *地产* OR *北纬30度* OR *名门* OR *鲁迅* OR *小区* OR *得云图书* OR *皮肤* OR *山河* )) OR desc:(( *考研* OR *图书* OR *时代* OR *旗舰店* ) AND ( *云图* ) AND NOT ( *词云* OR *卫星* OR *巨量* OR *美术馆* OR *图书馆* OR *房子* OR *热力* OR *粉丝* OR *装修* OR *公园* OR *分辨率* OR *税务* OR *二手房* OR *楼层* OR *房产* OR *车位* OR *好房* OR *风口* OR *公寓* OR *龙湖* OR *特效* OR *四川* OR *保利* OR *地产* OR *北纬30度* OR *名门* OR *鲁迅* OR *小区* OR *得云图书* OR *皮肤* OR *山河* ))) AND time:[2025-05-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *明基显示器* ) AND ( *明基* )) OR desc:(( *明基显示器* ) AND ( *明基* ))) AND time:[2025-06-16 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *天士力降茶* OR *降茶* ) AND NOT ( *升降* OR *桌面岩板* OR *三降茶* OR *空降茶圈* OR *投降茶宠* OR *升降茶台* OR *茯苓降茶* OR *降降茶* OR *2025升降茶几* OR *24宝* OR *金银花* OR *体重乖乖降* OR *花升子* OR *五指毛桃* OR *黄体酮* OR *空降* OR *回春堂双降茶* OR *茶叶崩盘* OR *霜降茶* OR *茶价骤降* OR *缓缓沉降* OR *唐宋以降* OR *下降* OR *渐降* )) OR desc:(( *天士力降茶* OR *降茶* ) AND NOT ( *升降* OR *桌面岩板* OR *三降茶* OR *空降茶圈* OR *投降茶宠* OR *升降茶台* OR *茯苓降茶* OR *降降茶* OR *2025升降茶几* OR *24宝* OR *金银花* OR *体重乖乖降* OR *花升子* OR *五指毛桃* OR *黄体酮* OR *空降* OR *回春堂双降茶* OR *茶叶崩盘* OR *霜降茶* OR *茶价骤降* OR *缓缓沉降* OR *唐宋以降* OR *下降* OR *渐降* ))) AND time:[2025-07-17 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *纪元117* OR *Anno117:* *Pax* *Romana* OR *Anno117* OR *纪元117：罗马和平* ) AND ( *试玩* ) AND NOT ( *卡游* OR *香草* OR *尸姬之梦* OR *詹姆斯* )) OR desc:(( *纪元117* OR *Anno117:* *Pax* *Romana* OR *Anno117* OR *纪元117：罗马和平* ) AND ( *试玩* ) AND NOT ( *卡游* OR *香草* OR *尸姬之梦* OR *詹姆斯* ))) AND time:[2025-09-01 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *YF* *Life* OR *万通保险* )) OR desc:(( *YF* *Life* OR *万通保险* ))) AND time:[2024-09-30 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *微泰动态* OR *微泰二代* OR *微泰动态血糖仪* OR *微泰动态二代* OR *微泰泵* OR *微泰倍稳* OR *微泰医疗* OR *微泰生活家* OR *微泰微甜生活季* OR *微泰动态我的测糖搭子* OR *检棠* OR *倍稳血糖仪* OR *微泰linx* OR *linx动态* ) AND NOT ( *微泰州* OR *微泰感* OR *微泰国* OR *微泰温泉* OR *领克* )) OR desc:(( *微泰动态* OR *微泰二代* OR *微泰动态血糖仪* OR *微泰动态二代* OR *微泰泵* OR *微泰倍稳* OR *微泰医疗* OR *微泰生活家* OR *微泰微甜生活季* OR *微泰动态我的测糖搭子* OR *检棠* OR *倍稳血糖仪* OR *微泰linx* OR *linx动态* ) AND NOT ( *微泰州* OR *微泰感* OR *微泰国* OR *微泰温泉* OR *领克* ))) AND time:[2025-04-06 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *稳健* OR *稳健理财* OR *稳稳收益* OR *低风险* OR *稳定盈利* OR *债券* OR *债* ) AND ( *支付宝理财* AND *zfb* AND *理财* AND *基金* )) OR desc:(( *稳健* OR *稳健理财* OR *稳稳收益* OR *低风险* OR *稳定盈利* OR *债券* OR *债* ) AND ( *支付宝理财* AND *zfb* AND *理财* AND *基金* ))) AND time:[2025-08-27 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *同桌雅思外教* OR *同桌雅思* OR *同桌外教* OR *同桌英语* OR *同桌教育* ) AND ( *外教* OR *口语外教* OR *菲教* OR *母语外教* OR *精选* OR *优选* OR *串题* OR *串题包* ) AND NOT ( *机考* OR *作文* OR *班课* OR *奋斗猿* )) OR desc:(( *同桌雅思外教* OR *同桌雅思* OR *同桌外教* OR *同桌英语* OR *同桌教育* ) AND ( *外教* OR *口语外教* OR *菲教* OR *母语外教* OR *精选* OR *优选* OR *串题* OR *串题包* ) AND NOT ( *机考* OR *作文* OR *班课* OR *奋斗猿* ))) AND time:[2025-03-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *京东支付* )) OR desc:(( *京东支付* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *理财牛人* ) AND ( *京东金融* )) OR desc:(( *理财牛人* ) AND ( *京东金融* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *京东金条* )) OR desc:(( *京东金条* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *京东白条* ) AND NOT ( *补贴* OR *二手机* OR *优惠* OR *波司登* )) OR desc:(( *京东白条* ) AND NOT ( *补贴* OR *二手机* OR *优惠* OR *波司登* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *京东金融* ) AND NOT ( *入职要求* OR *支付券* OR *红包* OR *福利* )) OR desc:(( *京东金融* ) AND NOT ( *入职要求* OR *支付券* OR *红包* OR *福利* ))) AND time:[2025-08-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *黄金* OR *支付宝* OR *zfb* OR *金价* ) AND ( *理财* AND *基金* ) AND NOT ( *招行* OR *京东* OR *理财通* OR *天天基金* OR *同花顺* OR *招银* OR *招商银行* )) OR desc:(( *黄金* OR *支付宝* OR *zfb* OR *金价* ) AND ( *理财* AND *基金* ) AND NOT ( *招行* OR *京东* OR *理财通* OR *天天基金* OR *同花顺* OR *招银* OR *招商银行* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *基金* OR *理财* OR *支付宝* OR *zfb* OR *金价* ) AND ( *黄金* AND *大黄* AND *买金* ) AND NOT ( *黄金周* OR *黄金会员* OR *黄金券* OR *黄金窗口* OR *黄金鱼* OR *黄金期* OR *黄金喂养* OR *黄金扫货* OR *黄金薯* OR *黄金鳞片* OR *黄金2小时* OR *黄金救援* OR *黄金观位* OR *黄金坑* OR *黄金时期* OR *黄金维权* OR *黄金区域* OR *黄金时代* OR *黄金回收* OR *黄金批发* OR *水贝* OR *京东* OR *跌* )) OR desc:(( *基金* OR *理财* OR *支付宝* OR *zfb* OR *金价* ) AND ( *黄金* AND *大黄* AND *买金* ) AND NOT ( *黄金周* OR *黄金会员* OR *黄金券* OR *黄金窗口* OR *黄金鱼* OR *黄金期* OR *黄金喂养* OR *黄金扫货* OR *黄金薯* OR *黄金鳞片* OR *黄金2小时* OR *黄金救援* OR *黄金观位* OR *黄金坑* OR *黄金时期* OR *黄金维权* OR *黄金区域* OR *黄金时代* OR *黄金回收* OR *黄金批发* OR *水贝* OR *京东* OR *跌* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *八子补肾* ) AND ( ( *衰老* ) AND NOT ( *银龄集市* ) )) OR desc:(( *八子补肾* ) AND ( ( *衰老* ) AND NOT ( *银龄集市* ) ))) AND time:[2025-06-16 16:00:00 TO *] AND uid:(null OR null)','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *三诺* ) AND ( ( *三诺爱看* OR *三诺i看* OR *三诺动态* OR *三诺二代* OR *三诺血糖* OR *三诺讲糖* OR *i6* OR *ican* ) AND NOT ( *特兰德* OR *关税* OR *贸易* OR *粘合* OR *三诺血压计* ) ) AND NOT ( *三诺血压计* OR *三诺易巧* OR *三诺血糖尿酸* )) OR desc:(( *三诺* ) AND ( ( *三诺爱看* OR *三诺i看* OR *三诺动态* OR *三诺二代* OR *三诺血糖* OR *三诺讲糖* OR *i6* OR *ican* ) AND NOT ( *特兰德* OR *关税* OR *贸易* OR *粘合* OR *三诺血压计* ) ) AND NOT ( *三诺血压计* OR *三诺易巧* OR *三诺血糖尿酸* ))) AND time:[2025-05-04 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *上市* ) AND ( *理想i6* )) OR desc:(( *上市* ) AND ( *理想i6* ))) AND time:[2025-07-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *移宇泵* OR *移宇* OR *移宇胰岛素泵* ) AND ( *泵* )) OR desc:(( *移宇泵* OR *移宇* OR *移宇胰岛素泵* ) AND ( *泵* ))) AND time:[2025-07-11 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *富国基金* )) OR desc:(( *富国基金* ))) AND time:[2025-08-29 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *券商* ) AND ( *证券公司* )) OR desc:(( *券商* ) AND ( *证券公司* ))) AND time:[2025-08-29 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *华硕adol* OR *华硕a豆* OR *a豆14Air* OR *a豆14Air香氛版* OR *a豆香氛机械键盘* OR *a豆氮化镓充电* ) AND NOT ( *二手* OR *闲鱼* OR *回收* OR *租赁* OR *出租* OR *维修* OR *修电脑* OR *闲置* OR *远程* OR *转转* OR *快修* OR *官翻* OR *翻新* OR *组装* OR *渠道报价* OR *报价单* )) OR desc:(( *华硕adol* OR *华硕a豆* OR *a豆14Air* OR *a豆14Air香氛版* OR *a豆香氛机械键盘* OR *a豆氮化镓充电* ) AND NOT ( *二手* OR *闲鱼* OR *回收* OR *租赁* OR *出租* OR *维修* OR *修电脑* OR *闲置* OR *远程* OR *转转* OR *快修* OR *官翻* OR *翻新* OR *组装* OR *渠道报价* OR *报价单* ))) AND time:[2024-12-31 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *AI小智* )) OR desc:(( *AI小智* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *中国教育在线* )) OR desc:(( *中国教育在线* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *被坑* OR *騙局* OR *踩雷* OR *假象* OR *大話* OR *騙人* OR *坑了* OR *中伏* OR *陷阱* OR *破产* OR *债务* OR *爆雷* OR *败家* OR *巨债* OR *欠债* OR *暴雷* OR *财困* OR *危机* OR *不良资产炒作* OR *割韭菜* OR *洗脑* OR *恐吓* OR *老千* OR *造假* OR *炮灰* OR *崩盘* OR *爆仓* OR *自爆* OR *黑幕* OR *翻车* OR *无底洞* OR *踩坑* OR *毒鸡汤* OR *心灰意冷* OR *被带风向* ) AND ( *周大福人寿* )) OR desc:(( *被坑* OR *騙局* OR *踩雷* OR *假象* OR *大話* OR *騙人* OR *坑了* OR *中伏* OR *陷阱* OR *破产* OR *债务* OR *爆雷* OR *败家* OR *巨债* OR *欠债* OR *暴雷* OR *财困* OR *危机* OR *不良资产炒作* OR *割韭菜* OR *洗脑* OR *恐吓* OR *老千* OR *造假* OR *炮灰* OR *崩盘* OR *爆仓* OR *自爆* OR *黑幕* OR *翻车* OR *无底洞* OR *踩坑* OR *毒鸡汤* OR *心灰意冷* OR *被带风向* ) AND ( *周大福人寿* ))) AND time:[2025-05-18 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *教育* ) AND ( *头条菌* )) OR desc:(( *教育* ) AND ( *头条菌* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *在线教育* ) AND ( *南京大学* )) OR desc:(( *在线教育* ) AND ( *南京大学* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *360* OR *三六零* ) AND ( *教育在线* )) OR desc:(( *360* OR *三六零* ) AND ( *教育在线* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *金沙中国* OR *澳门金沙* OR *澳门威尼斯人* OR *澳门巴黎人* OR *澳门伦敦人* ) OR ( ( *澳门四季* ) AND ( *酒店.购物* OR *名店* OR *名荟* ) ) OR ( ( *澳门* OR *澳門* ) AND ( *康莱德* OR *瑞吉* OR *贡多拉* OR *巴黎铁塔* OR *Q立方* OR *百乐宫* OR *御匾会* OR *淮扬晓宴* OR *汉普阁* OR *希雅度* OR *妙泰* OR *雅舍* OR *北方鸣苑* OR *丘吉尔餐厅* OR *醉江南* OR *品粤轩* OR *巴黎轩* OR *御匾名汇* OR *华庭* ) )) OR desc:(( *金沙中国* OR *澳门金沙* OR *澳门威尼斯人* OR *澳门巴黎人* OR *澳门伦敦人* ) OR ( ( *澳门四季* ) AND ( *酒店.购物* OR *名店* OR *名荟* ) ) OR ( ( *澳门* OR *澳門* ) AND ( *康莱德* OR *瑞吉* OR *贡多拉* OR *巴黎铁塔* OR *Q立方* OR *百乐宫* OR *御匾会* OR *淮扬晓宴* OR *汉普阁* OR *希雅度* OR *妙泰* OR *雅舍* OR *北方鸣苑* OR *丘吉尔餐厅* OR *醉江南* OR *品粤轩* OR *巴黎轩* OR *御匾名汇* OR *华庭* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *新濠国际* OR *新濠天地* OR *新濠锋* OR *澳门新濠* ) OR ( ( *澳门* OR *澳門* OR *新濠* ) AND ( *摩珀斯* OR *颐居* OR *君悦* OR *巨星汇* OR *明星汇* OR *W酒店* OR *映星滙* OR *映星汇* OR *影汇* OR *摩天轮* OR *瀛方* OR *Artelli* OR *童梦天地* OR *蒲點* OR *天皇卡* OR *誉珑轩* OR *天颐* OR *御膳扒房* OR *杜卡斯餐厅* OR *鮨金悦* OR *mezza9* OR *玥龙轩* OR *帝影楼* OR *奥罗拉* OR *天政* ) )) OR desc:(( *新濠国际* OR *新濠天地* OR *新濠锋* OR *澳门新濠* ) OR ( ( *澳门* OR *澳門* OR *新濠* ) AND ( *摩珀斯* OR *颐居* OR *君悦* OR *巨星汇* OR *明星汇* OR *W酒店* OR *映星滙* OR *映星汇* OR *影汇* OR *摩天轮* OR *瀛方* OR *Artelli* OR *童梦天地* OR *蒲點* OR *天皇卡* OR *誉珑轩* OR *天颐* OR *御膳扒房* OR *杜卡斯餐厅* OR *鮨金悦* OR *mezza9* OR *玥龙轩* OR *帝影楼* OR *奥罗拉* OR *天政* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门美高梅* OR *澳门MGM* ) OR ( ( *澳門* OR *澳门* ) AND ( *美狮* OR *雍華府* OR *雍华府* OR *文华东方酒店* OR *一号广场* OR *壹號廣場* OR *2049* OR *保利博物馆* OR *狮王卡* OR *金狮会* OR *雅吉* OR *涛岸* OR *盛焰* OR *金殿堂* ) )) OR desc:(( *澳门美高梅* OR *澳门MGM* ) OR ( ( *澳門* OR *澳门* ) AND ( *美狮* OR *雍華府* OR *雍华府* OR *文华东方酒店* OR *一号广场* OR *壹號廣場* OR *2049* OR *保利博物馆* OR *狮王卡* OR *金狮会* OR *雅吉* OR *涛岸* OR *盛焰* OR *金殿堂* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门永利* AND *澳門永利* ) OR ( ( *澳门* OR *澳門* ) AND ( *永利皇宫* OR *谭卉* OR *永翠宫* OR *永利扒房* OR *鮨泓* OR *永利轩* OR *九鲲* OR *发财树* OR *馥乐庭* ) )) OR desc:(( *澳门永利* AND *澳門永利* ) OR ( ( *澳门* OR *澳門* ) AND ( *永利皇宫* OR *谭卉* OR *永翠宫* OR *永利扒房* OR *鮨泓* OR *永利轩* OR *九鲲* OR *发财树* OR *馥乐庭* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门博彩控股* OR *澳博控股* OR *澳门葡京* OR *澳門葡京* ) OR ( ( *澳门* OR *澳門* ) AND ( *欧舒丹酒店* OR *line* *friends酒店* OR *范思哲酒店* OR *老佛爷酒店* OR *versace酒店* OR *卡尔拉格斐酒店* OR *飛索* OR *英皇戏院* OR *上藝坊* OR *上艺坊* OR *上尚会* OR *御花园* OR *瑞兆* OR *奥丰素* OR *自助山* OR *味赏* OR *天巢* OR *8餐厅* ) )) OR desc:(( *澳门博彩控股* OR *澳博控股* OR *澳门葡京* OR *澳門葡京* ) OR ( ( *澳门* OR *澳門* ) AND ( *欧舒丹酒店* OR *line* *friends酒店* OR *范思哲酒店* OR *老佛爷酒店* OR *versace酒店* OR *卡尔拉格斐酒店* OR *飛索* OR *英皇戏院* OR *上藝坊* OR *上艺坊* OR *上尚会* OR *御花园* OR *瑞兆* OR *奥丰素* OR *自助山* OR *味赏* OR *天巢* OR *8餐厅* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *银河会议中心* OR *银河综艺馆* OR *银河娱乐* OR *银娱集团* OR *澳门银河* OR *澳门星际* OR *银河酒店* OR *星际酒店* ) OR ( ( *澳门* OR *澳門* ) AND ( *百老汇* OR *大仓* OR *悦榕庄* OR *万豪* OR *丽思* OR *安达仕* OR *莱佛士* OR *嘉佩乐* OR *天浪淘园* OR *巨钻秀* OR *时尚汇* OR *优越会* OR *童乐坊* OR *宝卡* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) )) OR desc:(( *银河会议中心* OR *银河综艺馆* OR *银河娱乐* OR *银娱集团* OR *澳门银河* OR *澳门星际* OR *银河酒店* OR *星际酒店* ) OR ( ( *澳门* OR *澳門* ) AND ( *百老汇* OR *大仓* OR *悦榕庄* OR *万豪* OR *丽思* OR *安达仕* OR *莱佛士* OR *嘉佩乐* OR *天浪淘园* OR *巨钻秀* OR *时尚汇* OR *优越会* OR *童乐坊* OR *宝卡* OR *丽轩* OR *鮨吉祥* OR *承铁板* OR *花悦庭* OR *尚坊* OR *风味居* OR *丹桂轩* OR *彭庆记* ) ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门美高梅* OR *澳门美狮美高梅* ) OR ( *澳門美高梅* OR *澳門美獅美高梅* )) OR desc:(( *澳门美高梅* OR *澳门美狮美高梅* ) OR ( *澳門美高梅* OR *澳門美獅美高梅* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门新濠天地* OR *澳门新濠影汇* OR *澳门新濠锋* OR *澳门新濠* ) OR ( *澳門新濠天地* OR *澳門新濠影彙* OR *澳門新濠鋒* OR *澳門新濠* )) OR desc:(( *澳门新濠天地* OR *澳门新濠影汇* OR *澳门新濠锋* OR *澳门新濠* ) OR ( *澳門新濠天地* OR *澳門新濠影彙* OR *澳門新濠鋒* OR *澳門新濠* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门威尼斯人* OR *澳门巴黎人* OR *澳门伦敦人* OR *澳门金沙* ) OR ( *澳門威尼斯人* OR *澳門巴黎人* OR *澳門倫敦人* OR *澳門金沙* )) OR desc:(( *澳门威尼斯人* OR *澳门巴黎人* OR *澳门伦敦人* OR *澳门金沙* ) OR ( *澳門威尼斯人* OR *澳門巴黎人* OR *澳門倫敦人* OR *澳門金沙* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);
INSERT INTO `bc_subscribe_query` (`query`,`group_id`,`effective`) VALUES ('(title:(( *澳门银河* OR *澳门星际* OR *澳门百老汇* ) OR ( *澳門银河* OR *澳門星際* OR *澳門百老滙* )) OR desc:(( *澳门银河* OR *澳门星际* OR *澳门百老汇* ) OR ( *澳門银河* OR *澳門星際* OR *澳門百老滙* ))) AND time:[2025-08-28 16:00:00 TO *]','GID_niop_dc_bc_xhs_dp_opus_prod',1);