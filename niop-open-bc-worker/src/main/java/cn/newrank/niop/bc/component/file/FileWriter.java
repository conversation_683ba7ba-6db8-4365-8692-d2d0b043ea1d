package cn.newrank.niop.bc.component.file;

import cn.newrank.niop.bc.api.Record;

import java.io.IOException;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/24 16:40:54
 * @version: 1.0.0
 * @description:
 */
public class FileWriter {

    private FileWriterStrategy fileWriterStrategy;

    public FileWriter(FileWriterStrategy fileWriterStrategy) {
        this.fileWriterStrategy = fileWriterStrategy;
    }

    public void write(Record record, String filePath) throws IOException {
        if (fileWriterStrategy == null) {
            throw new RuntimeException("no file write strategy.");
        }
        fileWriterStrategy.write(record, filePath);
    }

}
