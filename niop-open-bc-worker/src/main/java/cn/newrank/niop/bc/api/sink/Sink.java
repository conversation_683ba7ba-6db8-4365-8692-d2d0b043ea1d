package cn.newrank.niop.bc.api.sink;

import cn.newrank.niop.bc.api.Record;
import com.typesafe.config.Config;

import java.io.IOException;

/**
 * @author: xuguangjie
 * @date: 2025/7/18 16:56:13
 * @version: 1.0.0
 * @description:
 */
public interface Sink extends SinkPlugin {

    /**
     * 初始化 sink 组件
     *
     * @param config hocon sink 配置信息
     */
    void init(Config config);

    /**
     * 将输入的数据写入目标
     *
     * @param record 输入的数据
     */
    void write(Record record);

    /**
     * 将所有的数据刷新到目标。该方法会定期或者关闭时调用
     */
    void flush() throws IOException;

    /**
     * 释放 sink 持有的所有资源
     */
    void close();

}
