package cn.newrank.niop.bc.component.connector.source.mysql;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.source.SourcePlugin;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * @author: xuguangjie
 * @date: 2025/7/23 16:45:27
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(SourcePlugin.class)
public class MysqlSource implements Source {

    private static final int batch = 500;
    private static final int interval = 200;
    private static final int timeout = 30000;

    private String pluginId;
    private String output;

    private Connection connection;
    private PreparedStatement statement;
    private ResultSet resultSet;
    private String query;

    @Override
    public String pluginId() {
        return pluginId;
    }

    @Override
    public String identifier() {
        return "mysql";
    }

    @Override
    public String output() {
        return output;
    }

    @Override
    public void init(Config config) {
        pluginId = config.getString("id");
        output = config.getString("output");

        String jdbcUrl = config.getString("url");
        String username = config.getString("username");
        String password = config.getString("password");
        query = config.getString("query");

        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("socketTimeout", String.valueOf(timeout));

        try {
            connection = DriverManager.getConnection(jdbcUrl, props);
            log.info("mysql source initialized: {}", jdbcUrl);
        } catch (SQLException e) {
            throw new RuntimeException("failed to connect to mysql", e);
        }
    }

    @Override
    public List<Record> read() {
        try {
            if (resultSet != null) {
                return records();
            }

            statement = connection.prepareStatement(query);
            // 启用流式结果集模式
            statement.setFetchSize(Integer.MIN_VALUE);

            log.info("executing mysql query: {}, with timestamp: {}", query, System.currentTimeMillis());
            resultSet = statement.executeQuery();
            log.info("executing mysql query finished: {}, with timestamp: {}", query, System.currentTimeMillis());

            return records();
        } catch (SQLException e) {
            log.error("error reading from mysql: ", e);
            throw new RuntimeException("数据读取异常");
        }
    }

    private List<Record> records() throws SQLException {
        List<Record> records = new ArrayList<>();
        long start = System.currentTimeMillis();
        while (records.size() < batch && (System.currentTimeMillis() - start) <= interval && resultSet.next()) {
            Record record = new Record();
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                record.put(columnName, resultSet.getObject(i));
            }
            records.add(record);
        }
        return records;
    }

    @Override
    public void close() {
        try {
            log.info("mysql source close start...");
            if (resultSet != null) resultSet.close();
            if (statement != null) statement.close();
            if (connection != null) connection.close();
            log.info("mysql source closed.");
        } catch (SQLException e) {
            log.error("error closing mysql source: ", e);
        }
    }

    @Override
    public String backupMeta() {
        return null;
    }

    @Override
    public void restore(String meta) {

    }
}
