package cn.newrank.niop.bc.component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/3 14:37:55
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Data
public class CheckpointManager {

    private final CountDownLatch latch;
    private final LocalDateTime checkpointTime;
    private final String checkpointId;
    private final AtomicLong counter = new AtomicLong(0);

    private final Map<String, String> meta;

    private boolean success = true;

    public CheckpointManager(String checkpointId, LocalDateTime checkpointTime, int numComponents) {
        this.checkpointId = checkpointId;
        this.checkpointTime = checkpointTime;
        this.latch = new CountDownLatch(numComponents);
        this.meta = new HashMap<>(numComponents * 2);
        log.info("CheckpointManager: checkpointTime {} CheckpointMeta {} initialized. Waiting for {} components.", checkpointTime, checkpointId, numComponents);
    }

    public void completedAndBackup(String pluginId, String meta) {
        long count = counter.incrementAndGet();
        latch.countDown();

        if (meta != null) {
            this.meta.put(pluginId, meta);
        }

        log.info("CheckpointManager: checkpoint {}, plugin {} completed its part. Current count: {}", checkpointId, pluginId, count);
    }

    public void failed(String pluginId) {
        for (long i = 0; i < latch.getCount(); i++) {
            latch.countDown();
        }
        success = false;
        log.info("CheckpointManager: checkpoint {}, plugin {} failed.", checkpointId, pluginId);
    }

    public boolean awaitCompletion(long timeout, TimeUnit unit) {
        try {
            return latch.await(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    public String getMeta(String pluginId) {
        return meta.get(pluginId);
    }

}
