package cn.newrank.niop.bc.component.file;

import cn.newrank.niop.bc.api.Record;

import java.io.IOException;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/24 16:01:57
 * @version: 1.0.0
 * @description:
 */
public interface FileWriterStrategy {

    /**
     * 将数据写入文件
     *
     * @param record   数据
     * @param filePath 文件地址
     * @throws IOException 异常
     */
    void write(Record record, String filePath) throws IOException;

}
