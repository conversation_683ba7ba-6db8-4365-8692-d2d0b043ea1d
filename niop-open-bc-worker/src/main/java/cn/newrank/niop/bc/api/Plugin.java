package cn.newrank.niop.bc.api;

import cn.newrank.niop.bc.component.checkpoint.Checkpoint;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/23 16:22:56
 * @version: 1.0.0
 * @description:
 */
public interface Plugin extends Checkpoint {

    /**
     * 获取当前插件实例的id
     *
     * @return id
     */
    String pluginId();

    /**
     * 获取同一类型插件下的唯一标识符
     *
     * @return 唯一标识符
     */
    String identifier();

}
