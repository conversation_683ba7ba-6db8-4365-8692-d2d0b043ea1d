package cn.newrank.niop.bc.job;

import cn.hutool.core.lang.UUID;
import cn.newrank.niop.bc.ctx.JobManager;
import cn.newrank.niop.bc.util.HoconUtil;
import cn.newrank.niop.bc.web.mapper.DatasourceMapper;
import cn.newrank.niop.bc.web.mapper.JobMapper;
import cn.newrank.niop.bc.web.mapper.TaskMapper;
import cn.newrank.niop.bc.web.pojo.Datasource;
import cn.newrank.niop.bc.web.pojo.Job;
import cn.newrank.niop.bc.web.pojo.Task;
import cn.newrank.niop.bc.web.pojo.TaskState;
import cn.newrank.niop.data.biz.storage.pojo.dto.DatasourceConfig;
import cn.newrank.niop.data.biz.storage.service.IDatasourceDubboService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @author: xuguangjie
 * @date: 2025/7/29 11:30:51
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Component
public class JobExecutor implements ApplicationListener<ContextClosedEvent> {

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            5,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100));

    private static final String SYSTEM_HOCON_PARAMETER = """
                {
                    env {
                        cur_date = "%s"
                    }
                }
            """;

    private static final String MYSQL_HOCON = """
                    {
                        url = "%s"
                        username = "%s"
                        password = "%s"
                    }
            """;

    private static final String OSS_HOCON = """
                {
                    endpoint = "%s"
                    ak = "%s"
                    sk = "%s"
                }
            """;

    private static final AtomicBoolean closed = new AtomicBoolean(true);

    private List<JobOrchestrator> orchestrators = new ArrayList<>();

    @DubboReference(providedBy = "dubbo-niop-open-data")
    IDatasourceDubboService datasourceDubboService;

    @Autowired
    private DatasourceMapper dsMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private JobMapper jobMapper;

    public boolean execute(String jobId, JSONObject params) {
        if (!closed.get()) {
            return false;
        }

        String taskId = UUID.fastUUID().toString();

        Task task = new Task();
        task.setTaskId(taskId);
        task.setJobId(jobId);
        task.setParams(JSON.toJSONString(params));
        task.setState(TaskState.PENDING);
        taskMapper.save(task);

        EXECUTOR.submit(() -> run(jobId, taskId, params));

        return true;
    }

    private void run(String jobId, String taskId, JSONObject params) {
        Config ctxConfig = ConfigFactory.parseString(String.format(SYSTEM_HOCON_PARAMETER, LocalDate.now().toString().replace("-", "")));
        if (params != null && !params.isEmpty()) {
            JSONObject object = new JSONObject();
            object.put("env", params);
            Config customConfig = ConfigFactory.parseString(object.toJSONString());
            ctxConfig = customConfig.withFallback(ctxConfig);
        }

        // 更新任务状态
        Task task = new Task();
        task.setTaskId(taskId);
        task.setState(TaskState.RUNNING);
        task.setStartTime(LocalDateTime.now());
        taskMapper.update(task);

        // 加载hocon配置
        Job job = jobMapper.findJobById(jobId);

        // 预处理config中的占位符
        String hocon = HoconUtil.processJsonWithPlaceholders(job.getConfig());

        Config baseConfig = ConfigFactory.parseString(hocon);

        Config config = null;
        try {
            // config前置处理, 包括添加系统参数, 数据源转换等..
            config = baseConfig.withFallback(ctxConfig);

            // 检查业务配置是否正确
            checkConfig(config);

            // 铺平数据源信息
            config = parseDatasource(config.resolve());

            log.info("hocon info : {}", config.root().render());

        } catch (Exception e) {
            task.setState(TaskState.FAILED);
            task.setReason(e.getMessage());
            taskMapper.update(task);
            return;
        }

        // 执行任务
        JobOrchestrator jobOrchestrator = new JobOrchestrator(jobId, taskId, config);
        orchestrators.add(jobOrchestrator);

        JobManager jobManager = jobOrchestrator.execute();
        orchestrators.remove(jobOrchestrator);

        // 记录任务执行结果
        task.setState(jobManager.getState());
        task.setReason(jobManager.getReason());
        task.setEndTime(LocalDateTime.now());
        taskMapper.update(task);
    }

    private Config parseDatasource(Config config) {
        Config source = config.getConfig("source");
        for (Map.Entry<String, ConfigValue> entry : source.root().entrySet()) {
            String name = entry.getKey();
            String dsId = source.getString(name + ".dsId");
            JSONObject object = getDsConfig(dsId);

            if (name.equals("mysql")) {
                String address = object.getString("address");
                String username = object.getString("username");
                String database = object.getString("database");
                String password = object.getString("password");

                Config cfg = ConfigFactory.parseString(String.format(MYSQL_HOCON, "jdbc:mysql://" + address + "/" + database, username, password));
                Config mysqlConfig = cfg.atPath("source.mysql");
                config = config.withFallback(mysqlConfig);
            }
        }

        Config sink = config.getConfig("sink");
        for (Map.Entry<String, ConfigValue> entry : sink.root().entrySet()) {
            String name = entry.getKey();
            String dsId = sink.getString(name + ".dsId");
            JSONObject object = getDsConfig(dsId);

            if (name.equals("oss")) {
                String endpoint = object.getString("endpoint");
                String ak = object.getString("ak");
                String sk = object.getString("sk");

                Config cfg = ConfigFactory.parseString(String.format(OSS_HOCON, endpoint, ak, sk));
                Config ossConfig = cfg.atPath("sink.oss");
                config = config.withFallback(ossConfig);
            }
        }
        return config;
    }

    private JSONObject getDsConfig(String dsId) {
        Datasource ds = dsMapper.getDs(dsId);
        if (ds != null) {
            return JSON.parseObject(ds.getDsMeta());
        }

        DatasourceConfig dsConfig = datasourceDubboService.getDatasourceConfig(dsId);
        if (dsConfig != null) {
            return JSON.parseObject(dsConfig.getConfig());
        }
        throw new RuntimeException("数据源异常, " + dsId);
    }

    private void checkConfig(Config config) {
        Config source = config.getConfig("source");
        if (source.root().size() > 1) {
            throw new RuntimeException("暂不支持同时存在多个source");
        }

        Config sink = config.getConfig("sink");
        if (sink.root().size() > 1) {
            throw new RuntimeException("暂不支持同时存在多个sink");
        }

        // TODO 校验 input output 是否正确
    }

    private void stop() {
        if (closed.compareAndSet(true, false)) {
            // 停止所有任务
            orchestrators.forEach(orchestrator -> {
                orchestrator.clear();
                JobManager jobManager = orchestrator.getJobManager();

                Task task = new Task();
                task.setState(TaskState.FAILED);
                task.setEndTime(LocalDateTime.now());
                task.setReason("服务重启");
                task.setTaskId(jobManager.getTaskId());
                taskMapper.update(task);
            });
        }
    }

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        log.info("关闭服务, 开始清理资源...");
        stop();
    }

}
