package cn.newrank.niop.bc.biz.subscribe.config;

import cn.newrank.niop.bc.biz.subscribe.log.LogReader;
import cn.newrank.niop.bc.biz.subscribe.log.LogWriter;
import cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeGroupMapper;
import cn.newrank.niop.bc.util.IpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 15:09:51
 * @version: 1.0.0
 * @description:
 */
@Configuration
public class BeanConfig {

    @Autowired
    private SubscribeGroupMapper subscribeGroupMapper;

    @Bean
    public LogWriter writer() {
        String ip = IpUtil.getIp();
        String groupName = subscribeGroupMapper.getSubscribeGroupName(ip);
        return new LogWriter("/home/<USER>/run/data/subscribe/" + groupName + "/", groupName);
    }

    @Bean
    public LogReader reader() {
        String ip = IpUtil.getIp();
        String groupName = subscribeGroupMapper.getSubscribeGroupName(ip);
        return new LogReader("/home/<USER>/run/data/subscribe/" + groupName + "/", groupName);
    }

}
