package cn.newrank.niop.bc.biz.subscribe.config;

import cn.newrank.niop.bc.biz.subscribe.log.LogReader;
import cn.newrank.niop.bc.biz.subscribe.log.LogWriter;
import cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeGroupMapper;
import cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeGroup;
import cn.newrank.niop.bc.biz.subscribe.query.LuceneSearcher;
import cn.newrank.niop.bc.util.IpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 15:09:51
 * @version: 1.0.0
 * @description:
 */
@Configuration
public class BeanConfig {

    private volatile String groupName;

    @Autowired
    private SubscribeGroupMapper subscribeGroupMapper;

    private String getGroupId() {
        if (groupName == null) {
            synchronized (this) {
                if (groupName == null) {
                    groupName = subscribeGroupMapper.getSubscribeGroupName(IpUtil.getIp()).getGroupName();
                    return groupName;
                }
            }
        }
        return groupName;
    }

    @Bean
    public LogWriter writer() {
        String groupId = getGroupId();
        return new LogWriter("/home/<USER>/run/data/subscribe/source/" + groupId + "/", groupId);
    }

    @Bean
    public LogReader reader() {
        String groupId = getGroupId();
        return new LogReader("/home/<USER>/run/data/subscribe/source/" + groupId + "/");
    }

    @Bean
    public LuceneSearcher searcher() {
        SubscribeGroup subscribeGroup = subscribeGroupMapper.getSubscribeGroupName(IpUtil.getIp());
        String groupId = subscribeGroup.getGroupId();
        String queryConfig = subscribeGroup.getQueryConfig();
        return new LuceneSearcher("/home/<USER>/run/data/subscribe/lucene/" + groupId + "/", queryConfig);
    }

}
