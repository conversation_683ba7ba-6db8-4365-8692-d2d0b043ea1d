package cn.newrank.niop.bc.web.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 20:49:53
 * @version: 1.0.0
 * @description:
 */
@Data
public class Task {

    private Long id;

    private String jobId;

    private String taskId;

    private String params;

    private TaskState state;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String reason;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
