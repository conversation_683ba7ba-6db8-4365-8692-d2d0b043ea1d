package cn.newrank.niop.bc.job;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.sink.Sink;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.ctx.JobManager;
import cn.newrank.niop.bc.factory.Factory;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigObject;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @author: xuguangjie
 * @date: 2025/7/31 14:29:17
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class JobOrchestrator {

    private  final ThreadPoolExecutor SOURCE_EXECUTOR = new ThreadPoolExecutor(
            20,
            50,
            5,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100));
    private  final ThreadPoolExecutor TRANSFORM_EXECUTOR = new ThreadPoolExecutor(
            30,
            50,
            5,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100));
    private  final ThreadPoolExecutor SINK_EXECUTOR = new ThreadPoolExecutor(
            20,
            50,
            5,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100));

    private Config config;

    private Source source;
    private List<Transform> transforms;
    private Sink sink;

    private JobManager jobManager;

    private List<Future<?>> futures;

    /**
     * 记录输入/输出集合对应的队列
     */
    private Map<String, BlockingQueue<Record>> queueMap;

    public JobOrchestrator(String jobId, String taskId, Config config) {
        this.config = config;
        this.transforms = new ArrayList<>();
        this.jobManager = new JobManager(jobId, taskId);
        this.queueMap = new HashMap<>();
        this.futures = new ArrayList<>();
    }

    public void clear() {
        // 关闭所有组件
        if (source != null) source.close();
        if (transforms != null) transforms.forEach(Transform::close);
        if (sink != null) sink.close();

        // 清空队列
        if (queueMap != null) {
            queueMap.forEach((k, v) -> {
                if (v != null) v.clear();
            });
            queueMap.clear();
        }

        if (transforms != null) transforms.clear();
    }

    public JobManager getJobManager() {
        return jobManager;
    }

    public JobManager execute() {
        try {
            // 初始化所有组件
            initComponents();

            // 编排组件执行流程
            createDataFlowGraph();

            // 执行组件
            futures.add(startSource());
            futures.addAll(startTransform());
            futures.add(startSink());

            // 等待组件执行完成
            for (Future<?> future : futures) {
                future.get();
            }
            return jobManager;
        } catch (Exception e) {
            log.error("任务处理异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
            jobManager.failure(e.getMessage());
            return jobManager;
        } finally {
            clear();
        }
    }

    /**
     * 取消当前任务的执行
     */
    public void cancel() {
        for (Future<?> future : futures) {
            future.cancel(true);
        }
        clear();
    }

    private void initComponents() {
        // 动态加载Source
        ConfigObject source = config.getObject("source");
        ConfigObject sink = config.getObject("sink");

        String sourceName = source.keySet().stream().findFirst().orElse("");
        this.source = Factory.newSource(sourceName);
        this.source.init(source.toConfig());
        log.info("source initialized: {}", sourceName);

        // 动态加载Transform (可选)
        if (config.hasPath("transform")) {
            ConfigObject transforms = config.getObject("transform");

            for (String transformName : transforms.keySet()) {
                Config config = transforms.toConfig();

                Transform transform = Factory.newTransform(transformName);
                transform.init(config);
                this.transforms.add(transform);
                log.info("transform initialized: {}", transformName);
            }
        }

        // 动态加载Sink
        String sinkName = sink.keySet().stream().findFirst().orElse("");
        this.sink = Factory.newSink(sinkName);
        this.sink.init(sink.toConfig());
        log.info("sink initialized: {}", sinkName);
    }

    private void createDataFlowGraph() {
        for (Transform transform : transforms) {
            String transformName = transform.identifier();
            String input = config.getString("transform." + transformName + ".input");
            String output = config.getString("transform." + transformName + ".output");

            queueMap.compute(input, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
            queueMap.compute(output, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
        }

        String sourceName = this.source.identifier();
        String output = config.getString("source." + sourceName + ".output");
        queueMap.compute(output, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);

        String sinkName = sink.identifier();
        String input = config.getString("sink." + sinkName + ".input");
        queueMap.compute(input, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
    }

    private Future<?> startSource() {
        return SOURCE_EXECUTOR.submit(() -> {
            String sourceName = this.source.identifier();
            String output = config.getString("source." + sourceName + ".output");

            BlockingQueue<Record> queue = queueMap.get(output);

            log.info("source({}) start...", sourceName);

            try {
                while (true) {
                    if (jobManager.shouldStop()) {
                        return;
                    }

                    List<Record> records = source.read();
                    if (records == null || records.isEmpty()) {
                        jobManager.noMore();
                        break;
                    }

                    for (Record record : records) {
                        queue.put(record);
                    }
                }
            } catch (Exception e) {
                jobManager.failure(e.getMessage());
                log.error("数据读取异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
            }
        });
    }

    private List<Future<?>> startTransform() {
        List<Future<?>> futures = new ArrayList<>(transforms.size());
        for (Transform transform : transforms) {
            Future<?> future = TRANSFORM_EXECUTOR.submit(() -> {
                String transformName = transform.identifier();
                String input = config.getString("transform." + transformName + ".input");
                String output = config.getString("transform." + transformName + ".output");

                BlockingQueue<Record> inputQueue = queueMap.get(input);
                BlockingQueue<Record> outputQueue = queueMap.get(output);

                if (inputQueue == null) {
                    jobManager.failure("输入源不存在: " + input);
                    return;
                }

                log.info("transform({}) start...", transformName);

                try {
                    while (true) {
                        Record record = inputQueue.poll(3, TimeUnit.SECONDS);
                        if (record == null) {
                            if (!jobManager.hasMore()) {
                                break;
                            }
                            if (jobManager.shouldStop()) {
                                return;
                            }
                            continue;
                        }

                        List<Record> records = transform.transform(record);

                        for (Record r : records) {
                            outputQueue.put(r);
                        }
                    }
                } catch (Exception e) {
                    jobManager.failure("数据转换异常, 转换器: " + transformName);
                    log.error("数据转换异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
                }
            });
            futures.add(future);
        }
        return futures;
    }

    private Future<?> startSink() {
        return SINK_EXECUTOR.submit(() -> {
            String sinkName = sink.identifier();
            String input = config.getString("sink." + sinkName + ".input");

            log.info("sink({}) start...", sinkName);

            try {
                BlockingQueue<Record> queue = queueMap.get(input);
                while (true) {
                    Record record = queue.poll(3, TimeUnit.SECONDS);
                    if (record == null) {
                        if (!jobManager.hasMore()) {
                            break;
                        }
                        if (jobManager.shouldStop()) {
                            return;
                        }
                        continue;
                    }
                    sink.write(record);
                }
                sink.flush();
            } catch (Exception e) {
                jobManager.failure("数据输出异常, 输出: " + sinkName + ", reason: " + e.getMessage());
                log.error("数据输出异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
            }
        });
    }

}
