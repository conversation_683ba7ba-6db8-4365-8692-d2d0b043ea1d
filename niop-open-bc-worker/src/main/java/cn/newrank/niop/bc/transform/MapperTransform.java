package cn.newrank.niop.bc.transform;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.api.transform.TransformPlugin;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: xuguangjie
 * @date: 2025/7/24 14:32:15
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(TransformPlugin.class)
public class MapperTransform implements Transform {

    private static final String MAPPER_INFO = "field_mapper";

    private String input;
    private String output;
    private String[] sources;
    private String[] targets;

    @Override
    public String identifier() {
        return "mapper";
    }

    @Override
    public String input() {
        return input;
    }

    @Override
    public String output() {
        return output;
    }

    @Override
    public void init(Config config) {
        input = config.getString("input");
        output = config.getString("output");

        Config conf = config.getConfig(MAPPER_INFO);

        int size = conf.entrySet().size();

        // 初始化 sources 和 targets
        sources = new String[size];
        targets = new String[size];

        AtomicInteger idx = new AtomicInteger(0);
        conf.root().forEach((key, value) -> {
            sources[idx.get()] = key;
            targets[idx.get()] = value.unwrapped().toString();
            idx.incrementAndGet();
        });
    }

    @Override
    public List<Record> transform(Record inputRecord) {
        Record outputRecord = new Record();

        for (int idx = 0; idx < sources.length; idx++) {
            Object val = inputRecord.get(sources[idx]);

            String newKey = targets[idx];
            outputRecord.put(newKey, val);
        }

        return Collections.singletonList(outputRecord);
    }

    @Override
    public void close() {

    }

}
