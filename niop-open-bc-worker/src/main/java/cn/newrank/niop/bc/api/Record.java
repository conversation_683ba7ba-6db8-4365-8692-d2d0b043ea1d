package cn.newrank.niop.bc.api;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xug<PERSON><PERSON><PERSON>
 * @date: 2025/7/23 16:13:02
 * @version: 1.0.0
 * @description:
 */
@Data
public class Record {

    /**
     * 标记后续没有新的数据
     */
    public static final Record OVER = new Record();

    private Map<String, Object> data;

    public Record() {
        this.data = new HashMap<>();
    }

    public Record(Map<String, Object> data) {
        this.data = new HashMap<>(data);
    }

    public Object get(String key) {
        return data.get(key);
    }

    public void put(String key, Object value) {
        data.put(key, value);
    }

}
