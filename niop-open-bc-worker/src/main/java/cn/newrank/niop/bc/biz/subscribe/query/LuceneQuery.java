package cn.newrank.niop.bc.biz.subscribe.query;

import cn.newrank.niop.bc.biz.subscribe.log.LogReader;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 15:34:05
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LuceneQuery {
    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final Directory directory;
    private final IndexWriter writer;
    private Map<String, LuceneType> typeMap;

    public LuceneQuery(String indexPath) {
        try {
            directory = FSDirectory.open(Path.of(indexPath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        Analyzer analyzer = new KeywordAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void init(String queryConfig) {
        // 1. 解析被查询字段的数据类型
        JSONObject object = JSON.parseObject(queryConfig);
        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String field = entry.getKey();
            String value = String.valueOf(entry.getValue());

            LuceneType type = LuceneType.valueOf(value.toUpperCase());
            if (typeMap == null) {
                typeMap = new HashMap<>();
            }
            typeMap.put(field, type);
        }
    }

    public void load(File file) {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                writeDoc(JSON.parseObject(line));
            }
        } catch (IOException e) {
            log.error("读取文件 {} 失败, error: {}", file.getName(), e.getMessage());
            throw new RuntimeException(e);
        }
    }


    private void writeDoc(JSONObject object) {
        Document document = new Document();

        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                LuceneType type = typeMap.get(key);
                if (type == null || type == LuceneType.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == LuceneType.INT) {
                    document.add(new IntPoint(key, value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value))));
                } else if (type == LuceneType.LONG) {
                    document.add(new LongPoint(key, value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value))));
                } else if (type == LuceneType.FLOAT) {
                    document.add(new FloatPoint(key, value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value))));
                } else if (type == LuceneType.DOUBLE) {
                    document.add(new DoublePoint(key, value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value))));
                } else if (type == LuceneType.DATE) {
                    document.add(new LongPoint(key, toTimestamp(value)));
                } else {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<String> query(String query) {
        return Collections.emptyList();
    }

    public void clear() {
        try {
            writer.deleteAll();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }
}
