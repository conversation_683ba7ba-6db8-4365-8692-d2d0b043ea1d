package cn.newrank.niop.bc;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/22 10:38
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableDubbo
public class WorkerApplication {

    public static void main(String[] args) {
        SpringApplication.run(WorkerApplication.class, args);
    }

}