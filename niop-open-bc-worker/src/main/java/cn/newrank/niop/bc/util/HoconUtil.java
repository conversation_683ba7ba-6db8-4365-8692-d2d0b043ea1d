package cn.newrank.niop.bc.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/8/7 16:44:36
 * @version: 1.0.0
 * @description:
 */
public abstract class HoconUtil {

    // 使用正则表达式匹配 "key": "value with ${placeholder}" 的模式
    private static final Pattern PATTERN = Pattern.compile("(\"[^\"]*\"\\s*:\\s*)(\"[^\"]*\\$\\{[^}]+\\}[^\"]*\")");
    // 查找所有的 ${...} 占位符
    private static final Pattern PLACEHOLDERS_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    /**
     * 处理占位符
     * 将占位符放在引号外, 如
     *  "select * from table_${partition}" -> "select * from table_"${partition}
     * @param json json
     * @return hocon
     */
    public static String processJsonWithPlaceholders(String json) {
        // 这里我们需要找到所有包含占位符的字符串值并处理它们
        Matcher matcher = PATTERN.matcher(json);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String key = matcher.group(1); // "key":
            String value = matcher.group(2); // "value with ${placeholder}"

            // 去掉值两边的引号
            String valueContent = value.substring(1, value.length() - 1);

            // 处理占位符
            String processedValue = processPlaceholders(valueContent);

            // 构建替换字符串，需要转义特殊字符
            String replacement = key + processedValue;
            // 转义 $ 和 \ 字符，避免被误认为是正则表达式的特殊语法
            replacement = replacement.replace("\\", "\\\\").replace("$", "\\$");

            // 替换整个匹配项
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 处理包含占位符的字符串，将占位符移到引号外以支持HOCON解析
     * @param string 原始字符串值（可能包含占位符）
     * @return 处理后的HOCON格式字符串
     */
    private static String processPlaceholders(String string) {
        Matcher matcher = PLACEHOLDERS_PATTERN.matcher(string);

        if (!matcher.find()) {
            // 没有占位符，直接返回带引号的字符串
            return "\"" + string.replace("\"", "\\\"") + "\"";
        }

        // 有占位符，需要拆分字符串
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        matcher.reset();

        while (matcher.find()) {
            // 添加占位符前的字符串部分（带引号）
            if (matcher.start() > lastEnd) {
                String beforePlaceholder = string.substring(lastEnd, matcher.start());
                if (!beforePlaceholder.isEmpty()) {
                    // 转义引号并添加到结果中
                    result.append("\"").append(beforePlaceholder.replace("\"", "\\\"")).append("\"");
                }
            }


            // 添加占位符（不带引号）
            result.append("${").append(matcher.group(1)).append("}");
            lastEnd = matcher.end();

        }

        // 添加最后剩余的字符串部分
        if (lastEnd < string.length()) {
            String afterLastPlaceholder = string.substring(lastEnd);
            if (!afterLastPlaceholder.isEmpty()) {
                result.append("\"").append(afterLastPlaceholder.replace("\"", "\\\"")).append("\"");
            }
        }

        return result.toString();
    }

}
