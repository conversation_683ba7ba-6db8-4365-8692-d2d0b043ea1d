package cn.newrank.niop.bc.biz.subscribe.query;

import cn.newrank.niop.bc.util.DateTimeFormatters;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.index.IndexableField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;


/**
 * @author: xuguangjie
 * @date: 2025/9/8 16:20:17
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LuceneSearcher {

    private final Directory directory;
    private final IndexWriter writer;
    private final Map<String, LuceneType> typeMap;

    public LuceneSearcher(String indexPath, String queryConfig) {
        try {
            directory = FSDirectory.open(Path.of(indexPath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 1. 初始化 directory 和 writer
        Analyzer analyzer = new KeywordAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 2. 解析需要被查询的字段的类型
        try {
            JSONObject object = JSON.parseObject(queryConfig);
            typeMap = new HashMap<>();

            for (Map.Entry<String, Object> entry : object.entrySet()) {
                String field = entry.getKey();
                String value = String.valueOf(entry.getValue());

                try {
                    LuceneType type = LuceneType.valueOf(value.toUpperCase());
                    typeMap.put(field, type);
                    log.debug("字段 {} 配置为类型 {}", field, type);
                } catch (IllegalArgumentException e) {
                    log.warn("未知的字段类型: {}, 字段: {}, 将使用 STRING 类型", value, field);
                    typeMap.put(field, LuceneType.STRING);
                }
            }

            log.info("初始化完成，配置了 {} 个字段类型", typeMap.size());
        } catch (Exception e) {
            log.error("初始化配置失败: {}", queryConfig, e);
            throw new RuntimeException("配置解析失败", e);
        }
    }

    public int load(File file) {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int batchSize = 1000;
            int count = 0;

            while ((line = reader.readLine()) != null) {
                writeDoc(JSON.parseObject(line));
                count++;

                // 批量提交
                if (count % batchSize == 0) {
                    writer.commit();
                }
            }

            // 最后提交剩余的文档
            writer.commit();
            return writer.getDocStats().numDocs;
        } catch (IOException e) {
            log.error("读取文件 {} 失败, error: {}", file.getName(), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void writeDoc(JSONObject object) {
        Document document = new Document();

        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                LuceneType type = typeMap.get(key);
                if (type == null || type == LuceneType.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == LuceneType.INT) {
                    Integer iv = value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value));
                    document.add(new IntPoint(key, iv));
                    document.add(new StoredField(key, iv)); // 为 IntPoint 增加存储字段
                } else if (type == LuceneType.LONG) {
                    Long lv = value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value));
                    document.add(new LongPoint(key, lv));
                    document.add(new StoredField(key, lv));
                } else if (type == LuceneType.FLOAT) {
                    Float fv = value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value));
                    document.add(new FloatPoint(key, fv));
                    document.add(new StoredField(key, fv));
                } else if (type == LuceneType.DOUBLE) {
                    Double dv = value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value));
                    document.add(new DoublePoint(key, dv));
                    document.add(new StoredField(key, dv));
                } else if (type == LuceneType.DATE) {
                    long timestamp = toTimestamp(value);
                    document.add(new LongPoint(key, timestamp));
                    document.add(new StoredField(key, String.valueOf(value)));
                } else {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<String> query(String queryString, int maxResults) {
        List<String> results = new ArrayList<>();

        try (DirectoryReader reader = DirectoryReader.open(directory)) {
            IndexSearcher searcher = new IndexSearcher(reader);

            QueryParser parser = new DateRangeQueryParser("none", new KeywordAnalyzer(), typeMap);
            parser.setAllowLeadingWildcard(true);
            Query query = parser.parse(queryString);

            TopDocs topDocs = searcher.search(query, maxResults);

            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = searcher.doc(scoreDoc.doc);
                results.add(documentToJson(doc));
            }

        } catch (IOException | ParseException e) {
            log.error("lucene查询失败, {} \n error: {}", queryString, e.getMessage());
            throw new RuntimeException(e);
        }

        return results;
    }

    private String documentToJson(Document doc) {
        Map<String, Object> resultObject = new HashMap<>();

        for (IndexableField field : doc) {
            if (field.fieldType().stored()) {
                String fieldName = field.name();
                String fieldValue = doc.get(fieldName);

                // 根据字段类型转换值
                LuceneType fieldType = typeMap.get(fieldName);
                if (fieldType != null) {
                    switch (fieldType) {
                        case INT:
                            resultObject.put(fieldName, Integer.parseInt(fieldValue));
                            break;
                        case LONG:
                            resultObject.put(fieldName, Long.parseLong(fieldValue));
                            break;
                        case FLOAT:
                            resultObject.put(fieldName, Float.parseFloat(fieldValue));
                            break;
                        case DOUBLE:
                            resultObject.put(fieldName, Double.parseDouble(fieldValue));
                            break;
                        case DATE:
                            // 可以选择返回时间戳或格式化的日期
                            resultObject.put(fieldName, fieldValue);
                            break;
                        default:
                            resultObject.put(fieldName, fieldValue);
                    }
                } else {
                    resultObject.put(fieldName, fieldValue);
                }
            }
        }

        return JSON.toJSONString(resultObject);
    }

    public void clear() {
        try {
            writer.deleteAll();
            writer.commit();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DateTimeFormatters.DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DateTimeFormatters.DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }

}
