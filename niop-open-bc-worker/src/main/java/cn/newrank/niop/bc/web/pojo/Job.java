package cn.newrank.niop.bc.web.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/7/31 10:03:11
 * @version: 1.0.0
 * @description:
 */
@Data
public class Job {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 工作id
     */
    private String jobId;
    /**
     * 工作名称
     */
    private String jobName;
    /**
     * hocon配置信息
     */
    private String config;
    /**
     * cron表达式
     */
    private String cron;
    /**
     * 上次执行时间
     */
    private LocalDateTime lastTime;
    /**
     * 下次执行时间
     */
    private LocalDateTime nextTime;
    /**
     * 状态, 1.启用, 0.禁用
     */
    private Integer status;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
