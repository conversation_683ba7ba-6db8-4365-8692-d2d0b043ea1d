package cn.newrank.niop.bc.biz.subscribe.consume;

import cn.newrank.niop.bc.biz.subscribe.log.LogWriter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 15:07:39
 * @version: 1.0.0
 * @description:
 */
@Component
public class Subscriber {

    @Autowired
    private LogWriter logWriter;

    private boolean subscribe(ConsumerRecords<String, String> consumerRecords) {
        if (!logWriter.canWrite()) {
            return false;
        }

        for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
            logWriter.write(consumerRecord.value());
        }
        return true;
    }

}
