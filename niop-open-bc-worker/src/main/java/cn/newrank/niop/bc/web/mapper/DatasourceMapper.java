package cn.newrank.niop.bc.web.mapper;

import cn.newrank.niop.bc.web.pojo.Datasource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/8/5 13:46:18
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface DatasourceMapper {

    /**
     * 查询指定数据源配置
     *
     * @param dsId 数据源id
     * @return 数据源配置信息
     */
    Datasource getDs(@Param("dsId") String dsId);

}
