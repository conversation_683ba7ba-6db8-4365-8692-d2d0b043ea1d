package cn.newrank.niop.bc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/8/11 17:50:45
 * @version: 1.0.0
 * @description:
 */
@Configuration
public class TpConfig {

    @Bean
    public ThreadPoolExecutor sourceTp() {
        return new ThreadPoolExecutor(
                20,
                50,
                5,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(100));
    }

    @Bean
    public ThreadPoolExecutor transformTp() {
        return new ThreadPoolExecutor(
                30,
                50,
                5,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(100));
    }

    @Bean
    public ThreadPoolExecutor sinkTp() {
        return new ThreadPoolExecutor(
                20,
                50,
                5,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(100));
    }

}
