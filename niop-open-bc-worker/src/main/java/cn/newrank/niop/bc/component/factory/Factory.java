package cn.newrank.niop.bc.component.factory;

import cn.newrank.niop.bc.api.sink.Sink;
import cn.newrank.niop.bc.api.sink.SinkPlugin;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.source.SourcePlugin;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.api.transform.TransformPlugin;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/7/23 16:38:31
 * @version: 1.0.0
 * @description:
 */
public class Factory {

    private static final Map<String, Class<?>> SOURCE_PLUGIN = new HashMap<>();
    private static final Map<String, Class<?>> TRANSFORM_PLUGIN = new HashMap<>();
    private static final Map<String, Class<?>> SINK_PLUGIN = new HashMap<>();

    static {
        ServiceLoader<SourcePlugin> sources = ServiceLoader.load(SourcePlugin.class);
        ServiceLoader<TransformPlugin> transforms = ServiceLoader.load(TransformPlugin.class);
        ServiceLoader<SinkPlugin> sinks = ServiceLoader.load(SinkPlugin.class);

        sources.forEach(obj -> SOURCE_PLUGIN.put(obj.identifier(), obj.getClass()));
        transforms.forEach(obj -> TRANSFORM_PLUGIN.put(obj.identifier(), obj.getClass()));
        sinks.forEach(obj -> SINK_PLUGIN.put(obj.identifier(), obj.getClass()));
    }

    public static Source newSource(String identifier) {
        Class<?> clazz = SOURCE_PLUGIN.get(identifier);
        if (clazz == null) {
            throw new RuntimeException("unsupported source plugin: " + identifier);
        }

        Source instance = null;
        try {
            instance = (Source) clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException("unsupported source plugin: " + identifier);
        }
        return instance;
    }

    public static Transform newTransform(String identifier) {
        Class<?> clazz = TRANSFORM_PLUGIN.get(identifier);
        if (clazz == null) {
            throw new RuntimeException("unsupported source plugin: " + identifier);
        }

        Transform instance = null;
        try {
            instance = (Transform) clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException("unsupported transform plugin: " + identifier);
        }
        return instance;
    }

    public static Sink newSink(String identifier) {
        Class<?> clazz = SINK_PLUGIN.get(identifier);
        if (clazz == null) {
            throw new RuntimeException("unsupported source plugin: " + identifier);
        }

        Sink instance = null;
        try {
            instance = (Sink) clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException("unsupported sink plugin: " + identifier);
        }
        return instance;
    }

}
