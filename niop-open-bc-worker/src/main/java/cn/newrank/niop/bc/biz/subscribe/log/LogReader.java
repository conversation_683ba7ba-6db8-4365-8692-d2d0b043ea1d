package cn.newrank.niop.bc.biz.subscribe.log;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.stream.Stream;

/**
 * @author: xuguang<PERSON>e
 * @date: 2025/9/8 15:40:40
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LogReader {

    private final String basePath;

    public LogReader(String basePath) {
        this.basePath = basePath;
    }

    public boolean hasFile() {
        File dir = new File(basePath);
        File[] files = dir.listFiles();
        if (files == null) {
            return false;
        }
        return Stream.of(files).map(File::getName).anyMatch(name -> !name.endsWith(".tmp"));
    }

    public File getFile() {
        File dir = new File(basePath);
        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }
        for (File file : files) {
            if (!file.getName().endsWith(".tmp")) {
                return file;
            }
        }
        return null;
    }

}
