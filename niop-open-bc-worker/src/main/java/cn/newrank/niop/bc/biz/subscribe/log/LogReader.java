package cn.newrank.niop.bc.biz.subscribe.log;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.stream.Stream;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 15:40:40
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LogReader {

    private final String basePath;

    private final String groupName;

    public LogReader(String basePath, String groupName) {
        this.basePath = basePath;
        this.groupName = groupName;
    }

    public boolean hasFile() {
        File dir = new File(basePath);
        File[] files = dir.listFiles();
        if (files == null) {
            return false;
        }
        return Stream.of(files).map(File::getName).anyMatch(name -> !name.endsWith(".tmp"));
    }

    public File getFile() {
        File dir = new File(basePath);
        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }
        for (File file : files) {
            if (!file.getName().endsWith(".tmp")) {
                return file;
            }
        }
        return null;
    }

//    public String read() throws IOException {
//        File dir = new File(basePath);
//        File[] files = dir.listFiles();
//        if (files == null) {
//            return "";
//        }
//
//        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
//            String line;
//            while ((line = reader.readLine()) != null) {
//                System.out.println("读取到的行内容: " + line);
//            }
//        } catch (IOException e) {
//            log.error("读取文件 {} 失败, error: {}", fileName, e.getMessage());
//            throw e;
//        }
//        return "";
//    }

}
