package cn.newrank.niop.bc.biz.subscribe.query;

import cn.newrank.niop.bc.util.DateTimeFormatters;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.DoublePoint;
import org.apache.lucene.document.FloatPoint;
import org.apache.lucene.document.IntPoint;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 20:36:39
 * @version: 1.0.0
 * @description: 自定义 QueryParser，用于处理日期范围查询
 */
public class DateRangeQueryParser extends QueryParser {

    private final Map<String, LuceneType> typeMap;

    public DateRangeQueryParser(String f, Analyzer a, Map<String, LuceneType> typeMap) {
        super(f, a);
        this.typeMap = typeMap;
    }

    @Override
    public Query parse(String query) throws ParseException {
        // 预处理查询字符串，为日期时间字段的值添加引号
        String processedQuery = preprocessDateTimeQuery(query);
        return super.parse(processedQuery);
    }

    @Override
    protected Query getRangeQuery(String field, String part1, String part2,
                                  boolean inclusive1, boolean inclusive2) throws ParseException {
        LuceneType fieldType = typeMap != null ? typeMap.get(field) : null;

        if (fieldType == null) {
            return super.getRangeQuery(field, part1, part2, inclusive1, inclusive2);
        }

        return switch (fieldType) {
            case INT -> createIntRangeQuery(field, part1, part2, inclusive1, inclusive2);
            case LONG -> createLongRangeQuery(field, part1, part2, inclusive1, inclusive2);
            case FLOAT -> createFloatRangeQuery(field, part1, part2, inclusive1, inclusive2);
            case DOUBLE -> createDoubleRangeQuery(field, part1, part2, inclusive1, inclusive2);
            case DATE -> createDateRangeQuery(field, part1, part2, inclusive1, inclusive2);
            default -> super.getRangeQuery(field, part1, part2, inclusive1, inclusive2);
        };
    }

    private String preprocessDateTimeQuery(String query) {
        if (typeMap == null) {
            return query;
        }

        String result = query;

        // 为每个日期类型字段处理查询字符串
        for (Map.Entry<String, LuceneType> entry : typeMap.entrySet()) {
            if (entry.getValue() == LuceneType.DATE) {
                String fieldName = entry.getKey();
                result = addQuotesToDateTimeValues(result, fieldName);
            }
        }

        return result;
    }

    private String addQuotesToDateTimeValues(String query, String fieldName) {
        // 使用更精确的正则表达式
        String dateTimePattern = "\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}";
        String datePattern = "\\d{4}-\\d{2}-\\d{2}(?!\\s+\\d{2}:\\d{2}:\\d{2})"; // 负向前瞻，确保不匹配日期时间

        String result = query;

        // 定义所有可能的边界符号组合
        String[][] boundaries = {
                {"\\[", "\\]"},  // 包含边界
                {"\\{", "\\}"},  // 不包含边界
                {"\\[", "\\}"},  // 左包含，右不包含
                {"\\{", "\\]"}   // 左不包含，右包含
        };

        for (String[] boundary : boundaries) {
            String leftBoundary = boundary[0];
            String rightBoundary = boundary[1];

            // 处理日期时间格式
            result = processDateTimePattern(result, fieldName, dateTimePattern, leftBoundary, rightBoundary);

            // 处理日期格式
            result = processDateTimePattern(result, fieldName, datePattern, leftBoundary, rightBoundary);
        }

        return result;
    }

    private String processDateTimePattern(String query, String fieldName, String pattern,
                                          String leftBoundary, String rightBoundary) {
        String result = query;

        // [* TO datetime] 或 {* TO datetime} 等格式
        String pattern1 = "(" + fieldName + ":" + leftBoundary + "\\* TO )(" + pattern + ")(" + rightBoundary + ")";
        result = result.replaceAll(pattern1, "$1\"$2\"$3");

        // [datetime TO *] 或 {datetime TO *} 等格式
        String pattern2 = "(" + fieldName + ":" + leftBoundary + ")(" + pattern + ")( TO \\*" + rightBoundary + ")";
        result = result.replaceAll(pattern2, "$1\"$2\"$3");

        // [datetime TO datetime] 或 {datetime TO datetime} 等格式
        String pattern3 = "(" + fieldName + ":" + leftBoundary + ")(" + pattern + ")( TO )(" + pattern + ")(" + rightBoundary + ")";
        result = result.replaceAll(pattern3, "$1\"$2\"$3\"$4\"$5");

        return result;
    }

    private Query createIntRangeQuery(String field, String part1, String part2,
                                      boolean inclusive1, boolean inclusive2) throws ParseException {
        try {
            int min, max;

            if ("*".equals(part1) || part1 == null) {
                min = Integer.MIN_VALUE;
            } else {
                int value = Integer.parseInt(part1);
                // 如果左边界不包含，需要加1
                min = inclusive1 ? value : value + 1;
            }

            if ("*".equals(part2) || part2 == null) {
                max = Integer.MAX_VALUE;
            } else {
                int value = Integer.parseInt(part2);
                // 如果右边界不包含，需要减1
                max = inclusive2 ? value : value - 1;
            }

            return IntPoint.newRangeQuery(field, min, max);
        } catch (NumberFormatException e) {
            throw new ParseException("Invalid integer range: " + part1 + " TO " + part2);
        }
    }

    private Query createLongRangeQuery(String field, String part1, String part2,
                                       boolean inclusive1, boolean inclusive2) throws ParseException {
        try {
            long min, max;

            if ("*".equals(part1) || part1 == null) {
                min = Long.MIN_VALUE;
            } else {
                long value = Long.parseLong(part1);
                // 如果左边界不包含，需要加1
                min = inclusive1 ? value : value + 1;
            }

            if ("*".equals(part2) || part2 == null) {
                max = Long.MAX_VALUE;
            } else {
                long value = Long.parseLong(part2);
                // 如果右边界不包含，需要减1
                max = inclusive2 ? value : value - 1;
            }

            return LongPoint.newRangeQuery(field, min, max);
        } catch (NumberFormatException e) {
            throw new ParseException("Invalid long range: " + part1 + " TO " + part2);
        }
    }

    private Query createFloatRangeQuery(String field, String part1, String part2,
                                        boolean inclusive1, boolean inclusive2) throws ParseException {
        try {
            float min, max;

            if ("*".equals(part1) || part1 == null) {
                min = Float.NEGATIVE_INFINITY;
            } else {
                float value = Float.parseFloat(part1);
                // 对于浮点数，如果不包含边界，使用 Math.nextUp/Math.nextDown
                min = inclusive1 ? value : Math.nextUp(value);
            }

            if ("*".equals(part2) || part2 == null) {
                max = Float.POSITIVE_INFINITY;
            } else {
                float value = Float.parseFloat(part2);
                // 如果右边界不包含，使用前一个可表示的浮点数
                max = inclusive2 ? value : Math.nextDown(value);
            }

            return FloatPoint.newRangeQuery(field, min, max);
        } catch (NumberFormatException e) {
            throw new ParseException("Invalid float range: " + part1 + " TO " + part2);
        }
    }

    private Query createDoubleRangeQuery(String field, String part1, String part2,
                                         boolean inclusive1, boolean inclusive2) throws ParseException {
        try {
            double min, max;

            if ("*".equals(part1) || part1 == null) {
                min = Double.NEGATIVE_INFINITY;
            } else {
                double value = Double.parseDouble(part1);
                // 对于双精度浮点数，如果不包含边界，使用 Math.nextUp/Math.nextDown
                min = inclusive1 ? value : Math.nextUp(value);
            }

            if ("*".equals(part2) || part2 == null) {
                max = Double.POSITIVE_INFINITY;
            } else {
                double value = Double.parseDouble(part2);
                // 如果右边界不包含，使用前一个可表示的浮点数
                max = inclusive2 ? value : Math.nextDown(value);
            }

            return DoublePoint.newRangeQuery(field, min, max);
        } catch (NumberFormatException e) {
            throw new ParseException("Invalid double range: " + part1 + " TO " + part2);
        }
    }

    private Query createDateRangeQuery(String field, String part1, String part2,
                                       boolean inclusive1, boolean inclusive2) throws ParseException {
        try {
            long min, max;

            if ("*".equals(part1) || part1 == null) {
                min = Long.MIN_VALUE;
            } else {
                long timestamp = parseTimestamp(part1);
                // 如果左边界不包含，需要加1（假设时间戳精度为秒）
                min = inclusive1 ? timestamp : timestamp + 1;
            }

            if ("*".equals(part2) || part2 == null) {
                max = Long.MAX_VALUE;
            } else {
                long timestamp = parseTimestamp(part2);
                // 如果右边界不包含，需要减1
                max = inclusive2 ? timestamp : timestamp - 1;
            }

            return LongPoint.newRangeQuery(field, min, max);
        } catch (Exception e) {
            throw new ParseException("Invalid date range: " + part1 + " TO " + part2 + ", error: " + e.getMessage());
        }
    }

    private long parseTimestamp(String value) {
        LocalDateTime datetime;
        try {
            // 先尝试解析完整的日期时间
            datetime = LocalDateTime.parse(value, DateTimeFormatters.DATE_TIME_FORMAT);
        } catch (Exception e) {
            try {
                // 再尝试解析日期
                datetime = LocalDate.parse(value, DateTimeFormatters.DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e2) {
                throw new RuntimeException("无法解析日期: " + value);
            }
        }
        return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
    }
}
