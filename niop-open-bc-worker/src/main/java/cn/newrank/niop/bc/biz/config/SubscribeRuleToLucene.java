package cn.newrank.niop.bc.biz.config;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

public class SubscribeRuleToLucene {

    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\订阅规则.xlsx";

        // 读取第一个sheet（索引0）
        List<SubscribeRule> rules = EasyExcel.read(filePath)
                .sheet(0)
                .head(SubscribeRule.class)
                .doReadSync();

        // 读取第二个sheet（索引1）
        List<SubscribeUID> uids = EasyExcel.read(filePath)
                .sheet(1)
                .head(SubscribeUID.class)
                .doReadSync();

        // 按订阅id分组uid
        Map<String, List<String>> subscribeIdToUids = uids.stream()
                .collect(Collectors.groupingBy(SubscribeUID::getSubscribeId,
                        Collectors.mapping(SubscribeUID::getUid, Collectors.toList())));

        // 为每个订阅规则生成Lucene查询
        for (SubscribeRule rule : rules) {
            String luceneQuery = generateLuceneQuery(rule, subscribeIdToUids.get(rule.getSubscribeId()));
            System.err.println("Subscribe ID: " + rule.getSubscribeId() + " Lucene Query: " + luceneQuery);
        }
    }

    private static String generateLuceneQuery(SubscribeRule rule, List<String> uidList) {
        // 1. 转换规则字符串为wildcard格式，并构建title和desc查询
        String convertedRule = convertRuleToWildcard(rule.getRule());
        String ruleQuery = "(title:(" + convertedRule + ") OR desc:(" + convertedRule + "))";

        // 2. 处理时间范围查询
        String startTimeISO = convertTimeToISO(rule.getStartTime());
        String endTimeISO = rule.getEndTime() == null || rule.getEndTime().isEmpty() ? "*" : convertTimeToISO(rule.getEndTime());
        String timeQuery = "time:[" + startTimeISO + " TO " + endTimeISO + "]";

        // 3. 处理uid查询条件
        String uidQuery = "";
        if (uidList != null && !uidList.isEmpty()) {
            uidQuery = "uid:(" + String.join(" OR ", uidList) + ")";
        }

        // 4. 组合所有查询条件
        String luceneQuery = ruleQuery + " AND " + timeQuery;
        if (!uidQuery.isEmpty()) {
            luceneQuery += " AND " + uidQuery;
        }

        return luceneQuery;
    }

    private static String convertRuleToWildcard(String ruleStr) {
        // 移除引号，因为wildcard匹配中引号不需要
        ruleStr = ruleStr.replace("\"", "");
        // 在运算符前后添加空格以便分割
        String normalized = ruleStr.replace("(", " ( ")
                .replace(")", " ) ")
                .replace("AND", " AND ")
                .replace("OR", " OR ")
                .replace("NOT", " NOT ");
        // 分割字符串为tokens
        String[] tokens = normalized.split("\\s+");
        StringBuilder sb = new StringBuilder();
        for (String token : tokens) {
            if (token.isEmpty()) continue;
            // 保留运算符不变
            if (token.equals("(") || token.equals(")") || token.equalsIgnoreCase("AND") || token.equalsIgnoreCase("OR") || token.equalsIgnoreCase("NOT")) {
                sb.append(token).append(" ");
            } else {
                // 对非运算符的术语添加wildcard
                sb.append("*").append(token).append("*").append(" ");
            }
        }
        return sb.toString().trim();
    }

    private static String convertTimeToISO(String timeStr) {
        // 输入时间格式为"2025/9/8 0:00"（UTC+8）
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy/M/d H:mm");
        inputFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        // 输出时间格式为ISO8601（UTC）
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        outputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date date = inputFormat.parse(timeStr);
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return timeStr; // 解析失败时返回原字符串
        }
    }

    // 第一个sheet的POJO类
    public static class SubscribeRule {
        @ExcelProperty("订阅id")
        private String subscribeId;
        @ExcelProperty("订阅名称")
        private String rule;
        @ExcelProperty("订阅规则")
        private String name;
        @ExcelProperty("平台（1：快手；2：抖音；3：公众号；4：视频号；5：微博；6：小红书；7：b站）")
        private String platforms;
        @ExcelProperty("订阅开始时间")
        private String startTime;
        @ExcelProperty("订阅结束时间")
        private String endTime;

        // getters and setters
        public String getSubscribeId() {
            return subscribeId;
        }

        public void setSubscribeId(String subscribeId) {
            this.subscribeId = subscribeId;
        }

        public String getRule() {
            return rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPlatforms() {
            return platforms;
        }

        public void setPlatforms(String platforms) {
            this.platforms = platforms;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
    }

    // 第二个sheet的POJO类（假设有uid列）
    public static class SubscribeUID {
        @ExcelProperty("订阅id")
        private String subscribeId;
        @ExcelProperty("uid") // 根据实际列名调整
        private String uid;

        // getters and setters
        public String getSubscribeId() {
            return subscribeId;
        }

        public void setSubscribeId(String subscribeId) {
            this.subscribeId = subscribeId;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }
    }
}

