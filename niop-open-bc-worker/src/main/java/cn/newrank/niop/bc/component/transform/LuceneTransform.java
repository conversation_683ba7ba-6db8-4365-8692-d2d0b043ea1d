package cn.newrank.niop.bc.component.transform;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.api.transform.TransformPlugin;
import com.alibaba.fastjson2.JSON;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.RAMDirectory;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: xuguangjie
 * @date: 2025/9/4 16:06:57
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(TransformPlugin.class)
public class LuceneTransform implements Transform {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private String pluginId;
    private String input;
    private String output;

    private Directory directory;
    private IndexWriter writer;
    private Query query;
    /**
     * 被查询的字段名
     */
    private Map<String, Type> queryFieldType;


    @Override
    public String pluginId() {
        return pluginId;
    }

    @Override
    public String identifier() {
        return "lucene";
    }

    @Override
    public String input() {
        return input;
    }

    @Override
    public String output() {
        return output;
    }

    @Override
    public void init(Config config) {
        pluginId = config.getString("id");
        input = config.getString("input");
        output = config.getString("output");

        directory = new RAMDirectory();
        Analyzer analyzer = new KeywordAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 1. 解析被查询字段的数据类型
        Config cfg = config.getConfig("query_field_type");
        for (Map.Entry<String, ConfigValue> entry : cfg.entrySet()) {
            String field = entry.getKey();
            Type type = Type.valueOf(cfg.getString(field).toUpperCase());
            if (queryFieldType == null) {
                queryFieldType = new HashMap<>();
            }
            queryFieldType.put(field, type);
        }

        // 2. 检查query语句语法是否正确
        String queryQl = config.getString("query");
        CustomQueryParser parser = new CustomQueryParser("default", analyzer, queryFieldType);
        parser.setAllowLeadingWildcard(true);
        try {
            query = parser.parse(queryQl);
            log.info("Parsed query: {}", query.toString());
        } catch (ParseException e) {
            throw new RuntimeException("query syntax error: " + e.getMessage());
        }
    }

    @Override
    public List<Record> transform(Record inputRecord) {
        Map<String, Object> data = inputRecord.getData();

        Document document = new Document();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                Type type = queryFieldType.get(key);
                if (type == null || type == Type.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == Type.INT) {
                    document.add(new IntPoint(key, value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value))));
                } else if (type == Type.LONG) {
                    document.add(new LongPoint(key, value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value))));
                } else if (type == Type.FLOAT) {
                    document.add(new FloatPoint(key, value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value))));
                } else if (type == Type.DOUBLE) {
                    document.add(new DoublePoint(key, value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value))));
                } else if (type == Type.DATE) {
                    document.add(new LongPoint(key, toTimestamp(value)));
                } else {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        List<Record> records;
        try {
            writer.commit();
            DirectoryReader reader = DirectoryReader.open(directory);
            IndexSearcher searcher = new IndexSearcher(reader);
            TopDocs topDocs = searcher.search(query, 1);

            records = topDocs.totalHits.value > 0 ? Collections.singletonList(inputRecord) : Collections.emptyList();
            writer.deleteAll();
        } catch (IOException e) {
            throw new RuntimeException("lucene query error");
        }

        return records;
    }

    private static long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }

    @Override
    public void close() {

    }

    @Override
    public String backupMeta() {
        return "";
    }

    @Override
    public void restore(String meta) {
    }

    private enum Type {
        STRING,
        INT,
        LONG,
        FLOAT,
        DOUBLE,
        DATE,
    }

    private static class CustomQueryParser extends QueryParser {
        private final Map<String, Type> fieldTypes;

        public CustomQueryParser(String defaultField, Analyzer analyzer, Map<String, Type> fieldTypes) {
            super(defaultField, analyzer);
            this.fieldTypes = fieldTypes;
        }

        @Override
        protected Query getRangeQuery(String field, String part1, String part2,
                                      boolean startInclusive, boolean endInclusive) throws ParseException {
            Type fieldType = fieldTypes.get(field);

            if (fieldType == Type.INT) {
                return createIntRangeQuery(field, part1, part2);
            } else if (fieldType == Type.LONG) {
                return createLongRangeQuery(field, part1, part2);
            } else if (fieldType == Type.FLOAT) {
                return createFloatRangeQuery(field, part1, part2);
            } else if (fieldType == Type.DOUBLE) {
                return createDoubleRangeQuery(field, part1, part2);
            } else if (fieldType == Type.DATE) {
                return createLongRangeQuery(field, part1, part2);
            }

            // 默认使用父类的文本范围查询
            return super.getRangeQuery(field, part1, part2, startInclusive, endInclusive);
        }

        private Query createIntRangeQuery(String field, String part1, String part2) {
            Integer min = "*".equals(part1) || part1 == null ? null : Integer.parseInt(part1);
            Integer max = "*".equals(part2) || part2 == null ? null : Integer.parseInt(part2);

            if (min == null && max == null) {
                // [* TO *] - 匹配所有有该字段的文档
                throw new RuntimeException("query syntax error, range query must have at least one bound");
            } else if (min == null) {
                // [* TO max] - 小于等于 max
                return IntPoint.newRangeQuery(field, Integer.MIN_VALUE, max);
            } else if (max == null) {
                // [min TO *] - 大于等于 min
                return IntPoint.newRangeQuery(field, min, Integer.MAX_VALUE);
            } else {
                // [min TO max] - 正常范围查询
                return IntPoint.newRangeQuery(field, min, max);
            }
        }

        private Query createLongRangeQuery(String field, String part1, String part2) {
            Long min = "*".equals(part1) || part1 == null ? null : Long.parseLong(part1);
            Long max = "*".equals(part2) || part2 == null ? null : Long.parseLong(part2);

            if (min == null && max == null) {
                throw new RuntimeException("query syntax error, range query must have at least one bound");
            } else if (min == null) {
                return LongPoint.newRangeQuery(field, Long.MIN_VALUE, max);
            } else if (max == null) {
                return LongPoint.newRangeQuery(field, min, Long.MAX_VALUE);
            } else {
                return LongPoint.newRangeQuery(field, min, max);
            }
        }

        private Query createFloatRangeQuery(String field, String part1, String part2) {
            Float min = "*".equals(part1) || part1 == null ? null : Float.parseFloat(part1);
            Float max = "*".equals(part2) || part2 == null ? null : Float.parseFloat(part2);

            if (min == null && max == null) {
                throw new RuntimeException("query syntax error, range query must have at least one bound");
            } else if (min == null) {
                return FloatPoint.newRangeQuery(field, Float.NEGATIVE_INFINITY, max);
            } else if (max == null) {
                return FloatPoint.newRangeQuery(field, min, Float.POSITIVE_INFINITY);
            } else {
                return FloatPoint.newRangeQuery(field, min, max);
            }
        }

        private Query createDoubleRangeQuery(String field, String part1, String part2) {
            Double min = "*".equals(part1) || part1 == null ? null : Double.parseDouble(part1);
            Double max = "*".equals(part2) || part2 == null ? null : Double.parseDouble(part2);

            if (min == null && max == null) {
                throw new RuntimeException("query syntax error, range query must have at least one bound");
            } else if (min == null) {
                return DoublePoint.newRangeQuery(field, Double.NEGATIVE_INFINITY, max);
            } else if (max == null) {
                return DoublePoint.newRangeQuery(field, min, Double.POSITIVE_INFINITY);
            } else {
                return DoublePoint.newRangeQuery(field, min, max);
            }
        }
    }

}
