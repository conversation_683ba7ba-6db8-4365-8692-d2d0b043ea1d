package cn.newrank.niop.bc.component.transform;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.api.transform.TransformPlugin;
import com.alibaba.fastjson2.JSON;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.RAMDirectory;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: xuguangjie
 * @date: 2025/9/4 16:06:57
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(TransformPlugin.class)
public class LuceneTransform implements Transform {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private String pluginId;
    private String input;
    private String output;

    private Directory directory;
    private Analyzer analyzer;
    private IndexWriterConfig iwConfig;
    private IndexWriter writer;
    private Query query;
    /**
     * 被查询的字段名
     */
    private Map<String, Type> queryFieldType;


    @Override
    public String pluginId() {
        return pluginId;
    }

    @Override
    public String identifier() {
        return "lucene";
    }

    @Override
    public String input() {
        return input;
    }

    @Override
    public String output() {
        return output;
    }

    @Override
    public void init(Config config) {
        directory = new RAMDirectory();
        analyzer = new KeywordAnalyzer();
        iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 1. 检查query语句语法是否正确
        String queryQl = config.getString("query");
        QueryParser parser = new QueryParser("default", analyzer);
        parser.setAllowLeadingWildcard(true);
        try {
            query = parser.parse(queryQl);
        } catch (ParseException e) {
            throw new RuntimeException("query syntax error");
        }

        // 2. 解析被查询字段的数据类型
        Config cfg = config.getConfig("query_field_type");
        for (Map.Entry<String, ConfigValue> entry : cfg.entrySet()) {
            String field = entry.getKey();
            Type type = Type.valueOf(cfg.getString(field).toUpperCase());
            if (queryFieldType == null) {
                queryFieldType = new HashMap<>();
            }
            queryFieldType.put(field, type);
        }
    }

    @Override
    public List<Record> transform(Record inputRecord) {
        Map<String, Object> data = inputRecord.getData();

        Document document = new Document();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                Type type = queryFieldType.get(key);
                if (type == null || type == Type.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == Type.INT) {
                    document.add(new IntPoint(key, value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value))));
                } else if (type == Type.LONG) {
                    document.add(new LongPoint(key, value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value))));
                } else if (type == Type.FLOAT) {
                    document.add(new FloatPoint(key, value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value))));
                } else if (type == Type.DOUBLE) {
                    document.add(new DoublePoint(key, value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value))));
                } else if (type == Type.DATE) {
                    document.add(new LongPoint(key, toTimestamp(value)));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        List<Record> records;
        try {
            writer.commit();
            DirectoryReader reader = DirectoryReader.open(directory);
            IndexSearcher searcher = new IndexSearcher(reader);
            TopDocs topDocs = searcher.search(query, 1);

            records = topDocs.totalHits.value > 0 ? Collections.singletonList(inputRecord) : Collections.emptyList();
            writer.deleteAll();
        } catch (IOException e) {
            throw new RuntimeException("lucene query error");
        }

        return records;
    }

    private long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }

    @Override
    public void close() {

    }

    @Override
    public String backupMeta() {
        return "";
    }

    @Override
    public void restore(String meta) {
    }

    @Data
    private static class QueryRule {
        private String field;
        private Type type;
    }

    private enum Type {
        STRING,
        INT,
        LONG,
        FLOAT,
        DOUBLE,
        DATE,
    }

    public static void main(String[] args) {
        String config = """
                {
                "query": "name:niop and age:[10 TO 20]",
                "query_field_type": {
                    "name": "string",
                    "age": "int"
                }
                }
                """;

        LuceneTransform transform = new LuceneTransform();
        transform.init(ConfigFactory.parseString(config));
        List<Record> records = transform.transform(new Record(Map.of("name", "niop", "age", 21)));
        System.err.println(JSON.toJSONString(records));
    }


}
