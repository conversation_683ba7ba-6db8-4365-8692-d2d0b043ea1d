package cn.newrank.niop.bc.api.source;


import cn.newrank.niop.bc.api.Record;
import com.typesafe.config.Config;

import java.util.List;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/7/18 16:29:15
 * @version: 1.0.0
 * @description:
 */
public interface Source extends SourcePlugin {

    /**
     * 初始化组件
     *
     * @param config hocon source配置信息
     */
    void init(Config config);

    /**
     * 从 source 中读取数据并返回
     *
     * @return 数据集
     */
    List<Record> read();

    /**
     * 释放 source 组件持有的所有资源
     */
    void close();

}
