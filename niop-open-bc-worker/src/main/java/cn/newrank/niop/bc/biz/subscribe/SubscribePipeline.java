package cn.newrank.niop.bc.biz.subscribe;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.bc.biz.subscribe.log.LogReader;
import cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeGroupMapper;
import cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeQueryMapper;
import cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeGroup;
import cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeQuery;
import cn.newrank.niop.bc.biz.subscribe.query.LuceneSearcher;
import cn.newrank.niop.bc.util.IpUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 19:09:45
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Component
public class SubscribePipeline implements CommandLineRunner {

    @Autowired
    private LogReader logReader;
    @Autowired
    private SubscribeGroupMapper subscribeGroupMapper;
    @Autowired
    private SubscribeQueryMapper subscribeQueryMapper;
    @Autowired
    private LuceneSearcher searcher;

    private static final BloomFilter<String> bloomFilter = BloomFilter.create(
            Funnels.stringFunnel(StandardCharsets.UTF_8),
            20000000,
            0.01);

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
            10,
            15,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(200),
            new ThreadPoolExecutor.CallerRunsPolicy());

    public void handle() {
        boolean hasFile = logReader.hasFile();
        if (!hasFile) {
            ThreadUtil.sleep(1000);
            return;
        }

        long start = System.currentTimeMillis();
        Stopwatch stopwatch = Stopwatch.createStarted();

        SubscribeGroup subscribeGroup = subscribeGroupMapper.getSubscribeGroupName(IpUtil.getIp());
        String groupId = subscribeGroup.getGroupId();
        String groupName = subscribeGroup.getGroupName();
        String sinkConfig = subscribeGroup.getSinkConfig();

        List<SubscribeQuery> queries = subscribeQueryMapper.listByGroupId(groupId);
        log.info("查询到 {} 条过滤规则, 耗时 {} ms", queries.size(), stopwatch.elapsed(TimeUnit.MILLISECONDS));

        stopwatch.stop().start();
        File file = logReader.getFile();
        int loadNum = searcher.load(file);
        log.info("加载 {} 条数据, 耗时 {} ms", loadNum, stopwatch.elapsed(TimeUnit.MILLISECONDS));

        stopwatch.stop().start();
        List<Future<List<String>>> futures = new ArrayList<>(queries.size());
        for (SubscribeQuery query : queries) {
            String queryString = query.getQuery();
            Future<List<String>> future = EXECUTOR.submit(() -> {
                try {
                    return searcher.query(queryString, 2000);
                } catch (Exception e) {
                    log.error("数据搜索异常, group {}; query {}, exception: ", groupName, queryString, e);
                    throw new RuntimeException(e);
                }
            });
            futures.add(future);
        }

        List<SubscribeResult> dataList = new ArrayList<>();
        for (Future<List<String>> future : futures) {
            try {
                List<String> strings = future.get();
                List<SubscribeResult> results = strings.stream()
                        .map(JSON::parseObject)
                        .filter(obj -> {
                            String opusId = obj.getString("opusId");
                            boolean mightContain = bloomFilter.mightContain(opusId);
                            bloomFilter.put(opusId);
                            return !mightContain;
                        })
                        .map(obj -> new SubscribeResult(obj.getString("opusId"), obj.toJSONString()))
                        .toList();
                dataList.addAll(results);
            } catch (InterruptedException | ExecutionException e) {
                // 发生异常
                throw new RuntimeException(e);
            }
        }
        log.info("查询到 {} 条数据, 耗时 {} ms", dataList.size(), stopwatch.elapsed(TimeUnit.MILLISECONDS));

        stopwatch.stop().start();
        // 投递到指定的 sinkConfig 中, 暂时保存到mysql中, 模拟投递
        if (CollUtil.isNotEmpty(dataList)) {
            subscribeGroupMapper.saveResults(groupName, dataList);
        }
        log.info("投递数据完成, 耗时 {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        // 处理完了, 删除掉文件数据
        file.delete();

        log.info("处理文件 {} 耗时 {} ms, 共 {} 条数据, 查询到 {} 条数据", file.getName(), System.currentTimeMillis() - start, loadNum, dataList.size());
    }

    @Override
    public void run(String... args) throws Exception {
        new Thread(() -> {
            while (true) {
                try {
                    handle();
                } catch (Exception e) {
                    log.error("订阅数据处理异常, e: ", e);
                } finally {
                    searcher.clear();
                }
            }
        }).start();
    }


    @Data
    @AllArgsConstructor
    public static class SubscribeResult {
        private String uniqueId;
        private String message;
    }

}
