package cn.newrank.niop.bc.component.file;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xug<PERSON><PERSON><PERSON>
 * @date: 2025/7/28 10:26:26
 * @version: 1.0.0
 * @description:
 */
public class FileWriterFactory {

    private static final Map<FileSupport, FileWriterStrategy> STRATEGY_MAP = new HashMap<>();

    static {
        STRATEGY_MAP.put(FileSupport.CSV, new CsvFileWriterStrategy());
    }

    public static FileWriterStrategy getStrategy(FileSupport support) {
        return STRATEGY_MAP.get(support);
    }

}
