package cn.newrank.niop.bc.biz.subscribe.log;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.stream.Stream;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 14:14:49
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LogWriter {

    /**
     * 区分写完的文件和正在写的文件
     * 写完的文件: /groupName/groupName_time.log
     * 正在写的文件: /groupName/groupName_time.log.tmp
     */

    private final String basePath;

    private final String groupName;

    private int count;

    private final long interval;

    public LogWriter(String basePath, String groupName) {
        this.basePath = basePath;
        this.groupName = groupName;
        this.count = 0;
        this.interval = 10 * 1000L;
    }

    public String getFileName() {
        File dir = new File(basePath);
        File[] files = dir.listFiles();
        if (files == null) {
            return basePath + groupName + "_" + System.currentTimeMillis() + ".log.tmp";
        }
        return Stream.of(files)
                .map(File::getName)
                .filter(name -> name.endsWith(".tmp"))
                .map(name -> basePath + "/" + name)
                .findAny()
                .orElse(basePath + groupName + "_" + System.currentTimeMillis() + ".log.tmp");
    }

    public void write(String logs) {
        String fileName = getFileName();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName, count != 0))) {
            writer.write(logs);
        } catch (IOException e) {
            log.error("写入数据失败, error: {}", e.getMessage());
            throw new RuntimeException(e);
        }

        int start = fileName.lastIndexOf("_");
        int end = fileName.indexOf(".log.tmp");

        if (++count >= 2000 || (System.currentTimeMillis() - Long.parseLong(fileName.substring(start, end)) > interval)) {
            try {
                Files.move(Path.of(fileName), Path.of(fileName.replace(".tmp", "")), StandardCopyOption.ATOMIC_MOVE);
            } catch (IOException e) {
                log.error("重命名文件失败, error: {}", e.getMessage());
                throw new RuntimeException(e);
            }
            count = 0;
        }
    }

    public boolean canWrite() {
        File dir = new File(basePath + "/" + groupName);
        File[] files = dir.listFiles();
        return files == null || files.length < 3;
    }

}
