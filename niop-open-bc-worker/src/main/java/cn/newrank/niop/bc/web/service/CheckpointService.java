package cn.newrank.niop.bc.web.service;

import cn.newrank.niop.bc.component.CheckpointManager;
import cn.newrank.niop.bc.web.mapper.CheckpointMetaMapper;
import cn.newrank.niop.bc.web.pojo.CheckpointMeta;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/9/3 14:36:16
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Service
public class CheckpointService {

    @Autowired
    private CheckpointMetaMapper checkpointMetaMapper;

    public void save(String pluginId, CheckpointManager checkpointManager) {
        String checkpointId = checkpointManager.getCheckpointId();
        LocalDateTime checkpointTime = checkpointManager.getCheckpointTime();
        String meta = checkpointManager.getMeta(pluginId);

        CheckpointMeta checkpointMeta = new CheckpointMeta();
        checkpointMeta.setCheckpointId(checkpointId);
        checkpointMeta.setPluginId(pluginId);
        checkpointMeta.setCheckpointTime(checkpointTime);
        checkpointMeta.setMeta(meta);
        checkpointMetaMapper.save(checkpointMeta);
        log.info("CheckpointService: Saved meta for {} at checkpoint {}", pluginId, checkpointId);
    }

    public String load(String checkpointId, String pluginId) {
        CheckpointMeta checkpointMeta = checkpointMetaMapper.getCheckpointMeta(checkpointId, pluginId);
        return checkpointMeta.getMeta();
    }
}
