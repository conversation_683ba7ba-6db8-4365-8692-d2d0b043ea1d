package cn.newrank.niop.bc.util;

import java.time.format.DateTimeFormatter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 20:38:19
 * @version: 1.0.0
 * @description:
 */
public abstract class DateTimeFormatters {
    public static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
}
