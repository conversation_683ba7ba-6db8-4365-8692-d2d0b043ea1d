package cn.newrank.niop.bc.web.mapper;

import cn.newrank.niop.bc.web.pojo.Job;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/31 10:02:35
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface JobMapper {

    /**
     * 查询指定的job
     *
     * @param jobId jobId
     * @return job
     */
    Job findJobById(@Param("jobId") String jobId);

}
