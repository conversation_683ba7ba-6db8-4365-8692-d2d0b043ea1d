package cn.newrank.niop.bc.web.mapper;

import cn.newrank.niop.bc.web.pojo.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/30 20:48:43
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface TaskMapper {

    /**
     * 保存任务
     *
     * @param task 任务信息
     */
    void save(@Param("task") Task task);

    /**
     * 更新任务信息
     * @param task  任务信息
     */
    void update(@Param("task") Task task);

}
