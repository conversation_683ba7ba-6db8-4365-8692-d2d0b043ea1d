package cn.newrank.niop.bc.biz.subscribe.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 14:05:11
 * @version: 1.0.0
 * @description:
 */
@Data
public class SubscribeQuery {

    private Long id;
    private String query;
    private String groupId;
    private String effective;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

}
