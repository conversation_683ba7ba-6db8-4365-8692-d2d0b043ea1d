package cn.newrank.niop.bc.component.connector.source.kafka;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.source.SourcePlugin;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.SaslConfigs;
import org.assertj.core.util.Lists;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @author: xuguangjie
 * @date: 2025/9/1 14:20:00
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(SourcePlugin.class)
public class KafkaSource implements Source {

    private KafkaConsumer<String, String> kafkaConsumer;

    private String pluginId;
    private String output;

    /**
     * 记录消费位点
     */
    private Map<TopicPartition, Long> offsets;

    @Override
    public String pluginId() {
        return pluginId;
    }

    @Override
    public String identifier() {
        return "kafka";
    }

    @Override
    public String output() {
        return output;
    }

    @Override
    public void init(Config config) {
        pluginId = config.getString("plugin_id");
        output = config.getString("output");
        offsets = new HashMap<>();

        // 1. servers
        String servers = config.getString("servers");
        // 2. is plaintext
        boolean isPlaintext = config.getBoolean("isPlaintext");
        // 3. username && password
        String username = config.getString("username");
        String password = config.getString("password");
        // 4. group
        String group = config.getString("group");


        Properties props = new Properties();

        // 配置kafka连接
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        if (isPlaintext) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "PLAINTEXT");
        } else {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");

            final String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";",
                    "org.apache.kafka.common.security.plain.PlainLoginModule",
                    username, password);
            props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        }

        // 拉取数据的相关配置
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 1024);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 3000);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 30);

        // 消息的反序列化方式
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);

        // 指定消费者组
        props.put(ConsumerConfig.GROUP_ID_CONFIG, group);

        this.kafkaConsumer = new KafkaConsumer<>(props);
    }

    @Override
    public List<Record> read() {
        List<Record> records = Lists.newArrayList();

        while (true) {
            ConsumerRecords<String, String> consumerRecords = kafkaConsumer.poll(Duration.of(5, ChronoUnit.SECONDS));
            if (consumerRecords.isEmpty()) {
                continue;
            }

            for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
                String topic = consumerRecord.topic();
                int partition = consumerRecord.partition();
                long offset = consumerRecord.offset();
                String value = consumerRecord.value();

                JSONObject object = JSON.parseObject(value);
                records.add(new Record(object));

                offsets.put(new TopicPartition(topic, partition), offset);
            }
            break;
        }

        return records;
    }

    @Override
    public void close() {
        kafkaConsumer.close();
    }

    @Override
    public String backupMeta() {
        List<KafkaCheckpointMeta> metas = new ArrayList<>();
        offsets.forEach((topicPartition, offset) -> {
            KafkaCheckpointMeta meta = new KafkaCheckpointMeta();
            meta.setTopic(topicPartition.topic());
            meta.setPartition(topicPartition.partition());
            meta.setOffset(offset);
            metas.add(meta);
        });
        return JSON.toJSONString(metas);
    }

    @Override
    public void restore(String meta) {
        List<KafkaCheckpointMeta> kafkaCheckpointMetas = JSON.parseArray(meta, KafkaCheckpointMeta.class);
        kafkaCheckpointMetas.forEach(item -> {
            kafkaConsumer.seek(new TopicPartition(item.getTopic(), item.getPartition()), item.getOffset());
        });
    }

    @Data
    private static class KafkaCheckpointMeta {
        private String topic;
        private int partition;
        private long offset;
    }

}
