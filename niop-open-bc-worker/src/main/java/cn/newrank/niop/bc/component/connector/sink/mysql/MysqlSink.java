package cn.newrank.niop.bc.component.connector.sink.mysql;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.sink.Sink;
import cn.newrank.niop.bc.api.sink.SinkPlugin;
import com.alibaba.fastjson.JSON;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Properties;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/9/5 14:11:08
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(SinkPlugin.class)
public class MysqlSink implements Sink {

    private static final int timeout = 30000;

    private String pluginId;
    private String input;

    private Connection connection;
    private String query;

    @Override
    public String identifier() {
        return "mysql";
    }

    @Override
    public String pluginId() {
        return pluginId;
    }

    @Override
    public String input() {
        return input;
    }


    @Override
    public void init(Config config) {
        pluginId = config.getString("plugin_id");
        input = config.getString("input");

        String jdbcUrl = config.getString("url");
        String username = config.getString("username");
        String password = config.getString("password");
        query = config.getString("query");

        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("socketTimeout", String.valueOf(timeout));

        try {
            connection = DriverManager.getConnection(jdbcUrl, props);
            log.info("mysql source initialized: {}", jdbcUrl);
        } catch (SQLException e) {
            throw new RuntimeException("failed to connect to mysql", e);
        }

    }

    @Override
    public void write(Record record) {
        try {
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, JSON.toJSONString(record.getData()));
            statement.executeUpdate();
            statement.close();
        } catch (Exception e) {
            throw new RuntimeException("mysql data write error, " + e.getMessage());
        }
    }

    @Override
    public void flush() throws IOException {

    }

    @Override
    public void close() {
        try {
            connection.close();
        } catch (Exception e) {
            log.error("mysql connection close error, {}", e.getMessage());
        }
    }

    @Override
    public String backupMeta() {
        return "";
    }

    @Override
    public void restore(String meta) {

    }

}
