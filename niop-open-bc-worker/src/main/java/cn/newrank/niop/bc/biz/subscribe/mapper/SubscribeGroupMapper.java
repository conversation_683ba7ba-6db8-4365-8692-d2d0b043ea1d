package cn.newrank.niop.bc.biz.subscribe.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 14:10:23
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface SubscribeGroupMapper {

    /**
     * 查询指定ip下的订阅者组名称
     *
     * @param ip ip
     * @return 订阅者组名称
     */
    String getSubscribeGroupName(@Param("ip") String ip);

}
