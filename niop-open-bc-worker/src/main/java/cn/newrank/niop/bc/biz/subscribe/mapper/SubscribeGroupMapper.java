package cn.newrank.niop.bc.biz.subscribe.mapper;

import cn.newrank.niop.bc.biz.subscribe.SubscribePipeline;
import cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: xuguang<PERSON>e
 * @date: 2025/9/8 14:10:23
 * @version: 1.0.0
 * @description:
 */
@Mapper
public interface SubscribeGroupMapper {

    /**
     * 查询指定ip下的订阅者组名称
     *
     * @param ip ip
     * @return 订阅者组名称
     */
    SubscribeGroup getSubscribeGroupName(@Param("ip") String ip);

    /**
     * 保存结果
     *
     * @param groupName 组别
     * @param dataList  数据
     */
    void saveResults(@Param("groupName") String groupName, @Param("dataList") List<SubscribePipeline.SubscribeResult> dataList);

}
