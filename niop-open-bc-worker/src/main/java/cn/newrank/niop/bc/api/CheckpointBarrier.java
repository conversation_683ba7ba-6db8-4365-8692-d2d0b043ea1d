package cn.newrank.niop.bc.api;

import cn.newrank.niop.bc.component.CheckpointManager;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: xuguangjie
 * @date: 2025/9/3 14:31:53
 * @version: 1.0.0
 * @description: 检查点屏障, 用来隔绝数据
 */
@Data
public class CheckpointBarrier extends Record {

    private CheckpointManager checkpointManager;

    private CheckpointBarrier(String checkpointId, int numComponents) {
        super(null);
        this.checkpointManager = new CheckpointManager(checkpointId, LocalDateTime.now(), numComponents);
    }

    public static CheckpointBarrier of(String checkpointId, int numComponents) {
        return new CheckpointBarrier(checkpointId, numComponents);
    }

}
