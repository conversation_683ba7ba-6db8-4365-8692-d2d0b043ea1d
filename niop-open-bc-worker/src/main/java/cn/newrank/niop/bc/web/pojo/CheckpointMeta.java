package cn.newrank.niop.bc.web.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: xug<PERSON><PERSON>e
 * @date: 2025/9/4 15:48:38
 * @version: 1.0.0
 * @description:
 */
@Data
public class CheckpointMeta {

    /**
     * id
     */
    private Long id;
    /**
     * 检查点id（jobId）
     */
    private String checkpointId;
    /**
     * 插件id
     */
    private String pluginId;
    /**
     * 检查点时间
     */
    private LocalDateTime checkpointTime;
    /**
     * 检查点元数据
     */
    private String meta;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
