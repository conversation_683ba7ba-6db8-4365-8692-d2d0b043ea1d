package cn.newrank.niop.bc.connector.sink.oss;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.sink.Sink;
import cn.newrank.niop.bc.api.sink.SinkPlugin;
import cn.newrank.niop.bc.file.FileSupport;
import cn.newrank.niop.bc.file.FileWriterFactory;
import cn.newrank.niop.bc.file.FileWriterStrategy;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xug<PERSON>jie
 * @date: 2025/7/24 14:30:33
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(SinkPlugin.class)
public class OssSink implements Sink {

    private static final String FILE_DIRECTORY = "/mnt/bc/s3/%s/";

    private String path;
    private String fileName;

    private FileSupport support;

    private String bucket;
    private OSS client;

    @Override
    public String identifier() {
        return "oss";
    }

    @Override
    public void init(Config config) {
        path = config.getString("oss.path");
        fileName = config.getString("oss.file_name");
        String fileType = config.getString("oss.file_type");
        support = FileSupport.valueOf(fileType.toUpperCase());

        String ak = config.getString("oss.ak");
        String sk = config.getString("oss.sk");
        String endpoint = config.getString("oss.endpoint");
        bucket = config.getString("oss.bucket");


        CredentialsProvider provider = new DefaultCredentialProvider(ak, sk);
        client = new OSSClient(endpoint, provider, null);
    }

    @Override
    public void write(Record record) {
        FileWriterStrategy strategy = FileWriterFactory.getStrategy(support);
        try {
            strategy.write(record, FILE_DIRECTORY + fileName);
        } catch (IOException e) {
            log.error("写文件异常, e: ", e);
        }
    }

    @Override
    public void flush() throws IOException {
        String key = path + fileName;

        File file = new File(FILE_DIRECTORY + fileName);

        InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucket, key);
        InitiateMultipartUploadResult result = client.initiateMultipartUpload(request);
        String uploadId = result.getUploadId();
        List<PartETag> partETags = new ArrayList<>();
        long partSize = 104857600L;
        long fileLength = file.length();
        int partCount = (int) (fileLength / partSize);
        if (fileLength % partSize != 0L) {
            ++partCount;
        }

        for (int i = 0; i < partCount; ++i) {
            long startPos = (long) i * partSize;
            long curPartSize = i + 1 == partCount ? fileLength - startPos : partSize;
            InputStream instream = new FileInputStream(file);
            instream.skip(startPos);
            UploadPartRequest uploadPartRequest = new UploadPartRequest();
            uploadPartRequest.setBucketName(bucket);
            uploadPartRequest.setKey(key);
            uploadPartRequest.setUploadId(uploadId);
            uploadPartRequest.setInputStream(instream);
            uploadPartRequest.setPartSize(curPartSize);
            uploadPartRequest.setPartNumber(i + 1);
            UploadPartResult uploadPartResult = client.uploadPart(uploadPartRequest);
            partETags.add(uploadPartResult.getPartETag());
        }

        CompleteMultipartUploadRequest completeMultipartUploadRequest = new CompleteMultipartUploadRequest(bucket, key, uploadId, partETags);
        client.completeMultipartUpload(completeMultipartUploadRequest);
    }

    @Override
    public void close() {
        if (client != null) client.shutdown();
        File file = new File(FILE_DIRECTORY + fileName);
        if (file.exists()) file.delete();
    }

}
