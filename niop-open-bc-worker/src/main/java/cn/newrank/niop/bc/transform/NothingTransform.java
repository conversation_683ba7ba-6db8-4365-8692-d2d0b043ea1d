package cn.newrank.niop.bc.transform;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.transform.Transform;
import com.typesafe.config.Config;

import java.util.Collections;
import java.util.List;

/**
 * @author: x<PERSON><PERSON><PERSON>e
 * @date: 2025/7/24 15:57:26
 * @version: 1.0.0
 * @description:
 */
public class NothingTransform implements Transform {

    public static final NothingTransform INSTANCE = new NothingTransform();

    private  NothingTransform() {}

    @Override
    public String identifier() {
        return "";
    }

    @Override
    public void init(Config config) {

    }

    @Override
    public List<Record> transform(Record inputRecord) {
        return Collections.singletonList(inputRecord);
    }

    @Override
    public void close() {

    }

}
