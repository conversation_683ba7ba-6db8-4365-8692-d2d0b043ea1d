package cn.newrank.niop.bc.biz.subscribe.query;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.index.IndexableField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * @author: xuguangjie
 * @date: 2025/9/8 16:20:17
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LuceneQueryV2 {
    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final Directory directory;
    private final IndexWriter writer;
    private Map<String, LuceneType> typeMap;

    public LuceneQueryV2(String indexPath) {
        try {
            directory = FSDirectory.open(Path.of(indexPath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        Analyzer analyzer = new KeywordAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void init(String queryConfig) {
        try {
            JSONObject object = JSON.parseObject(queryConfig);
            typeMap = new HashMap<>();

            for (Map.Entry<String, Object> entry : object.entrySet()) {
                String field = entry.getKey();
                String value = String.valueOf(entry.getValue());

                try {
                    LuceneType type = LuceneType.valueOf(value.toUpperCase());
                    typeMap.put(field, type);
                    log.debug("字段 {} 配置为类型 {}", field, type);
                } catch (IllegalArgumentException e) {
                    log.warn("未知的字段类型: {}, 字段: {}, 将使用 STRING 类型", value, field);
                    typeMap.put(field, LuceneType.STRING);
                }
            }

            log.info("初始化完成，配置了 {} 个字段类型", typeMap.size());
        } catch (Exception e) {
            log.error("初始化配置失败: {}", queryConfig, e);
            throw new RuntimeException("配置解析失败", e);
        }
    }

    public void load(File file) {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int batchSize = 1000;
            int count = 0;

            while ((line = reader.readLine()) != null) {
                writeDoc(JSON.parseObject(line));
                count++;

                // 批量提交
                if (count % batchSize == 0) {
                    writer.commit();
                    log.info("已写入lucene {} 条记录", count);
                }
            }

            // 最后提交剩余的文档
            writer.commit();
            log.info("总共写入 {} 条记录", count);
        } catch (IOException e) {
            log.error("读取文件 {} 失败, error: {}", file.getName(), e.getMessage());
            throw new RuntimeException(e);
        }
    }


    private void writeDoc(JSONObject object) {
        Document document = new Document();

        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                LuceneType type = typeMap.get(key);
                if (type == null || type == LuceneType.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == LuceneType.INT) {
                    Integer iv = value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value));
                    document.add(new IntPoint(key, iv));
                    document.add(new StoredField(key, iv)); // 为 IntPoint 增加存储字段
                } else if (type == LuceneType.LONG) {
                    Long lv = value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value));
                    document.add(new LongPoint(key, lv));
                    document.add(new StoredField(key, lv));
                } else if (type == LuceneType.FLOAT) {
                    Float fv = value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value));
                    document.add(new FloatPoint(key, fv));
                    document.add(new StoredField(key, fv));
                } else if (type == LuceneType.DOUBLE) {
                    Double dv = value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value));
                    document.add(new DoublePoint(key, dv));
                    document.add(new StoredField(key, dv));
                } else if (type == LuceneType.DATE) {
                    long timestamp = toTimestamp(value);
                    document.add(new LongPoint(key, timestamp));
                    document.add(new StoredField(key, String.valueOf(value)));
                } else {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        LuceneQueryV2 queryV2 = new LuceneQueryV2("C:\\Users\\<USER>\\Desktop\\lucene\\");
        queryV2.init("""
                {
                    "name": "string",
                    "age": "int",
                    "birthday": "date",
                    "title": "string"
                }
                """);

        queryV2.load(new File("C:\\Users\\<USER>\\Desktop\\lucene\\data\\data.log"));

        long start = System.currentTimeMillis();
//        for (int i = 0; i < 100; i++) {
            List<String> records = queryV2.query("(name:open OR title:*jay*) AND age:{21 TO *] AND birthday:{2025-02-02 12:00:00 TO 2025-02-03 12:00:00]", 2000);
            System.err.println(JSON.toJSONString(records));
//        }
        //
        System.err.println("cost time: " + (System.currentTimeMillis() - start) + "ms");

        queryV2.clear();
    }

    public List<String> query(String queryString, int maxResults) {
        List<String> results = new ArrayList<>();

        try (DirectoryReader reader = DirectoryReader.open(directory)) {
            IndexSearcher searcher = new IndexSearcher(reader);

            QueryParser parser = new DateRangeQueryParser("none", new KeywordAnalyzer(), typeMap);
            parser.setAllowLeadingWildcard(true);
            Query query = parser.parse(queryString);

            TopDocs topDocs = searcher.search(query, maxResults);

            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = searcher.doc(scoreDoc.doc);
                results.add(documentToJson(doc));
            }

        } catch (IOException | ParseException e) {
            log.error("lucene查询失败, {} \n error: {}", queryString, e.getMessage());
            throw new RuntimeException(e);
        }

        return results;
    }

    private String documentToJson(Document doc) {
        Map<String, Object> resultObject = new HashMap<>();

        for (IndexableField field : doc) {
            if (field.fieldType().stored()) {
                String fieldName = field.name();
                String fieldValue = doc.get(fieldName);

                // 根据字段类型转换值
                LuceneType fieldType = typeMap.get(fieldName);
                if (fieldType != null) {
                    switch (fieldType) {
                        case INT:
                            resultObject.put(fieldName, Integer.parseInt(fieldValue));
                            break;
                        case LONG:
                            resultObject.put(fieldName, Long.parseLong(fieldValue));
                            break;
                        case FLOAT:
                            resultObject.put(fieldName, Float.parseFloat(fieldValue));
                            break;
                        case DOUBLE:
                            resultObject.put(fieldName, Double.parseDouble(fieldValue));
                            break;
                        case DATE:
                            // 可以选择返回时间戳或格式化的日期
                            resultObject.put(fieldName, fieldValue);
                            break;
                        default:
                            resultObject.put(fieldName, fieldValue);
                    }
                } else {
                    resultObject.put(fieldName, fieldValue);
                }
            }
        }

        return JSON.toJSONString(resultObject);
    }

    public void clear() {
        try {
            writer.deleteAll();
            writer.commit();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }

    /**
     * 自定义 QueryParser，用于处理日期范围查询
     */
    private static class DateRangeQueryParser extends QueryParser {
        private final Map<String, LuceneType> typeMap;

        public DateRangeQueryParser(String f, Analyzer a, Map<String, LuceneType> typeMap) {
            super(f, a);
            this.typeMap = typeMap;
        }

        @Override
        public Query parse(String query) throws ParseException {
            // 预处理查询字符串，为日期时间字段的值添加引号
            String processedQuery = preprocessDateTimeQuery(query);
            return super.parse(processedQuery);
        }

        @Override
        protected Query getRangeQuery(String field, String part1, String part2,
                                      boolean inclusive1, boolean inclusive2) throws ParseException {
            LuceneType fieldType = typeMap != null ? typeMap.get(field) : null;

            if (fieldType == null) {
                return super.getRangeQuery(field, part1, part2, inclusive1, inclusive2);
            }

            return switch (fieldType) {
                case INT -> createIntRangeQuery(field, part1, part2);
                case LONG -> createLongRangeQuery(field, part1, part2);
                case FLOAT -> createFloatRangeQuery(field, part1, part2);
                case DOUBLE -> createDoubleRangeQuery(field, part1, part2);
                case DATE -> createDateRangeQuery(field, part1, part2);
                default -> super.getRangeQuery(field, part1, part2, inclusive1, inclusive2);
            };
        }

        private String preprocessDateTimeQuery(String query) {
            if (typeMap == null) {
                return query;
            }

            String result = query;

            // 为每个日期类型字段处理查询字符串
            for (Map.Entry<String, LuceneType> entry : typeMap.entrySet()) {
                if (entry.getValue() == LuceneType.DATE) {
                    String fieldName = entry.getKey();
                    result = addQuotesToDateTimeValues(result, fieldName);
                }
            }

            return result;
        }

//        private String addQuotesToDateTimeValues(String query, String fieldName) {
//            // 匹配模式：fieldName:[* TO yyyy-MM-dd HH:mm:ss] 或 fieldName:[yyyy-MM-dd HH:mm:ss TO *]
//            // 或 fieldName:[yyyy-MM-dd HH:mm:ss TO yyyy-MM-dd HH:mm:ss]
//
//            String dateTimePattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
//
//            // 处理 [* TO datetime] 格式
//            String pattern1 = "(" + fieldName + ":\\[\\* TO )(" + dateTimePattern + ")(\\])";
//            query = query.replaceAll(pattern1, "$1\"$2\"$3");
//
//            // 处理 [datetime TO *] 格式
//            String pattern2 = "(" + fieldName + ":\\[)(" + dateTimePattern + ")( TO \\*\\])";
//            query = query.replaceAll(pattern2, "$1\"$2\"$3");
//
//            // 处理 [datetime1 TO datetime2] 格式
//            String pattern3 = "(" + fieldName + ":\\[)(" + dateTimePattern + ")( TO )(" + dateTimePattern + ")(\\])";
//            query = query.replaceAll(pattern3, "$1\"$2\"$3\"$4\"$5");
//
//            return query;
//        }

        private String addQuotesToDateTimeValues(String query, String fieldName) {
            // 使用更精确的正则表达式
            String dateTimePattern = "\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}";
            String datePattern = "\\d{4}-\\d{2}-\\d{2}(?!\\s+\\d{2}:\\d{2}:\\d{2})"; // 负向前瞻，确保不匹配日期时间

            String result = query;

            // 定义所有可能的边界符号组合
            String[][] boundaries = {
                    {"\\[", "\\]"},  // 包含边界
                    {"\\{", "\\}"},  // 不包含边界
                    {"\\[", "\\}"},  // 左包含，右不包含
                    {"\\{", "\\]"}   // 左不包含，右包含
            };

            for (String[] boundary : boundaries) {
                String leftBoundary = boundary[0];
                String rightBoundary = boundary[1];

                // 处理日期时间格式
                result = processDateTimePattern(result, fieldName, dateTimePattern, leftBoundary, rightBoundary);

                // 处理日期格式
                result = processDateTimePattern(result, fieldName, datePattern, leftBoundary, rightBoundary);
            }

            return result;
        }

        private String processDateTimePattern(String query, String fieldName, String pattern,
                                              String leftBoundary, String rightBoundary) {
            String result = query;

            // [* TO datetime] 或 {* TO datetime} 等格式
            String pattern1 = "(" + fieldName + ":" + leftBoundary + "\\* TO )(" + pattern + ")(" + rightBoundary + ")";
            result = result.replaceAll(pattern1, "$1\"$2\"$3");

            // [datetime TO *] 或 {datetime TO *} 等格式
            String pattern2 = "(" + fieldName + ":" + leftBoundary + ")(" + pattern + ")( TO \\*" + rightBoundary + ")";
            result = result.replaceAll(pattern2, "$1\"$2\"$3");

            // [datetime TO datetime] 或 {datetime TO datetime} 等格式
            String pattern3 = "(" + fieldName + ":" + leftBoundary + ")(" + pattern + ")( TO )(" + pattern + ")(" + rightBoundary + ")";
            result = result.replaceAll(pattern3, "$1\"$2\"$3\"$4\"$5");

            return result;
        }


        private Query createIntRangeQuery(String field, String part1, String part2) throws ParseException {
            try {
                int min = "*".equals(part1) || part1 == null ? Integer.MIN_VALUE : Integer.parseInt(part1);
                int max = "*".equals(part2) || part2 == null ? Integer.MAX_VALUE : Integer.parseInt(part2);
                return IntPoint.newRangeQuery(field, min, max);
            } catch (NumberFormatException e) {
                throw new ParseException("Invalid integer range: " + part1 + " TO " + part2);
            }
        }

        private Query createLongRangeQuery(String field, String part1, String part2) throws ParseException {
            try {
                long min = "*".equals(part1) || part1 == null ? Long.MIN_VALUE : Long.parseLong(part1);
                long max = "*".equals(part2) || part2 == null ? Long.MAX_VALUE : Long.parseLong(part2);
                return LongPoint.newRangeQuery(field, min, max);
            } catch (NumberFormatException e) {
                throw new ParseException("Invalid long range: " + part1 + " TO " + part2);
            }
        }

        private Query createFloatRangeQuery(String field, String part1, String part2) throws ParseException {
            try {
                float min = "*".equals(part1) || part1 == null ? Float.NEGATIVE_INFINITY : Float.parseFloat(part1);
                float max = "*".equals(part2) || part2 == null ? Float.POSITIVE_INFINITY : Float.parseFloat(part2);
                return FloatPoint.newRangeQuery(field, min, max);
            } catch (NumberFormatException e) {
                throw new ParseException("Invalid float range: " + part1 + " TO " + part2);
            }
        }

        private Query createDoubleRangeQuery(String field, String part1, String part2) throws ParseException {
            try {
                double min = "*".equals(part1) || part1 == null ? Double.NEGATIVE_INFINITY : Double.parseDouble(part1);
                double max = "*".equals(part2) || part2 == null ? Double.POSITIVE_INFINITY : Double.parseDouble(part2);
                return DoublePoint.newRangeQuery(field, min, max);
            } catch (NumberFormatException e) {
                throw new ParseException("Invalid double range: " + part1 + " TO " + part2);
            }
        }

        private Query createDateRangeQuery(String field, String part1, String part2) throws ParseException {
            try {
                long min = "*".equals(part1) || part1 == null ? Long.MIN_VALUE : parseTimestamp(part1);
                long max = "*".equals(part2) || part2 == null ? Long.MAX_VALUE : parseTimestamp(part2);
                return LongPoint.newRangeQuery(field, min, max);
            } catch (Exception e) {
                throw new ParseException("Invalid date range: " + part1 + " TO " + part2 + ", error: " + e.getMessage());
            }
        }

        private long parseTimestamp(String value) {
            LocalDateTime datetime;
            try {
                // 先尝试解析完整的日期时间
                datetime = LocalDateTime.parse(value, DATE_TIME_FORMAT);
            } catch (Exception e) {
                try {
                    // 再尝试解析日期
                    datetime = LocalDate.parse(value, DATE_FORMAT).atTime(LocalTime.MIN);
                } catch (Exception e2) {
                    throw new RuntimeException("无法解析日期: " + value);
                }
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }
}
