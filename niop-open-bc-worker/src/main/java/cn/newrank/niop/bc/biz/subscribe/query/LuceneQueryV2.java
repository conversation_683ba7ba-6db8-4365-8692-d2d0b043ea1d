package cn.newrank.niop.bc.biz.subscribe.query;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.component.transform.LuceneTransform;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.typesafe.config.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.core.KeywordAnalyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.index.IndexableField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * @author: xuguangjie
 * @date: 2025/9/8 16:20:17
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class LuceneQueryV2 {
    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final Directory directory;
    private final IndexWriter writer;
    private Map<String, LuceneType> typeMap;

    public LuceneQueryV2(String indexPath) {
        try {
            directory = FSDirectory.open(Path.of(indexPath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        Analyzer analyzer = new KeywordAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        try {
            writer = new IndexWriter(directory, iwConfig);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void init(String queryConfig) {
        // 1. 解析被查询字段的数据类型
        JSONObject object = JSON.parseObject(queryConfig);
        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String field = entry.getKey();
            String value = String.valueOf(entry.getValue());

            LuceneType type = LuceneType.valueOf(value.toUpperCase());
            if (typeMap == null) {
                typeMap = new HashMap<>();
            }
            typeMap.put(field, type);
        }
    }

    public void load(File file) {
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                writeDoc(JSON.parseObject(line));
            }
        } catch (IOException e) {
            log.error("读取文件 {} 失败, error: {}", file.getName(), e.getMessage());
            throw new RuntimeException(e);
        }
    }


    private void writeDoc(JSONObject object) {
        Document document = new Document();

        for (Map.Entry<String, Object> entry : object.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                LuceneType type = typeMap.get(key);
                if (type == null || type == LuceneType.STRING) {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                } else if (type == LuceneType.INT) {
                    document.add(new IntPoint(key, value instanceof Integer ? (Integer) value : Integer.valueOf(String.valueOf(value))));
                    document.add(new StoredField(key, (Integer) value)); // 为 IntPoint 增加存储字段
                } else if (type == LuceneType.LONG) {
                    document.add(new LongPoint(key, value instanceof Long ? (Long) value : Long.valueOf(String.valueOf(value))));
                    document.add(new StoredField(key, (Long) value));
                } else if (type == LuceneType.FLOAT) {
                    document.add(new FloatPoint(key, value instanceof Float ? (Float) value : Float.valueOf(String.valueOf(value))));
                    document.add(new StoredField(key, (Float) value));
                } else if (type == LuceneType.DOUBLE) {
                    document.add(new DoublePoint(key, value instanceof Double ? (Double) value : Double.valueOf(String.valueOf(value))));
                    document.add(new StoredField(key, (Double) value));
                } else if (type == LuceneType.DATE) {
                    long timestamp = toTimestamp(value);
                    document.add(new LongPoint(key, timestamp));
                    document.add(new StoredField(key, timestamp));
                } else {
                    document.add(new StringField(key, String.valueOf(value), Field.Store.YES));
                }
            } catch (Exception e) {
                throw new RuntimeException(key + " 数据类型异常, " + e.getMessage());
            }
        }

        try {
            writer.addDocument(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
//        String config = """
//                {
//                "plugin_id": "asdqwrq",
//                "input": "input",
//                "output": "output",
//                "query": "(name:open OR title:*jay*) AND age:[21 TO *] AND birthday:[* TO 1738468800]",
//                "query_field_type": {
//                    "name": "string",
//                    "age": "int",
//                    "birthday": "date",
//                    "title": "string"
//                }
//                }
//                """;


        LuceneQueryV2 queryV2 = new LuceneQueryV2("C:\\Users\\<USER>\\Desktop\\lucene\\");
        queryV2.init("""
                {
                    "name": "string",
                    "age": "int",
                    "birthday": "date",
                    "title": "string"
                }
                """);

        queryV2.load(new File("C:\\Users\\<USER>\\Desktop\\lucene\\data\\data.log"));

        long start = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            List<String> records = queryV2.query("(name:open OR title:*jay*) AND age:[21 TO *] AND birthday:[* TO 1738468800]");
            System.err.println(JSON.toJSONString(records));
        }
        //
        System.err.println("cost time: " + (System.currentTimeMillis() - start) + "ms");
    }

    public List<String> query(String queryString) {
        List<String> results = new ArrayList<>();
        DirectoryReader reader = null;

        try {
            writer.commit();

            reader = DirectoryReader.open(directory);
            IndexSearcher searcher = new IndexSearcher(reader);

            // 使用自定义的 QueryParser 来处理日期范围查询
            QueryParser parser = new DateRangeQueryParser("none", new KeywordAnalyzer(), typeMap);
            parser.setAllowLeadingWildcard(true);
            Query query = parser.parse(queryString);

            TopDocs topDocs = searcher.search(query, 2000);

            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = searcher.doc(scoreDoc.doc);
                Map<String, Object> resultObject = new HashMap<>();

                for (IndexableField field : doc) {
                    if (field.fieldType().stored()) {
                        resultObject.put(field.name(), doc.get(field.name()));
                    }
                }
                results.add(JSON.toJSONString(resultObject));
            }

        } catch (IOException | ParseException e) {
            log.error("lucene查询失败, {} \n error: {}", queryString, e.getMessage());
            throw new RuntimeException(e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("关闭 reader 失败", e);
                }
            }
        }
        return results;
    }

    public void clear() {
        try {
            writer.deleteAll();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static long toTimestamp(Object value) {
        if (value instanceof LocalDateTime time) {
            return time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        } else if (value instanceof Date date) {
            return date.getTime() / 1000;
        } else {
            String string = String.valueOf(value);
            LocalDateTime datetime;
            try {
                datetime = LocalDate.parse(string, DATE_FORMAT).atTime(LocalTime.MIN);
            } catch (Exception e) {
                datetime = LocalDateTime.parse(string, DATE_TIME_FORMAT);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }

    /**
     * 自定义 QueryParser，用于处理日期范围查询
     */
    private static class DateRangeQueryParser extends QueryParser {
        private final Map<String, LuceneType> typeMap;

        public DateRangeQueryParser(String f, Analyzer a, Map<String, LuceneType> typeMap) {
            super(f, a);
            this.typeMap = typeMap;
        }

        @Override
        protected Query getRangeQuery(String field, String part1, String part2, boolean inclusive1, boolean inclusive2) throws ParseException {
            // 如果字段是 DATE 类型，就手动解析并转换
            if (typeMap != null && typeMap.get(field) == LuceneType.DATE) {
                try {
                    Long lowerTimestamp = null;
                    Long upperTimestamp = null;

                    if (part1 != null && !part1.equals("*")) {
                        lowerTimestamp = toTimestamp(part1);
                    }

                    if (part2 != null && !part2.equals("*")) {
                        upperTimestamp = toTimestamp(part2);
                    }

                    return LongPoint.newRangeQuery(field, lowerTimestamp, upperTimestamp);
                } catch (Exception e) {
                    throw new ParseException("日期范围查询解析失败: " + e.getMessage());
                }
            }
            // 对于其他字段，使用父类的默认行为
            return super.getRangeQuery(field, part1, part2, inclusive1, inclusive2);
        }

        private long toTimestamp(String value) {
            LocalDateTime datetime;
            try {
                datetime = LocalDateTime.parse(value, DATE_TIME_FORMAT);
            } catch (Exception e) {
                datetime = LocalDate.parse(value, DATE_FORMAT).atTime(LocalTime.MIN);
            }
            return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
        }
    }
}
