package cn.newrank.niop.bc.component;

import cn.newrank.niop.bc.api.CheckpointBarrier;
import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.sink.Sink;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.transform.Transform;
import cn.newrank.niop.bc.component.factory.Factory;
import cn.newrank.niop.bc.util.ApplicationUtil;
import cn.newrank.niop.bc.web.service.CheckpointService;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.*;

/**
 * @author: xuguangjie
 * @date: 2025/7/31 14:29:17
 * @version: 1.0.0
 * @description:
 */
@Slf4j
public class JobOrchestrator {

    private final ThreadPoolExecutor SOURCE_EXECUTOR = ApplicationUtil.getBean("sourceTp", ThreadPoolExecutor.class);
    private final ThreadPoolExecutor TRANSFORM_EXECUTOR = ApplicationUtil.getBean("transformTp", ThreadPoolExecutor.class);
    private final ThreadPoolExecutor SINK_EXECUTOR = ApplicationUtil.getBean("sinkTp", ThreadPoolExecutor.class);

    private Config config;

    private List<Source> sources;
    private List<Transform> transforms;
    @Getter
    private List<Sink> sinks;

    private JobManager jobManager;

    private List<Future<?>> futures;

    /**
     * 记录输入/输出集合对应的队列
     */
    private Map<String, BlockingQueue<Record>> queueMap;

    /**
     * 是否开启检查点
     */
    private boolean checkpointEnabled;
    /**
     * 检查点持久化存储服务
     */
    private CheckpointService checkpointService;
    /**
     * 检查点队列
     */
    private Queue<Record> barrierQueue;
    /**
     * 检查点屏障
     */
    private CheckpointBarrier currentBarrier;

    public JobOrchestrator(String jobId, String taskId, Config config) {
        this.config = config;
        this.sources = new ArrayList<>();
        this.transforms = new ArrayList<>();
        this.sinks = new ArrayList<>();
        this.jobManager = new JobManager(jobId, taskId);
        this.queueMap = new HashMap<>();
        this.futures = new ArrayList<>();
        this.checkpointEnabled = config.hasPath("checkpoint.enabled") && config.getBoolean("checkpoint.enabled");
    }

    public void clear() {
        // 关闭所有组件
        if (sinks != null) sinks.forEach(Sink::close);
        if (transforms != null) transforms.forEach(Transform::close);
        if (sources != null) sources.forEach(Source::close);

        // 清空队列
        if (queueMap != null) queueMap.clear();

        // 清空集合
        if (sinks != null) sinks.clear();
        if (transforms != null) transforms.clear();
        if (sources != null) sources.clear();
    }

    public JobManager getJobManager() {
        return jobManager;
    }

    public JobManager execute() {
        ScheduledExecutorService checkpointTp = null;
        try {
            // 初始化所有组件
            initComponents();

            // 编排组件执行流程
            createDataFlowGraph();

            // 恢复到最近的一个检查点
            if (checkpointEnabled) {
                restore();
            }

            // 执行组件
            futures.addAll(startSource());
            futures.addAll(startTransform());
            futures.addAll(startSink());

            // 初始化检查点管理器
            if (checkpointEnabled) {
                this.checkpointService = ApplicationUtil.getBean("checkpointService", CheckpointService.class);
                barrierQueue = new ArrayBlockingQueue<>(1);
                checkpointTp = checkpointAsync();
            }

            // 等待组件执行完成
            for (Future<?> future : futures) {
                future.get();
            }

            log.info("{}, 组件执行结束...", jobManager.getTaskId());
            return jobManager;
        } catch (Exception e) {
            log.error("任务处理异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
            jobManager.failure(e.getMessage());
            if (checkpointTp != null) checkpointTp.shutdownNow();
            return jobManager;
        } finally {
            log.info("开始清理资源...");
            clear();
            log.info("清理资源完成...");
        }
    }

    /**
     * 取消当前任务的执行
     */
    public void cancel() {
        jobManager.cancel();
    }

    private void initComponents() {
        // 动态加载Source
        List<? extends ConfigObject> sources = config.getObjectList("source");

        for (ConfigObject sourceCfg : sources) {
            Config config = sourceCfg.toConfig();
            String sourceName = config.getString("plugin_name");

            Source source = Factory.newSource(sourceName);
            source.init(config);
            this.sources.add(source);
            log.info("source initialized: {}", sourceName);
        }


        // 动态加载Transform (可选)
        List<? extends ConfigObject> transforms = config.getObjectList("transform");
        for (ConfigObject transformCfg : transforms) {
            Config config = transformCfg.toConfig();
            String transformName = config.getString("plugin_name");

            Transform transform = Factory.newTransform(transformName);
            transform.init(config);
            this.transforms.add(transform);
            log.info("transform initialized: {}", transformName);
        }

        // 动态加载Sink
        List<? extends ConfigObject> sinks = config.getObjectList("sink");
        for (ConfigObject sinkCfg : sinks) {
            Config config = sinkCfg.toConfig();
            String sinkName = config.getString("plugin_name");

            Sink sink = Factory.newSink(sinkName);
            sink.init(config);
            this.sinks.add(sink);
            log.info("sink initialized: {}", sinkName);
        }
    }

    private void createDataFlowGraph() {
        for (Transform transform : transforms) {
            String input = transform.input();
            String output = transform.output();

            queueMap.compute(input, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
            queueMap.compute(output, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
        }

        for (Source source : sources) {
            String output = source.output();
            queueMap.compute(output, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
        }

        for (Sink sink : sinks) {
            String input = sink.input();
            queueMap.compute(input, (k, queue) -> queue == null ? new ArrayBlockingQueue<>(1000) : queue);
        }
    }

    private List<Future<?>> startSource() {
        List<Future<?>> futures = new ArrayList<>(sources.size());

        for (Source source : sources) {
            Future<?> future = SOURCE_EXECUTOR.submit(() -> {
                jobManager.registerWorker(Thread.currentThread());
                String sourceName = source.identifier();

                String output = source.output();
                BlockingQueue<Record> queue = queueMap.get(output);
                log.info("source({}) start...", sourceName);

                CheckpointManager checkpointManager = null;
                try {
                    while (true) {
                        if (checkpointEnabled && (barrierQueue.poll() instanceof CheckpointBarrier barrier)) {
                            queue.put(barrier);
                            checkpointManager = barrier.getCheckpointManager();
                            checkpointManager.completedAndBackup(source.pluginId(), source.backupMeta());
                        }

                        List<Record> records = source.read();
                        if (records == null || records.isEmpty()) {
                            queue.put(Record.OVER);
                            break;
                        }

                        for (Record record : records) {
                            queue.put(record);
                        }
                    }
                } catch (Exception e) {
                    jobManager.failure(e.getMessage());
                    if (checkpointManager != null) checkpointManager.failed(source.pluginId());
                    log.error("数据读取异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
                }
            });
            futures.add(future);
        }
        return futures;
    }

    private List<Future<?>> startTransform() {
        List<Future<?>> futures = new ArrayList<>(transforms.size());
        for (Transform transform : transforms) {
            Future<?> future = TRANSFORM_EXECUTOR.submit(() -> {
                jobManager.registerWorker(Thread.currentThread());
                String transformName = transform.identifier();

                String input = transform.input();
                String output = transform.output();

                BlockingQueue<Record> inputQueue = queueMap.get(input);
                BlockingQueue<Record> outputQueue = queueMap.get(output);

                log.info("transform({}) start...", transformName);

                CheckpointManager checkpointManager = null;
                try {
                    while (true) {
                        Record record = inputQueue.take();
                        if (record == Record.OVER) {
                            outputQueue.put(Record.OVER);
                            break;
                        }

                        if (record instanceof CheckpointBarrier barrier) {
                            outputQueue.put(record);
                            checkpointManager = barrier.getCheckpointManager();
                            checkpointManager.completedAndBackup(transform.pluginId(), transform.backupMeta());
                            continue;
                        }

                        List<Record> records = transform.transform(record);

                        for (Record r : records) {
                            outputQueue.put(r);
                        }
                    }
                } catch (Exception e) {
                    jobManager.failure("数据转换异常, 转换器: " + transformName);
                    if (checkpointManager != null) checkpointManager.failed(transform.pluginId());
                    log.error("数据转换异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
                }
            });
            futures.add(future);
        }
        return futures;
    }

    private List<Future<?>> startSink() {
        List<Future<?>> futures = new ArrayList<>(sinks.size());

        for (Sink sink : sinks) {
            Future<?> future = SINK_EXECUTOR.submit(() -> {
                jobManager.registerWorker(Thread.currentThread());
                String sinkName = sink.identifier();

                String input = sink.input();
                log.info("sink({}) start...", sinkName);

                BlockingQueue<Record> queue = queueMap.get(input);

                CheckpointManager checkpointManager = null;
                try {
                    while (true) {
                        Record record = queue.take();
                        if (record == Record.OVER) {
                            sink.flush();
                            return;
                        }

                        if (record instanceof CheckpointBarrier barrier) {
                            checkpointManager = barrier.getCheckpointManager();
                            checkpointManager.completedAndBackup(sink.pluginId(), sink.backupMeta());
                            continue;
                        }

                        sink.write(record);
                    }
                } catch (Exception e) {
                    jobManager.failure("数据输出异常, 输出: " + sinkName + ", reason: " + e.getMessage());
                    if (checkpointManager != null) checkpointManager.failed(sink.pluginId());
                    log.error("数据输出异常, job: {}, task: {}, exception: ", jobManager.getJobId(), jobManager.getTaskId(), e);
                }
            });
            futures.add(future);
        }
        return futures;
    }

    private ScheduledExecutorService checkpointAsync() {
        ScheduledExecutorService threadPool = Executors.newScheduledThreadPool(1);
        threadPool.scheduleWithFixedDelay(() -> {
            if (currentBarrier == null || currentBarrier.getCheckpointManager().awaitCompletion(1, TimeUnit.SECONDS)) {
                currentBarrier = CheckpointBarrier.of(jobManager.getJobId(), sources.size() + transforms.size() + sinks.size());
                barrierQueue.offer(currentBarrier);
            }

            CheckpointManager checkpointManager = currentBarrier.getCheckpointManager();
            boolean finished = checkpointManager.awaitCompletion(5, TimeUnit.SECONDS);
            if (finished) {
                for (Source source : sources) {
                    String pluginId = source.pluginId();
                    checkpointService.save(pluginId, checkpointManager);
                }
                for (Transform transform : transforms) {
                    String pluginId = transform.pluginId();
                    checkpointService.save(pluginId, checkpointManager);
                }
                for (Sink sink : sinks) {
                    String pluginId = sink.pluginId();
                    checkpointService.save(pluginId, checkpointManager);
                }
            }
        }, 10, 10, TimeUnit.SECONDS);
        return threadPool;
    }

    private void restore() {
        String jobId = jobManager.getJobId();
        for (Source source : sources) {
            String meta = checkpointService.load(jobId, source.pluginId());
            source.restore(meta);
        }
        for (Transform transform : transforms) {
            String meta = checkpointService.load(jobId, transform.pluginId());
            transform.restore(meta);
        }
        for (Sink sink : sinks) {
            String meta = checkpointService.load(jobId, sink.pluginId());
            sink.restore(meta);
        }
    }

}
