package cn.newrank.niop.bc.biz.subscribe.source;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.bc.biz.subscribe.consume.Subscriber;
import cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeGroupMapper;
import cn.newrank.niop.bc.biz.subscribe.properties.KafkaProperties;
import cn.newrank.niop.bc.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @author: xuguangjie
 * @date: 2025/9/8 19:50:31
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@Component
public class KafkaSource implements CommandLineRunner, ApplicationListener<ContextClosedEvent> {

    @Value("${spring.config.activate.on-profile}")
    private String activeProfile;

    @Autowired
    private SubscribeGroupMapper subscribeGroupMapper;
    @Autowired
    private KafkaProperties kafkaProperties;
    @Autowired
    private Subscriber subscriber;

    /**
     * 创建一个kafka消费者
     */
    public KafkaConsumer<String, String> createKafkaConsumer(KafkaProperties properties, String group) {
        final Properties props = new Properties();

        KafkaProperties.KafkaProps kafkaProps = properties.getKafkaProps();
        //设置接入点
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProps.getServers());

        if (activeProfile.equals("dev") || activeProfile.equals("develop")) {
            final Path path = Paths.get("./kafka/only.4096.client.truststore.jks");
            props.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, path.toAbsolutePath().toString());
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            props.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, "KafkaOnsClient");
            props.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");

            final String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";",
                    "org.apache.kafka.common.security.plain.PlainLoginModule",
                    kafkaProps.getUsername(), kafkaProps.getPassword());

            props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        } else {
            if (kafkaProps.isPlaintext()) {
                props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "PLAINTEXT");
            } else {
                props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");

                final String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";",
                        "org.apache.kafka.common.security.plain.PlainLoginModule",
                        kafkaProps.getUsername(), kafkaProps.getPassword());

                props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
                props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            }
        }

        //可更加实际拉去数据和客户的版本等设置此值，默认30s
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        // 1MB 或 5s
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 1024);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 5000);
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿
//        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaProps.getMaxPoll());
        //消息的反序列化方式
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //属于同一个组的消费实例，会负载消费消息
        props.put(ConsumerConfig.GROUP_ID_CONFIG, group);
        final KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);

        kafkaConsumer.subscribe(List.of(kafkaProps.getTopic()));
        return kafkaConsumer;
    }


    private final AtomicBoolean isRunning = new AtomicBoolean(true);


    @Override
    public void run(String... args) {
        if (!Objects.equals("product", activeProfile) && !Objects.equals("prod", activeProfile)) {
            return;
        }
        new Thread(() -> {
            String groupId = subscribeGroupMapper.getSubscribeGroupName(IpUtil.getIp()).getGroupId();
            KafkaConsumer<String, String> kafkaConsumer = createKafkaConsumer(kafkaProperties, groupId);
            while (isRunning.get()) {
                ConsumerRecords<String, String> consumerRecords = kafkaConsumer.poll(Duration.ofSeconds(5));
                if (consumerRecords.isEmpty()) {
                    ThreadUtil.sleep(500);
                    continue;
                }

                boolean success;
                try {
                    success = subscriber.subscribe(consumerRecords);
                } catch (Exception e) {
                    log.error("kafka消费异常, ip: {}, e: ", IpUtil.getIp(), e);
                    success = false;
                }

                if (success) {
                    kafkaConsumer.commitAsync();
                } else {
                    resetOffset(kafkaConsumer, consumerRecords);
                    ThreadUtil.sleep(1, TimeUnit.SECONDS);
                }
            }
        }).start();
    }

    private static void resetOffset(KafkaConsumer<String, String> kafkaConsumer, ConsumerRecords<String, String> consumerRecords) {
        // 处理失败，回滚到这批消息的开始位置
        final Map<TopicPartition, Long> partitionOffsets = new HashMap<>();

        for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
            final TopicPartition tp = new TopicPartition(consumerRecord.topic(), consumerRecord.partition());
            partitionOffsets.merge(tp, consumerRecord.offset(), Math::min);
        }

        // 遍历所有涉及的分区并重置偏移量
        partitionOffsets.forEach(kafkaConsumer::seek);
    }

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        isRunning.set(false);
    }

}
