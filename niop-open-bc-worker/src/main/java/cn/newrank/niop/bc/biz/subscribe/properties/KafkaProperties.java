package cn.newrank.niop.bc.biz.subscribe.properties;

import cn.newrank.niop.bc.util.IpUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 19:54:05
 * @version: 1.0.0
 * @description:
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "kafka")
public class KafkaProperties {

    private List<KafkaProps> producers;

    @Data
    public static class KafkaProps {
        private String ip;
        private String servers;
        private String topic;
        private boolean plaintext;
        private String username;
        private String password;
    }

    public KafkaProps getKafkaProps() {
        String ip = IpUtil.getIp();

        long count = producers.stream().filter(obj -> obj.ip.contains(ip)).count();
        if (count > 1) {
            throw new IllegalStateException("当前机器绑定了多个消费目标kafka, ip: " + ip);
        }

        for (KafkaProps kafkaProps : producers) {
            if (kafkaProps.ip.equals(ip)) {
                return kafkaProps;
            }
        }
        throw new IllegalStateException("未找到当前机器绑定的消费目标kafka, ip: " + ip);
    }

}
