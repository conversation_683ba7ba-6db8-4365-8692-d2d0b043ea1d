package cn.newrank.niop.bc.connector.source.mysql;

import cn.newrank.niop.bc.api.Record;
import cn.newrank.niop.bc.api.source.Source;
import cn.newrank.niop.bc.api.source.SourcePlugin;
import com.google.auto.service.AutoService;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xuguangjie
 * @date: 2025/7/23 16:45:27
 * @version: 1.0.0
 * @description:
 */
@Slf4j
@AutoService(SourcePlugin.class)
public class MysqlSource implements Source {

    private static final int batch = 500;

    private Connection connection;
    private PreparedStatement statement;
    private ResultSet resultSet;
    private String query;

    @Override
    public String identifier() {
        return "mysql";
    }

    @Override
    public void init(Config config) {
        String jdbcUrl = config.getString("mysql.url");
        String username = config.getString("mysql.username");
        String password = config.getString("mysql.password");
        query = config.getString("mysql.query");

        try {
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            log.info("mysql source initialized: {}", jdbcUrl);
        } catch (SQLException e) {
            throw new RuntimeException("failed to connect to mysql", e);
        }
    }

    @Override
    public List<Record> read() {
        try {
            if (resultSet != null) {
                return records();
            }

            statement = connection.prepareStatement(query);
            // 启用流式结果集模式
            statement.setFetchSize(Integer.MIN_VALUE);

            log.info("executing mysql query: {}, with timestamp: {}", query, System.currentTimeMillis());
            resultSet = statement.executeQuery();
            log.info("executing mysql query finished: {}, with timestamp: {}", query, System.currentTimeMillis());

            return records();
        } catch (SQLException e) {
            log.error("error reading from mysql: ", e);
            throw new RuntimeException("数据读取异常");
        }
    }

    private List<Record> records() throws SQLException {
        List<Record> records = new ArrayList<>();
        while (records.size() < batch && resultSet.next()) {
            Record record = new Record();
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                record.put(columnName, resultSet.getObject(i));
            }
            records.add(record);
        }
        return records;
    }

    @Override
    public void close() {
        try {
            if (resultSet != null) resultSet.close();
            if (statement != null) statement.close();
            if (connection != null) connection.close();
            log.info("mysql source closed.");
        } catch (SQLException e) {
            log.error("error closing mysql source: ", e);
        }
    }

}
