package cn.newrank.niop.bc.biz.subscribe.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/9/8 14:05:22
 * @version: 1.0.0
 * @description:
 */
@Data
public class SubscribeGroup {

    private Long id;
    private String groupId;
    private String groupName;
    private String queryConfig;
    private String ip;
    private String sinkConfig;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

}
