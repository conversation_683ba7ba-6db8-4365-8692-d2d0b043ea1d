package cn.newrank.niop.bc.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 优雅关闭配置
 * 
 * @author: xuguangjie
 * @date: 2025/8/11
 * @version: 1.0.0
 * @description: 配置应用优雅关闭，确保在关闭过程中有足够时间完成数据库操作
 */
@Slf4j
@Configuration
public class GracefulShutdownConfig {

    /**
     * 配置 Tomcat 优雅关闭
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.setShutdown(org.springframework.boot.web.server.Shutdown.GRACEFUL);
            log.info("已配置 Tomcat 优雅关闭");
        };
    }
}
