package cn.newrank.niop.bc.util;

import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/8/11 17:53:23
 * @version: 1.0.0
 * @description:
 */
@Component
public class ApplicationUtil implements ApplicationListener<ApplicationStartedEvent> {

    private static ApplicationContext applicationContext;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        applicationContext = event.getApplicationContext();
    }

    public static <T> T getBean(String beanName, Class<T> clazz) {
        return applicationContext.getBean(beanName, clazz);
    }

}
