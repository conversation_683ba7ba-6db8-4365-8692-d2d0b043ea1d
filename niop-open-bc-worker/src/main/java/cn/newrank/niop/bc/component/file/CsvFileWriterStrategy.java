package cn.newrank.niop.bc.component.file;

import cn.newrank.niop.bc.api.Record;
import com.opencsv.CSVWriter;
import com.opencsv.CSVWriterBuilder;
import com.opencsv.ICSVWriter;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xug<PERSON><PERSON><PERSON>
 * @date: 2025/7/24 16:24:39
 * @version: 1.0.0
 * @description:
 */
public class CsvFileWriterStrategy implements FileWriterStrategy {

    @Override
    public void write(Record record, String filePath) throws IOException {
        File file = new File(filePath);

        if (!file.exists()) {
            file.getParentFile().mkdirs();
        }

        try (ICSVWriter writer = new CSVWriterBuilder(new java.io.FileWriter(file, true))
                .withSeparator(CSVWriter.DEFAULT_SEPARATOR)
                .withQuoteChar(CSVWriter.DEFAULT_QUOTE_CHARACTER)
                .build()) {

            if (file.length() <= 0) {
                String[] header = record.getData().keySet().toArray(new String[]{});
                writer.writeNext(header);
            }

            String[] values = toArray(new ArrayList<>(record.getData().values()));
            writer.writeNext(values);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String[] toArray(List<Object> values) {
        String[] array = new String[values.size()];

        for (int i = 0; i < values.size(); i++) {
            Object val = values.get(i);

            if (val == null) {
                array[i] = "";
            }

            if (val instanceof String) {
                array[i] = (String) val;
            } else {
                array[i] = String.valueOf(val);
            }
        }
        return array;
    }

}
