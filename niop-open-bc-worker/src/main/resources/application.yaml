server:
  port: 9301
  servlet:
    context-path: /open/bc/worker
spring:
  application:
    name: niop-open-bc-worker
  cloud:
    nacos:
      server-addr: @spring.cloud.nacos.server-addr@
      discovery:
        namespace: @spring.cloud.nacos.discovery.namespace@
        group: @spring.cloud.nacos.config.group@
      config:
        namespace: @spring.cloud.nacos.config.namespace@
        group: @spring.cloud.nacos.config.group@
  config:
    import:
      - nacos:niop-open-bc-worker.yaml

# dubbo配置
dubbo:
  application:
    name: dubbo-${spring.application.name}
  registry:
    address: nacos://@spring.cloud.nacos.server-addr@
    parameters.accessKey: @ali.access-key@
    parameters.secretKey: @ali.secret-key@
    parameters.namespace: @spring.cloud.nacos.config.namespace@
    group: @spring.cloud.nacos.config.group@
  protocol:
    # 沿用原grpc的端口
    port: 10301
    name: dubbo

---
spring:
  config:
    activate:
      on-profile: product
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: dev
    import:
      - optional:classpath:application-dev.yml
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
