<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.bc.web.mapper.TaskMapper">

    <insert id="save">
        insert into bc_task(job_id, task_id, params, state)
            value (
                   #{task.jobId},
                   #{task.taskId},
                   #{task.params},
                   #{task.state}
            )
    </insert>

    <update id="update">
        update bc_task
        <set>
            <if test="task.state != null">
                state = #{task.state},
            </if>
            <if test="task.startTime != null">
                start_time = #{task.startTime},
            </if>
            <if test="task.endTime != null">
                end_time = #{task.endTime},
            </if>
            <if test="task.reason != null and task.reason != ''">
                reason = #{task.reason},
            </if>
        </set>
        where task_id = #{task.taskId}
    </update>

</mapper>
