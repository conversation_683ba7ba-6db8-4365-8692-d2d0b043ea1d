<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.bc.web.mapper.CheckpointMetaMapper">

    <insert id="save">
        insert into bc_checkpoint_meta(checkpoint_id, plugin_id, checkpoint_time, meta)
        values (#{checkpointMeta.checkpointId}, #{checkpointMeta.pluginId},
                #{checkpointMeta.checkpointTime}, #{checkpointMeta.meta})
        on duplicate key update checkpoint_time = #{checkpointMeta.checkpointTime},
                                meta            = #{checkpointMeta.meta}
    </insert>

    <select id="getCheckpointMeta" resultType="cn.newrank.niop.bc.web.pojo.CheckpointMeta">
        select *
        from bc_checkpoint_meta
        where checkpoint_id = #{checkpointId}
          and plugin_id = #{pluginId}
    </select>

</mapper>
