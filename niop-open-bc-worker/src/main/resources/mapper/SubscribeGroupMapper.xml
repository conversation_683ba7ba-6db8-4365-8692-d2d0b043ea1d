<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.bc.biz.subscribe.mapper.SubscribeGroupMapper">
    <insert id="saveResults">
        insert into bc_subscribe_result(unique_id, message, group_name)
        values
        <foreach collection="dataList" item="item" separator=",">
            (#{item.uniqueId}, #{item.message}, #{groupName})
        </foreach>
    </insert>


    <select id="getSubscribeGroupName" resultType="cn.newrank.niop.bc.biz.subscribe.pojo.SubscribeGroup">
        select *
        from bc_subscribe_group
        where ip = #{ip}
    </select>

</mapper>
