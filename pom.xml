<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.newrank.niop</groupId>
        <artifactId>pom-niop</artifactId>
        <version>2.1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>niop-open-data</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>


    <modules>
        <module>niop-open-data-system</module>
        <module>niop-open-data-api</module>
        <module>niop-open-data-api-system</module>
        <module>niop-open-data-common</module>
        <module>niop-open-bc-worker</module>
        <module>niop-open-data-boot</module>
        <module>niop-open-bc-dispatcher</module>
    </modules>

    <properties>
        <revision>0.7.20</revision>

        <kafka-clients.version>2.6.2</kafka-clients.version>
        <iam-sdk-starter.version>2.0.4</iam-sdk-starter.version>
        <niop-util.version>2.4.1</niop-util.version>
        <elasticsearch.version>7.10.0</elasticsearch.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <atsp-sdk-client>4.0.7</atsp-sdk-client>
        <pinyin4j.version>2.5.1</pinyin4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--kafka-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>

            <!--es start-->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-elasticsearch</artifactId>
                <version>4.3.1</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <!--es end-->

            <dependency>
                <groupId>cn.newrank.niop</groupId>
                <artifactId>niop-iam-client-boot3-starter</artifactId>
                <version>${iam-sdk-starter.version}</version>
            </dependency>


            <!--niop start-->
            <dependency>
                <groupId>cn.newrank.niop</groupId>
                <artifactId>niop-web</artifactId>
                <version>${niop-util.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.newrank.niop</groupId>
                <artifactId>niop-core</artifactId>
                <version>${niop-util.version}</version>
            </dependency>
            <!--niop end-->

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.newrank.niop</groupId>
                <artifactId>open-atsp-sdk-http-client-starter</artifactId>
                <version>${atsp-sdk-client}</version>
            </dependency>

            <!-- 汉字转拼音工具包 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>5.8.26</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>