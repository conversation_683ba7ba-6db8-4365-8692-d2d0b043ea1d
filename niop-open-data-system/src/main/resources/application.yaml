server:
  port: 9298
  servlet:
    context-path: /open/data
spring:
  application:
    name: niop-open-data
  cloud:
    nacos:
      server-addr: @spring.cloud.nacos.server-addr@
      discovery:
        namespace: @spring.cloud.nacos.discovery.namespace@
        group: @spring.cloud.nacos.config.group@
      config:
        namespace: @spring.cloud.nacos.config.namespace@
        group: @spring.cloud.nacos.config.group@
  config:
    import:
      - nacos:niop-open-data.yaml
newrank:
  jackson:
    base-enum-package: cn.newrank.niop.data
  #  gateway:
  #    token: 3ad0c2ac7f5c4cd8ba0536764db0a1c4
  oss:
    ak: @ali.access-key@
    sk: @ali.secret-key@


mybatis-plus:
  configuration:
    default-enum-type-handler: cn.newrank.nrcore.dao.mybatis.NrDbEnumTypeHandler
    variables:
      xhs-topic-opus-schema: xhs
      xhs-exp-topic-schema: xhs
# dubbo配置
dubbo:
  application:
    name: dubbo-${spring.application.name}
    qos-enable: false
    check-serializable: false
    auto-trust-serialize-class: true
    trust-serialize-class-level: 2
    serialize-check-status: WARN
  registry:
    address: nacos://@spring.cloud.nacos.server-addr@
    parameters.accessKey: @ali.access-key@
    parameters.secretKey: @ali.secret-key@
    parameters.namespace: @spring.cloud.nacos.config.namespace@
    group: @spring.cloud.nacos.config.group@
    register-mode: instance
    use-as-config-center: false
    use-as-metadata-center: false
  protocol:
    # 沿用原grpc的端口
    port: 9299
    name: dubbo
  consumer:
    check: false
  metadata-report:
    report-metadata: false
---
spring:
  config:
    activate:
      on-profile: product
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@

---
spring:
  config:
    activate:
      on-profile: dev
    import:
      - optional:classpath:application-dev.yml
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
---
spring:
  config:
    activate:
      on-profile: dev1
    import:
      - optional:classpath:application-dev1.yml
  cloud:
    nacos:
      discovery:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@
      config:
        access-key: @ali.access-key@
        secret-key: @ali.secret-key@


