<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.data.ark.mapper.ObjDataMapper">
    <insert id="batchInsert">
        upsert into
        ods_data_ark_obj_data(obj_id,obj_name,data_detail,dimension_id,acq_task_id,acq_time,acq_timing,create_time,update_time)
        values
        <foreach collection="list" item="obj" index="index" separator=",">
            (#{obj.objId},#{obj.objName},#{obj.dataDetail},#{obj.dimensionId},
            #{obj.acqTaskId},#{obj.acqTime},#{obj.acqTiming},#{obj.createTime},#{obj.updateTime})
        </foreach>
    </insert>
</mapper>