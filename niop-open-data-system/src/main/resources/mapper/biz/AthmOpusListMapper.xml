<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.athm.mapper.AthmOpusListMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.athm.pojo.AthmOpusList">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="commentNum" column="comment_num" jdbcType="INTEGER"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="readNum" column="read_num" jdbcType="INTEGER"/>
        <result property="likeNum" column="like_num" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="playNum" column="play_num" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="publishTime" column="publish_time" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,title,comment_num,
        duration,opus_id,url,
        cover,uid,nickname,
        avatar,read_num,like_num,
        description,content,play_num,
        update_time,publish_time,gmt_create,
        gmt_modified
    </sql>

    <insert id="insertBatch">
        insert into niop_data_biz_athm_opus_list(
        title, comment_num, duration, opus_id, url, cover, uid, nickname, avatar,
        read_num, like_num, description, content, play_num, update_time, publish_time,gmt_create, gmt_modified
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.title}, #{item.commentNum}, #{item.duration}, #{item.opusId}, #{item.url}, #{item.cover},
            #{item.uid},
            #{item.nickname}, #{item.avatar}, #{item.readNum}, #{item.likeNum}, #{item.description}, #{item.content},
            #{item.playNum}, #{item.updateTime}, #{item.publishTime}, now(), now()
            )
        </foreach>
        ON CONFLICT (uid, opus_id) DO UPDATE SET
        title = EXCLUDED.title,
        comment_num = EXCLUDED.comment_num,
        duration = EXCLUDED.duration,
        url = EXCLUDED.url,
        cover = EXCLUDED.cover,
        nickname = EXCLUDED.nickname,
        avatar = EXCLUDED.avatar,
        read_num = EXCLUDED.read_num,
        like_num = EXCLUDED.like_num,
        description = EXCLUDED.description,
        content = EXCLUDED.content,
        play_num = EXCLUDED.play_num,
        update_time = EXCLUDED.update_time,
        publish_time = EXCLUDED.publish_time,
        gmt_modified = EXCLUDED.gmt_modified;
    </insert>
    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_athm_opus_list
        where opus_id = #{opusId}
    </select>
</mapper>
