<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusHoloMapper">

    <insert id="storeBatch">
        INSERT INTO ${xhs-topic-opus-schema}.dwd_opus_log
        (opus_id,cover,title,"desc",hash_tags,
         create_time,type,collected_count,liked_count,shared_count,comments_count,
        user_id,user_images,user_nickname,
        token,user_fans,ana_time,is_visible,is_delete,interactive_count,
        note_counter_type_v2,note_counter_type_v1,ip_location_web,
        official_keyword,video_info,share_info_link,is_cooperate,cooperate_name,
        cooperate_id,poi_name,official_warn_msg,discern_business_brand_id,discern_business_brand_name,
        seed_brand_id,seed_brand_name,
        images_list,"time",last_update_time,gmt_create,acq_aweme_detail,first_detail_ana_time,
        ds,"source",message_id,update_time)
        VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.opusId}, #{item.cover},#{item.title},#{item.desc},#{item.hashTags}::JSON,
            #{item.createTime},#{item.type},#{item.collectNum},#{item.likes},#{item.shareNum},#{item.commentNum},
            #{item.uid},#{item.images},#{item.nickname},
            #{item.token},  #{item.userFans},#{item.anaTime},#{item.isVisible},#{item.isDelete},#{item.interactiveCount},
            #{item.noteCounterTypeV2},#{item.noteCounterTypeV1},#{item.ipLocationWeb},
            #{item.officialKeyword},#{item.videoInfo}::JSON,#{item.shareInfoLink},#{item.isCooperate},#{item.cooperateName},
            #{item.cooperateId},#{item.poiName},#{item.officialWarnMsg},#{item.discernBusinessBrandId},#{item.discernBusinessBrandName},
            #{item.seedBrandId},#{item.seedBrandName},
            #{item.imagesList}::JSON,#{item.time},#{item.lastUpdateTime},#{item.gmtTime},#{item.acqAwemeDetail},#{item.firstDetailAnaTime},
            #{item.ds},#{item.source},#{item.messageId},now())
        </foreach>
        ON CONFLICT (opus_id,message_id,ds)
        DO NOTHING
    </insert>
</mapper>
