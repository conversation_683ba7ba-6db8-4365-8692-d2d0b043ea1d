<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti">
        <result property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="cover" column="cover" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="desc" column="desc" jdbcType="VARCHAR"/>
        <result property="hashTags" column="topics" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="collectNum" column="collected_count" jdbcType="INTEGER"/>
        <result property="likes" column="liked_count" jdbcType="INTEGER"/>
        <result property="shareNum" column="shared_count" jdbcType="INTEGER"/>
        <result property="commentNum" column="comments_count" jdbcType="INTEGER"/>
        <result property="uid" column="user_id" jdbcType="VARCHAR"/>
        <result property="images" column="user_images" jdbcType="VARCHAR"/>
        <result property="nickname" column="user_nickname" jdbcType="VARCHAR"/>
        <result property="lastUpdateTime" column="last_update_time" jdbcType="VARCHAR"/>
        <result property="userFans" column="user_fans" jdbcType="VARCHAR"/>
        <result property="gmtTime" column="gmt_create" jdbcType="VARCHAR"/>
        <result property="anaTime" column="ana_time" jdbcType="VARCHAR"/>
        <result property="time" column="time" jdbcType="VARCHAR"/>
        <result property="imagesList" column="images_list" jdbcType="VARCHAR"/>
        <result property="isVisible" column="is_visible" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="interactiveCount" column="interactive_count" jdbcType="INTEGER"/>
        <result property="noteCounterTypeV2" column="note_counter_type_v2" jdbcType="VARCHAR"/>
        <result property="noteCounterTypeV1" column="note_counter_type_v1" jdbcType="VARCHAR"/>
        <result property="ipLocationWeb" column="ip_location_web" jdbcType="VARCHAR"/>
        <result property="officialKeyword" column="official_keyword" jdbcType="VARCHAR"/>
        <result property="videoInfo" column="video_info" jdbcType="VARCHAR"/>
        <result property="shareInfoLink" column="share_info_link" jdbcType="VARCHAR"/>
        <result property="isCooperate" column="is_cooperate" jdbcType="INTEGER"/>
        <result property="cooperateName" column="cooperate_name" jdbcType="VARCHAR"/>
        <result property="cooperateId" column="cooperate_id" jdbcType="VARCHAR"/>
        <result property="poiName" column="poi_name" jdbcType="VARCHAR"/>
        <result property="officialWarnMsg" column="official_warn_msg" jdbcType="VARCHAR"/>
        <result property="discernBusinessBrandId" column="discern_business_brand_id" jdbcType="VARCHAR"/>
        <result property="discernBusinessBrandName" column="discern_business_brand_name" jdbcType="VARCHAR"/>
        <result property="seedBrandId" column="seed_brand_id" jdbcType="VARCHAR"/>
        <result property="seedBrandName" column="seed_brand_name" jdbcType="VARCHAR"/>
        <result property="firstDetailAnaTime" column="first_detail_ana_time" jdbcType="VARCHAR"/>
        <result property="token" column="token" jdbcType="VARCHAR"/>
        <result property="acqAwemeDetail" column="acq_aweme_detail" jdbcType="INTEGER"/>
        <result property="dataLabel" column="data_label" jdbcType="VARCHAR"/>
        <result property="videoId" column="video_id" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="saveBatch">
        UPSERT INTO dim_opus(
        opus_id,
        cover,
        title,
        `desc`,
        topics,
        create_time,
        `type`,
        collected_count,
        liked_count,
        shared_count,
        comments_count,
        user_id,
        user_fans,
        user_images,
        user_nickname,
        token,
        video_info,
        ana_time,
        is_visible,
        is_delete,
        interactive_count,
        note_counter_type_v2,
        note_counter_type_v1,
        ip_location_web,
        official_keyword,
        share_info_link,
        is_cooperate,
        cooperate_id,
        cooperate_name,
        poi_name,
        official_warn_msg,
        discern_business_brand_id,
        discern_business_brand_name,
        seed_brand_id,
        seed_brand_name,
        images_list,
        `time`,
        last_update_time,
        gmt_create,
        acq_aweme_detail,
        first_detail_ana_time,
        data_label,
        video_id
        )
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},
            #{item.cover},
            #{item.title},
            #{item.desc},
            #{item.hashTags},
            #{item.createTime},
            #{item.type},
            #{item.collectNum},
            #{item.likes},
            #{item.shareNum},
            #{item.commentNum},
            #{item.uid},
            #{item.userFans},
            #{item.images},
            #{item.nickname},
            #{item.token},
            #{item.videoInfo},
            #{item.anaTime},
            #{item.isVisible},
            #{item.isDelete},
            #{item.interactiveCount},
            #{item.noteCounterTypeV2},
            #{item.noteCounterTypeV1},
            #{item.ipLocationWeb},
            #{item.officialKeyword},
            #{item.shareInfoLink},
            #{item.isCooperate},
            #{item.cooperateName},
            #{item.cooperateId},
            #{item.poiName},
            #{item.officialWarnMsg},
            #{item.discernBusinessBrandId},
            #{item.discernBusinessBrandName},
            #{item.seedBrandId},
            #{item.seedBrandName},
            #{item.imagesList},
            #{item.time,jdbcType=TIMESTAMP},
            #{item.lastUpdateTime},
            #{item.gmtTime},
            #{item.acqAwemeDetail},
            #{item.firstDetailAnaTime},
            #{item.dataLabel},
            #{item.videoId})
        </foreach>
    </insert>

    <select id="getOpusExistBatch" resultMap="BaseResultMap">
            SELECT
            /*+_l_ignore_search_index_*/
            *
            FROM dim_opus
            WHERE opus_id in
            <foreach item="item" collection="items" index="index" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="getOpusExistIdsBatch" resultType="java.lang.String">
        SELECT opus_id
        FROM dim_opus
        WHERE opus_id in
        <foreach item="item" collection="items" index="index" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>



    <insert id="fillFieldSaveBatch">
        UPSERT INTO dim_opus(
        opus_id,
        images_list
        )
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},
            #{item.imagesList})
        </foreach>
    </insert>

</mapper>
