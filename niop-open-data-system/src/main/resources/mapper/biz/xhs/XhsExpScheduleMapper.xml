<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpScheduleMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpSubmitSchedule">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="topicWeight" column="topic_weight" jdbcType="INTEGER"/>
        <result property="execHourRange" column="exec_hour_range" jdbcType="VARCHAR"/>
        <result property="lastExecTime" column="last_exec_time" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, topic_weight, exec_hour_range, last_exec_time
    </sql>
    <sql id="Select_Column_List">
        id, topic_weight, exec_hour_range, last_exec_time
    </sql>

    <select id="listSchedules" resultMap="BaseResultMap">
        select <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_schedule
        order by topic_weight desc
    </select>

    <update id="updateScheduleExecTime">
        update niop_data_xhs_expansion_topic_schedule
        set last_exec_time = #{lastExecTime},gmt_modified = now()
        where id = #{id}
    </update>
</mapper>
