<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusExpandMapper">

    <insert id="batchInsert">
        UPSERT INTO dim_extend_opus(
        opus_id,
        data_type,
        user_id,
        acq_time,
        public_time,
        ana_time,
        user_name,
        user_avatar,
        cover,
        like_count,
        comment_count,
        collect_count,
        opus_type,
        title
        ) VALUES
        <foreach collection="list" item="opus" separator=",">
        (
        #{opus.opusId, jdbcType=VARCHAR},
        #{opus.dataType, jdbcType=VARCHAR},
        #{opus.userId, jdbcType=VARCHAR},
        #{opus.acqTime, jdbcType=TIMESTAMP},
        #{opus.publicTime, jdbcType=TIMESTAMP},
        #{opus.anaTime, jdbcType=TIMESTAMP},
        #{opus.userName, jdbcType=VARCHAR},
        #{opus.userAvatar, jdbcType=VARCHAR},
        #{opus.cover, jdbcType=VARCHAR},
        #{opus.likeCount, jdbcType=INTEGER},
        #{opus.commentCount, jdbcType=INTEGER},
        #{opus.collectCount, jdbcType=INTEGER},
        #{opus.opusType, jdbcType=VARCHAR},
        #{opus.title, jdbcType=VARCHAR}
        )
        </foreach>
    </insert>
</mapper>
