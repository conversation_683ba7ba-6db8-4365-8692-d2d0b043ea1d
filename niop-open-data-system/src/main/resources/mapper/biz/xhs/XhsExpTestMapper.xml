<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTestMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTestTask">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="testCode" column="test_code" jdbcType="VARCHAR"/>
        <result property="taskTriggerTime" column="task_trigger_time" jdbcType="VARCHAR"/>
        <result property="taskParam" column="task_param" jdbcType="VARCHAR"/>
        <result property="taskResult" column="task_result" jdbcType="VARCHAR"/>
    </resultMap>



    <insert id="saveTask">
        insert into niop_data_xhs_expansion_task_test (test_code, task_trigger_time, task_id, task_param, task_result)
        values (#{testCode}, #{taskTriggerTime}, #{taskId}, #{taskParam}, #{taskResult})
    </insert>
    <select id="submitTask" resultMap="BaseResultMap">
        select *
        from niop_data_xhs_expansion_task_test
        where test_code = #{testCode} and task_trigger_time = #{taskTriggerTime} and task_param = #{taskParam}
    </select>
</mapper>
