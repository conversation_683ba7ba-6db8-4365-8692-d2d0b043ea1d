<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsTopicUniIndexLmMapper">

    <sql id="Base_Insert_Column_List">
        topic_id,page_id,topic_name,discuss_num,view_num,share_info_desc,acq_time,topic_status
    </sql>
    <sql id="Cal_Result_Insert_Column_List">
        topic_id,topic_name,collection_time,
        page_id,is_brand_topic,share_info_desc,
        topic_type,topic_type_multi,view_num,discuss_num,
        is_invalid,note_num_thirty,interactive_count_thirty,
        calculate_date,topic_second_type,topic_status,
        acq_time,suggest
    </sql>
    <sql id="Brand_Insert_Column_List">
        topic_id,brand_id,is_related_brand
    </sql>
    <sql id="Category_Insert_Column_List">
        topic_id,category_id,is_related_category
    </sql>

    <insert id="storeBaseBatch">
        UPSERT INTO dim_topic(<include refid="Base_Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.topicId},#{item.pageId},#{item.topicName},#{item.discussNum},
            #{item.viewNum},#{item.shareInfoDesc},#{item.acqTime},#{item.topicStatus})
        </foreach>
    </insert>

    <insert id="storeCalResultBatch">
        UPSERT INTO dim_topic(<include refid="Cal_Result_Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.topicId},#{item.topicName},#{item.collectionTime},
            #{item.pageId},#{item.isBrandTopic},#{item.shareInfoDesc},
            #{item.topicType},#{item.topicTypeMulti},#{item.viewNum},#{item.discussNum},
            #{item.isInvalid},#{item.noteNumThirty},#{item.interactiveCountThirty},
            #{item.calculateDate},#{item.topicSecondType},#{item.topicStatus},
            #{item.acqTime},#{item.suggest})
        </foreach>
    </insert>

    <sql id="Cal_Result_Insert_Column_List_Test">
        topic_id,topic_name,collection_time,
        page_id,is_brand_topic,share_info_desc,
        topic_type,topic_type_multi,view_num,discuss_num,
        is_invalid,note_num_thirty,interactive_count_thirty,
        calculate_date,topic_second_type,topic_status,
        acq_time,suggest
    </sql>
    <insert id="storeCalResult">
        UPSERT INTO dim_topic(<include refid="Cal_Result_Insert_Column_List_Test"/>)
        VALUES
            (#{item.topicId},#{item.topicName},#{item.collectionTime},
            #{item.pageId},#{item.isBrandTopic},#{item.shareInfoDesc},
            #{item.topicType},#{item.topicTypeMulti},#{item.viewNum},#{item.discussNum},
            #{item.isInvalid},#{item.noteNumThirty},#{item.interactiveCountThirty},
            #{item.calculateDate},#{item.topicSecondType},#{item.topicStatus},
            #{item.acqTime},#{item.suggest})
    </insert>


    <insert id="storeBrandRecBatch">
        UPSERT INTO dim_topic(<include refid="Brand_Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.topicId},#{item.brandId},#{item.isRelatedBrand})
        </foreach>
    </insert>

    <insert id="storeCategoryRecBatch">
        UPSERT INTO dim_topic(<include refid="Category_Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.topicId},#{item.cateGoryId},#{item.isRelatedCateGory})
        </foreach>
    </insert>
</mapper>
