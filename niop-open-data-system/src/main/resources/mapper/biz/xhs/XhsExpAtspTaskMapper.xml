<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpAtspTaskMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspTask">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="execDate" column="exec_date" jdbcType="VARCHAR"/>
        <result property="param" column="param" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="INTEGER"/>
        <result property="nextExecTime" column="next_exec_time" jdbcType="VARCHAR"/>
        <result property="page" column="page" jdbcType="INTEGER"/>
        <result property="bizCode" column="biz_code" jdbcType="INTEGER"/>
        <result property="bizMsg" column="biz_Msg" jdbcType="VARCHAR"/>
        <result property="topicWeight" column="topic_weight" javaType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        task_id,parent_id, exec_date, param, task_status,next_exec_time,page, biz_code, biz_msg,topic_weight
    </sql>
    <sql id="Insert_Column_List">
        task_id,parent_id, exec_date, param, task_status,next_exec_time,page,topic_weight
    </sql>
    <sql id="Select_Column_List">
        task_id,parent_id, exec_date, param, task_status,next_exec_time,page, biz_code, biz_msg,topic_weight
    </sql>


    <update id="createTable">
        CREATE TABLE
            IF NOT EXISTS
            niop_data_xhs_expansion_topic_atsp_task_${partition}
            PARTITION OF niop_data_xhs_expansion_topic_atsp_task
            FOR VALUES IN ('${execDate}')
    </update>

    <delete id="dropTable">
        DROP TABLE IF EXISTS niop_data_xhs_expansion_topic_atsp_task_${partition}
    </delete>


    <insert id="storeBatch">
        INSERT INTO niop_data_xhs_expansion_topic_atsp_task_${partition}
        (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.taskId},
            #{item.parentId},
            #{item.execDate},
            #{item.param},
            #{item.taskStatus},
            #{item.nextExecTime},
            #{item.page},
            #{item.topicWeight})
        </foreach>
        ON CONFLICT DO NOTHING;
    </insert>


    <select id="countNotSubmitTask" resultType="java.lang.Integer">
        select count(*)
        from niop_data_xhs_expansion_topic_atsp_task
        where task_status = 0
    </select>


    <select id="listNotSubmitTask" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_atsp_task
        where task_status = 0
        order by topic_weight desc,page desc, gmt_create
        limit #{size}
    </select>

    <select id="listNotSubmitTaskByCursor" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_atsp_task
        where task_status = 0
        and task_id > #{cursor}
        order by task_id
        limit #{size}
    </select>

    <update id="updateTaskStatus">
        update niop_data_xhs_expansion_topic_atsp_task
        set task_id      = #{task.taskId},
            task_status  = #{task.taskStatus},
            gmt_modified = now()
        where exec_date = #{task.execDate}
          and task_id = #{taskId}
    </update>


    <select id="getTasksByIds" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_atsp_task
        where task_id in (
        <foreach collection="taskIds" item="taskId" index="index" separator=",">
            #{taskId}
        </foreach>
        )
    </select>

    <select id="getParentCurrentPageNum" resultType="java.lang.Integer">
        select count(*)
        from niop_data_xhs_expansion_topic_atsp_task
        where parent_id = #{parentId}
          and task_status = 2
    </select>

    <select id="listRunningTask" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_atsp_task
        where exec_date = #{execDate} and task_status = 1
            and task_id > #{cursor}
        order by task_id
        limit #{size}
    </select>

    <select id="listTasks" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_atsp_task
        where exec_date = #{execDate}
    </select>
</mapper>
