<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsUserLmMapper">

    <resultMap id="XhsUserLdmMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.XhsUserLdm">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="storeBatch">
        UPSERT INTO dim_extend_user(user_id,update_time) VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.userId}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="getExistUserIds" resultMap="XhsUserLdmMap">
        SELECT user_id,update_time
        FROM dim_extend_user
        WHERE user_id in
        <foreach item="item" collection="items" index="index" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
