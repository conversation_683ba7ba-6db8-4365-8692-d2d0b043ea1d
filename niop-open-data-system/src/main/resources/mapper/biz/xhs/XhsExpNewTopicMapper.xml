<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpNewTopicMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpNewTopic">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="addDate" column="add_date" jdbcType="VARCHAR"/>
        <result property="topicId" column="topic_id" jdbcType="VARCHAR"/>
        <result property="opusNum" column="opus_num" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,add_date, topic_id ,opus_num
    </sql>
    <sql id="Insert_Column_List">
        add_date, topic_id ,opus_num
    </sql>
    <sql id="Select_Column_List">
        id, add_date, topic_id ,opus_num
    </sql>

    <insert id="storeBatch">
        INSERT INTO niop_data_xhs_expansion_weight_new_topic
            (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.addDate},#{item.topicId},#{item.opusNum})
        </foreach>
        ON CONFLICT (add_date,topic_id) DO NOTHING;
    </insert>


    <update id="updateTopicOpusNum">
        update niop_data_xhs_expansion_weight_new_topic
        set opus_num = opus_num + #{newOpusNum}, gmt_modified = now()
        where add_date = #{item.addDate}
          and topic_id = #{item.topicId}
    </update>

    <select id="getExistByTopicIds" resultMap="BaseResultMap">
        select <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_weight_new_topic
        where add_date = #{addDate} and topic_id in
        (<foreach collection="items" item="item" index="index" separator=",">
        #{item}
        </foreach>)
    </select>

    <select id="getTopicIdLimitOpusNum" resultMap="BaseResultMap">
        select id,topic_id
        from niop_data_xhs_expansion_weight_new_topic
        where add_date = #{addDate}
          and opus_num > #{opusNum}
          and id > #{cursor}
        order by id
        limit #{size}
    </select>
</mapper>
