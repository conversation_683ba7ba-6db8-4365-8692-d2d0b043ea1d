<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpConfigMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="topicWeight" column="topic_weight" jdbcType="INTEGER"/>
        <result property="atspWeight" column="atsp_weight" jdbcType="INTEGER"/>
        <result property="opusNumRange" column="opus_num_range" jdbcType="VARCHAR"/>
        <result property="interval" column="interval" jdbcType="INTEGER"/>
        <result property="publishTimePeriod" column="publish_time_period" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, topic_weight, atsp_weight, opus_num_range, interval, publish_time_period
    </sql>
    <sql id="Select_Column_List">
        id, topic_weight, atsp_weight, opus_num_range, interval, publish_time_period
    </sql>

    <select id="listConfigs" resultMap="BaseResultMap">
        select <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_config
    </select>

    <select id="getConfig" resultMap="BaseResultMap">
        select <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_config
        where topic_weight = #{topicWeight}
    </select>
</mapper>
