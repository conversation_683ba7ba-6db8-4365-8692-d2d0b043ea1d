<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpParentTaskMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpParentTask">
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="topicId" column="topic_id" jdbcType="VARCHAR"/>
        <result property="topicWeight" column="topic_weight" jdbcType="INTEGER"/>
        <result property="execDate" column="exec_date" jdbcType="DATE"/>
        <result property="taskStatus" column="task_status" jdbcType="INTEGER"/>
        <result property="triggerTime" column="trigger_time" jdbcType="VARCHAR"/>
        <result property="minPublishTime" column="min_publish_time" jdbcType="VARCHAR"/>
        <result property="currentPageNum" column="current_page_num" jdbcType="VARCHAR"/>
        <result property="currentOpusNum" column="current_opus_num" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        parent_id,topic_id,topic_weight, exec_date, task_status,trigger_time, min_publish_time,current_page_num,current_opus_num
    </sql>
    <sql id="Insert_Column_List">
        parent_id,topic_id,topic_weight, exec_date, task_status,trigger_time, min_publish_time,current_page_num,current_opus_num
    </sql>
    <sql id="Select_Column_List">
        parent_id,topic_id,topic_weight, exec_date, task_status,trigger_time, min_publish_time,current_page_num,current_opus_num
    </sql>


    <update id="createTable">
        CREATE TABLE
        IF NOT EXISTS
        niop_data_xhs_expansion_topic_parent_task_${partition}
        PARTITION OF niop_data_xhs_expansion_topic_parent_task
        FOR VALUES IN ('${execDate}')
    </update>

    <delete id="dropTable">
        DROP TABLE IF EXISTS niop_data_xhs_expansion_topic_parent_task_${partition}
    </delete>

    <insert id="storeBatch">
        INSERT INTO niop_data_xhs_expansion_topic_parent_task_${partition}
        (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.parentId},
            #{item.topicId},
            #{item.topicWeight},
            #{item.execDate,jdbcType=DATE},
            #{item.taskStatus},
            #{item.triggerTime},
            #{item.minPublishTime},
            #{item.currentPageNum},
            #{item.currentOpusNum})
        </foreach>
        ON CONFLICT DO NOTHING;
    </insert>
    <select id="getParentTasksByIds" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_parent_task
        where exec_date = #{execDate}
            and parent_id in (
        <foreach collection="parentIds" item="parentId" index="index" separator=",">
            #{parentId}
        </foreach>)
    </select>

    <select id="getRunningParentTasksByIds" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_topic_parent_task
        where exec_date = #{execDate} and task_status = 0
        and parent_id in (
        <foreach collection="parentIds" item="parentId" index="index" separator=",">
            #{parentId}
        </foreach>)
    </select>

    <update id="updateParentTaskStatus">
        update niop_data_xhs_expansion_topic_parent_task
        set task_status = #{task.taskStatus},current_opus_num = #{task.currentOpusNum}, gmt_modified = now()
        where exec_date = #{task.execDate}
          and parent_id = #{task.parentId}
    </update>
    <update id="updateParentTaskValue">
        update niop_data_xhs_expansion_topic_parent_task
        set current_page_num = #{task.currentPageNum} , current_opus_num = #{task.currentOpusNum}, gmt_modified = now()
        where exec_date = #{task.execDate}
          and parent_id = #{task.parentId}
    </update>
    <select id="getParentIdByTopicId" resultType="java.lang.String">
        select parent_id
        from niop_data_xhs_expansion_topic_parent_task
        where exec_date = #{execDate}
          and topic_id = #{topicId}
    </select>
</mapper>
