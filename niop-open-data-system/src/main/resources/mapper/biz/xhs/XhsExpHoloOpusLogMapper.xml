<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpHoloOpusLogMapper">

    <sql id="Base_Column_List">
        opus_id,publish_time, uid,task_id, parent_task_id, page_id,acq_time,ds,gmt_create,gmt_modified
    </sql>
    <sql id="Insert_Column_List">
        opus_id,publish_time, uid, task_id, parent_task_id, page_id,acq_time,ds,gmt_create,gmt_modified
    </sql>


    <insert id="storeBatch">
        INSERT INTO ${xhs-topic-opus-schema}.dwd_expansion_topic_opus_log
            (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            ( #{item.opusId},#{item.publishTime},#{item.uid},
             #{taskId},
             #{parentId},
             #{topicId},
             #{acqTime},
             #{ds},#{dateTime},#{dateTime})
        </foreach>
        ON CONFLICT(page_id,opus_id,task_id,ds) DO NOTHING
    </insert>

    <select id="getOpusNumByParentTaskId" resultType="java.lang.Integer">
        select count(*)
        from  ${xhs-topic-opus-schema}.dwd_expansion_topic_opus_log
        where ds = #{ds} and parent_task_id = #{parentTaskId}
    </select>
</mapper>
