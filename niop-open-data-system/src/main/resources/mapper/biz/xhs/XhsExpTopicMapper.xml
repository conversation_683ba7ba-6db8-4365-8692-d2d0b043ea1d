<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTopicMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="topicId" column="topic_id" jdbcType="VARCHAR"/>
        <result property="topicWeight" column="topic_weight" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,topic_id, topic_weight
    </sql>
    <sql id="Insert_Column_List">
        topic_id, topic_weight
    </sql>
    <sql id="Select_Column_List">
        id,topic_id, topic_weight
    </sql>
    <insert id="storeBatch">
        INSERT INTO niop_data_xhs_expansion_weight_topic
            (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.topicId},#{item.topicWeight})
        </foreach>
        ON CONFLICT (topic_id) DO NOTHING;
    </insert>
    <select id="listByWeight" resultMap="BaseResultMap">
        select
        <include refid="Select_Column_List"/>
        from niop_data_xhs_expansion_weight_topic
        where
        topic_weight = #{weight}
        and
        id > #{cursor}
        order by id asc
        limit #{size}
    </select>
    <update id="updateTopicWeight">
        update niop_data_xhs_expansion_weight_topic
        set topic_weight = #{weight},gmt_modified = now()
        where topic_id = #{topicId}
    </update>

    <select id="getTopicWeight" resultType="java.lang.Integer">
        select topic_weight
        from niop_data_xhs_expansion_weight_topic
        where topic_id = #{topicId}
    </select>


    <select id="listExistTopicIds" resultType="java.lang.String">
        select topic_id
        from niop_data_xhs_expansion_weight_topic
        where topic_id in
        <foreach collection="items" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
