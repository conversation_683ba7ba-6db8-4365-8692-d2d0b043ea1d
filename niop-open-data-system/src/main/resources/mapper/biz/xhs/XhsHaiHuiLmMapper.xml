<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.XhsHaiHuiLmMapper">

    <resultMap id="HaiHuiResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.haihui.XhsHaiHuiOpus">
        <result property="opusId" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="userid" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="desc" column="desc" jdbcType="VARCHAR"/>
        <result property="officialKeyword" column="official_keyword" jdbcType="VARCHAR"/>
        <result property="cooperateBindsName" column="cooperate_binds_name" jdbcType="VARCHAR"/>
        <result property="topicsName" column="topics_name" jdbcType="VARCHAR"/>
        <result property="publishTime" column="publish_time" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="price" column="price" jdbcType="INTEGER"/>

        <result property="location" column="location" jdbcType="VARCHAR"/>
        <result property="ipLocation" column="ip_location" jdbcType="VARCHAR"/>
        <result property="mcnName" column="mcn_name" jdbcType="VARCHAR"/>
        <result property="fansNum" column="fans_num" jdbcType="INTEGER"/>
        <result property="personalTags" column="personal_tags" jdbcType="VARCHAR"/>
        <result property="featureTags" column="feature_tags" jdbcType="VARCHAR"/>
        <result property="contentTags" column="contentTags" jdbcType="VARCHAR"/>

        <result property="picturePrice" column="picture_price" jdbcType="DECIMAL"/>
        <result property="videoPrice" column="video_price" jdbcType="DECIMAL"/>
        <result property="pictureReadCost" column="picture_read_cost" jdbcType="DECIMAL"/>
        <result property="videoReadCost" column="video_read_cost" jdbcType="DECIMAL"/>
        <result property="fans30GrowthRate" column="fans_30_growth_rate" jdbcType="DECIMAL"/>
        <result property="normalPvAllImpMedian" column="normal_pv_all_imp_median" jdbcType="BIGINT"/>
        <result property="normalPvAllReadMedian" column="normal_pv_all_read_median" jdbcType="BIGINT"/>
        <result property="normalPvAllInteractionMedian" column="normal_pv_all_interaction_median" jdbcType="BIGINT"/>
        <result property="normalPvAllLikeMedian" column="normal_pv_all_like_median" jdbcType="BIGINT"/>
        <result property="normalPvAllCollectMedian" column="normal_pv_all_collect_median" jdbcType="BIGINT"/>
        <result property="normalPvAllCommentMedian" column="normal_pv_all_comment_median" jdbcType="BIGINT"/>
        <result property="normalPvAllShareMedian" column="normal_pv_all_share_median" jdbcType="BIGINT"/>
        <result property="highPercentFansAge" column="high_percent_fans_age" jdbcType="VARCHAR"/>
        <result property="highPercentFansDevice" column="high_percent_fans_device" jdbcType="VARCHAR"/>
        <result property="highPercentFansArea" column="high_percent_fans_area" jdbcType="VARCHAR"/>

        <result property="normalPvAllNoteNumber" column="normal_pv_all_note_number" jdbcType="INTEGER"/>
        <result property="normalPvAllNoteType" column="normal_pv_all_note_type" jdbcType="VARCHAR"/>
        <result property="normalPvAllThousandLikePercent" column="normal_pv_all_thousand_like_percent" jdbcType="DECIMAL"/>
        <result property="normalPvAllHundredLikePercent" column="normal_pv_all_hundred_like_percent" jdbcType="DECIMAL"/>
        <result property="cooperatePvAllNoteNumber" column="cooperate_pv_all_note_number" jdbcType="INTEGER"/>
        <result property="cooperatePvAllNoteType" column="cooperate_pv_all_note_type" jdbcType="VARCHAR"/>
        <result property="cooperatePvAllImpMedian" column="cooperate_pv_all_imp_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllReadMedian" column="cooperate_pv_all_read_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllInteractionMedian" column="cooperate_pv_all_interaction_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllLikeMedian" column="cooperate_pv_all_like_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllCollectMedian" column="cooperate_pv_all_collect_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllCommentMedian" column="cooperate_pv_all_comment_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllShareMedian" column="cooperate_pv_all_share_median" jdbcType="BIGINT"/>
        <result property="cooperatePvAllInteractionRate" column="cooperate_pv_all_interaction_rate" jdbcType="DECIMAL"/>
        <result property="cooperatePvAllThousandLikePercent" column="cooperate_pv_all_thousand_like_percent" jdbcType="DECIMAL"/>
        <result property="cooperatePvAllHundredLikePercent" column="cooperate_pv_all_hundred_like_percent" jdbcType="DECIMAL"/>
        <result property="coverSummary" column="cover_summary" jdbcType="VARCHAR"/>
        <result property="textField" column="text_field" jdbcType="VARCHAR"/>
        <result property="normalPvAllInteractionRate" column="normal_pv_all_interaction_rate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Insert_Column_List">
        id,
        userid,
        title,
        `desc`,
        official_keyword,
        cooperate_binds_name,
        topics_name,
        publish_time,
        gender,price,
        location,
        ip_location,
        mcn_name,
        fans_num,
        personal_tags,
        feature_tags,
        content_tags,
        picture_price,
        video_price,
        picture_read_cost,
        video_read_cost,
        fans30_growth_rate,
        normal_pv_all_imp_median,
        normal_pv_all_read_median,
        normal_pv_all_interaction_median,
        normal_pv_all_like_median,
        normal_pv_all_collect_median,
        normal_pv_all_comment_median,
        normal_pv_all_share_median,
        normal_pv_all_interaction_rate,
        high_percent_fans_age,
        high_percent_fans_device,
        high_percent_fans_area,
        text_field,
        normal_pv_all_note_number,
        normal_pv_all_note_type,
        normal_pv_all_thousand_like_percent,
        normal_pv_all_hundred_like_percent,
        cooperate_pv_all_note_number,
        cooperate_pv_all_note_type,
        cooperate_pv_all_imp_median,
        cooperate_pv_all_read_median,
        cooperate_pv_all_interaction_median,
        cooperate_pv_all_like_median,
        cooperate_pv_all_collect_median,
        cooperate_pv_all_comment_median,
        cooperate_pv_all_share_median,
        cooperate_pv_all_interaction_rate,
        cooperate_pv_all_thousand_like_percent,
        cooperate_pv_all_hundred_like_percent,
        cover_summary
    </sql>

    <sql id="Insert_Field_Column_List">
        id,
        text_field,
        cover_summary
    </sql>

    <insert id="saveBatch">
        UPSERT INTO dim_xhs_business_opus(<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},
            #{item.userId},
            #{item.title},
            #{item.desc},
            #{item.officialKeyword},
            #{item.cooperateBindsName},
            #{item.topicsName},
            #{item.publishTime},
            #{item.gender},
            #{item.price},
            #{item.location},
            #{item.ipLocation},
            #{item.mcnName},
            #{item.fansNum},
            #{item.personalTags},
            #{item.featureTags},
            #{item.contentTags},
            #{item.picturePrice},
            #{item.videoPrice},
            #{item.pictureReadCost},
            #{item.videoReadCost},
            #{item.fans30GrowthRate},
            #{item.normalPvAllImpMedian},
            #{item.normalPvAllReadMedian},
            #{item.normalPvAllInteractionMedian},
            #{item.normalPvAllLikeMedian},
            #{item.normalPvAllCollectMedian},
            #{item.normalPvAllCommentMedian},
            #{item.normalPvAllShareMedian},
            #{item.normalPvAllInteractionRate},
            #{item.highPercentFansAge},
            #{item.highPercentFansDevice},
            #{item.highPercentFansArea},
            #{item.textField},

            #{item.normalPvAllNoteNumber},
            #{item.normalPvAllNoteType},
            #{item.normalPvAllThousandLikePercent},
            #{item.normalPvAllHundredLikePercent},
            #{item.cooperatePvAllNoteNumber},
            #{item.cooperatePvAllNoteType},
            #{item.cooperatePvAllImpMedian},
            #{item.cooperatePvAllReadMedian},
            #{item.cooperatePvAllInteractionMedian},
            #{item.cooperatePvAllLikeMedian},
            #{item.cooperatePvAllCollectMedian},
            #{item.cooperatePvAllCommentMedian},
            #{item.cooperatePvAllShareMedian},
            #{item.cooperatePvAllInteractionRate},
            #{item.cooperatePvAllThousandLikePercent},
            #{item.cooperatePvAllHundredLikePercent},
            #{item.coverSummary})
        </foreach>
    </insert>

    <insert id="addFieldSaveBatch">
        UPSERT INTO dim_xhs_business_opus(<include refid="Insert_Field_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},
            #{item.textField},
            #{item.coverSummary})
        </foreach>
    </insert>
    <insert id="saveBatchUser">
        UPSERT INTO dim_xhs_business_opus(id,gender)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},#{item.gender})
        </foreach>
    </insert>

    <select id="searchOpusBatch" resultMap="HaiHuiResultMap">
        select
        id,
        text_field,
        cover_summary
        from dim_xhs_business_opus
        where id in
        <foreach item="item" collection="items" index="index" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="pageXhsOpus" resultMap="HaiHuiResultMap">
        SELECT  id, userid, gender
        FROM dim_xhs_business_opus
        WHERE id > #{cursor}
        ORDER BY id
        LIMIT #{size}

    </select>
</mapper>
