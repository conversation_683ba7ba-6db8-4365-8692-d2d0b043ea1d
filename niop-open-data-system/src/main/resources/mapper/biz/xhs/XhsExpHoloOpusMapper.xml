<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpHoloOpusMapper">


    <resultMap id="XhsOpusFromMultiResultMap" type="cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopicOpus">
        <id property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="publishTime" column="publish_time" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="desc" column="desc" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="images" column="images" jdbcType="VARCHAR"/>
        <result property="imagesList" column="images_list" jdbcType="VARCHAR"/>
        <result property="hashTags" column="hash_tags" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="token" column="token" jdbcType="VARCHAR"/>
        <result property="collectNum" column="collect_num" jdbcType="BIGINT"/>
        <result property="commentNum" column="comment_num" jdbcType="BIGINT"/>
        <result property="likes" column="likes" jdbcType="BIGINT"/>
        <result property="shareNum" column="share_num" jdbcType="BIGINT"/>
        <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP"/>
        <result property="editTime" column="edit_time" jdbcType="TIMESTAMP"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="width" column="width" jdbcType="INTEGER"/>
        <result property="height" column="height" jdbcType="INTEGER"/>
        <result property="videoId" column="video_id" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        opus_id,task_id, parent_task_id, page_id, es_exist_flag,
        publish_time, uid, title,"desc",nickname,images,images_list,
        hash_tags,type,token,collect_num,comment_num,likes,share_num,
        edit_time,video_url,acq_time,width,height,video_id,duration,cover
    </sql>
    <sql id="Insert_Column_List">
        opus_id,publish_time, uid, title,
        "desc",nickname,images,images_list,
            hash_tags,type,token,collect_num,comment_num,likes,share_num,
            edit_time,video_url,width,height,video_id,duration,cover,
            task_id, parent_task_id, page_id,acq_time,ds,gmt_create,gmt_modified
    </sql>


    <insert id="storeBatchTest">
        INSERT INTO  ${xhs-topic-opus-schema}.dwd_expansion_topic_opus_test
            (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},#{item.publishTime},#{item.uid},#{item.title},#{item.desc},#{item.nickname},#{item.images},#{item.imagesList},
             #{item.hashTags},#{item.type},#{item.token},#{item.collectNum},#{item.commentNum},#{item.likes},#{item.shareNum},
             #{item.editTime},#{item.videoUrl},#{item.width},#{item.height},#{item.videoId},#{item.duration},#{item.cover},
            #{taskId},#{parentId},#{topicId},#{acqTime},#{ds},#{dateTime},#{dateTime})
        </foreach>
        ON CONFLICT DO NOTHING
    </insert>

    <insert id="storeBatch">
        INSERT INTO  ${xhs-topic-opus-schema}.dwd_expansion_topic_opus
        (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.opusId},#{item.publishTime},#{item.uid},#{item.title},#{item.desc},#{item.nickname},#{item.images},#{item.imagesList},
            #{item.hashTags},#{item.type},#{item.token},#{item.collectNum},#{item.commentNum},#{item.likes},#{item.shareNum},
            #{item.editTime},#{item.videoUrl},#{item.width},#{item.height},#{item.videoId},#{item.duration},#{item.cover},
            #{taskId},#{parentId},#{topicId},#{acqTime},#{ds},#{dateTime},#{dateTime})
        </foreach>
        ON CONFLICT DO NOTHING
    </insert>

    <select id="getOpusNumByParentTaskId" resultType="java.lang.Integer">
        select count(*)
        from  ${xhs-topic-opus-schema}.dwd_expansion_topic_opus
        where parent_task_id = #{parentTaskId}
    </select>


    <select id="getOpusNumIsExist" resultType="java.lang.Integer">
        select count(*)
        from ${xhs-topic-opus-schema}.dwd_expansion_topic_opus
        where (ds = to_char(current_date, 'YYYYMMDD') OR ds = to_char(current_date - interval '1 day', 'YYYYMMDD'))
        AND opus_id in (
        <foreach collection="items" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>


    <select id="listOpusIds" resultMap="XhsOpusFromMultiResultMap">
        select *
        from ${xhs-topic-opus-schema}.dwd_expansion_topic_opus
        where ds = #{ds} and opus_id > #{cursor}
        order by opus_id
        limit #{size}
    </select>


    <select id="isUserExist" resultType="java.lang.String">
        select DISTINCT uid
        from ${xhs-topic-opus-schema}.dwd_expansion_topic_opus
        where ds > #{ds} and uid in (
        <foreach collection="items" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>
</mapper>
