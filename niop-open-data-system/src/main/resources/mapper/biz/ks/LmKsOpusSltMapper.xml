<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusSltMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus">
        <result property="photoId" column="photo_id" jdbcType="VARCHAR"/>
        <result property="anaDel" column="ana_del" jdbcType="INTEGER"/>
        <result property="anaTags" column="ana_tags" jdbcType="VARCHAR"/>
        <result property="awemePortrait" column="aweme_portrait" jdbcType="VARCHAR"/>
        <result property="awemePortraitUpdateTime" column="aweme_portrait_update_time" jdbcType="TIMESTAMP"/>
        <result property="baseInsertTime" column="base_insert_time" jdbcType="TIMESTAMP"/>
        <result property="caption" column="caption" jdbcType="VARCHAR"/>
        <result property="collectCount" column="collect_count" jdbcType="BIGINT"/>
        <result property="commentCount" column="comment_count" jdbcType="BIGINT"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="coverType" column="cover_type" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="BIGINT"/>
        <result property="extParams" column="ext_params" jdbcType="VARCHAR"/>
        <result property="forwardCount" column="forward_count" jdbcType="BIGINT"/>
        <result property="headurls" column="headurls" jdbcType="VARCHAR"/>
        <result property="headurl" column="headurl" jdbcType="VARCHAR"/>
        <result property="imageUrls" column="image_urls" jdbcType="VARCHAR"/>
        <result property="insertTime" column="insert_time" jdbcType="TIMESTAMP"/>
        <result property="isHotAweme" column="is_hot_aweme" jdbcType="INTEGER"/>
        <result property="isLowHot" column="is_low_hot" jdbcType="INTEGER"/>
        <result property="isPromotion" column="is_promotion" jdbcType="INTEGER"/>
        <result property="kwaiId" column="kwai_id" jdbcType="VARCHAR"/>
        <result property="likeCount" column="like_count" jdbcType="BIGINT"/>
        <result property="mainMvUrls" column="main_mv_urls" jdbcType="VARCHAR"/>
        <result property="merchant" column="merchant" jdbcType="VARCHAR"/>
        <result property="mtype" column="mtype" jdbcType="INTEGER"/>
        <result property="music" column="music" jdbcType="VARCHAR"/>
        <result property="ownerCount" column="owner_count" jdbcType="BIGINT"/>
        <result property="poi" column="poi" jdbcType="VARCHAR"/>
        <result property="screenType" column="screen_type" jdbcType="VARCHAR"/>
        <result property="shareCount" column="share_count" jdbcType="BIGINT"/>
        <result property="shareInfo" column="share_info" jdbcType="VARCHAR"/>
        <result property="soundTrack" column="sound_track" jdbcType="VARCHAR"/>
        <result property="standardSerial" column="standard_serial" jdbcType="VARCHAR"/>
        <result property="streamManifest" column="stream_manifest" jdbcType="VARCHAR"/>
        <result property="subMtype" column="sub_mtype" jdbcType="VARCHAR"/>
        <result property="time" column="time" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="BIGINT"/>
        <result property="unlikeCount" column="unlike_count" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="userFan" column="user_fan" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="verifiedDetail" column="verified_detail" jdbcType="VARCHAR"/>
        <result property="viewCount" column="view_count" jdbcType="BIGINT"/>
        <result property="vpf" column="vpf" jdbcType="DOUBLE"/>
        <result property="awemeAcqDataType" column="aweme_acq_data_type" jdbcType="VARCHAR"/>
        <result property="anaTime" column="ana_time" jdbcType="TIMESTAMP"/>
        <result property="awemePortraitAcqTime" column="aweme_portrait_acq_time" jdbcType="TIMESTAMP"/>
        <result property="flinkAwemeSyncTime" column="flink_aweme_sync_time" jdbcType="TIMESTAMP"/>
        <result property="mcAwemeSyncTime" column="mc_aweme_sync_time" jdbcType="TIMESTAMP"/>
        <result property="inDaily" column="in_daily" jdbcType="BIGINT"/>
        <result property="hasMerchant" column="has_merchant" jdbcType="BOOLEAN"/>
        <result property="originalPhotoId" column="original_photo_id" jdbcType="VARCHAR"/>
        <result property="disclaimerMessage" column="disclaimer_message" jdbcType="VARCHAR"/>
        <result property="merchantAdType" column="merchant_ad_type" jdbcType="VARCHAR"/>
        <result property="soundTrackId" column="sound_track_id" jdbcType="VARCHAR"/>
        <result property="musicId" column="music_id" jdbcType="VARCHAR"/>
        <result property="searchTags" column="search_tags" jdbcType="VARCHAR"/>
        <result property="userIdL" column="user_id_l" jdbcType="BIGINT"/>
        <result property="categoryLv1" column="category_lv1" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        photo_id,ana_del,ana_tags,
        aweme_portrait,aweme_portrait_update_time,base_insert_time,
        caption,collect_count,comment_count,
        cover,cover_type,duration,
        ext_params,forward_count,headurls,
        headurl,image_urls,insert_time,
        is_hot_aweme,is_low_hot,is_promotion,
        kwai_id,like_count,main_mv_urls,
        merchant,mtype,music,
        owner_count,poi,screen_type,
        share_count,share_info,sound_track,
        standard_serial,stream_manifest,sub_mtype,
        time,type,unlike_count,
        update_time,user_fan,user_id,
        user_name,user_type,verified_detail,
        view_count,vpf,aweme_acq_data_type,
        ana_time,aweme_portrait_acq_time,
        flink_aweme_sync_time,mc_aweme_sync_time,in_daily,has_merchant,original_photo_id,
        disclaimer_message, merchant_ad_type, music_id, sound_track_id, search_tags, user_id_l, category_lv1
    </sql>
    <insert id="storeBasicBatch">
        UPSERT INTO dim_opus_youshu_slt_custom(photo_id,ana_del,ana_tags,
        aweme_portrait,aweme_portrait_update_time,base_insert_time,
        caption,collect_count,comment_count,
        cover,cover_type,duration,
        ext_params,forward_count,headurls,
        headurl,image_urls,insert_time,
        is_hot_aweme,is_low_hot,is_promotion,
        kwai_id,like_count,main_mv_urls,
        merchant,mtype,music,
        owner_count,poi,screen_type,
        share_count,share_info,sound_track,
        standard_serial,stream_manifest,sub_mtype,
        `time`,type,unlike_count,
        update_time,user_fan,user_id,
        user_name,user_type,verified_detail,
        view_count,vpf,aweme_acq_data_type,
        ana_time,aweme_portrait_acq_time,
        flink_aweme_sync_time,mc_aweme_sync_time,in_daily, has_merchant, original_photo_id,
        disclaimer_message,  merchant_ad_type, music_id, sound_track_id, search_tags, user_id_l)
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.photoId},#{item.anaDel},#{item.anaTags},
            #{item.awemePortrait},#{item.awemePortraitUpdateTime},#{item.baseInsertTime},
            #{item.caption},#{item.collectCount},#{item.commentCount},
            #{item.cover},#{item.coverType},#{item.duration},
            #{item.extParams},#{item.forwardCount},#{item.headurls},
            #{item.headurl},#{item.imageUrls},#{item.insertTime},
            #{item.isHotAweme},#{item.isLowHot},#{item.isPromotion},
            #{item.kwaiId},#{item.likeCount},#{item.mainMvUrls},
            #{item.merchant},#{item.mtype},#{item.music},
            #{item.ownerCount},#{item.poi},#{item.screenType},
            #{item.shareCount},#{item.shareInfo},#{item.soundTrack},
            #{item.standardSerial},#{item.streamManifest},#{item.subMtype},
            #{item.time},#{item.type},#{item.unlikeCount},
            #{item.updateTime},#{item.userFan},#{item.userId},
            #{item.userName},#{item.userType},#{item.verifiedDetail},
            #{item.viewCount},#{item.vpf},#{item.awemeAcqDataType},
            #{item.anaTime},#{item.awemePortraitAcqTime},
            #{item.flinkAwemeSyncTime},#{item.mcAwemeSyncTime},#{item.inDaily},
            #{item.hasMerchant},#{item.originalPhotoId},#{item.disclaimerMessage},
            #{item.merchantAdType},#{item.musicId},#{item.soundTrackId},#{item.searchTags},#{item.userIdL}
            )
        </foreach>
    </insert>
</mapper>
