<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokAccountMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokAccount">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="secUid" column="sec_uid" jdbcType="VARCHAR"/>
        <result property="fansNum" column="fans_num" jdbcType="INTEGER"/>
        <result property="followNum" column="follow_num" jdbcType="INTEGER"/>
        <result property="opusNum" column="opus_num" jdbcType="INTEGER"/>
        <result property="likeNum" column="like_num" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,account,uid,
        nickname,avatar,sec_uid,region,
        fans_num,follow_num,opus_num,
        like_num,update_time,gmt_create,
        gmt_modified,version,sample_id,sample_status,avatar_url
    </sql>
    <insert id="save">
        insert into niop_data_biz_tiktok_account (account, uid,
        nickname, avatar, sec_uid,region,
        fans_num, follow_num, opus_num,
        like_num, update_time, gmt_create,
        gmt_modified, version, sample_id,sample_status,avatar_url)
        values
        <foreach collection="items" item="item" separator=",">
            (#{item.sampleId}, #{item.uid},
            #{item.nickname}, #{item.avatar}, #{item.secUid},#{item.region},
            #{item.fansNum}, #{item.followNum}, #{item.opusNum},
            #{item.likeNum}, #{item.updateTime}, now(),
            now(),  #{item.version}, #{item.sampleId},#{item.sampleStatus},#{item.avatarUrl})
        </foreach>
        ON CONFLICT (sample_id) DO UPDATE SET
        uid = EXCLUDED.uid,
        account = EXCLUDED.sample_id,
        nickname = EXCLUDED.nickname,
        avatar = EXCLUDED.avatar,
        sec_uid = EXCLUDED.sec_uid,
        region = EXCLUDED.region,
        fans_num = EXCLUDED.fans_num,
        follow_num = EXCLUDED.follow_num,
        opus_num = EXCLUDED.opus_num,
        like_num = EXCLUDED.like_num,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified,
        version = EXCLUDED.version,
        sample_status = EXCLUDED.sample_status,
        avatar_url = EXCLUDED.avatar_url
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_tiktok_account
        where account = #{account}
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_tiktok_account
        where id > #{cursor} and uid is not null and uid != '[]'
        order by id
        limit 200
    </select>
</mapper>
