<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.bz.mapper.BzLiveMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.bz.pojo.BzLive">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
        <result property="fansNum" column="fans_num" jdbcType="BIGINT"/>
        <result property="guardNum" column="guard_num" jdbcType="BIGINT"/>
        <result property="hot" column="hot" jdbcType="BIGINT"/>
        <result property="maxHot" column="max_hot" jdbcType="BIGINT"/>
        <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
        <result property="totalLikeNum" column="total_like_num" jdbcType="BIGINT"/>
        <result property="liveStatus" column="live_status" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="duration" column="duration" jdbcType="TIMESTAMP"/>
        <result property="avgHot" column="avg_hot" jdbcType="BIGINT"/>
        <result property="calculated" column="calculated" jdbcType="BOOLEAN"/>
        <result property="updateTime" column="update_time" jdbcType="BIGINT"/>
        <result property="watchedNum" column="watched_num" jdbcType="BIGINT"/>
        <result property="audienceNum" column="audience_num" jdbcType="BIGINT"/>
        <result property="avgAudienceNum" column="avg_audience_num" jdbcType="BIGINT"/>
        <result property="maxAudienceNum" column="max_audience_num" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,gmt_modified,gmt_create,
        version,sample_id,sample_status,
        fans_num,guard_num,hot, max_hot,avg_hot,
        room_id,total_like_num,live_status,
        end_time,start_time,duration,update_time,calculated,watched_num,
        audience_num,avg_audience_num,max_audience_num
    </sql>

    <insert id="saveAll">
        insert into niop_data_biz_bz_live(sample_id,gmt_modified,gmt_create, version, sample_status,
        fans_num, guard_num, hot, room_id, total_like_num, live_status,avg_hot,
        end_time,start_time,duration, update_time,watched_num, audience_num)
        values
        <foreach collection="items" item="item" separator=",">
            (#{item.sampleId},now(),now(),
            #{item.version},#{item.sampleStatus},
            #{item.fansNum},#{item.guardNum},#{item.hot},
            #{item.roomId},#{item.totalLikeNum},#{item.liveStatus},#{item.avgHot},
            #{item.endTime},#{item.startTime},#{item.duration}, #{item.updateTime},#{item.watchedNum},
            #{item.audienceNum})
        </foreach>
        ON CONFLICT (sample_id) DO UPDATE SET
        sample_status = EXCLUDED.sample_status,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified,
        version = EXCLUDED.version,
        fans_num = EXCLUDED.fans_num,
        guard_num = EXCLUDED.guard_num,
        hot = EXCLUDED.hot,
        room_id = EXCLUDED.room_id,
        total_like_num = EXCLUDED.total_like_num,
        live_status = EXCLUDED.live_status,
        end_time = EXCLUDED.end_time,
        watched_num=EXCLUDED.watched_num,
        audience_num=EXCLUDED.audience_num
    </insert>

    <update id="update">
        update niop_data_biz_bz_live
        set max_hot        = #{maxHot},
            duration       = #{duration},
            total_like_num = #{totalLikeNum},
            gmt_modified   = now(),
            avg_hot        = #{avgHot},
            watched_num    = #{watchedNum},
            avg_audience_num = #{avgAudienceNum},
            max_audience_num = #{maxAudienceNum},
            calculated     = true
        where sample_id = #{sampleId}
    </update>

    <select id="listCalculateLives" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_bz_live
        where live_status = '0' and calculated = false
        order by gmt_create
        limit 100
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_bz_live
        where sample_id in
        <foreach collection="sampleIds" item="sampleId" open="(" separator="," close=")">
            #{sampleId}
        </foreach>
    </select>
</mapper>
