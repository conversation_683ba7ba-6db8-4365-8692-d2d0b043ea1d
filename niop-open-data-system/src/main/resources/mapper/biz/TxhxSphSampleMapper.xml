<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.txhx.mapper.TxhxSphSampleMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.txhx.pojo.TxhxSphSample">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="viewId" column="view_id" jdbcType="VARCHAR"/>
        <result property="avgInteractionCount" column="avg_interaction_count" jdbcType="VARCHAR"/>
        <result property="avgLikeCount" column="avg_like_count" jdbcType="VARCHAR"/>
        <result property="avgReadCount" column="avg_read_count" jdbcType="VARCHAR"/>
        <result property="expectedCpm" column="expected_cpm" jdbcType="VARCHAR"/>
        <result property="interactionRate" column="interaction_rate" jdbcType="VARCHAR"/>
        <result property="longVideoPrice" column="long_video_price" jdbcType="VARCHAR"/>
        <result property="medianReadCount" column="median_read_count" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="playFinishRate" column="play_finish_rate" jdbcType="VARCHAR"/>
        <result property="recentChangedPrice" column="recent_changed_price" jdbcType="VARCHAR"/>
        <result property="shortVideoPrice" column="short_video_price" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,app_id,view_id,
        avg_interaction_count,avg_like_count,avg_read_count,
        expected_cpm,interaction_rate,long_video_price,
        median_read_count,nickname,play_finish_rate,
        recent_changed_price,short_video_price,gmt_create,
        gmt_modified
    </sql>
    <insert id="insertBatch">
        insert into niop_data_biz_txhx_sph_sample(
        app_id,view_id,
        avg_interaction_count,avg_like_count,avg_read_count,
        expected_cpm,interaction_rate,long_video_price,
        median_read_count,nickname,play_finish_rate,
        recent_changed_price,short_video_price,gmt_create,
        gmt_modified
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.appId}, #{item.viewId},
            #{item.avgInteractionCount}, #{item.avgLikeCount}, #{item.avgReadCount},
            #{item.expectedCpm}, #{item.interactionRate}, #{item.longVideoPrice},
            #{item.medianReadCount}, #{item.nickname}, #{item.playFinishRate},
            #{item.recentChangedPrice}, #{item.shortVideoPrice}, now(), now()
            )
        </foreach>
        ON CONFLICT (app_id) DO UPDATE SET
        app_id = EXCLUDED.app_id,
        view_id = EXCLUDED.view_id,
        avg_interaction_count = EXCLUDED.avg_interaction_count,
        avg_like_count = EXCLUDED.avg_like_count,
        avg_read_count = EXCLUDED.avg_read_count,
        expected_cpm = EXCLUDED.expected_cpm,
        interaction_rate = EXCLUDED.interaction_rate,
        long_video_price = EXCLUDED.long_video_price,
        median_read_count = EXCLUDED.median_read_count,
        nickname = EXCLUDED.nickname,
        play_finish_rate = EXCLUDED.play_finish_rate,
        recent_changed_price = EXCLUDED.recent_changed_price,
        short_video_price = EXCLUDED.short_video_price,
        gmt_modified = EXCLUDED.gmt_modified;
    </insert>
    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_txhx_sph_sample
        where app_id = #{appId}
    </select>
</mapper>
