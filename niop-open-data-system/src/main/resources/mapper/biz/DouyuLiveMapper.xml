<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.douyu.mapper.DouyuLiveMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.douyu.pojo.DouyuLive">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
        <result property="clubOrgName" column="club_org_name" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="hot" column="hot" jdbcType="BIGINT"/>
        <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
        <result property="liveStatus" column="live_status" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="BIGINT"/>
        <result property="avgHot" column="avg_hot" jdbcType="NUMERIC"/>
        <result property="maxHot" column="max_hot" jdbcType="BIGINT"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="calculated" column="calculated" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,gmt_modified,gmt_create,
        version,sample_id,sample_status,
        club_org_name,uid,nickname,
        hot,room_id,live_status,
        start_time,end_time,update_time,max_hot,
        avg_hot,duration,calculated
    </sql>

    <insert id="saveAll">
        insert into niop_data_biz_douyu_live(sample_id,gmt_modified,gmt_create, version, sample_status,
        club_org_name, uid, nickname, hot, room_id, live_status,
        start_time, end_time, update_time)
        values
        <foreach collection="items" item="item" separator=",">
            (#{item.sampleId},now(),now(),
            #{item.version},#{item.sampleStatus},
            #{item.clubOrgName},#{item.uid},#{item.nickname},#{item.hot},
            #{item.roomId},#{item.liveStatus},
            #{item.startTime},#{item.endTime},#{item.updateTime})
        </foreach>
        ON CONFLICT (sample_id) DO UPDATE SET
        sample_status = EXCLUDED.sample_status,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified,
        version = EXCLUDED.version,
        club_org_name = EXCLUDED.club_org_name,
        uid = EXCLUDED.uid,
        nickname = EXCLUDED.nickname,
        hot = EXCLUDED.hot,
        room_id = EXCLUDED.room_id,
        live_status = EXCLUDED.live_status,
        end_time = EXCLUDED.end_time
    </insert>

    <update id="update">
        update niop_data_biz_douyu_live
        set avg_hot      = #{avgHot},
            duration     = #{duration},
            max_hot      = #{maxHot},
            gmt_modified = now(),
            calculated   = true
        where sample_id = #{sampleId}
    </update>


    <select id="listCalculateLives" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_douyu_live
        where live_status = '0' and calculated = false
        order by gmt_create
        limit 30
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_douyu_live
        where sample_id in
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>
    </select>


</mapper>
