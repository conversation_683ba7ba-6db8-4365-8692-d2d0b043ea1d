<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.ds.mapper.DsMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.ds.pojo.po.DsEsSyncRecordPo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="platformType" column="platform_type" jdbcType="VARCHAR"/>
        <result property="lastSyncTime" column="last_sync_time" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , platform_type, last_sync_time
        , gmt_modified, gmt_create
    </sql>

    <insert id="insert">
        insert into niop_data_biz_ds_es_sync_record (platform_type, last_sync_time, gmt_create, gmt_modified)
        values (#{platformType}, #{lastSyncTime}, now(), now())
    </insert>

    <update id="update">
        update niop_data_biz_ds_es_sync_record
        set last_sync_time = #{lastSyncTime},
            gmt_modified   = now()
        where platform_type = #{platformType}
    </update>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_ds_es_sync_record
        where platform_type = #{platformType}
    </select>


    <!--    <update id="updateCheckCount">-->
    <!--         update niop_data_biz_ds_es_sync_record-->
    <!--         set open_syn_insert_count = #{openSynInsertCount},-->
    <!--             syn_insert_count=#{synInsertCount},-->
    <!--             gmt_modified   = now(),-->
    <!--             gmt_create=now(),-->
    <!--         where platform_type = #{platformType}-->
    <!--    </update>-->
    <update id="updateCheckCount">
        update niop_data_biz_ds_es_sync_record
        set open_syn_insert_count = #{openSynInsertCount},
            syn_insert_count      = #{synInsertCount},
            gmt_modified          = now()
        where platform_type = #{platformType}
    </update>
</mapper>
