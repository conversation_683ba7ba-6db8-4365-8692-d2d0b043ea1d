<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.wx.mapper.WxOpusMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.wx.pojo.WxOpus">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="oriUrl" column="ori_url" jdbcType="VARCHAR"/>
        <result property="countryName" column="country_name" jdbcType="VARCHAR"/>
        <result property="countryId" column="country_id" jdbcType="VARCHAR"/>
        <result property="provinceName" column="province_name" jdbcType="VARCHAR"/>
        <result property="musicUrl" column="music_url" jdbcType="VARCHAR"/>
        <result property="isAds" column="is_ads" jdbcType="BOOLEAN"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="gifs" column="gifs" jdbcType="VARCHAR"
                typeHandler="cn.newrank.niop.data.common.StringListArrayTypeHandler"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="images" column="images" jdbcType="VARCHAR"
                typeHandler="cn.newrank.niop.data.common.StringListArrayTypeHandler"/>
        <result property="author" column="author" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="oriFlag" column="ori_flag" jdbcType="BOOLEAN"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="biz" column="biz" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="indexUrl" column="index_url" jdbcType="VARCHAR"/>
        <result property="wxId" column="wx_id" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="audioUrl" column="audio_url" jdbcType="VARCHAR"/>
        <result property="publishOrder" column="publish_order" jdbcType="VARCHAR"/>
        <result property="oriAuthor" column="ori_author" jdbcType="VARCHAR"/>
        <result property="sourceUrl" column="source_url" jdbcType="VARCHAR"/>
        <result property="likeNum" column="like_num" jdbcType="INTEGER"/>
        <result property="viewingNum" column="viewing_num" jdbcType="INTEGER"/>
        <result property="shareNum" column="share_num" jdbcType="INTEGER"/>
        <result property="viewNum" column="view_num" jdbcType="INTEGER"/>
        <result property="commentNum" column="comment_num" jdbcType="INTEGER"/>
        <result property="publishTime" column="publish_time" jdbcType="INTEGER"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"
                typeHandler="cn.newrank.niop.data.common.StringListArrayTypeHandler"/>
        <result property="mid" column="mid" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
        <result property="topics" column="topics" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,opus_id,ori_url,
        country_name,country_id,province_name,
        music_url,is_ads,memo,
        title,content,image_url,
        gifs,summary,images,
        author,url,ori_flag,
        avatar,nickname,biz,publish_time,
        description,index_url,wx_id,
        account,video_url,audio_url,
        publish_order,ori_author,source_url,
        like_num,viewing_num,share_num,
        view_num,comment_num,tags,
        mid,update_time,gmt_create,version,
        sample_id,sample_status,
        gmt_modified,topics
    </sql>
    <insert id="insertBatch">
        insert into niop_data_biz_wx_opus(
        opus_id,ori_url,
        country_name,country_id,province_name,
        music_url,is_ads,memo,
        title,content,image_url,
        gifs,summary,images,
        author,url,ori_flag,
        avatar,nickname,biz,publish_time,
        description,index_url,wx_id,
        account,video_url,audio_url,
        publish_order,ori_author,source_url,
        like_num,viewing_num,share_num,
        view_num,comment_num,tags,
        mid,update_time,version,
        sample_id,sample_status,gmt_create,
        gmt_modified,topics
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.opusId}, #{item.oriUrl},
            #{item.countryName}, #{item.countryId}, #{item.provinceName},
            #{item.musicUrl}, #{item.isAds}, #{item.memo},
            #{item.title}, #{item.content}, #{item.imageUrl},
            #{item.gifs}, #{item.summary}, #{item.images},
            #{item.author}, #{item.url}, #{item.oriFlag},
            #{item.avatar}, #{item.nickname}, #{item.biz},#{item.publishTime},
            #{item.description}, #{item.indexUrl}, #{item.wxId},
            #{item.account}, #{item.videoUrl}, #{item.audioUrl},
            #{item.publishOrder}, #{item.oriAuthor}, #{item.sourceUrl},
            #{item.likeNum}, #{item.viewingNum}, #{item.shareNum},
            #{item.viewNum}, #{item.commentNum}, #{item.tags},
            #{item.mid}, #{item.updateTime}, #{item.version},#{item.sampleId},#{item.sampleStatus}, now(), now(),
            #{item.topics}::jsonb
            )
        </foreach>
        ON CONFLICT (opus_id) DO UPDATE SET
        ori_url = EXCLUDED.ori_url,
        country_name = EXCLUDED.country_name,
        country_id = EXCLUDED.country_id,
        province_name = EXCLUDED.province_name,
        music_url = EXCLUDED.music_url,
        is_ads = EXCLUDED.is_ads,
        memo = EXCLUDED.memo,
        title = EXCLUDED.title,
        content = EXCLUDED.content,
        image_url = EXCLUDED.image_url,
        gifs = EXCLUDED.gifs,
        summary = EXCLUDED.summary,
        images = EXCLUDED.images,
        author = EXCLUDED.author,
        url = EXCLUDED.url,
        ori_flag = EXCLUDED.ori_flag,
        avatar = EXCLUDED.avatar,
        nickname = EXCLUDED.nickname,
        biz = EXCLUDED.biz,
        description = EXCLUDED.description,
        index_url = EXCLUDED.index_url,
        wx_id = EXCLUDED.wx_id,
        account = EXCLUDED.account,
        video_url = EXCLUDED.video_url,
        audio_url = EXCLUDED.audio_url,
        publish_order = EXCLUDED.publish_order,
        ori_author = EXCLUDED.ori_author,
        source_url = EXCLUDED.source_url,
        like_num = EXCLUDED.like_num,
        viewing_num = EXCLUDED.viewing_num,
        share_num = EXCLUDED.share_num,
        view_num = EXCLUDED.view_num,
        comment_num = EXCLUDED.comment_num,
        tags = EXCLUDED.tags,
        mid = EXCLUDED.mid,
        update_time = EXCLUDED.update_time,
        version = EXCLUDED.version,
        sample_status = EXCLUDED.sample_status,
        gmt_modified = EXCLUDED.gmt_modified,
        topics = EXCLUDED.topics
    </insert>
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_wx_opus
        where opus_id in
        <foreach collection="opusIds" item="opusId" separator="," open="(" close=")">
            #{opusId}
        </foreach>
    </select>
</mapper>
