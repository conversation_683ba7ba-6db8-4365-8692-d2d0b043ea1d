<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.athm.mapper.AthmAccountMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.athm.pojo.AthmAccount">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="verifyType" column="verify_type" jdbcType="VARCHAR"/>
        <result property="verifyTypedes" column="verify_typedes" jdbcType="VARCHAR"/>
        <result property="enterpriseName" column="enterprise_name" jdbcType="VARCHAR"/>
        <result property="introduction" column="introduction" jdbcType="VARCHAR"/>
        <result property="fansNum" column="fans_num" jdbcType="INTEGER"/>
        <result property="followNum" column="follow_num" jdbcType="INTEGER"/>
        <result property="articleNum" column="article_num" jdbcType="INTEGER"/>
        <result property="verify" column="verify" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,uid,nickname,avatar,
        verify_type,verify_typedes,enterprise_name,
        introduction,fans_num,follow_num,
        article_num,verify,gmt_create,
        gmt_modified
    </sql>

    <insert id="insertBatch">
        insert into niop_data_biz_athm_account(
        uid, nickname, avatar, verify_type, verify_typedes, enterprise_name, introduction,
        fans_num, follow_num, article_num,verify, update_time, gmt_create, gmt_modified
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.uid}, #{item.nickname}, #{item.avatar}, #{item.verifyType}, #{item.verifyTypedes},
            #{item.enterpriseName},
            #{item.introduction},
            #{item.fansNum},#{item.followNum}, #{item.articleNum}, #{item.verify}, #{item.updateTime}, now(), now()
            )
        </foreach>
        ON CONFLICT (uid) DO UPDATE SET
        nickname = EXCLUDED.nickname,
        avatar = EXCLUDED.avatar,
        verify_type = EXCLUDED.verify_type,
        verify_typedes = EXCLUDED.verify_typedes,
        enterprise_name = EXCLUDED.enterprise_name,
        introduction = EXCLUDED.introduction,
        fans_num = EXCLUDED.fans_num,
        follow_num = EXCLUDED.follow_num,
        article_num = EXCLUDED.article_num,
        verify = EXCLUDED.verify,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified;
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_athm_account
        where uid = #{uid}
    </select>
</mapper>
