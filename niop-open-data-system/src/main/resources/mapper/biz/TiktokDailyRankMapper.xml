<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokDailyRankMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokDailyRank">
        <id property="uid" column="uid" jdbcType="VARCHAR"/>
        <id property="ds" column="ds" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="followingNum" column="following_num" jdbcType="INTEGER"/>
        <result property="fansNum" column="fans_num" jdbcType="BIGINT"/>
        <result property="likeNum" column="like_num" jdbcType="BIGINT"/>
        <result property="opusNum" column="opus_num" jdbcType="BIGINT"/>
        <result property="nrIndex" column="nr_index" jdbcType="NUMERIC"/>
        <result property="rankDate" column="rank_date" jdbcType="DATE"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uid
        ,ds,region,
        following_num,fans_num,like_num,
        opus_num,nr_index,rank_date,
        create_time,update_time
    </sql>
    <insert id="startCalculate">
        INSERT INTO ds_dws_tiktok_user_daily_rank_${ds}(uid, ds, region, following_num, fans_num, like_num, opus_num,
                                                        nr_index, nr_rank, rank_date, create_time, update_time)
        SELECT ALL_TABLE.uid,
               #{ds},
               ALL_TABLE.region,
               ALL_TABLE.following_num,
               ALL_TABLE.fans_num,
               COALESCE(ALL_TABLE.like_num, 0)                                                      as like_num,
               COALESCE(ALL_TABLE.opus_num, 0)                                                      as opus_num,
               ALL_TABLE.nr_index,
               ROW_NUMBER() OVER ( PARTITION BY ALL_TABLE.region ORDER BY ALL_TABLE.nr_index DESC ) as nr_rank,
               #{rankDate},
               now(),
               now()
        FROM (SELECT SUMARY.*,
                     SUMARY.popularity_score * 0.5 +
                     (SUMARY.fans_incr_score + SUMARY.share_score + SUMARY.like_score + SUMARY.comment_score) * 1000 *
                     0.5 AS nr_index
              FROM (SELECT U.region,
                           U.uid,
                           U.account,
                           U.nickname,
                           U.avatar,
                           U.sec_uid,
                           U.fans_num,
                           INCR.fans_incr_num,
                           U.following_num,
                           U.total_opus_num,
                           U.total_like_num,
                           O.opus_num,
                           O.like_num,
                           O.view_num,
                           O.comment_num,
                           O.share_num,
                           CASE
                               WHEN U.fans_num >= ********* THEN
                                   1000
                               ELSE
                                   (1000 * (ln(U.fans_num + 1) / ln(*********)))
                               END popularity_score,
                           CASE
                               WHEN INCR.fans_incr_num > 0 THEN
                                   0.4 * (ln(INCR.fans_incr_num + 1) / ln(300000))
                               ELSE
                                   0
                               END fans_incr_score,
                           CASE
                               WHEN O.share_num > 0 THEN
                                   0.3 * (ln(O.share_num + 1) / ln(12000))
                               ELSE
                                   0
                               END share_score,
                           CASE
                               WHEN o.like_num > 0 THEN
                                   0.1 * (ln(O.like_num + 1) / ln(300000))
                               ELSE
                                   0
                               END like_score,
                           CASE
                               WHEN O.comment_num > 0 THEN
                                   0.2 * (ln(O.comment_num + 1) / ln(7600))
                               ELSE
                                   0
                               END comment_score
                    FROM (SELECT region,
                                 account,
                                 uid,
                                 nickname,
                                 avatar,
                                 sec_uid,
                                 fans_num,
                                 following_num,
                                 opus_num AS total_opus_num,
                                 like_num AS total_like_num
                          FROM ds_dwd_tiktok_user) AS U
                             LEFT JOIN (SELECT P_1.uid                                  AS uid,
                                               P_1.fans_num - COALESCE(P_2.fans_num, 0) AS fans_incr_num
                                        FROM ds_dwd_tiktok_user P_1
                                                 LEFT JOIN ds_dws_tiktok_user_daily_rank P_2 ON P_1.uid = P_2.uid
                                        WHERE P_2.rank_date = #{beforeDay}) AS INCR ON INCR.uid = U.uid
                             LEFT JOIN (SELECT uid,
                                               COUNT(*)                      AS opus_num,
                                               COALESCE(SUM(collect_num), 0) AS collect_num,
                                               COALESCE(SUM(share_num), 0)   AS share_num,
                                               COALESCE(SUM(view_num), 0)    AS view_num,
                                               COALESCE(SUM(like_num), 0)    AS like_num,
                                               COALESCE(SUM(comment_num), 0) AS comment_num
                                        FROM ds_dwd_tiktok_opus
                                        WHERE publish_time >= #{rankDate}
                                          AND publish_time &gt; #{afterDay}
                                        GROUP BY uid) AS O ON O.uid = U.uid) AS SUMARY) AS ALL_TABLE
    </insert>

    <delete id="clear">
        DELETE
        FROM ds_dws_tiktok_user_daily_rank_${ds}
        WHERE rank_date = #{rankDate}
    </delete>
</mapper>
