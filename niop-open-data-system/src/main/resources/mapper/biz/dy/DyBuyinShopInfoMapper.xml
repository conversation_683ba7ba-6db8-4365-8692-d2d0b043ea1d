<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinShopInfoMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShopInfo">
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="promotionId" column="promotion_id" jdbcType="VARCHAR"/>
        <result property="secShopId" column="sec_shop_id" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="VARCHAR"/>
        <result property="oldPrice" column="old_price" jdbcType="VARCHAR"/>
        <result property="saleNum" column="sale_num" jdbcType="BIGINT"/>
        <result property="shopId" column="shop_id" jdbcType="VARCHAR"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_id
        ,promotion_id,sec_shop_id,price,old_price,
        sale_num,shop_id,shop_name,
        device_name,partition_offset
    </sql>

    <sql id="Insert_Column_List">
        (product_id,promotion_id,sec_shop_id,price,old_price,
            sale_num,shop_id,shop_name,
            device_name,partition_offset)
    </sql>
    <insert id="insertOne">
        insert into niop_data_biz_dy_buyin_shop_info
        <include refid="Insert_Column_List"/>
        values
        (
        #{item.productId},
        #{item.promotionId},
        #{item.secShopId},
        #{item.price},
        #{item.oldPrice},
        #{item.saleNum},
        #{item.shopId},
        #{item.shopName},
        #{item.deviceName},
        #{item.partitionOffset}
        )
        on conflict (product_id) do update set
        promotion_id=excluded.promotion_id,
        sec_shop_id=excluded.sec_shop_id,
        price=excluded.price,
        old_price=excluded.old_price,
        sale_num=excluded.sale_num,
        shop_id=excluded.shop_id,
        shop_name=excluded.shop_name,
        device_name=excluded.device_name,
        partition_offset=excluded.partition_offset,
        gmt_modified=excluded.gmt_modified
    </insert>
</mapper>
