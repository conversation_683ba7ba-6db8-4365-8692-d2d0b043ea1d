<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyShopCouponsSourceMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCouponsSource">
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
            <result property="sourceData" column="source_data" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_id,
        partition_offset,source_data
    </sql>


    <sql id="Insert_Column_List">
        (product_id,
        partition_offset,source_data)
    </sql>
    <insert id="insertOne">
        insert into niop_data_biz_dy_shop_coupons_source_data
        <include refid="Insert_Column_List"/>
        values
        (
        #{item.productId},
        #{item.partitionOffset},
        #{item.sourceData}
        )
        on conflict (product_id, partition_offset) do nothing
    </insert>


</mapper>
