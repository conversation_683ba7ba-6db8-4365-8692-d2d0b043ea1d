<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinShopMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShop">
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="promotionId" column="promotion_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="VARCHAR"/>
        <result property="gmv" column="gmv" jdbcType="VARCHAR"/>
        <result property="saleNum" column="sale_num" jdbcType="VARCHAR"/>
        <result property="replay" column="replay" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        room_id,
        product_id,promotion_id,"name",
        cover,price,gmv,
        sale_num,replay,device_name,partition_offset
    </sql>

    <sql id="Insert_Column_List">
        (room_id,
        product_id,promotion_id,"name",
        cover,price,gmv,
        sale_num,replay,device_name,partition_offset)
    </sql>
    <insert id="batchInsert">
        insert into niop_data_biz_dy_buyin_shop
        <include refid="Insert_Column_List"/>
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.roomId},
            #{item.productId},
            #{item.promotionId},
            #{item.name},
            #{item.cover},
            #{item.price},
            #{item.gmv},
            #{item.saleNum},
            #{item.replay},
            #{item.deviceName},
            #{item.partitionOffset}
            )
        </foreach>
        on conflict (room_id,product_id) do update set
        promotion_id=excluded.promotion_id,
        "name"=excluded."name",
        cover=excluded.cover,
        price=excluded.price,
        gmv=excluded.gmv,
        sale_num=excluded.sale_num,
        replay=excluded.replay,
        device_name=excluded.device_name,
        partition_offset=excluded.partition_offset,
        gmt_modified=excluded.gmt_modified
    </insert>
</mapper>
