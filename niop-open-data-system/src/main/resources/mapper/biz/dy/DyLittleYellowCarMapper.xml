<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyLittleYellowCarMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyLittleYellowCar">
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="shopId" column="shop_id" jdbcType="VARCHAR"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="shopSchema" column="shop_schema" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="promotionId" column="promotion_id" jdbcType="VARCHAR"/>
        <result property="regularPrice" column="regular_price" jdbcType="VARCHAR"/>
        <result property="stockNum" column="stock_num" jdbcType="BIGINT"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="shopGuarantee" column="shop_guarantee" jdbcType="VARCHAR"/>
        <result property="maxPrice" column="max_price" jdbcType="VARCHAR"/>
        <result property="minPrice" column="min_price" jdbcType="VARCHAR"/>
        <result property="lottery" column="lottery" jdbcType="BOOLEAN"/>
        <result property="itemType" column="item_type" jdbcType="INTEGER"/>
        <result property="isSoleSku" column="is_sole_sku" jdbcType="BOOLEAN"/>
        <result property="inStock" column="in_stock" jdbcType="BOOLEAN"/>
        <result property="flashType" column="flash_type" jdbcType="INTEGER"/>
        <result property="activityInfo" column="activity_info" jdbcType="VARCHAR"/>
        <result property="priceInfo" column="price_info" jdbcType="VARCHAR"/>
        <result property="showSkuId" column="show_sku_id" jdbcType="VARCHAR"/>
        <result property="elasticTitle" column="elastic_title" jdbcType="VARCHAR"/>
        <result property="discountPrice" column="discount_price" jdbcType="VARCHAR"/>
        <result property="discountLabel" column="discount_label" jdbcType="VARCHAR"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="firstCid" column="first_cid" jdbcType="BIGINT"/>
        <result property="secondCid" column="second_cid" jdbcType="BIGINT"/>
        <result property="thirdCid" column="third_cid" jdbcType="BIGINT"/>
        <result property="fourthCid" column="fourth_cid" jdbcType="BIGINT"/>
        <result property="canAddCart" column="can_add_cart" jdbcType="BOOLEAN"/>
        <result property="canSold" column="can_sold" jdbcType="BOOLEAN"/>
        <result property="applyCoupon" column="apply_coupon" jdbcType="INTEGER"/>
        <result property="campaign" column="campaign" jdbcType="BOOLEAN"/>
        <result property="campaignId" column="campaign_id" jdbcType="VARCHAR"/>
        <result property="campaignType" column="campaign_type" jdbcType="INTEGER"/>
        <result property="campaignStartTime" column="campaign_start_time" jdbcType="BIGINT"/>
        <result property="campaignEndTime" column="campaign_end_time" jdbcType="BIGINT"/>
        <result property="campaignIsPreheat" column="campaign_is_preheat" jdbcType="BOOLEAN"/>
        <result property="campaignLeftStock" column="campaign_left_stock" jdbcType="BIGINT"/>
        <result property="campaignMaxPrice" column="campaign_max_price" jdbcType="VARCHAR"/>
        <result property="campaignDepositPrice" column="campaign_deposit_price" jdbcType="VARCHAR"/>
        <result property="campaignOriginPrice" column="campaign_origin_price" jdbcType="VARCHAR"/>
        <result property="campaignPrice" column="campaign_price" jdbcType="VARCHAR"/>
        <result property="campaignPromotionId" column="campaign_promotion_id" jdbcType="VARCHAR"/>
        <result property="campaignRegularPrice" column="campaign_regular_price" jdbcType="VARCHAR"/>
        <result property="campaignStock" column="campaign_stock" jdbcType="BIGINT"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
        <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        shop_id
        ,shop_name,shop_schema,product_id,
        promotion_id,regular_price,stock_num,
        title,shop_guarantee,max_price,
        min_price,lottery,item_type,
        is_sole_sku,in_stock,flash_type,
        activity_info,price_info,show_sku_id,
        elastic_title,discount_price,discount_label,
        cover,first_cid,second_cid,
        third_cid,fourth_cid,can_add_cart,
        can_sold,apply_coupon,campaign,
        campaign_id,campaign_type,campaign_start_time,
        campaign_end_time,campaign_is_preheat,campaign_left_stock,
        campaign_max_price,campaign_deposit_price,campaign_origin_price,
        campaign_price,campaign_promotion_id,campaign_regular_price,
        campaign_stock,device_name,partition_offset,room_id
    </sql>


    <sql id="Insert_Column_List">
        (shop_id,shop_name,shop_schema,product_id,
            promotion_id,regular_price,stock_num,
            title,shop_guarantee,max_price,
            min_price,lottery,item_type,
            is_sole_sku,in_stock,flash_type,
            activity_info,price_info,show_sku_id,
            elastic_title,discount_price,discount_label,
            cover,first_cid,second_cid,
            third_cid,fourth_cid,can_add_cart,
            can_sold,apply_coupon,campaign,
            campaign_id,campaign_type,campaign_start_time,
            campaign_end_time,campaign_is_preheat,campaign_left_stock,
            campaign_max_price,campaign_deposit_price,campaign_origin_price,
            campaign_price,campaign_promotion_id,campaign_regular_price,
            campaign_stock,device_name,partition_offset,room_id)
    </sql>

    <insert id="batchSave">
        insert into niop_data_biz_dy_little_yellow_car
        <include refid="Insert_Column_List"></include>
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.shopId},
            #{item.shopName},
            #{item.shopSchema},
            #{item.productId},
            #{item.promotionId},
            #{item.regularPrice},
            #{item.stockNum},
            #{item.title},
            #{item.shopGuarantee},
            #{item.maxPrice},
            #{item.minPrice},
            #{item.lottery},
            #{item.itemType},
            #{item.isSoleSku},
            #{item.inStock},
            #{item.flashType},
            #{item.activityInfo},
            #{item.priceInfo},
            #{item.showSkuId},
            #{item.elasticTitle},
            #{item.discountPrice},
            #{item.discountLabel},
            #{item.cover},
            #{item.firstCid},
            #{item.secondCid},
            #{item.thirdCid},
            #{item.fourthCid},
            #{item.canAddCart},
            #{item.canSold},
            #{item.applyCoupon},
            #{item.campaign},
            #{item.campaignId},
            #{item.campaignType},
            #{item.campaignStartTime},
            #{item.campaignEndTime},
            #{item.campaignIsPreheat},
            #{item.campaignLeftStock},
            #{item.campaignMaxPrice},
            #{item.campaignDepositPrice},
            #{item.campaignOriginPrice},
            #{item.campaignPrice},
            #{item.campaignPromotionId},
            #{item.campaignRegularPrice},
            #{item.campaignStock},
            #{item.deviceName},
            #{item.partitionOffset},
            #{item.roomId}
            )
        </foreach>
        on conflict (product_id, partition_offset) do nothing
    </insert>
</mapper>
