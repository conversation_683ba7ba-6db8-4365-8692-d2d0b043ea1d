<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinRoomMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoom">
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
            <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
            <result property="cover" column="cover" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="BIGINT"/>
            <result property="streamUrl" column="stream_url" jdbcType="VARCHAR"/>
            <result property="viewerNums" column="viewer_nums" jdbcType="VARCHAR"/>
            <result property="maxViewerNum" column="max_viewer_num" jdbcType="VARCHAR"/>
            <result property="totalSaleNum" column="total_sale_num" jdbcType="VARCHAR"/>
            <result property="totalGmv" column="total_gmv" jdbcType="VARCHAR"/>
            <result property="productNum" column="product_num" jdbcType="VARCHAR"/>
            <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
            <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        room_id,"name",start_time,end_time,
        cover,status,stream_url,
        viewer_nums,max_viewer_num,total_sale_num,
        total_gmv,product_num,device_name,
        partition_offset
    </sql>


    <sql id="Insert_Column_List">
        (room_id,"name",start_time,end_time,
        cover,status,stream_url,
        viewer_nums,max_viewer_num,total_sale_num,
        total_gmv,product_num,device_name,
        partition_offset)
    </sql>

    <insert id="insertOne">
        insert into niop_data_biz_dy_buyin_room
        <include refid="Insert_Column_List"/>
        values
        (
        #{item.roomId},
        #{item.name},
        #{item.startTime},
        #{item.endTime},
        #{item.cover},
        #{item.status},
        #{item.streamUrl},
        #{item.viewerNums},
        #{item.maxViewerNum},
        #{item.totalSaleNum},
        #{item.totalGmv},
        #{item.productNum},
        #{item.deviceName},
        #{item.partitionOffset}
        )
        on conflict (room_id) do update set
        "name"=excluded."name",
        start_time=excluded.start_time,
        end_time=excluded.end_time,
        cover=excluded.cover,
        status=excluded.status,
        stream_url=excluded.stream_url,
        viewer_nums=excluded.viewer_nums,
        max_viewer_num=excluded.max_viewer_num,
        total_sale_num=excluded.total_sale_num,
        total_gmv=excluded.total_gmv,
        product_num=excluded.product_num,
        device_name=excluded.device_name,
        partition_offset=excluded.partition_offset,
        gmt_modified=excluded.gmt_modified
    </insert>
</mapper>
