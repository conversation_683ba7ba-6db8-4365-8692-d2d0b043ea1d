<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyShopCouponsMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCoupons">
        <result property="useAreaDesc" column="use_area_desc" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="couponTitle" column="coupon_title" jdbcType="VARCHAR"/>
        <result property="whatCoupon" column="what_coupon" jdbcType="VARCHAR"/>
        <result property="couponName" column="coupon_name" jdbcType="VARCHAR"/>
        <result property="activityId" column="activity_id" jdbcType="VARCHAR"/>
        <result property="activityType" column="activity_type" jdbcType="INTEGER"/>
        <result property="credit" column="credit" jdbcType="VARCHAR"/>
        <result property="threshold" column="threshold" jdbcType="BIGINT"/>
        <result property="discount" column="discount" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="expireTime" column="expire_time" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
        <result property="couponsDesc" column="coupons_desc" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        use_area_desc
        ,product_id,coupon_title,what_coupon,
        coupon_name,activity_id,activity_type,
        credit,threshold,discount,
        `type`,expire_time,device_name,partition_offset,coupons_desc
    </sql>

    <sql id="Insert_Column_List">
        (use_area_desc,product_id,coupon_title,what_coupon,
            coupon_name,activity_id,activity_type,
            credit,threshold,discount,
            "type",expire_time,device_name,partition_offset,coupons_desc)
    </sql>


    <insert id="batchSave">
        insert into niop_data_biz_dy_shop_coupons
        <include refid="Insert_Column_List"/>
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.useAreaDesc},
            #{item.productId},
            #{item.couponTitle},
            #{item.whatCoupon},
            #{item.couponName},
            #{item.activityId},
            #{item.activityType},
            #{item.credit},
            #{item.threshold},
            #{item.discount},
            #{item.type},
            #{item.expireTime},
            #{item.deviceName},
            #{item.partitionOffset},
            #{item.couponsDesc}
            )
        </foreach>
        on conflict (product_id, activity_id,partition_offset,use_area_desc) do nothing
    </insert>


</mapper>
