<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyLiveCommentV2Mapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyLiveCommentV2">
            <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
            <result property="msgId" column="msg_id" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="msgType" column="msg_type" jdbcType="VARCHAR"/>
            <result property="msgMethod" column="msg_method" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="INTEGER"/>
            <result property="secUid" column="sec_uid" jdbcType="VARCHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="shortId" column="short_id" jdbcType="VARCHAR"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="displayId" column="display_id" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="anaTime" column="ana_time" jdbcType="TIMESTAMP"/>
            <result property="followerCount" column="follower_count" jdbcType="INTEGER"/>
            <result property="dataType" column="data_type" jdbcType="VARCHAR"/>
        <result property="liveStartTime" column="live_start_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        room_id,msg_id,gmt_create,
        msg_type,msg_method,gender,
        sec_uid,avatar,short_id,
        uid,display_id,nickname,
        content,ana_time,follower_count,
        data_type
    </sql>
    <update id="update">
        UPSERT INTO dwd_live_comment_v2(room_id, msg_id, gmt_create, live_start_time)
        VALUES
        <foreach collection="lives" item="item" index="index" separator=",">
            (
            #{item.roomId}, #{item.msgId}, #{item.gmtCreate}, #{item.liveStartTime}
            )
        </foreach>
    </update>
    <select id="list" resultType="cn.newrank.niop.data.biz.biz.dy.pojo.DyLiveCommentV2">
        select room_id, msg_id, gmt_create
        from dwd_live_comment_v2
        where room_id = #{roomId}
          and msg_id > #{msgId}
        order by msg_id
        limit 5000
    </select>
    <select id="nextRoomId" resultType="java.lang.String">
        select room_id
        from dwd_live_comment_v2
        where room_id > #{start} and room_id &lt;= #{end}
        order by room_id
        limit 1
    </select>
</mapper>
