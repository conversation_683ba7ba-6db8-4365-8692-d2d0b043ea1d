<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyHaiHuiLmMapper">

    <insert id="saveBatch">
        UPSERT INTO dim_dy_business_opus(
        opus_id,
        aweme_desc,
        ocr_content,
        cover_summary,
        aweme_type,
        uid,
        star_id,
        nick_name,
        user_signature,
        follower,
        all_like,
        opus_num,
        custom_verify,
        e_commerce_enable,
        gender,
        province,
        city,
        mcn_name,
        tags_relation,
        cover_num,
        inter_num,
        industry_info,
        prospective_1_20_cpm,
        prospective_20_60_cpm,
        prospective_60_cpm,
        prospective_1_20_cpe,
        prospective_20_60_cpe,
        prospective_60_cpe,
        fans_increment_within_15d,
        fans_increment_rate_within_15d,
        price_1,
        price_2,
        price_71,
        star_index_value,
        personal_30d_item_num,
        personal_30d_play_over_rate,
        personal_30d_interact_rate,
        personal_30d_play_mid,
        personal_30d_like_avg,
        personal_30d_comment_avg,
        personal_30d_share_avg,
        personal_30d_avg_duration,
        star_30d_item_num,
        star_30d_play_over_rate,
        star_30d_interact_rate,
        star_30d_play_mid,
        star_30d_like_avg,
        star_30d_comment_avg,
        star_30d_share_avg,
        star_30d_avg_duration,
        convert_ability_30d_play_mid,
        convert_ability_30d_component_click_cnt_range,
        convert_ability_30d_component_click_rate_range,
        convert_ability_30d_related_cpc_range,
        convert_ability_30d_rec_product_cnt,
        convert_ability_30d_avg_sales_amount_range,
        convert_ability_30d_rec_product_price_range,
        convert_ability_30d_gpm_range,
        expected_play_num,
        text_field,
        create_time
        )
        VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (
        #{item.awemeId},
        #{item.awemeDesc},
        #{item.ocrContent},
        #{item.coverSummary},
        #{item.awemeType},
        #{item.uid},
        #{item.starId},
        #{item.nickName},
        #{item.userSignature},
        #{item.follower},
        #{item.allLike},
        #{item.opusNum},
        #{item.customVerify},
        #{item.eCommerceEnable},
        #{item.gender},
        #{item.province},
        #{item.city},
        #{item.mcnName},
        #{item.tagsRelation},
        #{item.coverNum},
        #{item.interNum},
        #{item.industryInfo},
        #{item.prospective1To20Cpm},
        #{item.prospective20To60Cpm},
        #{item.prospective60Cpm},
        #{item.prospective1To20Cpe},
        #{item.prospective20To60Cpe},
        #{item.prospective60Cpe},
        #{item.fansIncrementWithin15D},
        #{item.fansIncrementRateWithin15D},
        #{item.price1},
        #{item.price2},
        #{item.price71},
        #{item.starIndexValue},
        #{item.personal30dItemNum},
        #{item.personal30dPlayOverRate},
        #{item.personal30dInteractRate},
        #{item.personal30dPlayMid},
        #{item.personal30dLikeAvg},
        #{item.personal30dCommentAvg},
        #{item.personal30dShareAvg},
        #{item.personal30dAvgDuration},
        #{item.star30dItemNum},
        #{item.star30dPlayOverRate},
        #{item.star30dInteractRate},
        #{item.star30dPlayMid},
        #{item.star30dLikeAvg},
        #{item.star30dCommentAvg},
        #{item.star30dShareAvg},
        #{item.star30dAvgDuration},
        #{item.convertAbility30dPlayMid},
        #{item.convertAbility30dComponentClickCntRange},
        #{item.convertAbility30dComponentClickRateRange},
        #{item.convertAbility30dRelatedCpcRange},
        #{item.convertAbility30dRecProductCnt},
        #{item.convertAbility30dAvgSalesAmountRange},
        #{item.convertAbility30dRecProductPriceRange},
        #{item.convertAbility30dGpmRange},
        #{item.expectedPlayNum},
        #{item.textField},
        #{item.createTime}
            )
        </foreach>
    </insert>
</mapper>
