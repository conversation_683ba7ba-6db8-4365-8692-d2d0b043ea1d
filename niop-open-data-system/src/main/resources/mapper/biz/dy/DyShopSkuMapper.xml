<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyShopSkuMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyShopSku">
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="skuLongId" column="sku_long_id" jdbcType="VARCHAR"/>
        <result property="skuPrice" column="sku_price" jdbcType="VARCHAR"/>
        <result property="skuStockNum" column="sku_stock_num" jdbcType="BIGINT"/>
        <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
        <result property="skuExtra" column="sku_extra" jdbcType="VARCHAR"/>
        <result property="specsName" column="specs_name" jdbcType="VARCHAR"/>
        <result property="specsItems" column="specs_items" jdbcType="VARCHAR"/>
        <result property="pic" column="pic" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_id
        ,sku_long_id,
        sku_price,sku_stock_num,sku_id,
        sku_extra,specs_name,specs_items,
        pic,device_name,partition_offset
    </sql>

    <sql id="Insert_Column_List">
        (product_id,sku_long_id,
            sku_price,sku_stock_num,sku_id,
            sku_extra,specs_name,specs_items,
            pic,device_name,partition_offset)
    </sql>


    <insert id="batchSave">
        insert into niop_data_biz_dy_shop_sku
        <include refid="Insert_Column_List"/>
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.productId},
            #{item.skuLongId},
            #{item.skuPrice},
            #{item.skuStockNum},
            #{item.skuId},
            #{item.skuExtra},
            #{item.specsName},
            #{item.specsItems},
            #{item.pic},
            #{item.deviceName},
            #{item.partitionOffset}
            )
        </foreach>
    </insert>
    <insert id="oneSave">
        insert into niop_data_biz_dy_shop_sku
        <include refid="Insert_Column_List"/>
        values
        (
        #{item.productId},
        #{item.skuLongId},
        #{item.skuPrice},
        #{item.skuStockNum},
        #{item.skuId},
        #{item.skuExtra},
        #{item.specsName},
        #{item.specsItems},
        #{item.pic},
        #{item.deviceName},
        #{item.partitionOffset}
        )
    </insert>
</mapper>
