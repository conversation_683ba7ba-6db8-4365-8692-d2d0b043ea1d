<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.dy.mapper.DyShopVerifyMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.dy.pojo.DyShopVerify">
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="priceInfo" column="price_info" jdbcType="VARCHAR"/>
        <result property="onSale" column="on_sale" jdbcType="BOOLEAN"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="partitionOffset" column="partition_offset" jdbcType="VARCHAR"/>
        <result property="saleInfo" column="sale_info" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_id
        ,title,price_info,on_sale,device_name,
        partition_offset,sale_info
    </sql>

    <sql id="Insert_Column_List">
        (product_id, title,price_info,on_sale,device_name,
            partition_offset,sale_info)
    </sql>
    <insert id="saveOne">
        insert into niop_data_biz_dy_shop_verify
        <include refid="Insert_Column_List"/>
        values
        (
        #{item.productId},
        #{item.title},
        #{item.priceInfo},
        #{item.onSale},
        #{item.deviceName},
        #{item.partitionOffset},
        #{item.saleInfo}
        )
        on conflict (product_id, partition_offset) do nothing
    </insert>


</mapper>
