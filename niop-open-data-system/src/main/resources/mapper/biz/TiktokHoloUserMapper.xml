<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokHoloUserMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokHoloUser">
        <id property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="secUid" column="sec_uid" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="signature" column="signature" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="fansNum" column="fans_num" jdbcType="BIGINT"/>
        <result property="followingNum" column="following_num" jdbcType="INTEGER"/>
        <result property="opusNum" column="opus_num" jdbcType="INTEGER"/>
        <result property="likeNum" column="like_num" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        uid
        ,account,nickname,
        sec_uid,avatar,signature,
        region,fans_num,following_num,
        opus_num,like_num,create_time,
        update_time
    </sql>
    <insert id="save">
        insert into ds_dwd_tiktok_user
        (uid,account,nickname,
        sec_uid,avatar,signature,
        region,fans_num,following_num,
        opus_num,like_num,create_time,
        update_time)
        values
        <foreach collection="users" item="item" separator=",">
            (#{item.uid}, #{item.account}, #{item.nickname},
            #{item.secUid}, #{item.avatar}, #{item.signature}, #{item.region},
            #{item.fansNum}, #{item.followingNum}, #{item.opusNum},
            #{item.likeNum}, now(), now())
        </foreach>
        ON CONFLICT (uid) DO UPDATE SET
        account = EXCLUDED.account,
        nickname = EXCLUDED.nickname,
        sec_uid = EXCLUDED.sec_uid,
        avatar = EXCLUDED.avatar,
        signature = EXCLUDED.signature,
        region = EXCLUDED.region,
        fans_num = EXCLUDED.fans_num,
        following_num = EXCLUDED.following_num,
        opus_num= EXCLUDED.opus_num,
        like_num = EXCLUDED.like_num,
        update_time = EXCLUDED.update_time
    </insert>
</mapper>
