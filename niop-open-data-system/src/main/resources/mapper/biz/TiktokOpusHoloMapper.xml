<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokOpusHoloMapper">

    <insert id="save">
        insert into ds_dwd_tiktok_opus_${ds}
        (opus_id, collect_num, comment_num, cover, like_num,
        publish_time, share_num,
        uid, view_num, ds,update_time, create_time)
        values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.opusId}, #{item.collectNum}, #{item.commentNum},
            #{item.cover}, #{item.likeNum},
            #{item.publishTime}, #{item.shareNum},
            #{item.uid}, #{item.viewNum}, #{item.ds},now(), now()
            )
        </foreach>
        ON CONFLICT (ds,uid, opus_id) DO UPDATE SET
        collect_num = EXCLUDED.collect_num,
        comment_num = EXCLUDED.comment_num,
        cover = EXCLUDED.cover,
        like_num = EXCLUDED.like_num,
        publish_time = EXCLUDED.publish_time,
        share_num = EXCLUDED.share_num,
        view_num = EXCLUDED.view_num,
        update_time = EXCLUDED.update_time
    </insert>

</mapper>
