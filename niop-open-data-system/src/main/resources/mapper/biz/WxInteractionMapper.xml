<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.wx.mapper.WxInteractionMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.wx.pojo.WxInteraction">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="likeNum" column="like_num" jdbcType="BIGINT"/>
        <result property="viewingNum" column="viewing_num" jdbcType="BIGINT"/>
        <result property="shareNum" column="share_num" jdbcType="BIGINT"/>
        <result property="viewNum" column="view_num" jdbcType="BIGINT"/>
        <result property="commentNum" column="comment_num" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="BIGINT"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,gmt_modified,gmt_create,
        version,opus_id,
        url,like_num,viewing_num,
        share_num,view_num,comment_num,
        update_time,sample_id,sample_status
    </sql>
    <insert id="save">
        insert into
        niop_data_biz_wx_interaction(gmt_modified,gmt_create,version,opus_id,url,like_num,viewing_num
        ,share_num,view_num,comment_num,update_time,sample_id,sample_status)
        values
        <foreach collection="items" item="item" separator=",">
            (
            now(),now(), #{item.version},#{item.opusId},
            #{item.url},#{item.likeNum},#{item.viewingNum},
            #{item.shareNum},#{item.viewNum},#{item.commentNum},#{item.updateTime},
            #{item.sampleId},#{item.sampleStatus}
            )
        </foreach>
        ON CONFLICT (sample_id) DO UPDATE SET
        gmt_modified = EXCLUDED.gmt_modified,
        version = EXCLUDED.version,
        opus_id = EXCLUDED.opus_id,
        url = EXCLUDED.url,
        like_num = EXCLUDED.like_num,
        viewing_num = EXCLUDED.viewing_num,
        share_num = EXCLUDED.share_num,
        view_num = EXCLUDED.view_num,
        comment_num = EXCLUDED.comment_num,
        update_time = EXCLUDED.update_time,
        sample_status = EXCLUDED.sample_status
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_wx_interaction
        where sample_id = #{identifier}
    </select>
</mapper>
