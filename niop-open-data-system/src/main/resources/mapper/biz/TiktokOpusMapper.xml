<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokOpusMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokOpus">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="collectNum" column="collect_num" jdbcType="INTEGER"/>
        <result property="commentNum" column="comment_num" jdbcType="INTEGER"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="likeNum" column="like_num" jdbcType="INTEGER"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="opusId" column="opus_id" jdbcType="VARCHAR"/>
        <result property="publishTime" column="publish_time" jdbcType="VARCHAR"/>
        <result property="secUid" column="sec_uid" jdbcType="VARCHAR"/>
        <result property="shareNum" column="share_num" jdbcType="INTEGER"/>
        <result property="signature" column="signature" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="viewNum" column="view_num" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="sampleId" column="sample_id" jdbcType="VARCHAR"/>
        <result property="sampleStatus" column="sample_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,account,address,
        collect_num,comment_num,cover,
        description,duration,like_num,
        nickname,opus_id,publish_time,
        sec_uid,share_num,signature,
        uid,view_num,update_time,
        version,gmt_create,gmt_modified,sample_id,sample_status
    </sql>

    <insert id="save">
        insert into niop_data_biz_tiktok_opus
        (account, address,
        collect_num, comment_num, cover,
        description, duration, like_num,
        nickname, opus_id, publish_time,
        sec_uid, share_num, signature,
        uid, view_num, update_time,
        version, gmt_create, gmt_modified, sample_id, sample_status)
        values
        <foreach collection="items" item="item" separator=",">
            (#{item.account}, #{item.address},
            #{item.collectNum}, #{item.commentNum}, #{item.cover},
            #{item.description}, #{item.duration}, #{item.likeNum},
            #{item.nickname}, #{item.opusId}, #{item.publishTime},
            #{item.secUid}, #{item.shareNum}, #{item.signature},
            #{item.uid}, #{item.viewNum}, #{item.updateTime},
            #{item.version}, now(), now(), #{item.sampleId}, #{item.sampleStatus})
        </foreach>
        ON CONFLICT (opus_id) DO UPDATE SET
        account = EXCLUDED.account,
        address = EXCLUDED.address,
        collect_num = EXCLUDED.collect_num,
        comment_num = EXCLUDED.comment_num,
        cover = EXCLUDED.cover,
        description = EXCLUDED.description,
        duration = EXCLUDED.duration,
        like_num = EXCLUDED.like_num,
        nickname = EXCLUDED.nickname,
        publish_time = EXCLUDED.publish_time,
        sec_uid = EXCLUDED.sec_uid,
        share_num = EXCLUDED.share_num,
        signature = EXCLUDED.signature,
        uid = EXCLUDED.uid,
        view_num = EXCLUDED.view_num,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified,
        version = EXCLUDED.version,
        sample_status = EXCLUDED.sample_status,
        sample_id = EXCLUDED.sample_id
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_tiktok_opus
        where opus_id = #{opusId}
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_tiktok_opus
        where id > #{cursor} and publish_time >= #{startTime} and publish_time &lt; #{endTime}
        order by id
        limit 200
    </select>
</mapper>
