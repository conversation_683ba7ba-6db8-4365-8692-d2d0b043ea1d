<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.biz.txhx.mapper.TxhxSphIconMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.biz.txhx.pojo.TxhxSphIcon">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="aderLongVideoPrice" column="ader_long_video_price" jdbcType="VARCHAR"/>
        <result property="aderShortVideoPrice" column="ader_short_video_price" jdbcType="VARCHAR"/>
        <result property="authCompany" column="auth_company" jdbcType="VARCHAR"/>
        <result property="authProfession" column="auth_profession" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="avgInteractionCount" column="avg_interaction_count" jdbcType="VARCHAR"/>
        <result property="avgLikeCount" column="avg_like_count" jdbcType="VARCHAR"/>
        <result property="avgReadCount" column="avg_read_count" jdbcType="VARCHAR"/>
        <result property="categoryLevel1" column="category_level1" jdbcType="INTEGER"/>
        <result property="categoryLevel2" column="category_level2" jdbcType="INTEGER"/>
        <result property="cityLabel" column="city_label" jdbcType="VARCHAR"/>
        <result property="contactInfo" column="contact_info" jdbcType="VARCHAR"/>
        <result property="cooperationIndex" column="cooperation_index" jdbcType="INTEGER"/>
        <result property="costEffectivenessIndex" column="cost_effectiveness_index" jdbcType="INTEGER"/>
        <result property="expectedCpm" column="expected_cpm" jdbcType="INTEGER"/>
        <result property="fansNumGrowthRange" column="fans_num_growth_range" jdbcType="VARCHAR"/>
        <result property="fansNumGrowthRate" column="fans_num_growth_rate" jdbcType="INTEGER"/>
        <result property="fansNumIncrement" column="fans_num_increment" jdbcType="VARCHAR"/>
        <result property="fansNumLevel" column="fans_num_level" jdbcType="VARCHAR"/>
        <result property="interactionRate" column="interaction_rate" jdbcType="INTEGER"/>
        <result property="longVideoPrice" column="long_video_price" jdbcType="VARCHAR"/>
        <result property="longVideoPriceDeprecated" column="long_video_price_deprecated" jdbcType="VARCHAR"/>
        <result property="medianReadCount" column="median_read_count" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="playFinishRate" column="play_finish_rate" jdbcType="VARCHAR"/>
        <result property="plusV" column="plus_v" jdbcType="VARCHAR"/>
        <result property="proceedingOrderCount" column="proceeding_order_count" jdbcType="VARCHAR"/>
        <result property="propagationIndex" column="propagation_index" jdbcType="VARCHAR"/>
        <result property="qrCode" column="qr_code" jdbcType="VARCHAR"/>
        <result property="recallType" column="recall_type" jdbcType="VARCHAR"/>
        <result property="recentChangedPrice" column="recent_changed_price" jdbcType="VARCHAR"/>
        <result property="releaseDate" column="release_date" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="VARCHAR"/>
        <result property="shortVideoPrice" column="short_video_price" jdbcType="VARCHAR"/>
        <result property="shortVideoPriceDeprecated" column="short_video_price_deprecated" jdbcType="VARCHAR"/>
        <result property="socialInteractionIndex" column="social_interaction_index" jdbcType="VARCHAR"/>
        <result property="synopsis" column="synopsis" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="topReadCount" column="top_read_count" jdbcType="VARCHAR"/>
        <result property="avgReadCount15" column="avg_read_count15" jdbcType="VARCHAR"/>
        <result property="fansAge" column="fans_age" jdbcType="VARCHAR"/>
        <result property="fansArea" column="fans_area" jdbcType="VARCHAR"/>
        <result property="fansDevice" column="fans_device" jdbcType="VARCHAR"/>
        <result property="fansGender" column="fans_gender" jdbcType="VARCHAR"/>
        <result property="fansProvince" column="fans_province" jdbcType="VARCHAR"/>
        <result property="viewerAge" column="viewer_age" jdbcType="VARCHAR"/>
        <result property="viewerArea" column="viewer_area" jdbcType="VARCHAR"/>
        <result property="viewerDevice" column="viewer_device" jdbcType="VARCHAR"/>
        <result property="viewerGender" column="viewer_gender" jdbcType="VARCHAR"/>
        <result property="viewerProvince" column="viewer_province" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, ader_long_video_price, ader_short_video_price,
        auth_company, auth_profession, avatar,
        avg_interaction_count, avg_like_count, avg_read_count,
        category_level1, category_level2, city_label,
        contact_info, cooperation_index, cost_effectiveness_index,
        expected_cpm, fans_num_growth_range, fans_num_growth_rate,
        fans_num_increment, fans_num_level, interaction_rate,
        long_video_price, long_video_price_deprecated, median_read_count,
        nickname, play_finish_rate, plus_v,
        proceeding_order_count, propagation_index, qr_code,
        recall_type, recent_changed_price, release_date,
        score, short_video_price, short_video_price_deprecated,
        social_interaction_index, synopsis, tags,
        top_read_count, avg_read_count15, fans_age,
        fans_area, fans_device, fans_gender,
        fans_province, viewer_age, viewer_area,
        viewer_device, viewer_gender, viewer_province,
        update_time, gmt_create, gmt_modified
    </sql>
    <insert id="insertBatch">
        insert into niop_data_biz_txhx_sph_icon(
        app_id, ader_long_video_price, ader_short_video_price,
        auth_company, auth_profession, avatar,
        avg_interaction_count, avg_like_count, avg_read_count,
        category_level1, category_level2, city_label,
        contact_info, cooperation_index, cost_effectiveness_index,
        expected_cpm, fans_num_growth_range, fans_num_growth_rate,
        fans_num_increment, fans_num_level, interaction_rate,
        long_video_price, long_video_price_deprecated, median_read_count,
        nickname, play_finish_rate, plus_v,
        proceeding_order_count, propagation_index, qr_code,
        recall_type, recent_changed_price, release_date,
        score, short_video_price, short_video_price_deprecated,
        social_interaction_index, synopsis, tags,
        top_read_count, avg_read_count15, fans_age,
        fans_area, fans_device, fans_gender,
        fans_province, viewer_age, viewer_area,
        viewer_device, viewer_gender, viewer_province,
        update_time, gmt_create, gmt_modified
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.appId}, #{item.aderLongVideoPrice}, #{item.aderShortVideoPrice},
            #{item.authCompany}, #{item.authProfession}, #{item.avatar},
            #{item.avgInteractionCount}, #{item.avgLikeCount}, #{item.avgReadCount},
            #{item.categoryLevel1}, #{item.categoryLevel2}, #{item.cityLabel},
            #{item.contactInfo}, #{item.cooperationIndex}, #{item.costEffectivenessIndex},
            #{item.expectedCpm}, #{item.fansNumGrowthRange}, #{item.fansNumGrowthRate},
            #{item.fansNumIncrement}, #{item.fansNumLevel}, #{item.interactionRate},
            #{item.longVideoPrice}, #{item.longVideoPriceDeprecated}, #{item.medianReadCount},
            #{item.nickname}, #{item.playFinishRate}, #{item.plusV},
            #{item.proceedingOrderCount}, #{item.propagationIndex}, #{item.qrCode},
            #{item.recallType}, #{item.recentChangedPrice}, #{item.releaseDate},
            #{item.score}, #{item.shortVideoPrice}, #{item.shortVideoPriceDeprecated},
            #{item.socialInteractionIndex}, #{item.synopsis}, #{item.tags},
            #{item.topReadCount}, #{item.avgReadCount15}, #{item.fansAge},
            #{item.fansArea}, #{item.fansDevice}, #{item.fansGender},
            #{item.fansProvince}, #{item.viewerAge}, #{item.viewerArea},
            #{item.viewerDevice}, #{item.viewerGender}, #{item.viewerProvince},
            #{item.updateTime}, now(), now()
            )
        </foreach>
        ON CONFLICT (app_id) DO UPDATE SET
        app_id = EXCLUDED.app_id,
        ader_long_video_price = EXCLUDED.ader_long_video_price,
        ader_short_video_price = EXCLUDED.ader_short_video_price,
        auth_company = EXCLUDED.auth_company,
        auth_profession = EXCLUDED.auth_profession,
        avatar = EXCLUDED.avatar,
        avg_interaction_count = EXCLUDED.avg_interaction_count,
        avg_like_count = EXCLUDED.avg_like_count,
        avg_read_count = EXCLUDED.avg_read_count,
        category_level1 = EXCLUDED.category_level1,
        category_level2 = EXCLUDED.category_level2,
        city_label = EXCLUDED.city_label,
        contact_info = EXCLUDED.contact_info,
        cooperation_index = EXCLUDED.cooperation_index,
        cost_effectiveness_index = EXCLUDED.cost_effectiveness_index,
        expected_cpm = EXCLUDED.expected_cpm,
        fans_num_growth_range = EXCLUDED.fans_num_growth_range,
        fans_num_growth_rate = EXCLUDED.fans_num_growth_rate,
        fans_num_increment = EXCLUDED.fans_num_increment,
        fans_num_level = EXCLUDED.fans_num_level,
        interaction_rate = EXCLUDED.interaction_rate,
        long_video_price = EXCLUDED.long_video_price,
        long_video_price_deprecated = EXCLUDED.long_video_price_deprecated,
        median_read_count = EXCLUDED.median_read_count,
        nickname = EXCLUDED.nickname,
        play_finish_rate = EXCLUDED.play_finish_rate,
        plus_v = EXCLUDED.plus_v,
        proceeding_order_count = EXCLUDED.proceeding_order_count,
        propagation_index = EXCLUDED.propagation_index,
        qr_code = EXCLUDED.qr_code,
        recall_type = EXCLUDED.recall_type,
        recent_changed_price = EXCLUDED.recent_changed_price,
        release_date = EXCLUDED.release_date,
        score = EXCLUDED.score,
        short_video_price = EXCLUDED.short_video_price,
        short_video_price_deprecated = EXCLUDED.short_video_price_deprecated,
        social_interaction_index = EXCLUDED.social_interaction_index,
        synopsis = EXCLUDED.synopsis,
        tags = EXCLUDED.tags,
        top_read_count = EXCLUDED.top_read_count,
        avg_read_count15 = EXCLUDED.avg_read_count15,
        fans_age = EXCLUDED.fans_age,
        fans_area = EXCLUDED.fans_area,
        fans_device = EXCLUDED.fans_device,
        fans_gender = EXCLUDED.fans_gender,
        fans_province = EXCLUDED.fans_province,
        viewer_age = EXCLUDED.viewer_age,
        viewer_area = EXCLUDED.viewer_area,
        viewer_device = EXCLUDED.viewer_device,
        viewer_gender = EXCLUDED.viewer_gender,
        viewer_province = EXCLUDED.viewer_province,
        update_time = EXCLUDED.update_time,
        gmt_modified = EXCLUDED.gmt_modified;
    </insert>

    <select id="get" resultType="cn.newrank.niop.data.biz.biz.txhx.pojo.TxhxSphIcon">
        select
        <include refid="Base_Column_List"/>
        from niop_data_biz_txhx_sph_icon
        where app_id = #{appId}
    </select>

</mapper>
