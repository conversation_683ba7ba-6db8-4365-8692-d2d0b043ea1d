<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dataclear.mapper.DataClearMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="dcId" column="dc_id" jdbcType="VARCHAR"/>
        <result property="tableName" column="table_name" jdbcType="VARCHAR"/>
        <result property="cron" column="cron" jdbcType="VARCHAR"/>
        <result property="filterCondition" column="filter_condition" jdbcType="VARCHAR"/>
        <result property="principal" column="principal" jdbcType="VARCHAR"/>
        <result property="ruleStatus" column="rule_status" jdbcType="SMALLINT"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,rule_id
        , dc_id, table_name, cron, filter_condition, principal, rule_status,rule_name,gmt_create
    </sql>

    <insert id="insert">
        insert into open_data_clean_rule(gmt_modified, gmt_create, rule_id, dc_id, table_name, cron, filter_condition,
                                         principal, rule_status,
                                         rule_name)
        values (now(), now(), #{ruleId}, #{dcId}, #{tableName}, #{cron}, #{filterCondition}, #{principal},
                #{ruleStatus}, #{ruleName})
    </insert>

    <update id="update">
        update open_data_clean_rule
        <set>
            <if test="dcId != null and dcId != ''">dc_id = #{dcId},</if>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="cron != null and cron != ''">cron = #{cron},</if>
            <if test="filterCondition != null and filterCondition != ''">filter_condition = #{filterCondition},</if>
            <if test="principal != null and principal != ''">principal = #{principal},</if>
            <if test="ruleStatus != null">rule_status = #{ruleStatus},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            gmt_modified=now(),
        </set>
        where rule_id = #{ruleId}
    </update>

    <delete id="delete">
        delete
        from open_data_clean_rule
        where rule_id = #{ruleId}
    </delete>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_rule
        where rule_id = #{ruleId}
    </select>

    <select id="fuzzyQuery" resultType="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_rule
        <where>
            <if test="fuzzyQuery.ruleId != null and fuzzyQuery.ruleId != ''">
                and rule_id = #{fuzzyQuery.ruleId}
            </if>
            <if test="fuzzyQuery.dcId != null and fuzzyQuery.dcId != ''">
                and dc_id = #{fuzzyQuery.dcId}
            </if>
            <if test="fuzzyQuery.tableName != null and fuzzyQuery.tableName != ''">
                and table_name like concat('%',#{fuzzyQuery.tableName},'%')
            </if>
            <if test="fuzzyQuery.ruleName != null and fuzzyQuery.ruleName != ''">
                and rule_name like concat('%',#{fuzzyQuery.ruleName},'%')
            </if>
        </where>
        order by table_name
        limit 50
    </select>


    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_rule
        <where>
            <if test="pageQuery.dcId != null and pageQuery.dcId != ''">
                dc_id = #{pageQuery.dcId}
            </if>
            <if test="pageQuery.tableName != null and pageQuery.tableName != ''">
                and table_name = #{pageQuery.tableName}
            </if>
            <if test="pageQuery.principal != null and pageQuery.principal != ''">
                and principal = #{pageQuery.principal}
            </if>
            <if test="pageQuery.principal != null and pageQuery.principal != ''">
                and principal = #{pageQuery.principal}
            </if>
            <if test="pageQuery.ruleName != null and pageQuery.ruleName != ''">
                and rule_name like concat('%',#{pageQuery.ruleName},'%')
            </if>
        </where>
        order by gmt_create desc
    </select>


    <select id="list" resultType="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_rule
        where rule_status = 1
        and id > #{cursor}
        LIMIT 200
    </select>
    <!--    id > #{cursor} limt 500-->

    <select id="listRuleIds" resultType="java.lang.String">
        select rule_id
        from open_data_clean_rule
    </select>
</mapper>
