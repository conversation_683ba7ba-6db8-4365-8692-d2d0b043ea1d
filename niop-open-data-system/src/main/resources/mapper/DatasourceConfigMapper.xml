<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.DatasourceConfigMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.pojo.po.DatasourceConfigPo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="dcId" column="dc_id" jdbcType="CHAR"/>
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="config" column="config" jdbcType="VARCHAR"/>
        <result property="concurrencyPermit" column="concurrency_permit" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,dc_id,type,
        name,config,concurrency_permit,
        gmt_create,gmt_modified
    </sql>
    <insert id="insert">
        insert into niop_data_datasource_config(dc_id, type, name, config, concurrency_permit)
        values (#{dcId}, #{type}, #{name}, #{config}, #{concurrencyPermit})
    </insert>

    <update id="update">
        update niop_data_datasource_config
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="config != null">config = #{config},</if>
            <if test="concurrencyPermit != null">concurrency_permit = #{concurrencyPermit},</if>
        </set>
        where dc_id = #{dcId}
    </update>


    <delete id="delete">
        delete
        from niop_data_datasource_config
        where dc_id = #{dcId}
    </delete>

    <select id="getByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        where name = #{name}
    </select>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        where dc_id = #{dcId}
    </select>

    <select id="fuzzyQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        <where>
            <if test="fuzzyQuery.type != null">
                and type = #{fuzzyQuery.type}
            </if>
            <if test="fuzzyQuery.dcId != null and fuzzyQuery.dcId != ''">
                and dc_id = #{fuzzyQuery.dcId}
            </if>
            <if test="fuzzyQuery.name != null and fuzzyQuery.name != ''">
                and name like concat('%', #{fuzzyQuery.name}, '%')
            </if>
        </where>
        order by "name"
        limit 50
    </select>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
    </select>


    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        <where>
            <if test="pageQuery.keyword != null and pageQuery.keyword != ''">
                and name like concat('%', #{pageQuery.keyword}, '%')
                or dc_id = #{pageQuery.keyword}
            </if>
            <if test="pageQuery.type != null and pageQuery.type != ''">
                and "type" = #{pageQuery.type}
            </if>
        </where>
        order by gmt_create desc
    </select>
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        where
        dc_id in
        <foreach collection="dcIds" item="dcId" open="(" separator="," close=")">
            #{dcId}
        </foreach>
    </select>
    <select id="findByTypes" resultType="cn.newrank.niop.data.biz.pojo.po.DatasourceConfigPo">
        select
        <include refid="Base_Column_List"/>
        from niop_data_datasource_config
        where
        type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>


</mapper>
