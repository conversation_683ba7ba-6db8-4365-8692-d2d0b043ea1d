<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceAuthMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.pojo.po.DynamicInterfaceAuthPo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="authId" column="auth_id" jdbcType="VARCHAR"/>
        <result property="interfaceId" column="interface_id" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="refreshPermits" column="refresh_permits" jdbcType="INTEGER"/>
        <result property="refreshSeconds" column="refresh_seconds" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        auth_id,interface_id,app_id,
        refresh_permits,refresh_seconds
    </sql>
    <insert id="insert">
        insert into niop_data_dynamic_interface_auth
        (gmt_modified, gmt_create,
         auth_id, interface_id, app_id,
         refresh_permits, refresh_seconds)
        values (now(), now(),
                #{authId}, #{interfaceId}, #{appId},
                #{refreshPermits}, #{refreshSeconds})
    </insert>

    <update id="update">
        update niop_data_dynamic_interface_auth
        set gmt_modified = now()
        <if test="interfaceId != null">
            ,interface_id = #{interfaceId}
        </if>
        <if test="appId != null">
            ,app_id = #{appId}
        </if>
        <if test="refreshPermits != null">
            ,refresh_permits = #{refreshPermits}
        </if>
        <if test="refreshSeconds != null">
            ,refresh_seconds = #{refreshSeconds}
        </if>
        where auth_id = #{authId}
    </update>

    <delete id="delete">
        delete
        from niop_data_dynamic_interface_auth
        where auth_id = #{authId}
    </delete>


    <select id="count" resultType="int">
        select count(*)
        from niop_data_dynamic_interface_auth
        where interface_id = #{interfaceId}
          and app_id = #{appId}
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_auth
        where interface_id = #{interfaceId}
        order by gmt_create desc
    </select>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_auth
        where auth_id = #{authId}
    </select>

    <select id="getByInterfaceIdAndAppId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface_auth
        where interface_id = #{interfaceId}
        and app_id = #{appId}
    </select>
</mapper>
