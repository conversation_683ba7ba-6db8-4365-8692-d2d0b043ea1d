<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.subscriber.dao.mapper.SubscriberConfigMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.subscriber.pojo.po.SubscriberConfigPo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="subscriberId" column="subscriber_id" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="appIds" column="app_ids" jdbcType="VARCHAR"/>
        <result property="cbId" column="cb_id" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="sourceType" column="source_type" jdbcType="VARCHAR"/>
        <result property="enableParams" column="enable_params" jdbcType="INTEGER"/>
        <result property="sendStrategy" column="send_strategy" jdbcType="VARCHAR"/>
        <result property="maintainers" column="maintainers" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,subscriber_id,app_id,app_ids,
        cb_id,source_id,source_name,
        source_type,enable_params,send_strategy,maintainers,gmt_create, tags,
        gmt_modified
    </sql>

    <insert id="insert">
        insert into niop_data_subscriber_config
        (subscriber_id, app_id,app_ids,
         cb_id, source_id, source_name,
         source_type, enable_params, send_strategy,tags, maintainers, gmt_create,
         gmt_modified)
        values (#{subscriberId}, #{appId}, #{appIds}::jsonb,
                #{cbId}, #{sourceId}, #{sourceName},
                #{sourceType}, #{enableParams}, #{sendStrategy}::jsonb,#{tags}, #{maintainers}, now(),
                now())
    </insert>

    <update id="update">
        update niop_data_subscriber_config
        set app_id        = #{appId},
            app_ids       = #{appIds}::jsonb,
            cb_id         = #{cbId},
            source_id     = #{sourceId},
            source_name   = #{sourceName},
            source_type   = #{sourceType},
            enable_params = #{enableParams},
            send_strategy = #{sendStrategy}::jsonb,
            maintainers   = #{maintainers},
            tags          = #{tags},
            gmt_modified  = now()
        where subscriber_id = #{subscriberId}
    </update>
    <delete id="delete">
        delete
        from niop_data_subscriber_config
        where subscriber_id = #{subscriberId}
    </delete>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        where subscriber_id = #{subscriberId}
    </select>

    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        order by gmt_create desc
    </select>

    <select id="searchSbConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        where source_type = #{sourceType}
        and source_id = #{sourceId}
    </select>
    <select id="listByCbId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        where cb_id = #{cbId}
        order by gmt_create desc
    </select>
    <select id="getBy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        where
        cb_id = #{cbId}
        and source_type = #{sourceType}
        and source_id = #{sourceId}
    </select>
    
    <select id="searchSbConfigByTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_subscriber_config
        where source_id = #{sourceId}
        and source_type in
        <foreach collection="sourceTypes" item="sourceType" open="(" separator="," close=")">
            #{sourceType}
        </foreach>
    </select>
</mapper>
