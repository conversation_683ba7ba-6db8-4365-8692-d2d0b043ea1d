<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.CbConfigMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.pojo.po.CbConfigPo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="cbId" column="cb_id" jdbcType="CHAR"/>
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="config" column="config" jdbcType="VARCHAR"/>
        <result property="maintainers" column="maintainers" jdbcType="VARCHAR"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,cb_id,type,source,name,config,maintainers,uuid,gmt_create, gmt_modified
    </sql>

    <insert id="insert">
        insert into niop_data_callback_config(cb_id, type, name, source, config, maintainers,uuid)
        values (#{cbId}, #{type}, #{name}, #{source}, #{config}, #{maintainers}, #{uuid})
    </insert>

    <update id="update">
        update niop_data_callback_config
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="config != null and config != ''">config = #{config},</if>
            <if test="source != null">source = #{source},</if>
            <if test="maintainers != null">maintainers = #{maintainers},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
        </set>
        where cb_id = #{cbId}
    </update>

    <delete id="delete">
        delete
        from niop_data_callback_config
        where cb_id = #{cbId}
    </delete>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_callback_config
        where cb_id = #{cbId}
    </select>

    <select id="fuzzyQuery" resultType="cn.newrank.niop.data.biz.pojo.po.CbConfigPo">
        select
        <include refid="Base_Column_List"/>
        from niop_data_callback_config
        <where>
            <if test="fuzzyQuery.cbId != null and fuzzyQuery.cbId != ''">
                and cb_id = #{fuzzyQuery.cbId}
            </if>
            <if test="fuzzyQuery.name != null and fuzzyQuery.name != ''">
                and (
                    name like concat('%',#{fuzzyQuery.name},'%')
                    or cb_id= #{fuzzyQuery.name}
                )
            </if>
            <if test="fuzzyQuery.type != null">
                and type = #{fuzzyQuery.type}
            </if>
            <if test="fuzzyQuery.source != null and fuzzyQuery.source != ''">
                and source = #{fuzzyQuery.source}
            </if>
        </where>
        order by "name"
        limit 50
    </select>
    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_callback_config
        <where>
            <if test="pageQuery.keyword != null and pageQuery.keyword != ''">
                and (
                    name like concat('%',#{pageQuery.keyword},'%')
                    or cb_id like concat('%',#{pageQuery.keyword},'%')
                )
            </if>
            <if test="pageQuery.maintainer != null and pageQuery.maintainer != ''">
                and (
                    maintainers like concat('%', #{pageQuery.maintainer}, '%')
                )
            </if>
        </where>
        order by gmt_create desc
    </select>

    <select id="pageWithSubscriber" resultMap="BaseResultMap">
        select distinct cc.id, cc.cb_id, cc.type, cc.source, cc.name, cc.config, cc.maintainers, cc.uuid, cc.gmt_create, cc.gmt_modified
        from niop_data_callback_config cc join niop_data_subscriber_config sc on cc.cb_id = sc.cb_id
        <where>
            <if test="pageQuery.keyword != null and pageQuery.keyword != ''">
                and (
                    sc.source_id like concat('%',#{pageQuery.keyword},'%')
                    or sc.source_name like concat('%',#{pageQuery.keyword},'%')
                )
            </if>
            <if test="pageQuery.maintainer != null and pageQuery.maintainer != ''">
                and (
                    cc.maintainers like concat('%', #{pageQuery.maintainer}, '%')
                )
            </if>
        </where>
        order by cc.gmt_create desc
    </select>

    <select id="list" resultType="cn.newrank.niop.data.biz.pojo.po.CbConfigPo">
        select
        <include refid="Base_Column_List"/>
        from niop_data_callback_config
        where cb_id in
        <foreach collection="cbIds" item="cbId" open="(" separator="," close=")">
            #{cbId}
        </foreach>
    </select>

    <select id="listCbIds" resultType="java.lang.String">
        select cb_id from niop_data_callback_config
    </select>
    <select id="getByUUID" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_callback_config
        where uuid = #{uuid}
    </select>
</mapper>
