<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.callback.mapper.DcCbDataMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.callback.pojo.CbData">
            <id property="cid" column="cid" jdbcType="CHAR"/>
            <id property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="data" column="data" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        cid,gmt_create,data,partition,
        gmt_modified
    </sql>
    <insert id="insertBatch">
        insert into niop_dc_callback_data_${partition}(cid,data,partition,gmt_create,gmt_modified)
        values
        <foreach collection="dataList" item="item" separator=",">
            (#{item.cid},#{item.data},#{item.partition},#{item.gmtCreate},#{item.gmtModified})
        </foreach>
        on conflict(cid,partition) do update set
                    data = excluded.data,
                    gmt_modified = excluded.gmt_modified
    </insert>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS niop_dc_callback_data_${partition} PARTITION OF niop_dc_callback_data
                      FOR VALUES FROM ('${startDate}') TO ('${endDate}')
    </update>

    <delete id="dropTable">
        DROP TABLE IF EXISTS niop_dc_callback_data_${partition}
    </delete>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_dc_callback_data
        where cid in
        <foreach collection="cIds" item="cId" open="(" separator="," close=")">
            #{cId}
        </foreach>
    </select>
</mapper>
