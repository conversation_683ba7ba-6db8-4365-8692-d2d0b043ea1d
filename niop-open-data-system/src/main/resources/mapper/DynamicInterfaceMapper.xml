<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.pojo.po.DynamicInterfacePo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="interfaceId" column="interface_id" jdbcType="VARCHAR"/>
        <result property="dcId" column="dc_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="query" column="query" jdbcType="VARCHAR"/>
        <result property="args" column="args" jdbcType="VARCHAR"/>
        <result property="refreshPermits" column="refresh_permits" jdbcType="INTEGER"/>
        <result property="refreshSeconds" column="refresh_seconds" jdbcType="INTEGER"/>
        <result property="maintainerId" column="maintainer_id" jdbcType="VARCHAR"/>
        <result property="maintainerName" column="maintainer_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        interface_id,dc_id,name,
        query,args,refresh_permits,
        refresh_seconds,maintainer_id,maintainer_name,description
    </sql>


    <insert id="save">
        insert into niop_data_dynamic_interface(gmt_modified, gmt_create,
                                                interface_id, dc_id, name,
                                                query, args, refresh_permits,
                                                refresh_seconds, maintainer_id, maintainer_name, description)
        values (now(), now(), #{interfaceId}, #{dcId},
                #{name}, #{query}, #{args}, #{refreshPermits},
                #{refreshSeconds}, #{maintainerId}, #{maintainerName}, #{description})
    </insert>

    <update id="update">
        update niop_data_dynamic_interface
        <set>
            <if test="dcId != null">dc_id = #{dcId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="query != null">query = #{query},</if>
            <if test="args != null">args = #{args},</if>
            <if test="refreshPermits != null">refresh_permits = #{refreshPermits},</if>
            <if test="refreshSeconds != null">refresh_seconds = #{refreshSeconds},</if>
            <if test="maintainerId != null">maintainer_id = #{maintainerId},</if>
            <if test="maintainerName != null">maintainer_name = #{maintainerName},</if>
            description = #{description},
            gmt_modified = now()
        </set>
        where interface_id = #{interfaceId}
    </update>


    <delete id="delete">
        delete
        from niop_data_dynamic_interface
        where interface_id = #{interfaceId}
    </delete>

    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface
        <where>
            <if test="pageQuery.keyword != null">
                and (
                    name like concat('%', #{pageQuery.keyword}, '%')
                    or interface_id like concat('%', #{pageQuery.keyword}, '%')
                    )
            </if>
            <if test="pageQuery.maintainerName != null and pageQuery.maintainerName != ''">
                and maintainer_name = #{pageQuery.maintainerName}
            </if>
            <if test="pageQuery.dcId != null and pageQuery.dcId != '' ">
                and dc_id = #{pageQuery.dcId}
            </if>
        </where>
        order by gmt_create desc
    </select>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface
        where interface_id = #{interfaceId}
    </select>

    <select id="getByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_data_dynamic_interface
        where name = #{name}
    </select>


</mapper>
