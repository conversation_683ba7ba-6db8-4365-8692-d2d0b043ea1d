<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceTagMapper">


    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        interface_id,name
    </sql>
    <insert id="save">
        insert into niop_data_dynamic_interface_tag (gmt_modified,gmt_create,interface_id,name)
        values
        <foreach collection="tags" item="tag" separator=",">
            (now(),now(),#{interfaceId},#{tag})
        </foreach>
    </insert>


    <delete id="delete">
        delete
        from niop_data_dynamic_interface_tag
        where interface_id = #{interfaceId}
    </delete>

    <select id="getAll" resultType="java.lang.String">
        select name
        from niop_data_dynamic_interface_tag
        where interface_id = #{interfaceId}
    </select>
</mapper>
