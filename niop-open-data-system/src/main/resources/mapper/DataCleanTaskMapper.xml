<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dataclear.mapper.DataClearTaskMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask">
        <id column="rule_id" property="ruleId" jdbcType="INTEGER"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="dc_id" property="dcId" jdbcType="VARCHAR"/>
        <result column="table_name" property="tableName" jdbcType="VARCHAR"/>
        <result column="sql" property="sql" jdbcType="VARCHAR"/>
        <result column="real_sql" property="realSql" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="task_status" property="taskStatus" jdbcType="SMALLINT"/>
        <result column="real_delete_num" property="realDeleteNum" jdbcType="INTEGER"/>
        <result column="predict_delete_num" property="predictDeleteNum" jdbcType="INTEGER"/>
        <result column="next_exec_time" property="nextExecTime" jdbcType="TIMESTAMP"/>
        <result column="rule_id" property="ruleId" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        task_id
        ,
    dc_id,
    table_name,
    sql,
    duration,
    task_status,
    real_delete_num,
    predict_delete_num,
    next_exec_time,
    rule_id,
    real_sql
    </sql>

    <insert id="insert">
        insert into open_data_clean_task(gmt_modified, gmt_create, task_id, dc_id, table_name, sql, duration,
                                         task_status, real_delete_num,
                                         predict_delete_num, next_exec_time, rule_id, real_sql)
        values (now(), now(), #{taskId}, #{dcId}, #{tableName}, #{sql}, #{duration}, #{taskStatus}, #{realDeleteNum},
                #{predictDeleteNum}, #{nextExecTime}, #{ruleId}, #{realSql})
    </insert>

    <update id="update">
        update open_data_clean_task
        <set>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="dcId != null and dcId != ''">dc_id = #{dcId},</if>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="sql != null and sql != ''">sql = #{sql},</if>
            <if test="realSql != null and realSql != ''">real_sql = #{realSql},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="realDeleteNum != null">real_delete_num = #{realDeleteNum},</if>
            <if test="predictDeleteNum != null">predict_delete_num = #{predictDeleteNum},</if>
            <if test="nextExecTime != null">next_exec_time = #{nextExecTime},</if>
            gmt_modified=now()
        </set>
        where task_id = #{taskId}
    </update>

    <update id="updateByRuleId">
        update open_data_clean_task
        <set>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="dcId != null and dcId != ''">dc_id = #{dcId},</if>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="sql != null and sql != ''">sql = #{sql},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="realDeleteNum != null">real_delete_num = #{realDeleteNum},</if>
            <if test="predictDeleteNum != null">predict_delete_num = #{predictDeleteNum},</if>
            <if test="nextExecTime != null">next_exec_time = #{nextExecTime},</if>
            <if test="realSql != null and realSql != ''">real_sql = #{realSql},</if>
        </set>
        where rule_id = #{ruleId} and (task_status=0 or task_status=2)
    </update>

    <update id="updateStatus">
        update open_data_clean_task
        <set>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
        </set>
        where task_id = #{taskId}
    </update>

    <delete id="delete">
        delete
        from open_data_clean_task
        where task_id = #{taskId}
    </delete>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_task
        where task_id = #{task_id}
    </select>

    <select id="getByRuleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_task
        where rule_id = #{ruleId}
        and task_status>=0
        order by next_exec_time desc limit 1
    </select>

    <select id="fuzzyQuery" resultType="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_task
        <where>
            <if test="fuzzyQuery.taskId != null and fuzzyQuery.taskId != ''">
                and task_id like concat('%', #{fuzzyQuery.taskId}, '%')
            </if>
            <if test="fuzzyQuery.dcId != null and fuzzyQuery.dcId != ''">
                and dc_id like concat('%', #{fuzzyQuery.dcId}, '%')
            </if>
            <if test="fuzzyQuery.tableName != null and fuzzyQuery.tableName != ''">
                and table_name like concat('%', #{fuzzyQuery.tableName}, '%')
            </if>
            <if test="fuzzyQuery.sql != null and fuzzyQuery.sql != ''">
                and sql like concat('%', #{fuzzyQuery.sql}, '%')
            </if>
        </where>
        order by task_id
        limit 50
    </select>

    <select id="page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_task
        WHERE rule_id = #{pageQuery.ruleId}
        order by gmt_create desc
    </select>

    <select id="list" resultType="cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask">
        select
        <include refid="Base_Column_List"/>
        from open_data_clean_task
        where task_status=0
        <![CDATA[
            and next_exec_time < now()
          ]]>
        order by next_exec_time asc limit 5
    </select>

    <update id="updateIds">
        update open_data_clean_task
        set task_status = 2
        where task_id = #{taskId}
    </update>

    <update id="updatePending">
        update open_data_clean_task
        set task_status = 0
        where task_status = 2
    </update>

</mapper>
