<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.callback.mapper.CbHttpTaskMapper">

    <resultMap id="BaseResultMap" type="cn.newrank.niop.data.biz.callback.pojo.CbHttpTask">
        <id property="cid" column="cid" jdbcType="CHAR"/>
        <id property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="sourceKey" column="source_key" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceType" column="source_type" jdbcType="VARCHAR"/>
        <result property="cbId" column="cb_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="retryTimes" column="retry_times" jdbcType="INTEGER"/>
        <result property="errorMsg" column="error_msg" jdbcType="VARCHAR"/>
        <result property="nextCallbackTime" column="next_callback_time" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        cid,gmt_create,source_key,
        source_id,source_type,cb_id,
        status,retry_times,error_msg,partition,
        next_callback_time,gmt_modified
    </sql>
    <insert id="insertBatch">
        insert into niop_dc_callback_http_task_${partition}(cid,gmt_create,source_key,source_id,source_type,cb_id,
        status,retry_times,error_msg,next_callback_time,partition,gmt_modified)
        values
        <foreach collection="tasks" item="item" separator=",">
            (#{item.cid},#{item.gmtCreate},#{item.sourceKey},#{item.sourceId},#{item.sourceType},#{item.cbId},
            #{item.status},#{item.retryTimes},#{item.errorMsg},#{item.nextCallbackTime},#{item.partition},#{item.gmtModified})
        </foreach>
        on conflict(cid,partition) do update set
        source_key= excluded.source_key,
        source_id= excluded.source_id,
        source_type= excluded.source_type,
        cb_id= excluded.cb_id,
        status= excluded.status,
        retry_times= excluded.retry_times,
        error_msg= excluded.error_msg,
        next_callback_time= excluded.next_callback_time,
        gmt_modified= excluded.gmt_modified
    </insert>


    <update id="updateBatch">
        insert into niop_dc_callback_http_task_${partition}(cid,gmt_create,source_key,source_id,source_type,cb_id,
        status,retry_times,error_msg,next_callback_time,partition,gmt_modified)
        VALUES
        <foreach collection="tasks" item="item" separator=",">
            (#{item.cid},#{item.gmtCreate},#{item.sourceKey},#{item.sourceId},#{item.sourceType},#{item.cbId},
            #{item.status},#{item.retryTimes},#{item.errorMsg},#{item.nextCallbackTime},#{item.partition},#{item.gmtModified})
        </foreach>
        on conflict(cid,partition) do update set
        source_key= excluded.source_key,
        source_id= excluded.source_id,
        source_type= excluded.source_type,
        cb_id= excluded.cb_id,
        status= excluded.status,
        retry_times= excluded.retry_times,
        error_msg= excluded.error_msg,
        next_callback_time= excluded.next_callback_time,
        gmt_modified= excluded.gmt_modified
    </update>
    <update id="createTable">
        CREATE TABLE IF NOT EXISTS niop_dc_callback_http_task_${partition} PARTITION OF niop_dc_callback_http_task
                      FOR VALUES FROM ('${startDate}') TO ('${endDate}')
    </update>
    <delete id="dropTable">
        DROP TABLE IF EXISTS niop_dc_callback_http_task_${partition}
    </delete>

    <select id="listRunning" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from niop_dc_callback_http_task_${partition}
        where cb_id=#{cbId} and next_callback_time &lt;=#{startTime} and status='running'
        order by next_callback_time
        limit #{taskCount}
    </select>

    <select id="queryTables" resultType="java.lang.String">
        SELECT
            child.relname
        FROM
            pg_inherits
                JOIN
            pg_class parent ON pg_inherits.inhparent = parent.oid
                JOIN
            pg_class child ON pg_inherits.inhrelid = child.oid
        WHERE
            parent.relname = 'niop_dc_callback_http_task';
    </select>

    <select id="queryCleanEndPartition" resultType="java.sql.Date">
        SELECT MIN(partition) FROM niop_dc_callback_http_task WHERE status = 'running'
    </select>

    <select id="queryStartPartition" resultType="java.sql.Date">
        SELECT MIN(partition) FROM niop_dc_callback_http_task WHERE cb_id = #{cbId}
                                                                and status = 'running'
                                                                and next_callback_time &lt;= now()
    </select>
</mapper>
