<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.dao.mapper.CallbackRedirectRetryMapper">

    <sql id="Base_Column_List">
        id,gmt_modified,gmt_create,
        retry_id, retry_status, retry_nums, next_retry_time, error_info, storage_biz, source_key, source_id,
        source_type, app_id, consumer_record_carrier, payload
    </sql>

    <sql id="Insert_Column_List">
        gmt_modified, gmt_create,
        retry_id, retry_status, retry_nums, next_retry_time, error_info, storage_biz, source_key, source_id,
        source_type, app_id, consumer_record_carrier, payload
    </sql>

    <insert id="saveBatch">
        insert into niop_data_callback_redirect_retry
        (<include refid="Insert_Column_List"/>)
        values
        <foreach collection="retryPoList" item="item" separator=",">
            (now(),  #{item.gmtCreate}, #{item.retryId}, CAST(#{item.retryStatus} AS INTEGER),
            CAST(#{item.retryNums} AS INTEGER), #{item.nextRetryTime}, #{item.errorInfo}, #{item.storageBiz},
            #{item.sourceKey}, #{item.sourceId}, #{item.sourceType}, #{item.appId}, #{item.consumerRecordCarrier}, #{item.payload})
        </foreach>
        ON CONFLICT (source_key, source_id, source_type) DO UPDATE SET
        retry_id = EXCLUDED.retry_id,
        retry_status = CAST(EXCLUDED.retry_status AS INTEGER),
        retry_nums = CAST(EXCLUDED.retry_nums AS INTEGER),
        next_retry_time = EXCLUDED.next_retry_time,
        error_info = EXCLUDED.error_info,
        storage_biz = EXCLUDED.storage_biz,
        source_key = EXCLUDED.source_key,
        source_type = EXCLUDED.source_type,
        app_id = EXCLUDED.app_id,
        consumer_record_carrier = EXCLUDED.consumer_record_carrier,
        payload = EXCLUDED.payload,
        gmt_modified = EXCLUDED.gmt_modified;
    </insert>

    <update id="updateBatch">
        <foreach collection="retryPoList" item="item" index="index" separator=";">
            update niop_data_callback_redirect_retry
            <set>
                <if test="item.retryStatus != null">retry_status = CAST(#{item.retryStatus} AS INTEGER),</if>
                <if test="item.retryNums != null">retry_nums = CAST(#{item.retryNums} AS INTEGER),</if>
                <if test="item.nextRetryTime != null">next_retry_time = #{item.nextRetryTime},</if>
                <if test="item.errorInfo != null and item.errorInfo != ''">error_info = #{item.errorInfo},</if>
                <if test="item.storageBiz != null and item.storageBiz != ''">storage_biz = #{item.storageBiz},</if>
                <if test="item.sourceKey != null and item.sourceKey != ''">source_key = #{item.sourceKey},</if>
                <if test="item.sourceId != null and item.sourceId != ''">source_id = #{item.sourceId},</if>
                <if test="item.sourceType != null and item.sourceType != ''">source_type = #{item.sourceType},</if>
                <if test="item.appId != null and item.appId != ''">app_id = #{item.appId},</if>
                <if test="item.consumerRecordCarrier != null and item.consumerRecordCarrier != ''">consumer_record_carrier = #{item.consumerRecordCarrier},</if>
                <if test="item.payload != null and item.payload != ''">payload = #{item.payload},</if>
                gmt_modified = now()
            </set>
            where retry_id = #{item.retryId}
        </foreach>
    </update>

    <select id="listByRetryStatus" resultType="cn.newrank.niop.data.biz.pojo.po.CallbackRetryPo">
        select <include refid="Base_Column_List"/>
        from niop_data_callback_redirect_retry
        where retry_status = #{retryStatus}
        and next_retry_time &lt; now()
        limit #{size}
    </select>

</mapper>
