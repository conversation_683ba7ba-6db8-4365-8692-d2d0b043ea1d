<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.export.mapper.DataExportSubtaskMapper">

    <resultMap id="subtaskMap" type="cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="exportTaskId" column="export_task_id" jdbcType="CHAR"/>
        <result property="resultTaskId" column="result_task_id" jdbcType="VARCHAR"/>
        <result property="paramJson" column="param_json" jdbcType="VARCHAR"/>
        <result property="bizCode" column="biz_code" jdbcType="INTEGER"/>
        <result property="bizMsg" column="biz_msg" jdbcType="VARCHAR"/>
        <result property="subtaskStatus" column="task_status" jdbcType="VARCHAR"/>
        <result property="dataNum" column="data_num" jdbcType="INTEGER"/>
        <result property="taskFinishedTime" column="task_finished_time" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="subtaskExecutionMap" type="cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo">
        <id property="exportTaskId" column="export_task_id" jdbcType="CHAR"/>
        <result property="succeedCount" column="succeed_count" jdbcType="INTEGER"/>
        <result property="unfinishedCount" column="unfinished_count" jdbcType="INTEGER"/>
        <result property="lastFinishedTime" column="last_finished_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="batchInsert">
        insert into niop_data_export_subtask
        (export_task_id, param_json, task_status)
        values
        <foreach collection="subtasks" item="sub" separator=",">
            (
                #{sub.exportTaskId},
                #{sub.paramJson},
                #{sub.subtaskStatus}
            )
        </foreach>
        on conflict (export_task_id, param_json) do nothing
    </insert>

    <select id="countSubtasks" resultType="Integer">
        select count(*) from niop_data_export_subtask
        where export_task_id = #{exportTaskId}
    </select>

    <select id="listNotSubmittedSubtasks" resultMap="subtaskMap">
        select id, export_task_id, param_json
        from niop_data_export_subtask
        where export_task_id = #{exportTaskId} and task_status = '0'
        limit #{batchSize}
    </select>

    <select id="getSubtask" resultMap="subtaskMap">
        select id, export_task_id, result_task_id, param_json,
               biz_code, biz_msg, task_status, data_num, task_finished_time
        from niop_data_export_subtask
        where export_task_id = #{exportTaskId} and result_task_id = #{resultTaskId}
    </select>

    <select id="countNotSubmittedSubtasks" resultType="Integer">
        select count(*) from niop_data_export_subtask
        where export_task_id = #{exportTaskId} and task_status = '0'
    </select>

    <update id="subtaskSubmitted">
        update niop_data_export_subtask
        set result_task_id = #{resultTaskId},
            task_status = '1'
        where id = #{id}
    </update>

    <update id="subtaskFailed">
        update niop_data_export_subtask
        set biz_code = #{bizCode},
            biz_msg = #{bizMsg},
            task_status = -1,
            data_num = #{dataNum},
            task_finished_time = #{taskFinishedTime}
        where id = #{id}
    </update>

    <update id="batchUpdateSubtasks">
        <foreach collection="subtasks" item="task" separator=";">
            update niop_data_export_subtask
            set biz_code = #{task.bizCode},
                biz_msg = #{task.bizMsg},
                task_status = #{task.subtaskStatus},
                data_num = #{task.dataNum},
                task_finished_time = #{task.taskFinishedTime}
            where result_task_id = #{task.resultTaskId} and task_status = '1'
        </foreach>
    </update>

    <select id="getSubtaskExecutionInfo" resultMap="subtaskExecutionMap">
        select export_task_id,
            sum(case when task_status = '2' then 1 else 0 end) as succeed_count,
            sum(case when task_status in ('0', '1') then 1 else 0 end) as unfinished_count,
            max(case when task_status = '2' then task_finished_time else null end) as last_finished_time
        from niop_data_export_subtask
        where export_task_id = #{exportTaskId}
        group by export_task_id
    </select>

    <select id="listSubtasksByCursor" resultMap="subtaskMap">
        select id, export_task_id, result_task_id, biz_code, biz_msg
        from niop_data_export_subtask
        where export_task_id = #{exportTaskId} and task_status = '2'
        and id > #{cursor}
        order by id asc
        limit #{limit}
    </select>

    <select id="listResultTaskIds" resultType="String">
        select result_task_id
        from niop_data_export_subtask
        where export_task_id = #{exportTaskId}
        limit 20
    </select>

    <select id="page" resultMap="subtaskMap">
        select id, export_task_id, result_task_id, param_json, biz_code, biz_msg, task_status,
               data_num, task_finished_time, gmt_create
        from niop_data_export_subtask
        <where>
            <if test="query.exportTaskId != null and query.exportTaskId != ''">
                and export_task_id = #{query.exportTaskId}
            </if>
            <if test="query.subtaskStatus != null and query.subtaskStatus != ''">
                and task_status = #{query.subtaskStatus}
            </if>
        </where>
        order by id asc
    </select>

    <update id="emptyData">
        update niop_data_export_subtask
        set data_num = 0
        where result_task_id = #{resultTaskId}
        and data_num != 0
    </update>

    <update id="resetSubtask">
        update niop_data_export_subtask
        set result_task_id = null,
            biz_code = null,
            biz_msg = null,
            task_status = '0',
            data_num = null,
            task_finished_time = null
        where export_task_id = #{exportTaskId}
            and result_task_id = #{resultTaskId}
            and task_status != '0'
    </update>

</mapper>
