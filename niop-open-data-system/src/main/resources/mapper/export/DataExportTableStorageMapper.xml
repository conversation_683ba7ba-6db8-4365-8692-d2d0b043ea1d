<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.export.mapper.DataExportTableStorageMapper">

    <resultMap id="tableStorageMap" type="cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage">
        <id property="uniqueId" column="unique_id" jdbcType="CHAR"/>
        <result property="taskId" column="task_id" jdbcType="CHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryTableStorageContents" resultMap="tableStorageMap">
        select unique_id, task_id, content, create_time
        from niop_table_storage
        where task_id = #{taskId}
        <if test="uniqueId != null and uniqueId != ''">
            and unique_id > #{uniqueId}
        </if>
        order by unique_id asc
        limit #{limit}
    </select>

    <select id="existsData" resultType="Boolean">
        select exists (
            select 1 from niop_table_storage
            where task_id in
            <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
            limit 1
        )
    </select>
</mapper>
