<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.export.mapper.DataExportMapper">

    <resultMap id="dataExportMap" type="cn.newrank.niop.data.biz.export.pojo.po.DataExport">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="exportId" column="export_id" jdbcType="CHAR"/>
        <result property="targetId" column="target_id" jdbcType="VARCHAR"/>
        <result property="exportType" column="export_type" jdbcType="VARCHAR"/>
        <result property="exportName" column="export_name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insert">
        insert into niop_data_export
        (export_id, export_type, target_id, export_name, description)
        values (#{exportId}, #{exportType}, #{targetId}, #{exportName}, #{description})
        on conflict (export_id) do nothing
    </insert>

    <select id="getByExportId" resultMap="dataExportMap">
        select id, export_id, target_id, export_type, export_name, description,
               gmt_create, gmt_modified
        from niop_data_export
        where export_id = #{exportId}
    </select>

    <select id="existDataExport" resultType="Boolean">
        select count(*) from niop_data_export
        where export_id = #{exportId}
    </select>

    <select id="page" resultMap="dataExportMap">
        select id, export_id, target_id, export_type, export_name, description, gmt_create
        from niop_data_export
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                    export_name like concat('%', #{query.keyword}, '%')
                    or target_id like concat('%', #{query.keyword}, '%')
                )
            </if>
        </where>
        order by id desc
    </select>

</mapper>
