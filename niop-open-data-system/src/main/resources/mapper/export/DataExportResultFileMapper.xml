<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.export.mapper.DataExportResultFileMapper">

    <resultMap id="resultFileMap" type="cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="exportTaskId" column="export_task_id" jdbcType="CHAR"/>
        <result property="md5" column="md5" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="downloadUrl" column="download_url" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insert">
        insert into niop_data_export_result_file
        (export_task_id, md5, file_name, download_url)
        values
        (
            #{exportTaskId},
            #{md5},
            #{fileName},
            #{downloadUrl}
        )
        on conflict(export_task_id, md5) do nothing
    </insert>

    <select id="listResultFiles" resultMap="resultFileMap">
        select id, export_task_id, md5, file_name, download_url, gmt_create
        from niop_data_export_result_file
        where export_task_id = #{exportTaskId}
    </select>

</mapper>
