<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newrank.niop.data.biz.export.mapper.DataExportTaskMapper">

    <resultMap id="exportTaskMap" type="cn.newrank.niop.data.biz.export.pojo.po.DataExportTask">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="exportId" column="export_id" jdbcType="CHAR"/>
        <result property="exportTaskId" column="export_task_id" jdbcType="CHAR"/>
        <result property="bizScene" column="biz_scene" jdbcType="VARCHAR"/>
        <result property="taskParamFile" column="task_param_file" jdbcType="VARCHAR"/>
        <result property="deliveryType" column="delivery_type" jdbcType="VARCHAR"/>
        <result property="runningStatus" column="running_status" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="export_task_status" jdbcType="VARCHAR"/>
        <result property="paramTotalNum" column="param_total_num" jdbcType="INTEGER"/>
        <result property="succeedTotalNum" column="succeed_total_num" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="taskFinishedTime" column="task_finished_time" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="taskExecutionDTOMap" type="cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="exportId" column="export_id" jdbcType="CHAR"/>
        <result property="exportTaskId" column="export_task_id" jdbcType="CHAR"/>
        <result property="targetId" column="target_id" jdbcType="VARCHAR"/>
        <result property="exportType" column="export_type" jdbcType="VARCHAR"/>
        <result property="deliveryType" column="delivery_type" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="export_task_status" jdbcType="VARCHAR"/>
        <result property="paramTotalNum" column="param_total_num" jdbcType="INTEGER"/>
        <result property="succeedTotalNum" column="succeed_total_num" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insert">
        insert into niop_data_export_task
        (export_id, export_task_id, biz_scene, task_param_file, delivery_type, running_status,
         export_task_status, param_total_num, succeed_total_num, creator, task_finished_time)
        values
        (
            #{exportId},
            #{exportTaskId},
            #{bizScene},
            #{taskParamFile},
            #{deliveryType},
            #{runningStatus},
            #{taskStatus},
            #{paramTotalNum},
            #{succeedTotalNum},
            #{creator},
            #{taskFinishedTime}
        )
    </insert>

    <select id="page" resultMap="exportTaskMap">
        select id, export_id, export_task_id, biz_scene, task_param_file, delivery_type, running_status,
        export_task_status, param_total_num, succeed_total_num, creator, task_finished_time, gmt_create
        from niop_data_export_task
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                biz_scene like concat('%', #{query.keyword}, '%')
                or export_task_id like concat('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.creator != null and query.creator != ''">
                and creator = #{query.creator}
            </if>
        </where>
        order by id desc
    </select>

    <select id="getExportTask" resultMap="exportTaskMap">
        select id, export_id, export_task_id, biz_scene, task_param_file, delivery_type, running_status,
        export_task_status, param_total_num, succeed_total_num, creator, task_finished_time, gmt_create
        from niop_data_export_task
        where export_task_id = #{exportTaskId}
    </select>

    <update id="updateRunningStatus">
        update niop_data_export_task
        set running_status = #{runningStatus}
        where id = #{id}
    </update>

    <select id="listRunningExportTasksByStatus" resultMap="exportTaskMap">
        select id, export_id, export_task_id, biz_scene, task_param_file, delivery_type, running_status,
               export_task_status, param_total_num, succeed_total_num, creator, task_finished_time, gmt_create
        from niop_data_export_task
        where export_task_status = #{taskStatus} and running_status = '1'
        limit 4
    </select>

    <select id="listRunningExportTaskDTOByStatus" resultMap="taskExecutionDTOMap">
        select et.id, et.export_id, et.export_task_id, e.export_type, e.target_id, et.delivery_type,
               et.export_task_status, et.param_total_num, et.succeed_total_num
        from niop_data_export_task et join niop_data_export e on et.export_id = e.export_id
        where et.export_task_status = #{taskStatus} and et.running_status = '1'
        limit 8
    </select>

    <select id="getExportTaskExecutionDTO" resultMap="taskExecutionDTOMap">
        select et.id, et.export_id, et.export_task_id, e.export_type, e.target_id, et.delivery_type,
               et.export_task_status, et.param_total_num, et.succeed_total_num
        from niop_data_export_task et join niop_data_export e on et.export_id = e.export_id
        where et.export_task_id = #{exportTaskId}
    </select>

    <update id="update">
        update niop_data_export_task
        <set>
            <if test="runningStatus != null">running_status = #{runningStatus},</if>
            <if test="taskStatus != null">export_task_status = #{taskStatus},</if>
            <if test="paramTotalNum != null">param_total_num = #{paramTotalNum},</if>
            <if test="succeedTotalNum != null">succeed_total_num = #{succeedTotalNum},</if>
            <if test="taskFinishedTime != null">task_finished_time = #{taskFinishedTime},</if>
        </set>
        where id = #{id}
    </update>

    <update id="taskFinished">
        update niop_data_export_task
        set export_task_status = #{taskStatus},
            succeed_total_num = #{succeedTotalNum},
            task_finished_time = #{taskFinishedTime}
        where export_task_id = #{exportTaskId}
    </update>

</mapper>
