package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;


import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监测话题表
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
public interface XhsExpConfigMapper {

    /**
     * 获取话题调度器
     * @return 调度器列表
     */
    List<XhsExpConfig> listConfigs();

    /**
     * 根据话题权重获取配置
     * @param topicWeight 话题权重
     * @return 配置
     */
    XhsExpConfig getConfig(@Param("topicWeight") Integer topicWeight);

}




