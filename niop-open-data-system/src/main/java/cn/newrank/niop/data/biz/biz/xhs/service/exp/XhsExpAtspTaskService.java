package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsExpTaskEnum;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.*;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.*;
import cn.newrank.niop.data.common.AtspClient;
import cn.newrank.niop.data.common.enums.AtspTaskEnum;
import cn.newrank.niop.data.context.AppContext;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.sdk.common.entity.Resp;
import cn.newrank.niop.sdk.common.producer.ProducerClient;
import cn.newrank.niop.sdk.common.producer.TaskParam;
import cn.newrank.niop.sdk.common.producer.TaskResult;
import cn.newrank.niop.sdk.http.base.DefaultProducerClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/28 11:46:10
 */
@Slf4j
@Service
public class XhsExpAtspTaskService {

    private final XhsExpAtspTaskMapper xhsExpAtspTaskMapper;
    private final XhsExpParentTaskMapper xhsExpParentTaskMapper;
    private final XhsExpTopicMapper xhsExpTopicMapper;
    private final XhsExpCacheService xhsExpCacheService;
    private final XhsExpHoloOpusMapper xhsExpHoloOpusMapper;
    private final XhsExpHoloOpusLogMapper xhsExpHoloOpusLogMapper;
    private final XhsExpNewTopicMapper xhsExpNewTopicMapper;
    private final ProducerClient producerClient;
    private final XhsExpTaskStoreService xhsExpTaskStoreService;
    private static final Integer SUBMIT_SIZE = 200;
    private static final String ABILITY_ID = "CWWEVCGP";
    private static final String SCENE_ID = "X7YM7KEH";
    private static final String APP_KEY_TEST = "adf7b87fe889480fbf786f277badd093";
    private static final String APP_KEY_PRODUCT = "4d605661c2a54d708398be9f1addc10a";
    public static final String APP_KEY = AppContext.isProduct() ? APP_KEY_PRODUCT : APP_KEY_TEST;
    private static final Integer TASK_SUCCESS = 0;
    private static final Integer TASK_FAILED = -1;
    private static final Integer TASK_TIMEOUT = -2;

    public XhsExpAtspTaskService(XhsExpAtspTaskMapper xhsExpAtspTaskMapper,
                                 XhsExpParentTaskMapper xhsExpParentTaskMapper,
                                 XhsExpTopicMapper xhsExpTopicMapper,
                                 XhsExpCacheService xhsExpCacheService,
                                 XhsExpHoloOpusMapper xhsExpHoloOpusMapper,
                                 XhsExpHoloOpusLogMapper xhsExpHoloOpusLogMapper,
                                 XhsExpNewTopicMapper xhsExpNewTopicMapper,
                                 XhsExpTaskStoreService xhsExpTaskStoreService) {
        this.xhsExpAtspTaskMapper = xhsExpAtspTaskMapper;
        this.xhsExpParentTaskMapper = xhsExpParentTaskMapper;
        this.xhsExpTopicMapper = xhsExpTopicMapper;
        this.xhsExpCacheService = xhsExpCacheService;
        this.xhsExpHoloOpusMapper = xhsExpHoloOpusMapper;
        this.xhsExpHoloOpusLogMapper = xhsExpHoloOpusLogMapper;
        this.xhsExpNewTopicMapper = xhsExpNewTopicMapper;
        this.xhsExpTaskStoreService = xhsExpTaskStoreService;
        this.producerClient = new DefaultProducerClient(AtspClient.HOST.getGatewayContextPath(), APP_KEY);
    }

    /**
     * 提交任务
     *
     * @param param 任务id
     */
    public void runSubmit(String param) {
        Integer submitCount = Strings.isBlank(param) ? 2000 : Integer.parseInt(param);
        int count = 0;
        // 限制最大提交次数
        for (int i = 0; i < submitCount / SUBMIT_SIZE; i++) {
            List<XhsExpAtspTask> tasks = xhsExpAtspTaskMapper.listNotSubmitTask(SUBMIT_SIZE);
            if (CollectionUtil.isEmpty(tasks)) {
                break;
            }
            count += submitAtspTaskBatch(tasks);
        }
        XxlJobLogger.log("提交任务, size: {}", count);
    }

    /**
     * 获取结果
     *
     * @param param
     */
    public void runResult(String param) {
        LocalDate execDate = LocalDate.now().minusDays(1);
        String cursor = "";
        int count = 0;
        while (true) {
            List<XhsExpAtspTask> atspTasks = xhsExpAtspTaskMapper.listRunningTask(execDate, cursor, SUBMIT_SIZE);
            if (CollectionUtil.isEmpty(atspTasks)) {
                break;
            }
            List<XhsExpAtspResult> taskResult = atspTasks.stream()
                    .map(this::dealAtspTaskResult)
                    .filter(Objects::nonNull).toList();
            taskResultBatch(taskResult);
            count += atspTasks.size();
            cursor = atspTasks.get(atspTasks.size() - 1).getTaskId();
        }
        XxlJobLogger.log("处理积压任务, size: {}", count);
    }

    private @Nullable XhsExpAtspResult dealAtspTaskResult(XhsExpAtspTask task) {
        try {
            Resp<TaskResult> resp = producerClient.getResult(task.getTaskId());
            XhsExpAtspResult dataResult;
            if (resp.is200Code()) {
                dataResult = XhsExpAtspResult.buildByTaskResult(resp.getData());
                return dataResult;
            }
            final String code = String.valueOf(resp.getCode());
            final AtspTaskEnum atspTaskEnum = AtspTaskEnum.of(code);
            switch (Objects.requireNonNull(atspTaskEnum)) {
                case ATSP_RUNNING, ATSP_QUEUE -> {
                    return null;
                }
                case ATSP_FAILED -> {
                    TaskResult result = new TaskResult();
                    result.setTaskId(task.getTaskId());
                    result.setBizCode(1102);
                    result.setBizMsg("执行失败");
                    result.setFinishTime(LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER));
                    dataResult = XhsExpAtspResult.buildByTaskResult(result);
                    return dataResult;
                }
                case ATSP_TIMEOUT -> {
                    TaskResult result = new TaskResult();
                    result.setTaskId(task.getTaskId());
                    result.setBizCode(1105);
                    result.setBizMsg("任务执行超时");
                    result.setFinishTime(LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER));
                    dataResult = XhsExpAtspResult.buildByTaskResult(result);
                    return dataResult;
                }
                default -> {
                    XxlJobLogger.log("未识别的状态:{}", code);
                    log.warn("未识别的状态:{}", code);
                }
            }
        } catch (Exception e) {
            log.warn("获取任务结果失败 error:{} msg: {}", e, JSON.toJSONString(task));
        }
        return null;
    }


    /**
     * 批量提交任务
     *
     * @param tasks 任务
     */
    private int submitAtspTaskBatch(List<XhsExpAtspTask> tasks) {
        int count = 0;
        Map<LocalDate, List<XhsExpAtspTask>> taskGroupMap = tasks.stream()
                .collect(Collectors.groupingBy(XhsExpAtspTask::getExecDate));

        for (Map.Entry<LocalDate, List<XhsExpAtspTask>> entry : taskGroupMap.entrySet()) {
            Set<String> parentIds = entry.getValue().stream().map(XhsExpAtspTask::getParentId).collect(Collectors.toSet());
            List<XhsExpParentTask> parentTasks = xhsExpParentTaskMapper.getRunningParentTasksByIds(entry.getKey(), parentIds.stream().toList());
            if (CollectionUtil.isEmpty(parentTasks)) {
                XxlJobLogger.log("获取父任务不存在,parentIds:{}", parentIds);
                continue;
            }
            Map<String, Integer> weightMap = parentTasks.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(XhsExpParentTask::getParentId, XhsExpParentTask::getTopicWeight));
            for (XhsExpAtspTask task : entry.getValue()) {
                String initId = task.getTaskId();
                String parentId = task.getParentId();
                if (Strings.isBlank(parentId)) {
                    XxlJobLogger.log("获取parentId失败,taskId:{}", parentId);
                    continue;
                }
                XhsExpConfig config = xhsExpCacheService.getXhsExpConfig(weightMap.get(parentId));
                if (Objects.isNull(config)) {
                    XxlJobLogger.log("获取atspWeight失败,taskId:{}", initId);
                    continue;
                }
                if (submitTaskUpdateTaskId(task, config.getAtspWeight())) {
                    xhsExpAtspTaskMapper.updateTaskStatus(task, initId);
                    count += 1;
                }
            }
        }
        return count;
    }


    /**
     * 4h超时值 单位：秒
     */
    private static final Integer TIMEOUT_VALUE_4_HOUR = 4 * 60 * 60;
    /**
     * 2h超时值 单位：秒
     */
    private static final Integer TIMEOUT_VALUE_2_HOUR = 2 * 60 * 60;

    /**
     * 提交任务
     *
     * @param task 任务
     * @return 是否提交成功
     */
    private boolean submitTaskUpdateTaskId(XhsExpAtspTask task, Integer atspWeight) {
        try {
            TaskParam taskParam = new TaskParam();
            taskParam.setParams(JSON.parseObject(task.getParam()));
            taskParam.setSceneIds(new String[]{SCENE_ID});
            taskParam.setTimeout(switchTimeOut(atspWeight));
            taskParam.setPriority(atspWeight);
            Resp<String> resp = producerClient.submit(ABILITY_ID, taskParam);
            if (resp.is200Code()) {
                task.setTaskId(resp.getData());
                task.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.RUNNING.getDbCode()));
                return true;
            } else {
                XxlJobLogger.log("提交任务失败：{} error: {}", task.getTaskId(), resp.getMsg());
            }
        } catch (Exception e) {
            log.error("提交任务失败：{} error: {}", task.getTaskId(), e.getMessage());
        }
        return false;
    }

    /**
     * 超时时间
     * @param atspWeight 权重
     * @return 超时时间
     */
    public static Integer switchTimeOut(Integer atspWeight) {
        return switch (atspWeight) {
            case 3, 4, 5 -> TIMEOUT_VALUE_2_HOUR;
            default -> TIMEOUT_VALUE_4_HOUR;
        };
    }

    /**
     * 能力回调任务结果批量处理
     *
     * @param results 任务结果
     */
    public void taskResultBatch(List<XhsExpAtspResult> results) {
        if (CollectionUtil.isEmpty(results)) {
            return;
        }
        List<XhsExpAtspTask> tasks = getXhsExpAtspTasks(results);
        if (CollectionUtil.isEmpty(tasks)) {
            return;
        }
        // 按日期分组
        Map<LocalDate, List<XhsExpAtspTask>> taskGroupMap = tasks.stream()
                .collect(Collectors.groupingBy(XhsExpAtspTask::getExecDate));
        List<XhsExpParentTask> parentTasks = List.of();
        for (Map.Entry<LocalDate, List<XhsExpAtspTask>> entry : taskGroupMap.entrySet()) {
            parentTasks = getXhsExpParentTasks(entry.getKey(), tasks);
        }

        final Map<String, XhsExpParentTask> parentTaskMap = getParentTaskMap(parentTasks);
        final Map<String, XhsExpAtspTask> taskMap = getTaskMap(tasks);
        // 处理结果，创建新任务
        List<XhsExpAtspTask> newTasks = dealTaskAndAddNewTask(results, taskMap, parentTaskMap)
                .stream().filter(Objects::nonNull).toList();
        if (CollectionUtil.isEmpty(newTasks)) {
            return;
        }

        // 新任务存储，更新父任务
        Map<LocalDate, List<XhsExpAtspTask>> newTaskGroupMap = newTasks.stream()
                .collect(Collectors.groupingBy(XhsExpAtspTask::getExecDate));
        for (Map.Entry<LocalDate, List<XhsExpAtspTask>> entry : newTaskGroupMap.entrySet()) {
            String partition = DateTimeUtil.dayOfPartition(entry.getKey());
            xhsExpTaskStoreService.storeAtspBatchAndUpdateParent(entry.getValue(), partition, parentTaskMap);
        }
    }


    /**
     * 处理任务结果并创建新翻页任务
     *
     * @param results       结果
     * @param taskMap       任务
     * @param parentTaskMap 父
     * @return 新任务
     */
    private List<XhsExpAtspTask> dealTaskAndAddNewTask(List<XhsExpAtspResult> results,
                                                       Map<String, XhsExpAtspTask> taskMap,
                                                       Map<String, XhsExpParentTask> parentTaskMap) {
        List<XhsExpAtspTask> newTasks = new ArrayList<>();
        for (XhsExpAtspResult result : results) {
            switchTaskDeal(taskMap, parentTaskMap, result, newTasks);
        }
        return newTasks;
    }


    /**
     * 选择处理任务
     *
     * @param taskMap       任务
     * @param parentTaskMap 父
     * @param result        结果
     * @param newTasks      新任务
     */
    private void switchTaskDeal(Map<String, XhsExpAtspTask> taskMap,
                                Map<String, XhsExpParentTask> parentTaskMap,
                                XhsExpAtspResult result,
                                List<XhsExpAtspTask> newTasks) {
        final String resultTaskId = result.getTaskId();
        final AtspTaskEnum bizCode = AtspTaskEnum.of(String.valueOf(result.getBizCode()));
        XhsExpAtspTask task = taskMap.get(resultTaskId);
        if (Objects.isNull(task)) {
            return;
        }
        XhsExpParentTask parentTask = parentTaskMap.get(task.getParentId());
        if (Objects.isNull(parentTask)) {
            return;
        }
        XhsExpAtspTask pageTask;
        if (bizCode == null) {
            pageTask = doFailed(result, parentTask, task);
        } else {
            pageTask = switch (Objects.requireNonNull(bizCode)) {
                case SUCCESS -> doSuccess(result, parentTask, task);
                case FAILED, TIMEOUT, ATSP_FAILED, ATSP_TIMEOUT -> doFailed(result, parentTask, task);
                case ATSP_RUNNING, ATSP_QUEUE -> doRunning();
            };
        }
        newTasks.add(pageTask);
    }


    /**
     * 运行中
     *
     * @return null
     */
    private XhsExpAtspTask doRunning() {
        return null;
    }

    /**
     * 失败
     *
     * @param parentTask 父
     * @param task       任务
     */
    private XhsExpAtspTask doFailed(XhsExpAtspResult result,
                                    XhsExpParentTask parentTask,
                                    XhsExpAtspTask task) {
        task.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.FAILED.getDbCode()));
        task.setBizCode(result.getBizCode());
        task.setBizMsg(result.getBizMsg());

        // 子任务失败不继续翻页
        parentTask.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.SUCCESS.getDbCode()));
        xhsExpTaskStoreService.updateParentTaskAndAtspTask(parentTask, task);
        return null;
    }


    /**
     * 获取父任务
     *
     * @param tasks 任务
     * @return 父任务
     */
    private List<XhsExpParentTask> getXhsExpParentTasks(LocalDate execDate, List<XhsExpAtspTask> tasks) {
        final List<String> parentIds = tasks.stream().map(XhsExpAtspTask::getParentId).toList();
        return xhsExpParentTaskMapper.getParentTasksByIds(execDate, parentIds);
    }


    /**
     * 获取atsp任务
     *
     * @param results 任务结果
     * @return atsp任务
     */
    private List<XhsExpAtspTask> getXhsExpAtspTasks(List<XhsExpAtspResult> results) {
        final List<String> taskIds = results.stream().filter(Objects::nonNull)
                .map(XhsExpAtspResult::getTaskId).toList();
        return xhsExpAtspTaskMapper.getTasksByIds(taskIds);
    }

    private static final Integer PAGE_MAX_LIMIT = 50;
    private static final Integer TOPIC_WEIGHT_UPGRADE_PAGE_NUM = 30;
    private static final Integer TOPIC_WEIGHT_FIVE = 5;
    private static final Integer TOPIC_WEIGHT_FOUR = 4;
    private static final Integer TOPIC_WEIGHT_SIX = 6;
    private static final Integer EXP_ATSP_TASK_RUNNING = 1;

    /**
     * 任务成功处理
     *
     * @param result     任务结果
     * @param parentTask 父任务
     * @param task       任务
     * @return 创建翻页atsp任务
     */
    private XhsExpAtspTask doSuccess(XhsExpAtspResult result,
                                     XhsExpParentTask parentTask,
                                     XhsExpAtspTask task) {
        if (!EXP_ATSP_TASK_RUNNING.equals(task.getTaskStatus())) {
            log.info("【小红书扩量】任务已处理成功，跳过：{}", task.getTaskId());
            return null;
        }
        final String parentId = task.getParentId();
        final String topicId = parentTask.getTopicId();
        boolean isExistRateLimit = true;
        boolean isNotPublishTimePeriod = true;
        boolean isPageNumLimit = true;
        List<XhsExpTopicOpus> resultOpusList = result.getList();
        if (CollectionUtil.isNotEmpty(resultOpusList)) {
            if (6 == parentTask.getTopicWeight()) {
                isExistRateLimit = false;
            } else {
                isExistRateLimit = checkIsLimitExistRate(resultOpusList);
            }
            isPageNumLimit = parentTask.getCurrentPageNum() >= PAGE_MAX_LIMIT;
            isNotPublishTimePeriod = dealOpusListNotPeriod(result, parentTask, task);
        }

        if (resultOpusList == null) {
            doFailed(result, parentTask, task);
            return null;
        }

        task.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.SUCCESS.getDbCode()));
        task.setBizCode(result.getBizCode());
        task.setBizMsg(result.getBizMsg());
        parentTask.setCurrentOpusNum(parentTask.getCurrentOpusNum() + resultOpusList.size());

        // 存在cursor
        boolean hasCursor = Strings.isNotBlank(result.getCursor());
        // 检查是否继续翻页
        final boolean isRun = result.getHasMore() && hasCursor && isNotPublishTimePeriod
                && !isPageNumLimit && !isExistRateLimit;
        if (isRun) {
            xhsExpAtspTaskMapper.updateTaskStatus(task, task.getTaskId());
            XhsExpAtspTask newTask = XhsExpAtspTask.initTask(
                    parentId,
                    topicId,
                    parentTask.getExecDate(),
                    result.getCursor(),
                    task.getPage() + 1,parentTask.getTopicWeight());
            parentTask.setCurrentPageNum(parentTask.getCurrentPageNum() + 1);
            return newTask;
        }

        parentTask.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.SUCCESS.getDbCode()));
        // 权重变更和新话题处理
        weightChange(parentTask);
        xhsExpTaskStoreService.updateParentTaskAndAtspTask(parentTask, task);
        return null;
    }


    /**
     * 处理作品列表
     *
     * @param result     结果
     * @param parentTask 父任务
     * @param task       任务
     * @return 是否未发布时间段
     */
    private boolean dealOpusListNotPeriod(XhsExpAtspResult result,
                                          XhsExpParentTask parentTask,
                                          XhsExpAtspTask task) {
        final String taskId = task.getTaskId();
        final String parentId = parentTask.getParentId();
        final String topicId = parentTask.getTopicId();
        final LocalDateTime acqTime = result.getFinishTime();
        final LocalDateTime minPublishTime = parentTask.getMinPublishTime();
        final List<XhsExpTopicOpus> opusList = result.getList();
        final LocalDateTime logDateTime = LocalDate.now().minusDays(6).atStartOfDay();
        // 避免0点跨天存储作品
        Map<LocalDate, List<XhsExpTopicOpus>> groupedByDate = getGroupedByPublishTime(opusList);
        for (Map.Entry<LocalDate, List<XhsExpTopicOpus>> entry : groupedByDate.entrySet()) {
            final String ds = entry.getKey().format(DatePattern.PURE_DATE_FORMATTER);
            final LocalDateTime dateTime = LocalDateTime.now();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<XhsExpTopicOpus> filterLogOpusList = new ArrayList<>();
            List<XhsExpTopicOpus> filterMinOpusList = new ArrayList<>();
            for (XhsExpTopicOpus opus : entry.getValue()) {
                final LocalDateTime publishTime = opus.getPublishTime();
                if (Objects.isNull(publishTime)) {
                    log.error("【小红书】作品发布时间为空，opus：{}", JSON.toJSONString(opus));
                }
                if (publishTime.isAfter(logDateTime)) {
                    filterLogOpusList.add(opus);
                }
                if (publishTime.isAfter(minPublishTime)) {
                    filterMinOpusList.add(opus);
                }
            }
            if (CollectionUtil.isNotEmpty(filterLogOpusList)) {
                xhsExpHoloOpusLogMapper.storeBatch(taskId, parentId, topicId, acqTime, filterLogOpusList, ds, dateTime);
            }
            if (CollectionUtil.isNotEmpty(filterMinOpusList)) {
                // 存储作品到唯一表
                xhsExpHoloOpusMapper.storeBatch(taskId, parentId, topicId, acqTime, filterMinOpusList, ds, dateTime);
            }
            // 存储新话题ID
            storeNewTopicIds(filterMinOpusList, parentTask);
        }
        // minPublishTime 对比最后一个作品的发布时间
        return minPublishTime.isBefore(opusList.get(opusList.size() - 1).getPublishTime());
    }

    private static @NotNull Map<LocalDate, List<XhsExpTopicOpus>> getGroupedByPublishTime(List<XhsExpTopicOpus> opusList) {
        return opusList.stream()
                .filter(opus -> opus.getPublishTime() != null)
                .collect(Collectors.groupingBy(
                        opus -> opus.getPublishTime().toLocalDate()
                ));
    }

    private static final Integer NEW_TOPIC_WEIGHT = 1;


    /**
     * 存储新话题
     *
     * @param storeOpuslist 存储作品
     */
    private void storeNewTopicIds(List<XhsExpTopicOpus> storeOpuslist,
                                  XhsExpParentTask parentTask) {
        if (CollectionUtil.isEmpty(storeOpuslist)) {
            return;
        }
        final LocalDate execDate = parentTask.getExecDate();
        // 获取监测表中没有的新话题ID
        Map<String, Integer> topicesMap = getNewTopicsOpusMap(storeOpuslist);
        if (CollectionUtil.isEmpty(topicesMap)) {
            return;
        }
        final List<String> newTopicIds = topicesMap.keySet().stream().toList();
        List<XhsExpNewTopic> existNewTopics = xhsExpNewTopicMapper.getExistByTopicIds(execDate, newTopicIds);

        // 存在的新话题作品计数
        if (CollectionUtil.isNotEmpty(existNewTopics)) {
            for (XhsExpNewTopic item : existNewTopics) {
                // 原子更新值
                xhsExpNewTopicMapper.updateTopicOpusNum(item, topicesMap.get(item.getTopicId()));
                topicesMap.remove(item.getTopicId());
            }
        }
        if (CollectionUtil.isEmpty(topicesMap)) {
            return;
        }

        // 去除已存在的话题
        xhsExpTopicMapper.listExistTopicIds(topicesMap.keySet().stream().toList()).forEach(topicesMap::remove);


        // 存储新的话题
        List<XhsExpNewTopic> otherNewTopics = topicesMap.keySet().stream()
                .map(topic -> XhsExpNewTopic.build(topic, execDate)).toList();
        if (CollectionUtil.isNotEmpty(otherNewTopics)) {
            List<List<XhsExpNewTopic>> partition = Lists.partition(otherNewTopics, 50);
            for (List<XhsExpNewTopic> items : partition) {
                xhsExpNewTopicMapper.storeBatch(items);
            }
        }
    }


    private static final String JSON_FIELD_TOPIC = "topic";
    private static final String JSON_FIELD_TYPE = "type";
    private static final String JSON_FIELD_LINK = "link";

    /**
     * 获取新话题
     *
     * @param storeOpuslist 存储作品
     * @return 新话题
     */
    private static Map<String, Integer> getNewTopicsOpusMap(List<XhsExpTopicOpus> storeOpuslist) {
        Map<String, Integer> topicIdMap = new HashMap<>(16);
        for (XhsExpTopicOpus opus : storeOpuslist) {
            String hashTags = opus.getHashTags();
            if (Strings.isBlank(hashTags)) {
                continue;
            }
            Set<String> topicIdSet = getTopicIdsFromOpus(hashTags);
            topicIdSet.stream().filter(Objects::nonNull).forEach(topicId -> {
                if (topicIdMap.containsKey(topicId)) {
                    topicIdMap.put(topicId, topicIdMap.get(topicId) + 1);
                } else {
                    topicIdMap.put(topicId, 1);
                }
            });
        }
        return topicIdMap;
    }

    /**
     * 从作品中提取话题ID
     *
     * @param hashTags
     * @return
     */
    private static @NotNull Set<String> getTopicIdsFromOpus(String hashTags) {
        Set<String> topicIdSet = new HashSet<>();
        List<JSONObject> hashTagsArray = JSON.parseArray(hashTags, JSONObject.class);
        hashTagsArray.stream().filter(Objects::nonNull).forEach(hashTag -> {
            if (JSON_FIELD_TOPIC.equals(hashTag.getString(JSON_FIELD_TYPE))) {
                // 提取话题ID
                String topicId = extractTopicId(hashTag.getString(JSON_FIELD_LINK));
                topicIdSet.add(topicId);
            }
        });
        return topicIdSet;
    }


    /**
     * 快速提取话题ID
     *
     * @param url 链接
     * @return 话题ID
     */
    public static String extractTopicId(String url) {
        if (Strings.isBlank(url)) {
            return null;
        }
        int paramIndex = url.indexOf("?");
        //是否带参数
        if (paramIndex == -1) {
            return null;
        }
        String query = url.substring(paramIndex);
        int idIndex = query.indexOf("id=");
        if (idIndex == -1) {
            return null;
        }
        return query.substring(idIndex + 3).split("&", 2)[0];
    }

    /**
     * 权重变更
     */
    private void weightChange(XhsExpParentTask parentTask) {
        final String topicId = parentTask.getTopicId();
        final Integer pageNum = parentTask.getCurrentPageNum();
        Integer topicWeight = xhsExpCacheService.getTopicWeight(topicId);
        if(Objects.isNull(topicWeight)){
            return;
        }
        if (TOPIC_WEIGHT_FIVE.equals(topicWeight)) {
            // 降权 5 -> 4
            if (pageNum < TOPIC_WEIGHT_UPGRADE_PAGE_NUM) {
                xhsExpTopicMapper.updateTopicWeight(topicId, TOPIC_WEIGHT_FOUR);
                xhsExpCacheService.invalidateTopic(topicId);
            }
        } else if (TOPIC_WEIGHT_SIX.equals(topicWeight)) {
            // 权重6 不变更
            return;
        } else {
            // 升权 1,2,3,4 -> 5
            if (pageNum >= TOPIC_WEIGHT_UPGRADE_PAGE_NUM) {
                xhsExpTopicMapper.updateTopicWeight(topicId, TOPIC_WEIGHT_FIVE);
                xhsExpCacheService.invalidateTopic(topicId);
            }
        }
    }


    /**
     * 检查作品存在率
     */
    private static final double OPUS_EXIST_RATE = 0.9;


    /**
     * 检查作品存在率
     *
     * @param resultOpusList resultOpusList
     * @return 是否继续翻页
     */
    private boolean checkIsLimitExistRate(List<XhsExpTopicOpus> resultOpusList) {
        if (CollectionUtil.isEmpty(resultOpusList)) {
            return false;
        }
        Set<String> opusIds = resultOpusList.stream().map(XhsExpTopicOpus::getOpusId).collect(Collectors.toSet());
        final int opusIdNum = opusIds.size();
        final int exist = xhsExpHoloOpusMapper.getOpusNumIsExist(opusIds);
        final double existRate = (double) exist / opusIdNum;
        return existRate > OPUS_EXIST_RATE;
    }


    /**
     * 获取任务Map
     *
     * @param tasks tasks
     * @return Map
     */
    private static @NotNull Map<String, XhsExpAtspTask> getTaskMap(List<XhsExpAtspTask> tasks) {
        return tasks.stream().collect(Collectors.toMap(
                XhsExpAtspTask::getTaskId,
                Function.identity()
        ));
    }

    /**
     * 获取父任务Map
     *
     * @param parentTasks parentTasks
     * @return Map
     */
    private static @NotNull Map<String, XhsExpParentTask> getParentTaskMap(List<XhsExpParentTask> parentTasks) {
        return parentTasks.stream().collect(Collectors.toMap(
                XhsExpParentTask::getParentId,
                Function.identity()
        ));
    }

}
