package cn.newrank.niop.data.biz.biz.xhs.service;

import cn.newrank.niop.data.biz.biz.xhs.pojo.DimOpusToken;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.common.ds.LindormSQLFactory;
import cn.newrank.niop.util.U;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/8 17:40
 */
@Service
public class DimOpusTokenService implements StorageBizService<DimOpusToken> {
    public static final String QUERY_OPUS = "select * from dim_opus_token where aweme_id in (:ids)";
    public static final BeanPropertyRowMapper<DimOpusToken> ENTITY_MAPPER = BeanPropertyRowMapper.newInstance(DimOpusToken.class);
    private static final String INSERT_TEMPLATE = """
            insert into dim_opus_token (aweme_id, account_id, public_time, xsec_token, mb_xsec_token, like_count,
                            comment_count, share_count, acq_time, ana_time)
            values (:awemeId, :accountId, :publicTime, :xsecToken, :mbXsecToken, :likeCount, :commentCount,
                            :shareCount, :acqTime, :anaTime)
            """;
    final NamedParameterJdbcTemplate jdbcTemplate;

    public DimOpusTokenService(DsConfigManager dsConfigManager) {
        this.jdbcTemplate = LindormSQLFactory.DEFAULT.create(dsConfigManager.chooseXhsLmConfig()).availableJdbcTemplate();
    }


    @Override
    public void storeBatch(List<DimOpusToken> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        final Map<String, DimOpusToken> tokenMap = mapOpuses(items);

        final SqlParameterSource[] params = items.stream()
                .filter(Objects::nonNull)
                .map(token -> {
                    if (tokenMap.containsKey(token.getAwemeId())) {
                        token.copyIfExistNull(tokenMap.get(token.getAwemeId()));
                    }
                    return new BeanPropertySqlParameterSource(token);
                })
                .toArray(SqlParameterSource[]::new);

        jdbcTemplate.batchUpdate(INSERT_TEMPLATE, params);
    }

    private Map<String, DimOpusToken> mapOpuses(List<DimOpusToken> items) {
        final List<String> ids = items.stream()
                .map(DimOpusToken::getAwemeId)
                .distinct()
                .toList();

        if (ids.isEmpty()) {
            return Map.of();
        }

        final List<DimOpusToken> tokenList = jdbcTemplate.query(QUERY_OPUS, Map.of("ids", ids), ENTITY_MAPPER);
        return U.toMap(tokenList, DimOpusToken::getAwemeId, Function.identity());
    }

    @Override
    public DimOpusToken castOf(JSONObject item) {
        return DimOpusToken.of(item);
    }
}
