package cn.newrank.niop.data.biz.subscriber.pojo.param;

import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 13:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubscriberConfigUpdate extends SubscriberConfigCreate {
    private String subscriberId;


    @Override
    public SubscriberConfig toDto() {
        final SubscriberConfig config = super.toDto();

        config.setSubscriberId(subscriberId);

        return config;
    }
}
