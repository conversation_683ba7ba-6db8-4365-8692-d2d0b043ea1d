package cn.newrank.niop.data.biz.biz.ds.scheduler;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonRankSyncHistoryInsertData;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonSyncHistoryData;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import java.util.EnumSet;


/**
 * [临时使用]
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DsTempScheduler {

    private final CommonSyncHistoryData commonSyncHistoryData;
    private final CommonRankSyncHistoryInsertData commonRankSyncHistoryInsertData;

    public DsTempScheduler(CommonSyncHistoryData commonSyncHistoryData,
                           CommonRankSyncHistoryInsertData commonRankSyncHistoryInsertData) {
        this.commonSyncHistoryData = commonSyncHistoryData;
        this.commonRankSyncHistoryInsertData = commonRankSyncHistoryInsertData;
    }


    @XxlJob("syncHistoryData")
    public ReturnT<String> syncHistoryData(String params) {
        boolean isStart = false;
        EnumSet<PlatformType> allTypes = EnumSet.allOf(PlatformType.class);
        for (PlatformType platformType : allTypes) {
            String code = platformType.getDbCode();
            if (StrUtil.isNotBlank(params) && !isStart) {
                if (code.equals(params)) {
                    isStart = true;
                } else {
                    continue;
                }
            }
            commonSyncHistoryData.syncHistoryData(code);
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("syncRankHistoryData")
    public ReturnT<String> syncRankHistoryData(String params) {
        boolean isStart = false;
        EnumSet<RankPlatformType> allTypes = EnumSet.allOf(RankPlatformType.class);
        for (RankPlatformType platformType : allTypes) {
            String code = platformType.getDbCode();
            if (StrUtil.isNotBlank(params) && !isStart) {
                if (code.equals(params)) {
                    isStart = true;
                } else {
                    continue;
                }
            }
            if (code.equals("dyr")) {
                commonRankSyncHistoryInsertData.syncInsertData(platformType);
            }
        }
        return ReturnT.SUCCESS;
    }
}
