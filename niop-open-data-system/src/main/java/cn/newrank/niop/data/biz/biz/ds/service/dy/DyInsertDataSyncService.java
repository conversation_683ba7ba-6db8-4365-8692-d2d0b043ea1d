package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DyEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_DY_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class DyInsertDataSyncService implements SyncBizService<DyEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public DyInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseDyEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_douyin_user";
    }

    @Override
    public String getSortField() {
        return "uid";
    }

    @Override
    public String getRangField() {
        return "crawl_time";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_DY_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        sourceFields.add("unique_id");
        sourceFields.add("short_id");
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<DyEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), DyEsMetaData.class);
        });
    }

    @Override
    public List<DyEsMetaData> convertData(List<DyEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (DyEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_DY_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", PlatformType.DY.getDbCode());

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.DY.getDbCode() + "_" + mainMap.get("account_id"));
            }

            mainMap.put("officail_display_id", data.get("unique_id") != null && !StrUtil.isEmpty(data.get("unique_id").toString()) ? data.get("unique_id") : data.get("short_id"));

            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 二级认证信息
//            String name = Optional.ofNullable(data.getJSONArray("organization_certs"))
//                    .map(array -> array.getJSONObject(0))
//                    .map(first -> first.getString("name"))
//                    .orElse(EMPTY);
//            mainMap.put("verify_type_v2", name);

            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), DyEsMetaData.class);
    }
}
