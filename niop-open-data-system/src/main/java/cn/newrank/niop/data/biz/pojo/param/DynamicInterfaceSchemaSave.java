package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceSchemaSave {

    /**
     * 接口SchemaID
     */
    private String interfaceSchemaId;

    /**
     * 接口ID
     */
    @NotBlank(message = "接口ID(interfaceId)不能为空")
    private String interfaceId;

    /**
     * 数据解析Schema
     */
    String dataSchema;

    /**
     * 数据样例
     */
    String dataExample;

    /**
     * 是否开始映射
     */
    Boolean enableMapping;

    public DynamicInterfaceSchema toDto() {
        final DynamicInterfaceSchema dynamicInterfaceSchema = new DynamicInterfaceSchema();

        dynamicInterfaceSchema.setInterfaceSchemaId(this.interfaceSchemaId);
        dynamicInterfaceSchema.setInterfaceId(this.interfaceId);
        dynamicInterfaceSchema.setDataSchema(this.dataSchema);
        dynamicInterfaceSchema.setDataExample(this.dataExample);

        Boolean enableMapping = Optional.ofNullable(this.enableMapping).orElse(false);
        dynamicInterfaceSchema.setEnableMapping(enableMapping);

        return dynamicInterfaceSchema;
    }
}