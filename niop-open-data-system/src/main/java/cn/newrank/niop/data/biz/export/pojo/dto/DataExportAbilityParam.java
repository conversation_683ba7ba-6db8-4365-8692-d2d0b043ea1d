package cn.newrank.niop.data.biz.export.pojo.dto;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class DataExportAbilityParam {

    /**
     * 标题
     */
    private String title;

    /**
     * 类型
     */
    private String type;

    /**
     * 字段属性
     */
    private Map<String, Field> properties;

    /**
     * 必填字段
     */
    private List<String> required;

    @Data
    static class Field {
        /**
         * 标题
         */
        private String title;
        /**
         * 类型
         */
        private String type;
        /**
         * 是否需要
         */
        private String need;
    }

    public List<HeaderField> toHeaderFields() {
        if (CollUtil.isEmpty(this.properties)) {
            return Collections.emptyList();
        }

        final List<HeaderField> fields = new ArrayList<>(this.properties.size());
        for (Map.Entry<String, Field> entry : this.getProperties().entrySet()) {
            final Field value = entry.getValue();
            HeaderField field = new HeaderField();
            field.setName(entry.getKey());
            field.setType(value.getType());
            field.setMandatory("是".equals(value.getNeed()));
            fields.add(field);
        }
        return fields;
    }

}
