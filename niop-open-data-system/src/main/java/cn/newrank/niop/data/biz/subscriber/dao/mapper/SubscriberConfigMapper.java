package cn.newrank.niop.data.biz.subscriber.dao.mapper;

import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigPageQuery;
import cn.newrank.niop.data.biz.subscriber.pojo.po.SubscriberConfigPo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SubscriberConfigMapper {


    void insert(SubscriberConfigPo configPo);

    SubscriberConfigPo get(String subscriberId);

    void update(SubscriberConfigPo configPo);

    Page<SubscriberConfigPo> page(@Param("pageQuery") SubscriberConfigPageQuery pageQuery,
                                  @Param("page") Page<Object> mybatisPlusPage);

    List<SubscriberConfigPo> searchSbConfig(@Param("sourceType") SubSourceType sourceType,
                                            @Param("sourceId") String sourceId);

    boolean delete(String subscriberId);

    List<SubscriberConfigPo> listByCbId(String cbId);

    SubscriberConfigPo getBy(@Param("cbId") String cbId,
                             @Param("sourceType") SubSourceType sourceType,
                             @Param("sourceId") String sourceId);
}




