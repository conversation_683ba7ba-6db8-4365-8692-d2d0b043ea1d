package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.biz.export.handler.FileUploadResult;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import java.util.List;

public interface DataExportResultFileService {

    /**
     * 保存文件上传结果
     *
     * @param fileUploadResult 文件上传结果
     */
    void save(FileUploadResult fileUploadResult);

    /**
     * 获取结果文件列表
     *
     * @param exportTaskId 导数任务id
     * @param authorize 是否需要授权
     * @return 结果文件列表
     */
    List<DataExportResultFile> listResultFiles(String exportTaskId, boolean authorize);

}
