package cn.newrank.niop.data.common;

import cn.newrank.niop.data.common.ds.SlsFactory;
import cn.newrank.niop.data.config.SystemConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/1/22 09:36:25
 */
@Slf4j
@Component
public class TraceCreator {

    private static final String SLS_PROJECT = "nr-niop";
    private final SystemConfig SYSTEM_CONFIG;
    private static final String ALIYUN_LOG_HANGZHOU_DEV = "cn-hangzhou.log.aliyuncs.com";
    private static final String ALIYUN_LOG_HANGZHOU_PRODUCT = "cn-hangzhou-intranet.log.aliyuncs.com";
    public SlsFactory.Sls sls;
    @Value("${newrank.aliyun.ak}")
    private String aliyunAk;
    @Value("${newrank.aliyun.sk}")
    private String aliyunSk;

    public TraceCreator(SystemConfig systemConfig) {
        SYSTEM_CONFIG = systemConfig;
    }

    @PostConstruct
    public void init() {
        try {
            this.sls = SlsFactory.DEFAULT.create(key -> switch (key) {
                case ADDRESS -> {
                    if (SYSTEM_CONFIG.isDevelop()) {
                        yield ALIYUN_LOG_HANGZHOU_DEV;
                    } else {
                        yield ALIYUN_LOG_HANGZHOU_PRODUCT;
                    }
                }
                case DATABASE -> SLS_PROJECT;
                case ALIYUN_AK -> aliyunAk;
                case ALIYUN_SK -> aliyunSk;
                default -> throw new IllegalArgumentException("Unknown key: " + key);
            });
        } catch (Exception e) {
            log.error("Sls initialization failed", e);
        }
    }
}
