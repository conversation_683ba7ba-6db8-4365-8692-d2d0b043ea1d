package cn.newrank.niop.data.biz.dao.mapper;

import cn.newrank.niop.data.biz.pojo.param.CbConfigFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.CbConfigPageQuery;
import cn.newrank.niop.data.biz.pojo.po.CbConfigPo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface CbConfigMapper {

    void insert(CbConfigPo cbConfigPo);

    int delete(String cbId);

    CbConfigPo get(String cbId);

    int update(CbConfigPo cbConfigPo);

    List<CbConfigPo> fuzzyQuery(@Param("fuzzyQuery") CbConfigFuzzyQuery fuzzyQuery);

    Page<CbConfigPo> page(@Param("pageQuery") CbConfigPageQuery pageQuery,
                          Page<CbConfigPo> mybatisPlusPage);

    Page<CbConfigPo> pageWithSubscriber(@Param("pageQuery") CbConfigPageQuery pageQuery,
        Page<CbConfigPo> mybatisPlusPage);

    List<CbConfigPo> list(List<String> cbIds);

    List<String> listCbIds();

    CbConfigPo getByUUID(String uuid);
}




