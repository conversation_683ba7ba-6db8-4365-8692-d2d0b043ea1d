package cn.newrank.niop.data.biz.biz.ds.pojo.dto;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import lombok.Data;

import java.util.List;

/**
 * 数据中心es元数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DataCenterEsMetaData implements EsEntity {
    private String accountId;
    private String accountName;

    private String accountNamePinyin;
    private String accountSecType;
    private Integer accountStatus;
    private List<String> accountTag;
    private String accountType;
    private String acqTime;
    private Integer age;
    private String anaTime;
    private String avatarUrl;
    private Float avgCollectedCountSeven;
    private Float avgCommentsCountSeven;
    private Float avgLikedCountSeven;
    private Float avgReadCountSeven;
    private Integer awemeCountRtThirtyDays;
    private String city;
    private Integer collectCount;
    private Integer douyinFollowerCount;
    private String enterpriseVerifyInfo;
    private String enterpriseVerifyUpdateTime;
    private String fansRange;
    private Integer fansCount;
    private String fansTime;
    private String fansType;
    private Integer followerCount;
    private String gender;
    private Integer huoshanFollowerCount;
    private String indexId;
    private Integer isConsult;
    private Integer isMutualSelection;
    private Integer lastWeekAwemeCount;
    private Integer lastWeekBarrageCount;
    private Integer lastWeekCoinCount;
    private Integer lastWeekCollectCount;
    private Integer lastWeekCommentCount;
    private Integer lastWeekForwardCount;
    private Integer lastWeekLikeCount;
    private Integer lastWeekReadCount;
    private Integer lastWeekShareCount;
    private Integer lastWeekWatchCount;
    private String mcnName;
    private Integer noteCountSeven;
    private Double nrIndexDay;
    private Double nrIndexMonth;
    private Double nrIndexWeek;
    private String nrIndexWeekDate;
    private String officailDisplayId;
    private String officialRedirectId;
    private String platformType;
    private String province;
    private String signature;
    private Integer totalAwemeCount;
    private Integer totalCollectedCount;
    private Long totalLikeCount;
    private Integer toutiaoFollowerCount;
    private String verifyInfo;
    private String verifyTypeV1;
    private String verifyTypeV2;

    @Override
    public String docId() {
        return indexId;
    }
}
