package cn.newrank.niop.data.config;


import cn.newrank.niop.data.common.lock.DistributeLockAspect;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/11 17:28
 */
@Configuration
@EnableScheduling
public class SchedulerConfig {

    /**
     * 分布式锁切面
     *
     * @param redissonClient redisson客户端
     * @return DistributeLockAspect
     */
    @Bean
    public DistributeLockAspect distributeLockAspect(RedissonClient redissonClient) {
        return new DistributeLockAspect(redissonClient);
    }

    /**
     * 定时任务线程池
     *
     * @return ThreadPoolTaskScheduler
     */
    @Bean
    public ThreadPoolTaskScheduler schedulerThreadPool() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(100);
        scheduler.setThreadNamePrefix("scheduled-"); // 设置线程名前缀
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return scheduler;
    }
}