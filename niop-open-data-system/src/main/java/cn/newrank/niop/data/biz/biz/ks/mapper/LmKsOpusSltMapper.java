package cn.newrank.niop.data.biz.biz.ks.mapper;

import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *<AUTHOR>
 *@since  2024/10/24 14:53
 *@version 1.0.0
 */
@Mapper
@DS("ks")
public interface LmKsOpusSltMapper {

    void storeBasicBatch(@Param("items") List<LmKsOpus> items);
}




