package cn.newrank.niop.data.biz.pojo.vo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.data.biz.component.biz.StorageVersionEntity;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static cn.newrank.niop.data.util.Iterables.toList;
import static cn.newrank.niop.data.util.Iterables.toMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/15 9:55
 */
@Data
public class StorageEntityVo {
    String identifier;

    int version;

    JSONObject entity;

    public static List<StorageEntityVo> of(List<String> identifiers, List<? extends StorageEntity> storageEntities) {
        final Map<String, ? extends StorageEntity> map = toMap(storageEntities,
                StorageEntity::identifier, Function.identity());

        return toList(identifiers, identifier -> {
            final StorageEntityVo storageEntityVo = new StorageEntityVo();

            storageEntityVo.setIdentifier(identifier);
            final StorageEntity entity = map.get(identifier);

            if (entity != null) {
                if (entity instanceof StorageVersionEntity storageVersionEntity) {
                    storageEntityVo.setVersion(storageVersionEntity.getVersion());
                } else {
                    storageEntityVo.setVersion(1);
                }

                storageEntityVo.setEntity(entity.toJSONObject());
            }

            return storageEntityVo;
        });
    }
}
