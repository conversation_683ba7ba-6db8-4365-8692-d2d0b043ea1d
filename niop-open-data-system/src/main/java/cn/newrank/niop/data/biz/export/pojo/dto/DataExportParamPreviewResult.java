package cn.newrank.niop.data.biz.export.pojo.dto;

import java.util.List;
import lombok.Data;

@Data
public class DataExportParamPreviewResult {

    /**
     * 参数总数
     */
    private Integer paramTotalNum;

    /**
     * 部分参数列表
     */
    private List<?> someParams;

    public static DataExportParamPreviewResult of(Integer paramTotalNum, List<?> params) {
        DataExportParamPreviewResult result = new DataExportParamPreviewResult();
        result.setParamTotalNum(paramTotalNum);
        result.setSomeParams(params);
        return result;
    }

}
