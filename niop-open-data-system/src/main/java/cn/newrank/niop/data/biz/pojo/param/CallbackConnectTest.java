package cn.newrank.niop.data.biz.pojo.param;


import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.enums.CbType;
import cn.newrank.niop.data.common.ConfigProperties;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/16 11:46
 */
@Data
public class CallbackConnectTest {
    @NotNull(message = "回调源类型(cbType)不能为空")
    CbType cbType;

    Map<String,Object> config;

    public CbConfig toDto() {
        final CbConfig cbConfig = new CbConfig();

        cbConfig.setType(cbType);
        cbConfig.setConfig(ConfigProperties.of(config));

        return cbConfig;
    }
}
