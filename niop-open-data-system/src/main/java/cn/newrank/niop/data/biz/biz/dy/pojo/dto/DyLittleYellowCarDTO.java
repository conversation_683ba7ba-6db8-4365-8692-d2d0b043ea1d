package cn.newrank.niop.data.biz.biz.dy.pojo.dto;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @createTime 2024/10/16
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DyLittleYellowCarDTO extends StorageEntity {


    /**
     * 直播id
     */
    private String roomId;

    /**
     * 商品所属小店id
     */
    private String shopId;

    /**
     * 商品所属小店名称
     */
    private String shopName;

    /**
     * 商品所属小店secid
     */
    private String shopSchema;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品pmid
     */
    private String promotionId;

    /**
     * 商品原价
     */
    private String regularPrice;

    /**
     * 商品库存
     */
    private Long stockNum;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 服务保障
     */
    private String shopGuarantee;

    /**
     * 多sku规格最高价格
     */
    private String maxPrice;

    /**
     * 多sku规格最低价格
     */
    private String minPrice;

    /**
     * 福袋标识
     */
    private Boolean lottery;

    /**
     * 商品类型
     */
    private Integer itemType;

    /**
     * 唯一sku标识
     */
    private Boolean isSoleSku;

    /**
     * 是否有库存
     */
    private Boolean inStock;

    /**
     * 闪购标识
     */
    private Integer flashType;

    /**
     * 活动信息
     */
    private String activityInfo;

    /**
     * 商品价格&价格对应skuid
     */
    private String priceInfo;


    private String showSkuId;


    private String elasticTitle;

    private String discountPrice;

    private String discountLabel;

    /**
     * 商品封面
     */
    private String cover;

    /**
     * 一级分类
     */
    private Long firstCid;

    /**
     * 二级分类
     */
    private Long secondCid;

    /**
     * 三级分类
     */
    private Long thirdCid;

    /**
     * 四级分类
     */
    private Long fourthCid;

    /**
     * 是否可加入购物车
     */
    private Boolean canAddCart;

    /**
     * 是否可售
     */
    private Boolean canSold;

    /**
     * 可用优惠券？
     */
    private Integer applyCoupon;

    /**
     * 是否有活动
     */
    private Boolean campaign;

    /**
     * 活动id
     */
    private String campaignId;

    /**
     * 活动类型
     */
    private Integer campaignType;

    /**
     * 活动开始时间
     */
    private Long campaignStartTime;

    /**
     * 活动结束时间
     */
    private Long campaignEndTime;

    /**
     * 活动预售商品标识？
     */
    private Boolean campaignIsPreheat;

    /**
     * 活动库存
     */
    private Long campaignLeftStock;

    /**
     * 活动商品最大价格（多sku规格时的最高价格？）
     */
    private String campaignMaxPrice;


    private String campaignDepositPrice;

    private String campaignOriginPrice;

    /**
     * 活动价格
     */
    private String campaignPrice;

    /**
     * 活动商品pmid
     */
    private String campaignPromotionId;

    /**
     * 商品原价
     */
    private String campaignRegularPrice;

    /**
     * 活动商品库存
     */
    private Long campaignStock;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     * 分区位点
     */
    private String partitionOffset;


    @Override
    public String identifier() {
        return "productId";
    }
}
