package cn.newrank.niop.data.biz.callback.pojo;

import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.web.model.BizEnum;

/**
 * <AUTHOR>
 */
public enum CbPageSearchType implements BizEnum {

    /**
     * 数据订阅搜索类型
     */
    CALLBACK_CONFIG("1", "数据订阅名称或ID"),
    DATA_SOURCE("2", "数据源名称或ID"),
    ;

    private final String code;
    private final String description;

    CbPageSearchType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CbPageSearchType parseByCode(String code) {
        if (CharSequenceUtil.isNotBlank(code)) {
            for (CbPageSearchType searchType : values()) {
                if (searchType.getDbCode().equals(code)) {
                    return searchType;
                }
            }
        }
        return CbPageSearchType.CALLBACK_CONFIG;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public String getJsonValue() {
        return this.code;
    }

    @Override
    public String getDbCode() {
        return this.code;
    }

}
