package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 10:47
 */
@Log4j2
@Service
public class KsNewSync extends DefaultHistorySynchronizer<String> {

    private final LmKsOpusMapper lmKsOpusMapper;

    protected KsNewSync(RedissonClient redissonClient, LmKsOpusMapper lmKsOpusMapper) {
        super("ks_new_sync", redissonClient, 1);
        this.lmKsOpusMapper = lmKsOpusMapper;
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final String next = cursor.getNext();
        final List<LmKsOpus> lmKsOpuses = lmKsOpusMapper.listNonSync(next == null ? "" : next);
        if (lmKsOpuses.isEmpty()) {
            return 0;
        }

        lmKsOpuses.forEach(lmKsOpus -> {
            final String merchant = lmKsOpus.getMerchant();
            if (StringUtils.isNotBlank(merchant)) {
                final JSONObject merchantJson = JSON.parseObject(merchant);
                lmKsOpus.setMerchantAdType(merchantJson.getString("adType"));
            }
            final String music = lmKsOpus.getMusic();
            if (StringUtils.isNotBlank(music)) {
                lmKsOpus.setMusicId(JSON.parseObject(music).getString("id"));
            }

            final String soundTrack = lmKsOpus.getSoundTrack();
            if (StringUtils.isNotBlank(soundTrack)) {
                lmKsOpus.setSoundTrackId(JSON.parseObject(soundTrack).getString("id"));
            }

            final String anaTags = lmKsOpus.getAnaTags();
            if (anaTags != null) {
                lmKsOpus.setSearchTags(anaTags);
            }

            final String userId = lmKsOpus.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                try {
                    lmKsOpus.setUserIdL(Long.parseLong(userId));
                } catch (NumberFormatException e) {
                    log.warn("{} userId is not number: {}", userId, lmKsOpus.getPhotoId());
                }
            }
        });

        lmKsOpusMapper.updateSync(lmKsOpuses);
        cursor.setNext(lmKsOpuses.get(lmKsOpuses.size() - 1).getPhotoId());

        return lmKsOpuses.size();
    }
}
