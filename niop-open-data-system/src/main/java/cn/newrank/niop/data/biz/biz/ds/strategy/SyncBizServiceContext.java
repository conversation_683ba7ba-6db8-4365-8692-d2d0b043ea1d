package cn.newrank.niop.data.biz.biz.ds.strategy;

import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 同步业务服务上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Component
public class SyncBizServiceContext implements ApplicationContextAware {
    private final ConcurrentMap<SyncBiz, SyncBizService<? extends SyncEntity>> syncBizServiceMap
            = new ConcurrentHashMap<>();

    ApplicationContext ioc;

    public SyncBizService<? extends SyncEntity> getService(SyncBiz syncBiz) {
        return syncBizServiceMap.get(syncBiz);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.ioc = applicationContext;
        for (SyncBiz syncBiz : SyncBiz.values()) {
            syncBizServiceMap.put(syncBiz, ioc.getBean(syncBiz.syncBizServiceClass));
        }
        log.info("SyncBizServiceContext init completely");
    }
}
