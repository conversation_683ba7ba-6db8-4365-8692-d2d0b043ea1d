package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyLittleYellowCarMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyLittleYellowCar;
import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyLittleYellowCarDTO;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024/10/16
 * @description
 */
@Service
@RequiredArgsConstructor
public class DyLittleYellowCarService implements StorageBizService<DyLittleYellowCarDTO> {

    private final DyLittleYellowCarMapper littleYellowCarMapper;


    private final DyShopSkuService shopSkuService;

    private final DyShopCouponsService couponsService;

    private final DyShopSingleSkuService singleSkuService;

    private final DyShopVerifyService shopVerifyService;

    private final DyBuyinShopService buyinShopService;

    private final DyBuyinShopInfoService buyinShopInfoService;

    @Override
    public void storeBatch(List<DyLittleYellowCarDTO> items) {
        //
    }

    @Override
    public DyLittleYellowCarDTO castOf(JSONObject item) {
        String dataType = item.getString("data_type");
        String deviceName = item.getString("device_name");
        //小黄车数据
        if ("custom_project_promotion_list_app".equals(dataType)) {
            littleYellowCarData(item,deviceName);
        }
        //sku数据
        if ("webcast_promotion_sku_app".equals(dataType)) {
            shopSkuService.shopSkuData(item,deviceName);
        }
        //优惠券数据
        if ("warcraft_api_coupon_couponlist_v2".equals(dataType)) {
            couponsService.shopSkuData(item,deviceName);
        }
        //订单数据
        if ("order_component_confirmOrder".equals(dataType)) {
            singleSkuService.shopSkuDataV1(item,deviceName);
        }
        if ("order_yata_confirmOrderRender".equals(dataType)) {
            singleSkuService.shopSkuDataV2(item,deviceName);
        }
        //商品详情数据
        if ("ecom_product_detail_stream".equals(dataType)) {
            shopVerifyService.shopSkuData(item,deviceName);
        }
        //百应直播商品列表
        if ("api_anchor_creative_get_live_detail".equals(dataType)) {
            buyinShopService.shopSkuData(item,deviceName);
        }
        //百应商品详情数据
        if ("pc_selection_decision_pack_detail".equals(dataType)) {
            buyinShopInfoService.shopSkuData(item,deviceName);
        }
        return null;
    }



    private void littleYellowCarData(JSONObject item,String deviceName) {
        JSONArray promotionList = item.getJSONObject("json_details").getJSONArray("promotions");
        String partitionOffset = item.getString("kafka_partition") + "_" + item.getString("kafka_offset");
        if (CollUtil.isEmpty(promotionList)) {
            return;
        }
        String roomId = item.getString("room_id");
        List<DyLittleYellowCarDTO> dtoList = promotionList.stream().map(json -> {
            JSONObject promotion = (JSONObject) json;
            DyLittleYellowCarDTO dto = new DyLittleYellowCarDTO()
                    .setRoomId(roomId)
                    .setPartitionOffset(partitionOffset)
                    .setDeviceName(deviceName)
                    .setShopId(promotion.getString("shop_id"))
                    .setShopName(getCustomName(promotion.getJSONObject("shop_info"),"name"))
                    .setShopSchema(getCustomName(promotion.getJSONObject("shop_info"),"schema"))
                    .setProductId(promotion.getString("product_id"))
                    .setPromotionId(promotion.getString("promotion_id"))
                    .setRegularPrice(promotion.getString("regular_price"))
                    .setStockNum(promotion.getLong("stock_num"))
                    .setTitle(promotion.getString("title"))
                    .setShopGuarantee(getShopGuarantee(promotion))
                    .setMaxPrice(promotion.getString("max_price"))
                    .setMinPrice(promotion.getString("min_price"))
                    .setLottery(promotion.getBoolean("lottery"))
                    .setItemType(promotion.getInteger("item_type"))
                    .setIsSoleSku(promotion.getBoolean("is_sole_sku"))
                    .setInStock(promotion.getBoolean("in_stock"))
                    .setFlashType(promotion.getInteger("flash_type"))
                    .setActivityInfo(getCustomName(promotion.getJSONObject("event_param"),"standard_product_marketing_param"))
                    .setPriceInfo(getCustomName(promotion.getJSONObject("event_param"),"price_info"))
                    .setShowSkuId(getShowSkuId(promotion.getJSONObject("event_param").getString("price_info")))
                    .setElasticTitle(promotion.getString("elastic_title"))
                    .setDiscountPrice(promotion.getJSONObject("discount_price") == null ? "" : JSON.toJSONString(promotion.getJSONObject("discount_price")))
                    .setDiscountLabel(promotion.getJSONArray("discount_label").isEmpty() ? "" : JSON.toJSONString(promotion.getJSONArray("discount_label")))
                    .setCover(promotion.getString("cover"))
                    .setCanAddCart(promotion.getBoolean("can_add_cart"))
                    .setCanSold(promotion.getBoolean("can_sold"))
                    .setApplyCoupon(promotion.getInteger("apply_coupon"))
                    .setCampaign(promotion.getBoolean("campaign"));
            //设置分类信息
            JSONObject category = promotion.getJSONObject("category");
            dto.setFirstCid(category.getLong("first_cid"))
                    .setSecondCid(category.getLong("second_cid"))
                    .setThirdCid(category.getLong("third_cid"))
                    .setFourthCid(category.getLong("fourth_cid"));
            //获取活动信息
            JSONObject campaignInfo = promotion.getJSONObject("campaign_info");
            if (campaignInfo != null) {
                dto.setCampaignId(campaignInfo.getString("campaign_id"))
                        .setCampaignType(campaignInfo.getInteger("campaign_type"))
                        .setCampaignStartTime(campaignInfo.getLong("start_time"))
                        .setCampaignEndTime(campaignInfo.getLong("end_time"))
                        .setCampaignIsPreheat(campaignInfo.getBoolean("is_preheat"))
                        .setCampaignLeftStock(campaignInfo.getLong("left_stock"))
                        .setCampaignMaxPrice(campaignInfo.getString("max_price"))
                        .setCampaignDepositPrice(getCustomName(campaignInfo.getJSONObject("pre_sale_data"),"deposit_price"))
                        .setCampaignOriginPrice(getCustomName(campaignInfo.getJSONObject("pre_sale_data"),"origin_price"))
                        .setCampaignPrice(campaignInfo.getString("price"))
                        .setCampaignPromotionId(campaignInfo.getString("promotion_id"))
                        .setCampaignRegularPrice(campaignInfo.getString("regular_price"))
                        .setCampaignStock(campaignInfo.getLong("stock"));
            }
            return dto;
        }).toList();
        List<DyLittleYellowCar> resultList = dtoList.stream().map(DyLittleYellowCar::createItem).toList();
        littleYellowCarMapper.batchSave(resultList);
    }


    public static String getCustomName(JSONObject jsonObject,String key) {
        if (jsonObject == null) {
            return "";
        }
        return jsonObject.getString(key);
    }


    public static String getShopGuarantee(JSONObject promotion) {
        JSONArray ritTagsArray = promotion.getJSONArray("rit_tags");
        if (CollUtil.isEmpty(ritTagsArray)) {
            return "";
        }
        List<String> collect = ritTagsArray.stream()
                .map(item -> {
                    JSONArray textArray = ((JSONObject) item).getJSONArray("text");
                    if (CollUtil.isEmpty(textArray)) {
                        return "";
                    }
                    return textArray.getString(0);
                })
                .toList();
        collect = collect.stream().filter(StrUtil::isNotBlank).toList();
        if (CollUtil.isEmpty(collect)) {
            return "";
        }
        return StrUtil.join(",", collect);
    }

    public static String getShowSkuId(String priceInfo) {
        if (StrUtil.isBlank(priceInfo)) {
            return "";
        }
        return JSONObject.parseObject(priceInfo).getString("show_sku_id");
    }

}
