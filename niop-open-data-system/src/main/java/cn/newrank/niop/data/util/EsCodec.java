package cn.newrank.niop.data.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.experimental.UtilityClass;

import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/16 13:50
 */
@UtilityClass
public class EsCodec {

    private static final ObjectMapper MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private static final ObjectMapper CONTAINS_NULL_MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static String serialize(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw createParamError("es serialize error, {}", e.getMessage());
        }
    }

    public static <T> T deserialize(String json, Class<T> clazz) {
        try {
            return MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            throw createParamError("es deserialize error, {}", e.getMessage());
        }
    }

    public static <T> List<T> deserializeToList(String json, Class<T> clazz) {
        try {
            TypeFactory typeFactory = MAPPER.getTypeFactory();
            return MAPPER.readValue(json, typeFactory.constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            throw createParamError("es deserialize to list error, {}", e.getMessage());
        }
    }

    public static String serializeWithContainsNull(Object obj) {
        try {
            return CONTAINS_NULL_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw createParamError("es serialize error, {}", e.getMessage());
        }
    }



}
