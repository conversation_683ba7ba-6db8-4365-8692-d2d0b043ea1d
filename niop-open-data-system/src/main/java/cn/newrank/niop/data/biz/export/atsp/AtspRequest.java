package cn.newrank.niop.data.biz.export.atsp;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import cn.newrank.niop.data.common.BizErr;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.log4j.Log4j2;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * <AUTHOR>
 */
@Log4j2
public class AtspRequest {

    private AtspRequest() {}

    private static final String APP_KEY_HEADER = "niop-app-key";

    private static final String ABILITY_HEADER = "niop-ability-id";

    private static final MediaType MEDIA_TYPE = MediaType.get("application/json");

    private static final int RETRIES = 3;

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
        .connectTimeout(Duration.ofSeconds(5))
        .readTimeout(Duration.ofSeconds(10))
        .writeTimeout(Duration.ofSeconds(10))
        .connectionPool(new ConnectionPool(40, 1, TimeUnit.MINUTES))
        .build();

    public static AtspSubmitResult submitTask(String abilityId, Map<String, Object> paramMap) {
        final Map<String, Object> param = buildParam(paramMap);

        try (Response response = CLIENT.newCall(submitRequest(abilityId, param)).execute()) {
            final ResponseBody body = response.body();

            if (Objects.nonNull(body)) {
               final String bodyStr = body.string();
               JSONObject respJson = JSONObject.parseObject(bodyStr);
               return AtspSubmitResult.of(respJson);
            }
            throw createBizException(BizErr.REQUEST_ERROR, "Status: {}, body is null", response.code());
        } catch (Exception e) {
            log.warn("任务提交失败, abilityId: {}, param: {}, e: ", abilityId, JSONObject.toJSONString(param), e);
        }
        return AtspSubmitResult.failed();
    }

    public static AbilityResult getResult(String taskId) {
        final String url = DataExportConstant.GET_RESULT_URL + "?taskId=" + taskId;

        final Request request = resultGetRequest(url);

        for (int i = 1; i <= RETRIES; i++) {
            try (Response response = CLIENT.newCall(request).execute()) {
                final ResponseBody body = response.body();

                if (Objects.nonNull(body)) {
                    return AbilityResult.of(body.string());
                }
            } catch (Exception e) {
                log.warn("能力结果查询失败, taskId: {}, e: ", taskId, e);
                ThreadUtil.sleep(300);
            }
        }

        throw createBizException(BizErr.REQUEST_ERROR, "能力结果查询多次失败, 请稍后重试");
    }

    private static Map<String, Object> buildParam(Map<String, Object> param) {
        final Map<String, Object> resultMap = new HashMap<>(4);
        resultMap.put("params", param);
        resultMap.put("sceneIds", DataExportConstant.SCENE_IDS);
        return resultMap;
    }

    private static Request resultGetRequest(String url) {
        return new Request.Builder().url(url)
            .addHeader(APP_KEY_HEADER, DataExportConstant.EXPORT_APP_KEY)
            .get().build();
    }

    private static Request submitRequest(String abilityId, Map<String, Object> param) {
        return new Request.Builder()
            .url(DataExportConstant.SUBMIT_URL)
            .addHeader(APP_KEY_HEADER, DataExportConstant.EXPORT_APP_KEY)
            .addHeader(ABILITY_HEADER, abilityId)
            .post(RequestBody.create(JSON.toJSONString(param), MEDIA_TYPE))
            .build();
    }

}
