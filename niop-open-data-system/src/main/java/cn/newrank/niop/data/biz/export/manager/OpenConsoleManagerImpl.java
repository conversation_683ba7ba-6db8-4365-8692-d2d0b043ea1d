package cn.newrank.niop.data.biz.export.manager;

import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.common.ServiceNames;
import cn.newrank.niop.console.biz.project.pojo.dto.AbilityScheme;
import cn.newrank.niop.console.biz.project.service.IAbilityDubboService;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class OpenConsoleManagerImpl implements OpenConsoleManager {

    @DubboReference(providedBy = ServiceNames.DUBBO_OPEN_CONSOLE)
    private IAbilityDubboService abilityDubboService;

    @Override
    public AbilityScheme getAbilityScheme(String abilityId) {
        if (CharSequenceUtil.isEmpty(abilityId)) {
            throw new IllegalArgumentException("能力id不能为空");
        }
        return abilityDubboService.getAbilityScheme(abilityId);
    }

}
