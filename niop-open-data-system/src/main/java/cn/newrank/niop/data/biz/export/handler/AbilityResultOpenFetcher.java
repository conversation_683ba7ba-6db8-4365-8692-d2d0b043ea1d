package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.atsp.AbilityResult;
import cn.newrank.niop.data.biz.export.atsp.AtspRequest;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportResultSource;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class AbilityResultOpenFetcher extends BaseDataExportResultFetcher {

    public AbilityResultOpenFetcher(DataExportSubtaskService subtaskService) {
        super(subtaskService);
    }

    @Override
    public DataExportType getDataSourceType() {
        return DataExportType.ABILITY;
    }

    @Override
    public DataExportResultSource getResultSource() {
        return DataExportResultSource.OPEN_QUERY;
    }

    @Override
    public ExportResult queryResult(String resultTaskId, String cursor) {
        final AbilityResult result = AtspRequest.getResult(resultTaskId);
        if (result.isNullData()) {
            handleNullData(resultTaskId);
        }
        return new NormalExportResult(resultTaskId, result.getDataAsList());
    }

}
