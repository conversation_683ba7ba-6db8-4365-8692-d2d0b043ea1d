package cn.newrank.niop.data.biz.pojo.param;


import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.enums.DsType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/20 11:31
 */
@Data
public class DsConfigCreate {
    @NotBlank(message = "名称(name)不能为空")
    String name;
    @NotNull(message = "数据源类型(type)不能为空")
    DsType type;
    /**
     * 配置信息
     */
    Map<String, Object> config;

    public DsConfig toDto() {
        final DsConfig datasource = new DsConfig();
        datasource.setType(type);
        datasource.setName(name);
        datasource.setConfig(ConfigProperties.of(config));

        return datasource;
    }
}

