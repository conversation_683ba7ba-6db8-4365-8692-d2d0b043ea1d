package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsExpTaskEnum;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpAtspTaskMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTestMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspTask;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTestTask;
import cn.newrank.niop.sdk.common.Environment;
import cn.newrank.niop.sdk.common.entity.Resp;
import cn.newrank.niop.sdk.common.producer.ProducerClient;
import cn.newrank.niop.sdk.common.producer.TaskResult;
import cn.newrank.niop.sdk.http.base.DefaultProducerClient;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayDeque;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025/4/15 21:51:31
 */
@Slf4j
@Service
public class XhsExpTestService {

    private static final String TOPIC = "niop_dc_xhs_topic_opus_ability_callback_test";
    private final ProducerClient producerClient;
    private final XhsExpTestMapper xhsExpTestMapper;
    public final Queue<String> queue;
    private static final String TEST_CODE = "exp-test01";
    private final ThreadPoolExecutor threadPoolExecutor;
    private final XhsExpAtspTaskMapper xhsExpAtspTaskMapper;
    private static final Boolean TEST_ENABLE = true;

    public XhsExpTestService(XhsExpTestMapper xhsExpTestMapper,
                             ThreadPoolExecutor threadPoolExecutor,
                             XhsExpAtspTaskMapper xhsExpAtspTaskMapper) {
        this.threadPoolExecutor = threadPoolExecutor;
        this.xhsExpAtspTaskMapper = xhsExpAtspTaskMapper;
        this.producerClient = new DefaultProducerClient(Environment.PRODUCT_OUTSIDE.getGatewayContextPath(), XhsExpAtspTaskService.APP_KEY);
        this.xhsExpTestMapper = xhsExpTestMapper;
        this.queue = new ArrayDeque<>(1000);
        // 是否启动测试线程
        this.runResult(false);
    }

    public void saveTestData(String taskId,
                             LocalDateTime triggerTime,
                             String taskParam) {
        try {
            Resp<TaskResult> resp = producerClient.getResult(taskId);
            if (resp.is200Code()) {
                xhsExpTestMapper.saveTask(TEST_CODE, triggerTime, taskId, taskParam, JSON.toJSONString(resp.getData()));
                log.info("saveTestData success, taskId: {}, triggerTime: {}, taskParam: {}, testCode: {}", taskId, triggerTime, taskParam, TEST_CODE);
            }
        } catch (Exception e) {
            log.error("sendTaskResultToKafka error", e);
        }
    }

    public String submitTask(String param, LocalDateTime triggerTime) {
        // 同一时间只有一个话题触发
        XhsExpTestTask testTask = xhsExpTestMapper.submitTask(TEST_CODE, triggerTime, param);
        queue.add(testTask.getTaskResult());
        return testTask.getTaskId();
    }

    public void runResult(Boolean enable) {
        if(!TEST_ENABLE.equals(enable)){
            return;
        }
        threadPoolExecutor.execute(() -> {
            while (true) {
                try {
                    String result = queue.poll();
                    if (result != null) {
                        JSONObject json = new JSONObject();
                        json.put("data", result);
                       // aliyunKafkaProducer.send(TOPIC, "", json.toJSONString());
                        log.info("⚽ send TO kafka success");
                    } else {
                        log.info("⚽ queue is empty");
                        ThreadUtil.sleep(3, TimeUnit.SECONDS);
                    }
                } catch (Exception e) {
                    log.error("runResult error", e);
                    break;
                }
            }
        });
    }


    public void runSubmitTest(String param) {
        while (true) {
            List<XhsExpAtspTask> tasks = xhsExpAtspTaskMapper.listNotSubmitTask(200);
            log.info("💎 submitAtspTaskBatch success, size: {}", tasks.size());
            if (CollectionUtil.isEmpty(tasks)) {
                break;
            }
            submitAtspTaskBatch(tasks);
            // 避免触发限额
            ThreadUtil.sleep(1, TimeUnit.SECONDS);
        }
    }

    private void submitAtspTaskBatch(List<XhsExpAtspTask> tasks) {
        tasks.forEach(task -> {
            try{
                String initId = task.getTaskId();
                if (submitTaskUpdateTaskIdTest(task)) {
                    xhsExpAtspTaskMapper.updateTaskStatus(task, initId);
                }
            }catch (Exception e){
                log.error("submitAtspTaskBatch error", e);
            }
        });
    }


    /**
     * 提交任务测试
     *
     * @param task 任务
     * @return 是否提交成功
     */
    private boolean submitTaskUpdateTaskIdTest(XhsExpAtspTask task) {
        String taskId = submitTask(task.getParam(), task.getExecDate().atStartOfDay().plusHours(12));
        task.setTaskId(taskId);
        task.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.RUNNING.getDbCode()));
        return true;
    }
}
