package cn.newrank.niop.data.biz.biz.xhs.pojo;


import cn.newrank.niop.data.biz.biz.xhs.enums.XhsTopicUniIndexDataTypeEnum;
import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.nrcore.json.JsonField;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 多回调源-小红书作品数据
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class XhsTopicUniIndex extends StorageEntity {

    @JsonField("page_id")
    private String pageId;
    @JsonField("topic_id")
    private String topicId;

    /**
     * 话题基础信息数据
     */
    @JsonField("topic_name")
    private String topicName;
    @JsonField("discuss_num")
    private Long discussNum;
    @JsonField("view_num")
    private Long viewNum;
    @JsonField("share_info_desc")
    private String shareInfoDesc;
    @JsonField("acq_time")
    private String acqTime;
    @JsonField("topic_status")
    private String topicStatus;

    /**
     * 话题计算结果数据
     */
    @JsonField("collection_time")
    private String collectionTime;
    @JsonField("is_brand_topic")
    private Integer isBrandTopic;
    @JsonField("topic_type")
    private String topicType;
    //@JsonField("topic_type_multi")
    private String topicTypeMulti;
    @JsonField("is_invalid")
    private Integer isInvalid;
    @JsonField("note_num_thirty")
    private Long noteNumThirty;
    @JsonField("interactive_count_thirty")
    private Long interactiveCountThirty;
    @JsonField("calculate_date")
    private String calculateDate;
    @JsonField("topic_second_type")
    private String topicSecondType;
    private String suggest;

    /**
     * 话题品牌识别
     */
    @JsonField("brand_id")
    private String brandId;
    @JsonField("is_related_brand")
    private Integer isRelatedBrand;

    /**
     * 话题品类识别
     */
    //@JsonField("category_id")
    private String cateGoryId;
    @JsonField("is_related_category")
    private Integer isRelatedCateGory;


    private String source;
    private String gmtTime;

    @Override
    public String identifier() {
        return topicId;
    }

    /**
     * 话题唯一索引数据-作品基础信息
     *
     * @param jsonObject 话题唯一索引
     * @return 话题唯一索引-作品基础信息
     */
    public static XhsTopicUniIndex convertToTopicUniIndex(JSONObject jsonObject,
                                                          XhsTopicUniIndexDataTypeEnum dataType) {
        JSONObject object = jsonObject.getJSONObject("json_details");
        String gmtCreate = jsonObject.getString("gmt_create");
        XhsTopicUniIndex opus = JsonParser.parseObject(object, XhsTopicUniIndex.class);
        if (Strings.isBlank(opus.getAcqTime())) {
            opus.setAcqTime(null);
        }
        if (Strings.isBlank(opus.getTopicId())) {
            return null;
        }
        opus.setTopicTypeMulti(Optional.ofNullable(object.getJSONArray("topic_type_multi"))
                .map(array -> array.toJSONString())
                .orElse(null));
        opus.setCateGoryId(Optional.ofNullable(object.getJSONArray("category_id"))
                .map(array -> array.toJSONString())
                .orElse(null));
        if (XhsTopicUniIndexDataTypeEnum.TOPIC_CAL_RESULT == dataType) {
            JSONObject ob = object.getJSONObject("suggest");
            opus.setSuggest(ob.toJSONString());
        }
        try {
            opus.setGmtTime(DateTimeUtil.toDateTime(gmtCreate).format(DateTimeUtil.NORMAL_D_FORMATTER));
        } catch (Exception e) {
            opus.setGmtTime(DateTimeUtil.formatUtcDateTime(gmtCreate));
        }
        opus.setSource(dataType.getDbCode());
        return opus;
    }

    /**
     * 获取ds
     *
     * @param dataTime str
     * @return ds
     */
    public static String getDs(String dataTime) {
        LocalDateTime time = DateTimeUtil.toDateTime(dataTime);
        return DateTimeUtil.format(time, "yyyyMM");
    }
}
