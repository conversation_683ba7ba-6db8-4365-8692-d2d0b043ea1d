package cn.newrank.niop.data.biz.component.sync;

import org.redisson.api.RedissonClient;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 19:41
 */
public abstract class DefaultHistorySynchronizer<T> extends AbstractHistorySynchronizer<T, Cursor<T>> {

    protected DefaultHistorySynchronizer(String name, RedissonClient redissonClient, int maxRunningCount) {
        super(name, new RedissonCursorStorage<>(redissonClient, name), maxRunningCount);
    }

    @Override
    protected Cursor<T> newCursor() {
        return new Cursor<>();
    }


}
