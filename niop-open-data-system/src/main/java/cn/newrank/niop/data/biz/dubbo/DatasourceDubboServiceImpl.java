package cn.newrank.niop.data.biz.dubbo;

import cn.newrank.niop.data.biz.dao.DatasourceConfigDao;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.DsPageQuery;
import cn.newrank.niop.data.biz.storage.pojo.dto.DatasourceConfig;
import cn.newrank.niop.data.biz.storage.service.IDatasourceDubboService;
import cn.newrank.niop.data.common.enums.DsType;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/8/4 16:36
 */
@Service
@DubboService
public class DatasourceDubboServiceImpl implements IDatasourceDubboService {

    private final DatasourceConfigDao datasourceConfigDao;

    public DatasourceDubboServiceImpl(DatasourceConfigDao datasourceConfigDao) {
        this.datasourceConfigDao = datasourceConfigDao;
    }

    private DatasourceConfig cast(DsConfig dsConfig) {
        DatasourceConfig config = new DatasourceConfig();
        config.setDcId(dsConfig.getDcId());
        config.setType(dsConfig.getType().getCode());
        config.setName(dsConfig.getName());
        config.setConfig(dsConfig.getConfig().toJSONString());
        config.setConcurrencyPermit(dsConfig.getConcurrencyPermit());
        config.setGmtCreate(dsConfig.getGmtCreate());
        config.setGmtModified(dsConfig.getGmtModified());
        return config;
    }

    @Override
    public DatasourceConfig getDatasourceConfig(String dcId) {
        DsConfig dsConfig = datasourceConfigDao.get(dcId);
        return Optional.ofNullable(dsConfig).map(this::cast).orElse(null);
    }

    @Override
    public List<DatasourceConfig> listDatasourceConfig(List<String> dcIds) {
        return datasourceConfigDao.list(new HashSet<>(dcIds)).stream().map(this::cast).toList();
    }

    @Override
    public List<String> listDatasourceType() {
        return datasourceConfigDao.listAll().stream().map(DsConfig::getType).map(DsType::getCode).distinct().toList();
    }

    @Override
    public List<DatasourceConfig> listDatasourceByType(String keyword, String type) {
        DsPageQuery query = new DsPageQuery();
        query.setKeyword(keyword);
        query.setType(type);
        query.setPageNum(1);
        query.setPageSize(1000);
        return datasourceConfigDao.page(query).getRecords().stream().map(this::cast).toList();
    }

}
