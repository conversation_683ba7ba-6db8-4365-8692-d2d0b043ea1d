package cn.newrank.niop.data.biz.data.ark.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.biz.data.ark.mapper.ObjDataMapper;
import cn.newrank.niop.data.biz.data.ark.pojo.ObjData;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/8/29 15:54
 */
@Service
public class ObjDataService implements StorageBizService<ObjData> {
    @Resource
    private ObjDataMapper objDataMapper;

    @Override
    public void storeBatch(List<ObjData> items) {
        if (CollUtil.isNotEmpty(items)) {
            objDataMapper.batchInsert(items);
        }
    }

    @Override
    public ObjData castOf(JSONObject item) {
        return item.getJSONObject("data").to(ObjData.class);
    }
}
