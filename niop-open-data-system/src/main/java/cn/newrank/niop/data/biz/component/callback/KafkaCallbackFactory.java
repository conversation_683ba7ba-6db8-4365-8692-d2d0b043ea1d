package cn.newrank.niop.data.biz.component.callback;

import cn.newrank.niop.data.common.ConfigProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/15 20:47
 */
@Component
public class KafkaCallbackFactory implements CallbackFactory {

    public static final KafkaCallbackFactory DEFAULT = new KafkaCallbackFactory();

    @Override
    public KafkaCallback newCallback(ConfigProperties configProperties) {
        return new KafkaCallback(configProperties);
    }
}
