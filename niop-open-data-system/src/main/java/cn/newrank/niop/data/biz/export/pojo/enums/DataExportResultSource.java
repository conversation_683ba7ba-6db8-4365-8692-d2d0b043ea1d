package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

/**
 * <AUTHOR>
 */
public enum DataExportResultSource implements BizEnum {

    /**
     * 导数结果来源
     */
    OPEN_QUERY("1", "Open查询"),
    TABLE_STORE_NODE("2", "表格存储"),
    ;

    private final String code;
    private final String description;

    DataExportResultSource(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

}
