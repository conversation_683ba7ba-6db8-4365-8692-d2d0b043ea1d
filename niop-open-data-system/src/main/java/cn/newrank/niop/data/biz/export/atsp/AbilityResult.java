package cn.newrank.niop.data.biz.export.atsp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AbilityResult {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 业务 code
     */
    private Integer bizCode;

    /**
     * 业务消息
     */
    private String bizMsg;

    /**
     * Json 数据
     */
    private Object data;

    /**
     * 数据类型
     */
    private DataType dataType;

    public static AbilityResult of(String bodyStr) {
        JSONObject body = JSON.parseObject(bodyStr);
        String dataJson = body.getString("data");
        AbilityResult result = JSON.parseObject(dataJson, AbilityResult.class);
        result.setDataType(DataType.of(result.getData()));
        return result;
    }

    /**
     * 是否为空数据
     *
     * @return 是否为空数据
     */
    public boolean isNullData() {
        return switch (this.getDataType()) {
            case NULL -> true;
            case JSON_ARRAY -> ((JSONArray) data).isEmpty();
            default -> false;
        };
    }

    public List<JSONObject> getDataAsList() {
        return switch (this.getDataType()) {
            case NULL -> Collections.emptyList();
            case JSON_ARRAY -> ((JSONArray) data).toJavaList(JSONObject.class);
            default -> Collections.singletonList(((JSONObject) data));
        };
    }

    /**
     * 数据类型
     */
    enum DataType {

        /**
         * 数据类型
         */
        JSON_OBJECT,
        JSON_ARRAY,
        NULL,
        ;

        static DataType of(Object obj) {
            if (Objects.isNull(obj)) {
                return DataType.NULL;
            }

            if (obj instanceof JSONObject) {
                return DataType.JSON_OBJECT;
            }
            if (obj instanceof JSONArray) {
                return DataType.JSON_ARRAY;
            }

            throw new IllegalArgumentException("能力结果返回未知的数据类型, data: " + JSON.toJSONString(obj));
        }

    }

}
