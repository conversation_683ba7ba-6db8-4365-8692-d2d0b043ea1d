package cn.newrank.niop.data.biz.subscriber.pojo.dto;

import cn.hutool.crypto.digest.MD5;
import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.noear.snack.ONode;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/27 17:43
 */
@Data
@Log4j2
public class SendStrategy {
    static final Pattern HASH_EXPR = Pattern.compile("^\\s*\\$\\.\\w+(?:\\.\\w+)*(?:\\s*,\\s*\\$\\.\\w+(?:\\.\\w+)*)*\\s*$");
    /**
     * 默认消息发送策略
     */
    public static final String DEFAULT = "default";
    /**
     * 一致性hash
     */
    public static final String CONSISTENT_HASH = "consistent_hash";
    /**
     * 顺序消息发送策略
     */
    @NotBlank(message = "策略不能为空")
    String strategy;
    /**
     * 配置
     */
    JSONObject config;

    public void validate() {
        if (CONSISTENT_HASH.equals(strategy)) {
            final String expr = config.getString("expr");
            if (StringUtils.isBlank(expr)) {
                throw createParamError("一致性hash策略必须配置expr");
            }

            if (!HASH_EXPR.matcher(expr).matches()) {
                throw createParamError("一致性hash策略expr格式错误");
            }
        }
    }

    /**
     * 消息发送策略
     *
     * @param callbackRedirect  回调重定向
     */
    public void strategy(CallbackRedirect callbackRedirect) {
        if (callbackRedirect == null || DEFAULT.equals(strategy)) {
            return;
        }

        try {
            if (strategy.equals(CONSISTENT_HASH)) {
                final ConsumerRecord<String, String> consumerRecord = callbackRedirect.getConsumerRecord();
                if (consumerRecord == null) {
                    return;
                }

                final ConsistentHash consistentHash = ConsistentHash.create(config.getString("expr"));
                final String hashKey = consistentHash.hash(callbackRedirect.getPayload());
                // 从新设置kafka key, 使用新的一致性 hashkey
                callbackRedirect.setConsumerRecord(
                        new ConsumerRecord<>(consumerRecord.topic(),
                                consumerRecord.partition(),
                                consumerRecord.offset(),
                                hashKey, consumerRecord.value())
                );

                callbackRedirect.setConsumerRecordCarrier(ConsumerRecordCarrier.buildBy(callbackRedirect.getConsumerRecord()));
            }
        } catch (Exception e) {
            log.error("SendStrategy error", e);
        }
    }

    static class ConsistentHash{
        static final ConcurrentMap<String, ConsistentHash> CACHE = new ConcurrentHashMap<>();
        final List<String> expressions;

        ConsistentHash(String expr) {
            this.expressions = parse(expr);

        }

        private static List<String> parse(String expr) {
            if (StringUtils.isBlank(expr)) {
                return List.of();
            }

            return List.of(expr.split("\\s*,\\s*"));
        }

        public static ConsistentHash create(String expr) {
            return CACHE.computeIfAbsent(expr, ConsistentHash::new);
        }


        public String hash(Object payload) {
            if (payload == null || expressions.isEmpty()) {
                return null;
            }

            final ONode json = ONode.load(payload);
            String value = "";
            for (String expression : expressions) {
                final ONode n = json.select(expression);
                if (n.isNull()) {
                    continue;
                }
                value += n.getString();
            }

            if (value.isBlank()) {
                return null;
            }

            return MD5.create().digestHex16(value);
        }
    }

    public static void main(String[] args) {
        final ConsistentHash consistentHash = ConsistentHash.create("$.a.b, $.c,$.l");
        final String hash = consistentHash.hash(JSON.parseObject("{\"a\":{\"b\":\"1\"},\"c\":\"2\"}"));
        System.out.println(hash);
    }

}
