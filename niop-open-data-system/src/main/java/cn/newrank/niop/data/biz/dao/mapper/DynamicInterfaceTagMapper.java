package cn.newrank.niop.data.biz.dao.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 15:03
 */
@Mapper
public interface DynamicInterfaceTagMapper {


    void delete(@Param("interfaceId") String interfaceId);

    void save(@Param("interfaceId") String interfaceId,
              @Param("tags") List<String> tags);

    List<String> getAll(@Param("interfaceId") String interfaceId);
}




