package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CursorExportResult extends BaseExportResult {

    /**
     * 游标
     */
    private final String cursor;

    protected CursorExportResult(String resultTaskId, List<JSONObject> dataList, String cursor) {
        super(resultTaskId, dataList);
        this.cursor = cursor;
    }

    public static CursorExportResult of(String resultTaskId, List<DaTaExportTableStorage> storages) {
        List<JSONObject> dataList = Collections.emptyList();
        String cursor = null;
        if (CollUtil.isNotEmpty(storages)) {
            dataList = storages.stream().map(storage -> JSON.parseObject(storage.getContent())).toList();
            cursor = CollUtil.getLast(storages).getUniqueId();
        }
        return new CursorExportResult(resultTaskId, dataList, cursor);
    }

    @Override
    public boolean hasMore() {
        return CharSequenceUtil.isNotBlank(nextCursor()) && getDataList().size() >= DataExportConstant.CURSOR_QUERY_SIZE;
    }

    @Override
    public String nextCursor() {
        return this.cursor;
    }

}
