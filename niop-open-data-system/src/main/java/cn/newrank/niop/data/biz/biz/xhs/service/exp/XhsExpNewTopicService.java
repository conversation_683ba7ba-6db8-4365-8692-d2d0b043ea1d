package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpNewTopicMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTopicMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpNewTopic;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CWWEVCGP
 *
 * <AUTHOR>
 * @since 2025/3/28 11:46:10
 */
@Slf4j
@Service
public class XhsExpNewTopicService {
    private final RedissonClient redissonClient;
    private final XhsExpNewTopicMapper xhsExpNewTopicMapper;
    private final XhsExpTopicMapper xhsExpTopicMapper;

    public XhsExpNewTopicService(RedissonClient redissonClient,
                                 XhsExpNewTopicMapper xhsExpNewTopicMapper,
                                 XhsExpTopicMapper xhsExpTopicMapper) {
        this.redissonClient = redissonClient;
        this.xhsExpNewTopicMapper = xhsExpNewTopicMapper;
        this.xhsExpTopicMapper = xhsExpTopicMapper;
    }

    /**
     * 运行调度器定时任务
     */
    public void runSchedule(String param) {
        XxlJobLogger.log("手动设置下次添加话题时间 参数格式：2025-04-30 09:00:00");
        LocalDateTime now = LocalDateTime.now();
        RBucket<String> bucket = redissonClient.getBucket(getAddNewTopicTimeKey());
        // 缓存为空初始化为当日23点或次日23点
        if (Strings.isBlank(bucket.get())) {
            bucket.set(getNextAddTopicTime(now));
        }
        // 手动设置下次更新时间
        if (Strings.isNotBlank(param)) {
            setAddTopicTime(param, bucket);
            return;
        }
        LocalDateTime execTime = DateTimeUtil.toDateTime(bucket.get());
        // 判断是否到达执行时间
        if (now.isBefore(execTime)) {
            XxlJobLogger.log("未到达执行时间: {} 小于下次添加话题时间: {}", now.toString(), execTime.toString());
            return;
        }
        try {
            XxlJobLogger.log("开始添加话题, 当前时间: {}", now.toString());
            updateTopicWeight(execTime.toLocalDate());
            bucket.set(getNextAddTopicTime(now));
        } catch (Exception e) {
            XxlJobLogger.log("添加话题, error: {}", e.getMessage());
            log.error("添加话题异常, error: {}", e.getMessage());
        }
    }

    /**
     * 获取更新权重时间key
     *
     * @return key
     */
    public static String getAddNewTopicTimeKey() {
        return "xhs:exp:topic:addNewTopicTime";
    }


    private static final Integer ADD_TOPIC_TIME_HOUR = 23;

    /**
     * 获取下次更新权重时间
     *
     * @param now 当前时间
     * @return 下次更新权重时间
     */
    private static @NotNull String getNextAddTopicTime(LocalDateTime now) {
        if (now.getHour() < ADD_TOPIC_TIME_HOUR) {
            return now.toLocalDate().atStartOfDay().plusHours(ADD_TOPIC_TIME_HOUR)
                    .format(DateTimeUtil.NORMAL_DT_FORMATTER);
        }
        return now.toLocalDate().plusDays(1).atStartOfDay().plusHours(ADD_TOPIC_TIME_HOUR)
                .format(DateTimeUtil.NORMAL_DT_FORMATTER);
    }

    /**
     * 设置更新权重时间
     *
     * @param param  时间字符串
     * @param bucket redisson bucket
     */
    private static void setAddTopicTime(String param, RBucket<String> bucket) {
        try {
            //有参 手动设置下次更新权重时间 yyyy-MM-dd HH:mm:ss
            bucket.set(DateTimeUtil.toDateTime(param).format(DateTimeUtil.NORMAL_DT_FORMATTER));
            XxlJobLogger.log("设置更新时间: {} 成功", param);
        } catch (Exception e) {
            XxlJobLogger.log("设置更新时间: {} 失败, 请检查时间格式 yyyy-MM-dd HH:mm:ss", param);
        }
    }

    private static final Integer OPUS_NUM_MIN = 20;
    private static final Integer TOPIC_SIZE = 200;
    private static final Integer NEW_TOPIC_WEIGHT = 1;
    /**
     * 更新话题权重,根据前一天任务结果
     */
    private void updateTopicWeight(LocalDate date) {
        Long cursor = 0L;
        while (true) {
            List<XhsExpNewTopic> topics = xhsExpNewTopicMapper.getTopicIdLimitOpusNum(date, OPUS_NUM_MIN, cursor, TOPIC_SIZE);
            if (CollectionUtil.isEmpty(topics)) {
                break;
            }
            xhsExpTopicMapper.storeBatch(topics.stream().map(topic -> XhsExpTopic.build(topic.getTopicId(), NEW_TOPIC_WEIGHT)).toList());
            cursor = topics.get(topics.size() - 1).getId();
        }
        XxlJobLogger.log("更新完毕");
    }
}
