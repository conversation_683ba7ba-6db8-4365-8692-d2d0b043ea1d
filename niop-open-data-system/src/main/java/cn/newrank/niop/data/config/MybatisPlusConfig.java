package cn.newrank.niop.data.config;

import cn.newrank.niop.data.common.ArrayTypeHandler;
import cn.newrank.niop.data.common.StringListArrayTypeHandler;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/17 15:18
 */
@Configuration
public class MybatisPlusConfig {


    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.getTypeHandlerRegistry().register(new StringListArrayTypeHandler());
            configuration.getTypeHandlerRegistry().register(new ArrayTypeHandler());
        };
    }
}
