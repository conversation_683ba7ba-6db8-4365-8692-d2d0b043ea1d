package cn.newrank.niop.data.biz.export.excel;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.export.excel.converter.ExportJSONArrayConverter;
import cn.newrank.niop.data.biz.export.excel.converter.ExportJSONObjectConverter;
import cn.newrank.niop.data.biz.export.excel.handler.ColumnFormatCellWriteHandler;
import cn.newrank.niop.data.biz.export.pojo.dto.HeaderField;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSONObject;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class ExportExcelWriter implements Closeable {

    private static final Logger log = LoggerFactory.getLogger(ExportExcelWriter.class);

    private final ExcelWriter excelWriter;

    private final Set<String> headerSet;

    private final WriteSheet writeSheet;

    public ExportExcelWriter(File file, Set<String> header) {
        this.headerSet = header;
        List<List<String>> head = ListUtils.newArrayList();
        this.headerSet.forEach(k -> head.add(Collections.singletonList(k)));
        this.excelWriter = EasyExcelFactory.write(file)
            .registerWriteHandler(createHeaderStyle())
            .registerConverter(new ExportJSONArrayConverter())
            .registerConverter(new ExportJSONObjectConverter())
            .head(head).build();
        this.writeSheet = new ExcelWriterBuilder().sheet("sheet1").build();
    }

    private ExportExcelWriter(File file, List<HeaderField> headerFields) {
        if (CollUtil.isEmpty(headerFields)) {
            throw new IllegalArgumentException("表头字段不能为空");
        }

        final Map<String, HeaderField> fieldMap = headerFields.stream()
            .collect(Collectors.toMap(HeaderField::getName, Function.identity()));

        this.headerSet = fieldMap.keySet();
        List<List<String>> head = ListUtils.newArrayList();
        this.headerSet.forEach(k -> head.add(Collections.singletonList(k)));
        this.excelWriter = EasyExcelFactory.write(file)
            .registerWriteHandler(createHeaderStyle())
            .registerWriteHandler(new ColumnFormatCellWriteHandler(fieldMap))
            .head(head).build();
        this.writeSheet = new ExcelWriterBuilder().sheet("sheet1").build();
    }

    public static boolean writeTemplateFile(File file, List<HeaderField> headerFields) {
        if (CollUtil.isEmpty(headerFields)) {
            return false;
        }

        try (ExportExcelWriter writer = new ExportExcelWriter(file, headerFields)) {
            writer.writeEmptyData();
            return true;
        } catch (IOException e) {
            log.warn("模板文件写入异常, e:", e);
            return false;
        }
    }

    public void write(List<JSONObject> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<List<Object>> dataList = new ArrayList<>(list.size());
            for (JSONObject json : list) {
                final List<Object> data = new ArrayList<>(json.size());
                // 按照表头名称顺序取数据, 保持一致
                this.headerSet.forEach(header -> data.add(json.getOrDefault(header, "")));
                dataList.add(data);
            }
            this.excelWriter.write(dataList, writeSheet);
        }
    }

    public void writeEmptyData() {
        this.excelWriter.write(Collections.emptyList(), this.writeSheet);
    }

    @Override
    public void close() throws IOException {
        this.excelWriter.finish();
    }

    private HorizontalCellStyleStrategy createHeaderStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        // 字体
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, Collections.emptyList());
    }

}
