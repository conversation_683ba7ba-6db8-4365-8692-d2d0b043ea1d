package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.biz.callback.util.CallbackRedirectUtil;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.util.ExceptionUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class CallbackRedirectService extends BaseRedirectService<CallbackRedirect> {

    public CallbackRedirectService(CallbackService callbackService,
                                   CommonRedirectService commonRedirectService,
                                   SubscriberConfigService subscriberConfigService) {
        super(callbackService, commonRedirectService, subscriberConfigService);
    }

    @Override
    public CallbackRedirect newCallbackRedirect(CallbackRedirect snapshot, JSONObject param, JSONObject result, SubSourceType sourceType, List<CallbackRetry> retryList) {
        try {
            // 结果为空异常重试
            if (result == null) {
                if (hasResult(snapshot)) {
                    log.error("ability result not found: {}", snapshot);

                    CallbackRetry retry = CallbackRetry.init(snapshot, "查询任务结果为空");
                    retry.setSourceType(sourceType.getJsonValue());
                    retryList.add(retry);

                    return null;
                } else {
                    result = new JSONObject();
                }
            }

            final CallbackRedirect redirect = new CallbackRedirect();

            // 构建结果
            CallbackRedirectUtil.buildRedirectResult(redirect, snapshot, param, result, sourceType);

            return redirect;
        } catch (Exception e) {
            // 结果解析异常重试
            log.error("回调重定向-原始结果解析异常异常: {}", ExceptionUtil.getStackTrace(e));

            CallbackRetry retry = CallbackRetry.init(snapshot, "原始结果解析异常: " + e.getMessage());
            retry.setSourceType(sourceType.getJsonValue());
            retryList.add(retry);

            return null;
        }
    }
}
