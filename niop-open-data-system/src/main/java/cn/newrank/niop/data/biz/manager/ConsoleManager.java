package cn.newrank.niop.data.biz.manager;

import cn.newrank.niop.console.biz.project.pojo.dto.App;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/26 10:46
 */
public interface ConsoleManager {

    /**
     * 获取应用信息
     *
     * @param appId 应用ID
     * @return 应用信息
     */
    App getApp(String appId);

    /**
     * 判断应用是否存在
     *
     * @param appId 应用ID
     * @return 是否存在
     */
    boolean hasApp(String appId);

    /**
     * 批量获取应用信息
     *
     * @param appIds 应用ID列表
     * @return 应用信息
     */
    Map<String, App> mapApp(List<String> appIds);


    List<App> listApp(List<String> appIds);

}
