package cn.newrank.niop.data.biz.biz.tiktok.service;

import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokAccountMapper;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokAccount;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 15:48
 */
@Log4j2
@Service
public class TiktokAccountService implements StorageBizService<TiktokAccount> {

    private final TiktokAccountMapper tiktokAccountMapper;

    public TiktokAccountService(TiktokAccountMapper tiktokAccountMapper) {
        this.tiktokAccountMapper = tiktokAccountMapper;
    }

    @Override
    public void storeBatch(List<TiktokAccount> items) {
        tiktokAccountMapper.save(items);
    }


    @Override
    public TiktokAccount get(String identifier) {
        return tiktokAccountMapper.get(identifier);
    }

    @Override
    public TiktokAccount castOf(JSONObject item) {
        final TiktokAccount account = StorageBizService.format(TiktokAccount.class, item);
        if (account.getAccount() ==null) {
            account.setAccount(account.getSampleId());
        }
        return account;
    }
}
