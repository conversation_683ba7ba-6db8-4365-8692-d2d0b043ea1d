package cn.newrank.niop.data.biz.biz.xhs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusExpandMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusExpand;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/8/27 16:27
 */
@Service
public class XhsOpusExpandServiceImpl implements StorageBizService<XhsOpusExpand> {

    @Resource
    private XhsOpusExpandMapper xhsOpusExpandMapper;

    @Override
    public void storeBatch(List<XhsOpusExpand> items) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        xhsOpusExpandMapper.batchInsert(items);
    }

    @Override
    public XhsOpusExpand castOf(JSONObject item) {
        XhsOpusExpand.Message message = item.to(XhsOpusExpand.Message.class);
        XhsOpusExpand opus = message.getDetail();
        opus.setDataType(message.getDataType());
        return opus;
    }
}
