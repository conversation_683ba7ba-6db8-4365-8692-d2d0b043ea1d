package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataExportResultFileVo {

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 下载链接
     */
    private String downloadUrl;

    public static DataExportResultFileVo buildBy(DataExportResultFile resultFile) {
        if (Objects.isNull(resultFile)) {
            return null;
        }
        final DataExportResultFileVo vo = new DataExportResultFileVo();
        vo.setFileName(resultFile.getFileName());
        vo.setMd5(resultFile.getMd5());
        vo.setDownloadUrl(resultFile.getDownloadUrl());
        return vo;
    }

}
