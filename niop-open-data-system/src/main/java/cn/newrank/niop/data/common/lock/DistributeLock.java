package cn.newrank.niop.data.common.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分布式锁注解
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/27 14:24
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributeLock {
    /**
     * 锁类型
     * <p>
     * false: 指定的方法的锁
     * true : 指定到方法参数值的锁(不同的参数值, 获取的锁不同)
     */
    boolean includeArgs() default false;

    /**
     * 是否抛出终止异常
     */
    boolean abortException() default true;
}
