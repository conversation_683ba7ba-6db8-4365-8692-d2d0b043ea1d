package cn.newrank.niop.data.biz.biz.ds.service.bz;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * [b站]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class BzVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET bili_user_data/_search?scroll=15m
            {
                "_source": [
                  "mid",
                  "official_title",
                  "official_type",
                  "official_role"
                ],
              "size": 1500,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "official_title" } },
                    { "exists": { "field": "official_type" } },
                    { "exists": { "field": "official_role" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected BzVerifyInfoHistorySync(RedissonClient redissonClient,
                                      CommonEsService commonEsService,
                                      DsConfigManager dsConfigManager) {
        super(PlatformType.BILI.getDbCode(), dsConfigManager.chooseBzEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final BzVerifyInfoUpdate update = new BzVerifyInfoUpdate();
        update.setIndexId(PlatformType.BILI.getDbCode() + "_" + sourceObj.get("mid"));
        update.setVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("official_title"), CharSequenceUtil.EMPTY));
        update.setEnterpriseVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("official_title"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV1(StrUtil.nullToDefault(sourceObj.getString("official_type"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV2(StrUtil.nullToDefault(sourceObj.getString("official_role"), CharSequenceUtil.EMPTY));

        return update;
    }

    @Data
    public static class BzVerifyInfoUpdate extends VerifyInfoUpdate {

        private String enterpriseVerifyInfo;

    }
}
