package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FileUploadResult {

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件 md5
     */
    private String md5;

    public static FileUploadResult of(String exportTaskId, String fileUrl, String fileName, String md5) {
        final FileUploadResult result = new FileUploadResult();
        result.setExportTaskId(exportTaskId);
        result.setFileUrl(fileUrl);
        result.setFileName(fileName);
        result.setMd5(md5);
        return result;
    }

    public static FileUploadResult failed(String exportTaskId) {
        final FileUploadResult result = new FileUploadResult();
        result.setExportTaskId(exportTaskId);
        return result;
    }

    public boolean succeed() {
        return CharSequenceUtil.isNotBlank(this.getFileUrl());
    }

}
