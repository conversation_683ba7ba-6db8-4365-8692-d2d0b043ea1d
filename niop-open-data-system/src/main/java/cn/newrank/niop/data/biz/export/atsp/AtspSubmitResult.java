package cn.newrank.niop.data.biz.export.atsp;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.Objects;
import lombok.Data;

@Data
public class AtspSubmitResult {

    /**
     * atsp 任务id
     */
    private String taskId;

    /**
     * 是否成功
     */
    private boolean succeed;

    /**
     * 消息
     */
    private String msg;

    /**
     * 响应code
     */
    private Integer code;

    private AtspSubmitResult(String taskId, Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.taskId = taskId;
        this.succeed = CharSequenceUtil.isNotBlank(taskId);
    }

    public static AtspSubmitResult failed() {
        return new AtspSubmitResult(null, null, null);
    }

    public static AtspSubmitResult of(JSONObject json) {
        return Objects.nonNull(json) ?
            new AtspSubmitResult(json.getString("data"), json.getInteger("code"), json.getString("msg")) : failed();
    }

    public SubmitResultType getResultType() {
        return SubmitResultType.of(this.code);
    }

}
