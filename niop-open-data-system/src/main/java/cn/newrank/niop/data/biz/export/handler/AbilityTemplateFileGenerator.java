package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.console.biz.project.pojo.dto.AbilityScheme;
import cn.newrank.niop.data.biz.export.excel.ExportExcelWriter;
import cn.newrank.niop.data.biz.export.manager.OpenConsoleManager;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportAbilityParam;
import cn.newrank.niop.data.biz.export.pojo.dto.HeaderField;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import com.alibaba.fastjson2.JSON;
import java.io.File;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class AbilityTemplateFileGenerator implements DataExportTemplateFileGenerator {

    private final OpenConsoleManager openConsoleManager;

    public AbilityTemplateFileGenerator(OpenConsoleManager openConsoleManager) {
        this.openConsoleManager = openConsoleManager;
    }

    @Override
    public DataExportType getExportType() {
        return DataExportType.ABILITY;
    }

    @Override
    public File createFile(String exportId) {
        return new File(getDir() + exportId + "-template.xlsx");
    }

    @Override
    public boolean writeTemplateFile(File file, DataExport export) {
        final String targetId = export.getTargetId();

        AbilityScheme paramScheme = openConsoleManager.getAbilityScheme(targetId);
        String paramJson = paramScheme.getParamJsonScheme();
        if (CharSequenceUtil.isBlank(paramJson)) {
            throw createParamError("当前导数源无参数配置");
        }

        DataExportAbilityParam abilityParam = JSON.parseObject(paramJson, DataExportAbilityParam.class);
        List<HeaderField> fields = abilityParam.toHeaderFields();

        return ExportExcelWriter.writeTemplateFile(file, fields);
    }

}
