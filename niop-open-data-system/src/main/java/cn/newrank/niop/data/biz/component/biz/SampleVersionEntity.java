package cn.newrank.niop.data.biz.component.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 样本中心的样本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/17 10:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class SampleVersionEntity extends StorageVersionEntity {
    /**
     * 样本ID
     */
    protected String sampleId;

    /**
     * 样本状态
     */
    protected String sampleStatus;

    /**
     * 样本更新时间
     */
    protected long updateTime;

    @Override
    public long versionUpdateTime() {
        return updateTime;
    }

    @Override
    public void setVersionUpdateTime(long versionUpdateTime) {
        this.updateTime = versionUpdateTime;
    }

    @Override
    public String identifier() {
        return sampleId;
    }
}
