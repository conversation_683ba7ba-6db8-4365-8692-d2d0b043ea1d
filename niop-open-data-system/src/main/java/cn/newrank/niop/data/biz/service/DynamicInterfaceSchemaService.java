package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;

/**
 * 动态取数接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:24
 */
public interface DynamicInterfaceSchemaService {

    /**
     * 保存动态接口Schema
     *
     * @return 接口ID
     */
    String save(DynamicInterfaceSchemaSave interfaceSchemaCreate);

    /**
     * 获取动态接口Schema
     *
     * @param interfaceId 接口ID
     * @return 接口详情
     */
    DynamicInterfaceSchema get(String interfaceId);

}
