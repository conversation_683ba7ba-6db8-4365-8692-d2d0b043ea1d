package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.web.model.PageQuery;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/1 15:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CollectionPageQuery extends PageQuery {


    @NotBlank(message = "数据源配置ID(dcId)不能为空")
    String dcId;

    String keyword;

    String schemaName;
}
