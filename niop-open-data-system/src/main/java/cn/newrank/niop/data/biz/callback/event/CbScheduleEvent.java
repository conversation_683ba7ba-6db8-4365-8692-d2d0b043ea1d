package cn.newrank.niop.data.biz.callback.event;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/29 9:08
 */
@Data
public class CbScheduleEvent {
    @NotBlank(message = "回调源ID(cbId)不能为空")
    String cbId;
    @NotBlank(message = "服务器地址(serverAddress)不能为空")
    String serverAddress;

    public static CbScheduleEvent of(String cbId, String address) {
        final CbScheduleEvent cbScheduleEvent = new CbScheduleEvent();
        cbScheduleEvent.setCbId(cbId);
        cbScheduleEvent.setServerAddress(address);

        return cbScheduleEvent;
    }
}
