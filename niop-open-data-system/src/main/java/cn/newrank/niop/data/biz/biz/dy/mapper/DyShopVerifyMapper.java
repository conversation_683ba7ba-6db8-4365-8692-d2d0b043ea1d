package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopVerify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_shop_verify】的数据库操作Mapper
 * @createDate 2024-10-23 16:39:01
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyShopVerify
 */
@Mapper
public interface DyShopVerifyMapper {


    /**
     * 插入一条数据
     * @param item
     */
    void saveOne(@Param("item") DyShopVerify item);

}




