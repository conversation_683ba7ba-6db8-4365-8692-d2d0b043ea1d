package cn.newrank.niop.data.biz.biz.dy.service;

import cn.newrank.niop.data.biz.biz.dy.mapper.DyLiveCommentV2Mapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyLiveCommentV2;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 10:47
 */
@Log4j2
@Service
public class DyLiveSync extends DefaultHistorySynchronizer<String> {

    final DyLiveCommentV2Mapper commentV2Mapper;
    protected DyLiveSync(RedissonClient redissonClient,
                         DyLiveCommentV2Mapper commentV2Mapper) {
        super("dy_live_sync", redissonClient, 15);
        this.commentV2Mapper = commentV2Mapper;
    }


    @Override
    protected int sync(Cursor<String> cursor) {
        final String next = cursor.getNext();
        String roomId;
        String msgId;
        if (StringUtils.isBlank(next)) {
            roomId = commentV2Mapper.nextRoomId(cursor.getStart(), cursor.getEnd());
            msgId = "0";
        }else {
            final String[] cursorPart = next.split("-", 2);
            roomId = cursorPart[0];
            msgId = cursorPart[1];
        }

        if (StringUtils.isBlank(roomId) || "null".equals(roomId)) {
            return 0;
        }

        final List<DyLiveCommentV2> lives = listNonSync(roomId, msgId);
        for (DyLiveCommentV2 live : lives) {
            live.setLiveStartTime(Timestamp.valueOf(RoomIdAnalysis.roomIdToCreateTime(Long.parseLong(live.getRoomId()))));
        }

        updateSync(lives);

        if (lives.size() != 5000) {
            final String nextRoomId = commentV2Mapper.nextRoomId(roomId, cursor.getEnd());
            cursor.setNext(formatCursor(nextRoomId, "0"));
        }else {
            final DyLiveCommentV2 live = lives.get(lives.size() - 1);
            cursor.setNext(formatCursor(live.getRoomId(), live.getMsgId()));
        }

        return lives.isEmpty() ? 1 : lives.size();
    }

    static String formatCursor(String roomId, String msgId) {
        return roomId + "-" + msgId;
    }

    private void updateSync(List<DyLiveCommentV2> lives) {
        if (lives == null || lives.isEmpty()) {
            return;
        }

        commentV2Mapper.update(lives);
    }

    private List<DyLiveCommentV2> listNonSync(String roomId, String msgId) {
        return commentV2Mapper.list(roomId, msgId);
    }




    @UtilityClass
    public static class RoomIdAnalysis {

        private static BigInteger binaryToDecimal(BigInteger binary) {
            BigInteger ten = BigInteger.valueOf(10L);
            BigInteger zero = BigInteger.valueOf(0L);
            BigInteger decimal = BigInteger.valueOf(0L);
            BigInteger two = BigInteger.valueOf(2L);

            int power = 0;
            while (binary.compareTo(zero) > 0) {
                decimal = decimal.add(binary.remainder(ten).multiply(two.pow(power)));
                binary = binary.divide(ten);
                power++;
            }

            return decimal;
        }

        public static LocalDateTime roomIdToCreateTime(long roomId) {
            // Convert roomId to binary string
            String idV1 = Long.toBinaryString(roomId);

            // Extract relevant bits (skip the first 2 characters which would be '0b' in Python)
            String idV2 = idV1.substring(0, 31);

            // Convert binary string to a long
            BigInteger idV3 = new BigInteger(idV2);
            // Convert binary to decimal
            idV3 = binaryToDecimal(idV3);

            // Convert timestamp to date string
            return timeStampToLocalDatetime(Long.parseLong(idV3.toString()));
        }

        /*
        /将时间戳转为时间类型
        */
        private static LocalDateTime timeStampToLocalDatetime(Long timeStamp) {
            ZoneId zoneId = ZoneId.systemDefault();
            return timeStamp != null ? Instant.ofEpochMilli(timeStamp * 1000).atZone(zoneId).toLocalDateTime() : null;
        }
    }
}
