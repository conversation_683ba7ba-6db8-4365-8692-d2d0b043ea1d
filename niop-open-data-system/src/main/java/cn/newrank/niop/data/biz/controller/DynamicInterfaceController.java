package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.pojo.vo.ApiPastParamVo;
import cn.newrank.niop.data.biz.service.DynamicInterfaceService;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import cn.newrank.niop.web.model.PageView;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 15:55
 */

@Validated
@RestController
@RequestMapping("dynamic-interface")
public class DynamicInterfaceController {

    private final DynamicInterfaceService dynamicInterfaceService;

    public DynamicInterfaceController(DynamicInterfaceService dynamicInterfaceService) {
        this.dynamicInterfaceService = dynamicInterfaceService;
    }


    /**
     * 创建动态接口
     *
     * @param interfaceCreate 接口创建参数
     * @return 接口ID
     */
    @PostMapping("create")
    public String create(@Valid @RequestBody DynamicInterfaceCreate interfaceCreate) {
        return dynamicInterfaceService.create(interfaceCreate);
    }

    /**
     * 更新动态接口
     *
     * @param interfaceUpdate 接口更新参数
     * @return 是否更新成功
     */
    @PostMapping("update")
    public boolean update(@Valid @RequestBody DynamicInterfaceUpdate interfaceUpdate) {
        return dynamicInterfaceService.update(interfaceUpdate);
    }

    /**
     * 添加接口描述
     *
     * @param tagUpdate 接口更新参数
     * @return 是否更新成功
     */
    @PostMapping("edit-tags")
    public boolean editTags(@Valid @RequestBody DynamicInterfaceTagUpdate tagUpdate) {
        return dynamicInterfaceService.updateTags(tagUpdate);
    }

    /**
     * 删除动态接口
     *
     * @param interfaceDelete 接口删除参数
     * @return 是否删除成功
     */
    @PostMapping("delete")
    public boolean delete(@Valid @RequestBody DynamicInterfaceDelete interfaceDelete) {
        return dynamicInterfaceService.delete(interfaceDelete);
    }

    /**
     * 分页查询动态接口
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    @GetMapping("page")
    public PageView<DynamicInterface> page(@Valid DynamicInterfacePageQuery pageQuery) {
        return dynamicInterfaceService.page(pageQuery);
    }

    /**
     * 获取动态接口
     *
     * @param interfaceId 接口ID
     * @return 接口
     */
    @GetMapping("get")
    public DynamicInterface get(@NotBlank(message = "接口ID(interfaceId)不能为空") String interfaceId) {
        return dynamicInterfaceService.get(interfaceId);
    }

    /**
     * 查询动态接口预览
     *
     * @param interfaceQueryPreview 预览参数
     * @return 接口预览
     */
    @PostMapping("query-preview")
    public Resp.PageView queryPreview(@Valid @RequestBody DynamicInterfaceQueryPreview interfaceQueryPreview) {
        return dynamicInterfaceService.queryPreview(interfaceQueryPreview).getPageView();
    }

    /**
     * 查询解析之后的动态接口预览
     *
     * @param interfaceQueryPreview 预览参数
     * @return 接口预览
     */
    @PostMapping("parsed-query-preview")
    public Resp.PageView parsedQueryPreview(@Valid @RequestBody DynamicInterfaceParsedQueryPreview interfaceQueryPreview) {
        return dynamicInterfaceService.parsedQueryPreview(interfaceQueryPreview).getPageView();
    }

    /**
     * 查询动态接口debug
     *
     * @param queryDebug debug参数
     * @return 接口预览
     */
    @PostMapping("debug")
    public Resp.PageView debug(@NotNull HttpServletRequest request, @Valid @RequestBody DynamicInterfaceQueryDebug queryDebug) {
        return dynamicInterfaceService.debug(request, queryDebug).getPageView();
    }

    /**
     * 获取动态接口最近20次请求参数列表
     *
     * @param interfaceId 接口ID
     * @return 参数列表
     */
    @GetMapping("debug/listPastParams")
    public List<ApiPastParamVo> listPastParams(@NotBlank(message = "接口ID(interfaceId)不能为空") String interfaceId) {
        return dynamicInterfaceService.listPastParams(interfaceId).stream().map(ApiPastParamVo::build).toList();
    }

    /**
     * 创建动态接口debug
     *
     * @param createDebug debug参数
     * @return 接口预览
     */
    @PostMapping("create-debug")
    public Resp.PageView createDebug(@Valid @RequestBody DynamicInterfaceCreateDebug createDebug) {
        return dynamicInterfaceService.createDebug(createDebug).getPageView();
    }


    /**
     * 创建动态接口授权
     *
     * @param authCreate 授权创建参数
     * @return 授权ID
     */
    @PostMapping("auth/create")
    public String createAuth(@RequestBody @Valid DynamicInterfaceAuthCreate authCreate) {
        return dynamicInterfaceService.createAuth(authCreate);
    }

    /**
     * 删除动态接口授权
     *
     * @param authDelete 授权删除参数
     * @return 是否删除成功
     */
    @PostMapping("auth/delete")
    public boolean deleteAuth(@RequestBody @Valid DynamicInterfaceAuthDelete authDelete) {
        return dynamicInterfaceService.deleteAuth(authDelete.getAuthId());
    }

    /**
     * 更新动态接口授权
     *
     * @param authUpdate 授权更新参数
     * @return 是否更新成功
     */
    @PostMapping("auth/update")
    public boolean updateAuth(@RequestBody @Valid DynamicInterfaceAuthUpdate authUpdate) {
        return dynamicInterfaceService.updateAuth(authUpdate);
    }

    /**
     * 获取动态接口授权列表
     *
     * @param interfaceId 接口ID
     * @return 授权列表
     */
    @GetMapping("auth/list")
    public List<DynamicInterfaceAuth> listAuth(@NotBlank(message = "接口ID(interfaceId)不能为空")
                                               String interfaceId) {
        return dynamicInterfaceService.listAuth(interfaceId);
    }

    @GetMapping("auth/get")
    public DynamicInterfaceAuth getAuth(@NotBlank(message = "授权ID(authId)不能为空") String authId) {
        return dynamicInterfaceService.getAuth(authId);
    }
}
