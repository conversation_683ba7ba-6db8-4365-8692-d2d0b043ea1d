package cn.newrank.niop.data.util;

import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/12/28 11:14
 */
@UtilityClass
public class Collections {


    public static boolean equals(Collection<?> a, Collection<?> b) {
        if (a == null) {
            return b == null;
        }
        if (b == null) {
            return false;
        }

        if (a.size() != b.size()) {
            return false;
        }

        for (Object o : a) {
            if (!b.contains(o)) {
                return false;
            }
        }

        return true;
    }

    public static <T> List<T> diff(List<T> a, List<T> b) {
        if (a == null || a.isEmpty()) {
            return b == null || b.isEmpty() ? new ArrayList<>() : b;
        }
        if (b == null || b.isEmpty()) {
            return a;
        }

        final List<T> ts = new ArrayList<>(a.size() + b.size());

        a.forEach(item -> {
            if (!b.contains(item)) {
                ts.add(item);
            }
        });

        b.forEach(item -> {
            if (!a.contains(item)) {
                ts.add(item);
            }
        });

        return ts;
    }
}
