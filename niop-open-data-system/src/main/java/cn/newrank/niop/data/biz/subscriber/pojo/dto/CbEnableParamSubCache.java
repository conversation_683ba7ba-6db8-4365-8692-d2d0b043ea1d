package cn.newrank.niop.data.biz.subscriber.pojo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
public class CbEnableParamSubCache {
    public static final CbEnableParamSubCache NOOP = new CbEnableParamSubCache("0", null, Set.of(), false, List.of());
    /**
     * 回调源ID
     */
    String cbId;
    /**
     * 发送策略
     */
    SendStrategy sendStrategy;
    /**
     * 应用ID列表
     */
    Set<String> appIds = new HashSet<>();
    /**
     * 是否启用参数
     */
     Boolean enableParams;
    /**
     * 标签列表
     */
    List<Tag> tags;


    public CbEnableParamSubCache(String cbId, SendStrategy sendStrategy, Set<String> appIds, Boolean enableParams, List<Tag> tags) {
        this.cbId = cbId;
        this.sendStrategy = sendStrategy;
        this.appIds = appIds;
        this.enableParams = enableParams;
        this.tags = tags;
    }
}
