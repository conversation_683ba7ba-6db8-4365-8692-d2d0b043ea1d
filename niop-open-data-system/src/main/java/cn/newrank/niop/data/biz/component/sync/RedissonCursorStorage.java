package cn.newrank.niop.data.biz.component.sync;

import cn.newrank.niop.data.util.DateTimeUtil;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 20:13
 */
public class RedissonCursorStorage<T> implements CursorStorage<T, Cursor<T>> {
    private final RMap<String, Cursor<T>> cache;

    public RedissonCursorStorage(RedissonClient redissonClient, String key) {
        this.cache = redissonClient.getMap("history:sync:cursor:" + key, new JsonJacksonCodec());
    }

    @Override
    public void update(Cursor<T> cursor) {
        cursor.setUpdateTime(DateTimeUtil.format(LocalDateTime.now()));
        cache.put(cursor.getKey(), cursor);
    }

    @Override
    public void save(Cursor<T> cursor) {
        cursor.setUpdateTime(DateTimeUtil.format(LocalDateTime.now()));
        cache.putIfAbsent(cursor.getKey(), cursor);
    }

    @Override
    public List<Cursor<T>> getStarted() {
        return cache.values()
                .stream()
                .filter(Cursor::isStarted)
                .toList();
    }

    @Override
    public List<Cursor<T>> getUnstarted(int count) {
        return cache.values()
                .stream()
                .filter(Cursor::isUnstarted)
                .sorted()
                .limit(count)
                .toList();
    }

    @Override
    public List<Cursor<T>> getAll() {
        return cache.values()
                .stream()
                .sorted()
                .toList();
    }

    @Override
    public Cursor<T> get(String key) {
        return cache.get(key);
    }

    @Override
    public void remove(String key) {
        cache.remove(key);
    }
}
