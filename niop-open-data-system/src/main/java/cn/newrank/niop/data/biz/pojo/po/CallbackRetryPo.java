package cn.newrank.niop.data.biz.pojo.po;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class CallbackRetryPo {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private Timestamp gmtModified;

    /**
     *
     */
    private Timestamp gmtCreate;

    /**
     * 重试id
     */
    private String retryId;

    /**
     * 重试状态
     */
    private RetryStatus retryStatus;

    /**
     * 重试次数
     */
    private Integer retryNums;

    /**
     * 下次重试时间
     */
    private Timestamp nextRetryTime;

    /**
     * 失败信息
     */
    private String errorInfo;

    /**
     * 存储信息
     */
    private String storageBiz;

    /**
     * 来源key
     */
    private String sourceKey;

    /**
     * 来源id
     */
    private String sourceId;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 消息记录
     */
    private String consumerRecordCarrier;

    /**
     * 负载
     */
    private String payload;

    /**
     * 是否初始化完成
     */
    Boolean initialized;

    public static CallbackRetryPo fromDto(CallbackRetry retry) {
        CallbackRetryPo retryPo = new CallbackRetryPo();

        // TODO 手动取值
        BeanUtil.copyProperties(retry, retryPo);

        ConsumerRecordCarrier carrier = retry.getConsumerRecordCarrier();
        retryPo.setConsumerRecordCarrier(JSON.toJSONString(carrier));

        // 初始化
        retryPo.setInitialized(true);
        final Timestamp now = Timestamp.from(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)));
        if (retryPo.getGmtCreate() == null) {
            retryPo.setGmtCreate(now);
        }
        return retryPo;
    }

    public CallbackRetry toDto() {
        CallbackRetry retry = new CallbackRetry();

        // TODO 手动取值
        BeanUtil.copyProperties(this, retry, CopyOptions.create().ignoreError());
        ConsumerRecordCarrier carrier = JSON.parseObject(this.consumerRecordCarrier, ConsumerRecordCarrier.class);
        retry.setConsumerRecordCarrier(carrier);
        return retry;
    }

}