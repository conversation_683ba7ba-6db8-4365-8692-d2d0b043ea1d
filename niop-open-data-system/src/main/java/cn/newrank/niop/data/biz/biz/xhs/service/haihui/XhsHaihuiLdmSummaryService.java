package cn.newrank.niop.data.biz.biz.xhs.service.haihui;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsHaiHuiLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.haihui.XhsHaiHuiOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:12:11
 */
@Slf4j
@Service
public class XhsHaihuiLdmSummaryService {

    private final XhsHaiHuiLmMapper xhsHaiHuiLmMapper;
    private final RedissonClient redissonClient;
    private final EsFactory.Es kolEs;
    public static final String XHS_OPUS_HAIHUI_KOL_PAGE = """
                     GET /ad_haihui_work_mmrecog/_search
                     {
                        "size": #{size},
                       "query": {
                          "match_all": {}
                       },
                       "sort": [ { "workId": "asc" } ],
                       "search_after": [#{searchAfter}],
                       "_source": ["workId", "cover.summary", "cover.otherSummary"]
                     }
            """;

    public XhsHaihuiLdmSummaryService(XhsHaiHuiLmMapper xhsHaiHuiLmMapper,
                                      DsConfigManager dsConfigManager,
                                      RedissonClient redissonClient) {
        this.xhsHaiHuiLmMapper = xhsHaiHuiLmMapper;
        this.redissonClient = redissonClient;
        this.kolEs = EsFactory.DEFAULT.create(dsConfigManager.chooseAdHoloKolConfig());
    }


    public void syncDataFormEsAndHolo() {
        long countOpusSize = 0L;
        RBucket<String> cachedCursor = redissonClient.getBucket(getHaiHuiSyncEsScrollIdKey());
        String opusCursor = Strings.isBlank(cachedCursor.get()) ? "" : cachedCursor.get();
        // 翻页拿es作品
        while (true) {
            Resp pageRes = getEsRespScroll(opusCursor);
            List<XhsHaiHuiOpus> coverSummaries = getCoverSummaryMap(pageRes);
            if (CollUtil.isEmpty(coverSummaries)) {
                break;
            }
            opusCursor = coverSummaries.get(coverSummaries.size() - 1).getOpusId();
            countOpusSize += coverSummaries.size();
            cachedCursor.set(opusCursor, 30, TimeUnit.MINUTES);

            Map<String, String> esDataMap = coverSummaries.stream()
                    .collect(Collectors.toMap(XhsHaiHuiOpus::getOpusId, XhsHaiHuiOpus::getCoverSummary));
            List<String> opusIds = esDataMap.keySet().stream().toList();
            List<XhsHaiHuiOpus> existOpus = xhsHaiHuiLmMapper.searchOpusBatch(opusIds);
            if(CollectionUtil.isEmpty(existOpus)){
                continue;
            }
            for (XhsHaiHuiOpus opus : existOpus) {
                opus.setCoverSummary(esDataMap.get(opus.getOpusId()));
                opus.setTextField(getTextField(opus));
            }
            xhsHaiHuiLmMapper.addFieldSaveBatch(existOpus);
            XxlJobLogger.log("数据同步到lindorm共: {} 作品, 新增 {}, 补充作品：{},cursor: {}",
                    countOpusSize, coverSummaries.size(), existOpus.size(), opusCursor);
        }
        cachedCursor.set("");
        XxlJobLogger.log("总数据同步到lindorm共: {} 作品", countOpusSize);
    }

    private Resp getEsRespScroll(String cursor) {
        if (Strings.isBlank(cursor)) {
            cursor = "";
        }
        return kolEs.query(kolEs.newQueryBuilder().template(XHS_OPUS_HAIHUI_KOL_PAGE)
                .addParam("size", 2000)
                .addParam("searchAfter", cursor));
    }


    private List<XhsHaiHuiOpus> getCoverSummaryMap(Resp pageRes) {
        Resp.DataView dataView = pageRes.getDataView();
        if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
            JSONObject resultObj = esDataView.resp();
            if (Objects.isNull(resultObj)) {
                return Collections.emptyList();
            }
            List<XhsHaiHuiOpus> coverSummaries = EsUtil.listHitsToEntity(resultObj, json -> {
                JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
                String opusId = sourceObj.getString("workId");
                JSONObject summary = sourceObj.getJSONObject("cover");
                String coverSummary = Optional.ofNullable(summary.getString("summary")).orElse("");
                String coverOtherSummary = Optional.ofNullable(summary.getString("otherSummary")).orElse("");

                XhsHaiHuiOpus res = new XhsHaiHuiOpus();
                res.setOpusId(opusId);
                res.setCoverSummary(getCoverSummary(coverSummary, coverOtherSummary));
                return res;
            });
            if (coverSummaries.isEmpty()) {
                return Collections.emptyList();
            }
            return coverSummaries;
        }
        return Collections.emptyList();
    }

    private static String getCoverSummary(String coverSummary, String coverOtherSummary) {
        if (Strings.isNotBlank(coverSummary) && Strings.isNotBlank(coverOtherSummary)) {
            return coverSummary + "," + coverOtherSummary;
        } else if (Strings.isBlank(coverSummary) && Strings.isBlank(coverOtherSummary)) {
            return "";
        } else if (Strings.isBlank(coverSummary)) {
            return coverOtherSummary;
        } else {
            return coverSummary;
        }
    }

    private static String getTextField(XhsHaiHuiOpus opus) {
        String interval = "。\n";
        List<String> fields = Arrays.asList(
                opus.getTextField(),
                opus.getCoverSummary()
        );
        return fields.stream().filter(Strings::isNotBlank).collect(Collectors.joining(interval));
    }

    /**
     * 保存数据到lindorm
     *
     * @param opusList
     */
    private void saveOpusToLindorm(List<XhsHaiHuiOpus> opusList) {
        xhsHaiHuiLmMapper.saveBatch(opusList);
    }


    public static String getHaiHuiSyncEsScrollIdKey() {
        return "haihui:sync:schedule:summary:scrollId";
    }
}
