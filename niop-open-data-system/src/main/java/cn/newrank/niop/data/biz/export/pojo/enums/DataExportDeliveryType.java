package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

public enum DataExportDeliveryType implements BizEnum {

    /**
     * 导数交付类型
     */
    EXCEL("excel", "Excel"),
    ;

    private final String code;
    private final String description;

    DataExportDeliveryType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

}
