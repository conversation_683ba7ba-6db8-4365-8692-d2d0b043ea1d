package cn.newrank.niop.data.biz.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/23 15:20
 */
@Data
public class DynamicInterfaceCreateDebug {
    /**
     * 数据源配置ID
     */
    @NotBlank(message = "数据源配置ID(dcId)不能为空")
    String dcId;
    /**
     * 语句模板
     */
    @NotBlank(message = "语句(query)不能为空")
    String query;
    /**
     * debug参数
     */
    private Map<String, Object> debugParams;
}
