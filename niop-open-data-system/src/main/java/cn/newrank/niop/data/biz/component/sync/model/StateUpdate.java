package cn.newrank.niop.data.biz.component.sync.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 16:10
 */
@Data
public class StateUpdate {
    @NotBlank(message = "name不能为空")
    String name;
    @NotBlank(message = "key不能为空")
    String key;

    @NotNull(message = "state不能为空")
    State state;
}
