package cn.newrank.niop.data.biz.dataclear.service.impl;

import cn.newrank.niop.data.biz.dataclear.mapper.DataClearTaskMapper;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.RuleStatus;
import cn.newrank.niop.data.biz.dataclear.pojo.TaskStatus;
import cn.newrank.niop.data.biz.dataclear.pojo.param.*;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanTaskService;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.niop.web.model.PageView;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.newrank.niop.data.biz.dataclear.utils.CronUtils.checkIsTimeBefore;
import static cn.newrank.niop.data.biz.dataclear.utils.CronUtils.getNextExecTime;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

@Service
@Slf4j
public class DataCleanTaskServiceImpl implements DataCleanTaskService {

    private static final String TASK_LOCK_KEY = "openData:dataCleanTask";
    // 任务超时阈值
    private static final long TASK_TIMEOUT_THRESHOLD = 500000L;
    private final DataClearTaskMapper dataClearTaskMapper;
    private final RedissonClient redissonClient;
    private final SqlService sqlService;

    public DataCleanTaskServiceImpl(DataClearTaskMapper dataClearTaskMapper, RedissonClient redissonClient, SqlService sqlService) {
        this.dataClearTaskMapper = dataClearTaskMapper;
        this.redissonClient = redissonClient;
        this.sqlService = sqlService;
    }


    //重建规则
    @EventListener
    public void handleCustomEvent(DataCleanRuleCreateQuery event) {
        //todo 有新增的cron的时候新增加入redis的list中，（或者跳表，记录下次执行的时间，每次执行完修改时间并且计算）
        //事件触发xxljob去执行一次
        if (!checkIsTimeBefore(event.getCron())) {
            return;
        }
        String s = create(event);
        log.info("生成一条规则表触发任务表生成 规则id为：{} ,任务id为:{} ", event.getRuleId(), s);
    }

    @Override
    public String create(DataCleanRuleCreateQuery event) {
        //检测当前任务是否去创建
        if (!checkLastIsExecuted(event.getRuleId())) {
            return null;
        }
        DataCleanTask dataCleanTask = buildByRule(event);

        dataClearTaskMapper.insert(dataCleanTask);

        return dataCleanTask.getTaskId();
    }

    //更新原有的规则
    @EventListener
    public void handleCustomEventForUpdate(CleanRuleUpadteQuery event) {
        //构建新增一条任务
        DataCleanTask dataCleanTask = new DataCleanTask();
        //设置下次执行时间
        dataCleanTask.setNextExecTime(getNextExecTime(event.getCron()));

        //拼接sql,仅仅是作为对外展示的语句
        dataCleanTask.setSql(sqlService.buildViewDataSourceSql(event));

        dataCleanTask.setDcId(event.getDcId());

        dataCleanTask.setRuleId(event.getRuleId());

        dataCleanTask.setTableName(event.getTableName());

        if (event.getRuleStatus() == RuleStatus.DISABLED) {
            dataCleanTask.setTaskStatus(TaskStatus.DISCARDED);
        }

        try {
            //构建真实的sql
            dataCleanTask.setRealSql(sqlService.buildRealSQL(event));
            long i = sqlService.sqlCount(event);
            dataCleanTask.setPredictDeleteNum((int) i);
        } catch (Exception e) {
            dataCleanTask.setTaskStatus(TaskStatus.FAILED);
        }
        //去更新停止执行的任务
        int i = dataClearTaskMapper.updateByRuleId(dataCleanTask);
    }

    @EventListener
    public void handleCustomEventForDelete(CleanRuleDeleteQuery event) {
        //构建新增一条任务
        DataCleanTask dataCleanTask = new DataCleanTask();

        dataCleanTask.setRuleId(event.getRuleId());

        if (event.getRuleStatus() == RuleStatus.DISABLED) {
            dataCleanTask.setTaskStatus(TaskStatus.DISCARDED);
        }
        //去更新停止执行的任务
        int i = dataClearTaskMapper.updateByRuleId(dataCleanTask);
    }


    @Override
    public PageView<DataCleanTask> page(DataCleanTaskPageQuery pageQuery) {
        final Page<DataCleanRuleCreate> mybatisPlusPage = pageQuery.toMybatisPlusPage();

        final Page<DataCleanTask> page = dataClearTaskMapper.page(pageQuery, mybatisPlusPage);

        return PageView.of(page);
    }

    //检测上次调度是否完成
    @Override
    public boolean checkLastIsExecuted(String ruleId) {
        //拿出上次執行的
        DataCleanTask dataCleanTask = dataClearTaskMapper.getByRuleId(ruleId);
        if (Objects.isNull(dataCleanTask) || dataCleanTask.getTaskStatus() == TaskStatus.SUCCESS) {
            return true;
        } else if ((dataCleanTask.getTaskStatus() == TaskStatus.EXECUTING &&
                dataCleanTask.getDuration() > TASK_TIMEOUT_THRESHOLD)) {
            //检测执行中任务是否超时，超时的话就设置状态为丢弃
            dataCleanTask.setTaskStatus(TaskStatus.FAILED);
            dataClearTaskMapper.update(dataCleanTask);
        }
        return false;
    }

    @Override
    public List<DataCleanTask> listWaitingTaskAndLock() {
        List<DataCleanTask> list;
        // 获取锁对象，去获取三个任务去执行
        RLock lock = redissonClient.getLock(TASK_LOCK_KEY);
        boolean tryLock = lock.tryLock();
        if (!tryLock) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return new ArrayList<>();
        }
        try {
            list = dataClearTaskMapper.list();

            if (list != null && !list.isEmpty()) {
                for (DataCleanTask dataCleanTask : list) {
                    dataClearTaskMapper.updateIds(dataCleanTask.getTaskId());
                }
            }
        } finally {
            lock.unlock();
        }
        return list;
    }


    @Override
    public boolean update(DataCleanTask cleanRuleUpadteQuery) {
        return dataClearTaskMapper.update(cleanRuleUpadteQuery) > 0;
    }

    @Override
    public boolean updateStatus(DataCleanTaskUpdateStatus cleanTaskUpdateStatus) {
        if (cleanTaskUpdateStatus.getTaskStatus() != TaskStatus.WAITING
                && cleanTaskUpdateStatus.getTaskStatus() != TaskStatus.STOPPED) {
            throw createParamError("状态码传参错误");
        }
        return dataClearTaskMapper.updateStatus(cleanTaskUpdateStatus) > 0;
    }

    @Override
    public boolean delete(String taskId) {
        return dataClearTaskMapper.delete(taskId) > 0;
    }

    @Override
    public DataCleanTask get(String taskId) {
        return dataClearTaskMapper.get(taskId);
    }

    @Override
    public List<DataCleanRuleCreate> fuzzyQuery(DataCleanTask fuzzyQuery) {
        return List.of();
    }

    public DataCleanTask buildByRule(DataCleanRuleCreateQuery event) {
        //构建新增一条任务
        DataCleanTask dataCleanTask = new DataCleanTask();
        //查询上次调度是否执行完成
        dataCleanTask.setTaskId(Ids.create(12));

        dataCleanTask.setTaskStatus(TaskStatus.WAITING);
        //设置下次执行时间
        dataCleanTask.setNextExecTime(getNextExecTime(event.getCron()));
        //拼接sql
        dataCleanTask.setSql(sqlService.buildViewDataSourceSql(event));

        dataCleanTask.setDcId(event.getDcId());

        dataCleanTask.setRuleId(event.getRuleId());

        dataCleanTask.setTableName(event.getTableName());

        try {
            dataCleanTask.setRealSql(sqlService.buildRealSQL(event));
            long i = sqlService.sqlCount(event);
            dataCleanTask.setPredictDeleteNum((int) i);
        } catch (Exception e) {
            dataCleanTask.setTaskStatus(TaskStatus.FAILED);
            log.error("任务：{}：执行预测数量时超时，执行停止操作", dataCleanTask.getTaskId());
        }
        return dataCleanTask;

    }
}
