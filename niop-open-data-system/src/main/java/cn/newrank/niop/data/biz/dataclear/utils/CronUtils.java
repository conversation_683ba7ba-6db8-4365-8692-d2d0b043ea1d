package cn.newrank.niop.data.biz.dataclear.utils;

import cn.newrank.niop.data.common.BizErr;
import com.xkzhangsan.time.cron.CronExpressionUtil;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;
import static com.xkzhangsan.time.cron.CronExpressionUtil.getNextTimeList;

public class CronUtils {


    public static boolean validateCron(String cronExpression) {
        List<Date> nextTimeList = getNextTimeList(cronExpression, 2);
        LocalDateTime localDateTime = dateConvertLocalDateTime(nextTimeList.get(0));
        LocalDateTime localDateTime1 = dateConvertLocalDateTime(nextTimeList.get(1));
        // 检查两次时间间隔是否小于60分钟
        return Duration.between(localDateTime, localDateTime1).toMinutes() >= 60;
        //  throw createParamError("Invalid cron expression for hourly or daily schedule: " + cronExpression);
    }

//    public static void main(String[] args) {
//        validateCron("0 0/10 9-23 * * ?");
//    }

    /**
     * 获取cron表达式下次执行时间
     *
     * @param cronExpression cron表达式
     * @return
     */
    public static LocalDateTime getNextExecTime(String cronExpression) {
        Date nextTime;
        try {
            nextTime = CronExpressionUtil.getNextTime(cronExpression);
        } catch (Exception e) {
            throw createBizException(BizErr.CRON_ERROR, "cron表达式错误");
        }
        if (nextTime == null) {
            throw createBizException(BizErr.CRON_ERROR, "cron表达式错误");
        }
        return dateConvertLocalDateTime(nextTime);
    }

    /**
     * 检测下次调度时间是否正好在当前这一小时
     *
     * @param cron
     * @return boolean
     */
    public static boolean checkIsTimeBefore(String cron) {
        //检测调度时间是否是当前小时
        LocalDateTime nextExecTime = getNextExecTime(cron);

        return isNextExecTimeInCurrentHour(nextExecTime);
    }

    public static boolean isNextExecTimeInCurrentHour(LocalDateTime nextExecTime) {
        if (nextExecTime == null) {
            return false;
        }
//
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfCurrentHour = now.truncatedTo(ChronoUnit.HOURS);
        LocalDateTime endOfCurrentHour = startOfCurrentHour.plusHours(1).plusMinutes(1);

        // 检查 nextExecTime 是否在当前小时的范围内，包括边界
        return !nextExecTime.isBefore(startOfCurrentHour) && nextExecTime.isBefore(endOfCurrentHour);
    }

    public static LocalDateTime dateConvertLocalDateTime(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault()) // 使用系统默认时区
                .toLocalDateTime();
    }

    /**
     * 将 LocalDateTime 转换为时间戳（秒和毫秒）。
     *
     * @param localDateTime 要转换的 LocalDateTime 对象
     * @return 一个包含秒和毫秒的时间戳对象
     */
    public static long convertToLocalDateTimeTimestamp(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        // 将 LocalDateTime 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        // 将 ZonedDateTime 转换为 Instant
        Instant instant = zonedDateTime.toInstant();

        // 获取时间戳（秒和毫秒）
        long timestampInSeconds = instant.getEpochSecond();
        long timestampInMillis = instant.toEpochMilli();

        return timestampInMillis;
    }

    public static void main(String[] args) {
        //2024-11-12T14:00
    }
}
