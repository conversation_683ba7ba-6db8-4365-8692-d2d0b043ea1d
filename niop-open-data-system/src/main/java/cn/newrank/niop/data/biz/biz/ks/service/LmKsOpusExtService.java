package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.util.U;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 更新数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:50
 */
@Service
@Log4j2
public class LmKsOpusExtService implements StorageBizService<LmKsOpus> {

    private final LmKsOpusMapper lmKsOpusMapper;

    public LmKsOpusExtService(LmKsOpusMapper lmKsOpusMapper) {
        this.lmKsOpusMapper = lmKsOpusMapper;
    }

    @Override
    public void storeBatch(List<LmKsOpus> items) {
        if (items.isEmpty()) {
            return;
        }

        final List<LmKsOpus> removed = new ArrayList<>();
        final List<LmKsOpus> updated = new ArrayList<>();
        for (LmKsOpus item : items) {
            if (item == null) {
                continue;
            }
            if (item.isDelete()) {
                removed.add(item);
            } else {
                updated.add(item);
            }
        }

        if (!removed.isEmpty()) {
            lmKsOpusMapper.deleteBatch(removed);
        }

        if (!updated.isEmpty()) {
            final Map<String, LmKsOpus> map = U.toMap(lmKsOpusMapper.list(U.toList(updated, LmKsOpus::getPhotoId)),
                    LmKsOpus::getPhotoId,
                    Function.identity());

            updated.forEach(item -> item.copyIfExistNull(map.get(item.getPhotoId())));
            lmKsOpusMapper.storeExtBatch(updated);
        }
    }

    @Override
    public LmKsOpus castOf(JSONObject item) {
        JSONObject details = item.getJSONObject("json_details");
        if (details == null || !"aweme_offline".equals(item.getString("data_type"))) {
            return null;
        }

        //作品被标记为删除的作品不写入
        final LmKsOpus ksOpus = new LmKsOpus();
        final Integer anaDel = details.getInteger("ana_del");
        if (anaDel != null) {
            ksOpus.setAnaDel(anaDel);
        }

        Integer isLowHot = details.getInteger("is_low_fan_account_hot_aweme");
        if (isLowHot != null) {
            ksOpus.setIsLowHot(isLowHot);
        }


        ksOpus.setPhotoId(details.getString("photo_id"));
        ksOpus.setLikeCount(details.getLong("like_count"));
        ksOpus.setCommentCount(details.getLong("comment_count"));
        ksOpus.setViewCount(details.getLong("view_count"));
        ksOpus.setShareCount(details.getLong("share_count"));
        ksOpus.setCollectCount(details.getLong("collect_count"));
        ksOpus.setUserType(details.getString("user_type"));
        ksOpus.setUserFan(details.getLong("user_fan"));
        ksOpus.setIsHotAweme(details.getInteger("is_hot_aweme"));

        ksOpus.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));

        return ksOpus;
    }
}
