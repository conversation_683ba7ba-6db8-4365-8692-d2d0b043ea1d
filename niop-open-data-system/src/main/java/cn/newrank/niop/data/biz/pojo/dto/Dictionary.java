package cn.newrank.niop.data.biz.pojo.dto;

import cn.hutool.crypto.digest.DigestUtil;
import cn.newrank.niop.data.common.ds.Column;
import cn.newrank.niop.data.common.enums.DsType;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.util.U;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 14:46
 */
@Data
public class Dictionary {
    private String dsType;
    private String dcId;
    private String database;
    private String collection;

    private List<DictionaryColumn> columns;
    private String dicId;
    private String description;
    /**
     * 自定字段
     */
    private String remark;
    /**
     * 自定字段
     */
    private List<String> tags;
    private String gmtModified;
    private String gmtCreate;

    public static String newId(String dcId, String collection) {
        return DigestUtil.md5Hex16(dcId + collection).toUpperCase();
    }

    public static Builder builder() {
        return new Builder();
    }

    @Data
    public static class DictionaryColumn {
        /**
         * 自定字段
         */
        private List<String> columnAliases;
        private String columnDescription;
        private String columnName;
        private String columnType;
        private Boolean columnNullable;
    }

    public static class Builder {
        private String dsType;
        private String dcId;
        private String database;
        private String collection;
        /**
         * 原始字段来自数据源
         */
        private List<DictionaryColumn> columnsForOriginal;
        /**
         * 自定义字段来自字典
         */
        private List<DictionaryColumn> columnsForCustom;
        private String dicId;
        private String description;
        /**
         * 自定字段
         */
        private String remark;
        /**
         * 自定字段
         */
        private List<String> tags;
        private String gmtModified;
        private String gmtCreate;


        public Builder dsType(DsType dsType) {
            if (dsType != null) {
                this.dsType = dsType.getJsonValue();
            }
            return this;
        }

        public Builder dcId(String dcId) {
            this.dcId = dcId;
            return this;
        }

        public Builder database(String database) {
            this.database = database;
            return this;
        }

        public Builder collection(String collection) {
            this.collection = collection;
            return this;
        }

        public Builder columnsOf(List<Column> columns) {
            this.columnsForOriginal = U.toList(columns, column -> {
                final DictionaryColumn dictionaryColumn = new DictionaryColumn();
                dictionaryColumn.setColumnName(column.getName());
                dictionaryColumn.setColumnType(column.getType());
                dictionaryColumn.setColumnNullable(column.getNullable());
                dictionaryColumn.setColumnDescription(column.getDescription());

                return dictionaryColumn;
            });
            return this;
        }


        public Builder dicId(String dicId) {
            this.dicId = dicId;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public Builder tags(List<String> tags) {
            this.tags = tags;
            return this;
        }

        public Builder gmtModified(LocalDateTime gmtModified) {
            this.gmtModified = DateTimeUtil.format(gmtModified);
            return this;
        }

        public Builder gmtCreate(LocalDateTime gmtCreate) {
            this.gmtCreate = DateTimeUtil.format(gmtCreate);
            return this;
        }

        public Dictionary buildForNew() {
            final Dictionary dictionary = new Dictionary();
            dictionary.setDsType(dsType);
            dictionary.setDcId(dcId);
            dictionary.setDatabase(database);
            dictionary.setCollection(collection);
            dictionary.setColumns(columnsForOriginal);
            dictionary.setDicId(dicId);
            dictionary.setDescription(description);
            dictionary.setRemark(remark);
            dictionary.setTags(tags);
            dictionary.setGmtModified(gmtModified);
            dictionary.setGmtCreate(gmtCreate);

            return dictionary;
        }

        public Dictionary buildForUpdate() {
            final Dictionary dictionary = buildForNew();

            // 去掉创建时间
            dictionary.setGmtCreate(null);

            // 合并自定义字段
            if (columnsForCustom != null) {
                final Map<String, DictionaryColumn> map = U.toMap(columnsForCustom
                        , DictionaryColumn::getColumnName
                        , Function.identity());

                columnsForOriginal.forEach(c -> {
                    final DictionaryColumn column = map.get(c.getColumnName());
                    if (column != null) {
                        // 保留自定义字段
                        c.setColumnAliases(column.getColumnAliases());
                    }
                });
            }

            return dictionary;
        }

        public Builder oldColumns(List<DictionaryColumn> columns) {
            this.columnsForCustom = columns;
            return this;
        }

        public Builder columns(List<DictionaryColumn> columns) {
            this.columnsForOriginal = columns == null ? new ArrayList<>() : columns;
            return this;
        }
    }
}
