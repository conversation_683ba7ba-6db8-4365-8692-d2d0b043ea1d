package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExportResult {

    /**
     * 获取结果任务id
     *
     * @return 结果任务id
     */
    String getResultTaskId();

    /**
     * 获取Json数据列表
     *
     * @return 数据列表
     */
    List<JSONObject> getDataList();

    /**
     * 是否有更多数据
     *
     * @return 是否有更多数据
     */
    boolean hasMore();

    /**
     * 获取下次游标参数
     *
     * @return 下次游标参数
     */
    default String nextCursor() {
        return null;
    }

    /**
     * 获取第一个数据
     *
     * @return 第一个数据
     */
    default JSONObject firstData() {
        return getDataList().get(0);
    }

    /**
     * 判断数据列表是否为空
     *
     * @return 是否为空
     */
    default boolean isNotEmpty() {
        return CollUtil.isNotEmpty(getDataList());
    }

    /**
     * 获取结果数据的条数
     *
     * @return 结果数据条数
     */
    default Integer resultSize() {
        return CollUtil.isNotEmpty(getDataList()) ? getDataList().size() : 0;
    }

}
