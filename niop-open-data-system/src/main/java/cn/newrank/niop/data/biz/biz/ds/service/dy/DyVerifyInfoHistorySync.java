package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.EMPTY;
import static cn.hutool.core.text.CharSequenceUtil.nullToDefault;


/**
 * [dy认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class DyVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET search_douyin_user/_search?scroll=15m
            {
                "_source": [
                  "uid",
                  "custom_verify",
                  "verify_label",
                  "organization_certs"
                ],
              "size": 1500,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "custom_verify" } },
                    { "exists": { "field": "verify_label" } },
                    { "exists": { "field": "organization_certs" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected DyVerifyInfoHistorySync(RedissonClient redissonClient,
                                      CommonEsService commonEsService,
                                      DsConfigManager dsConfigManager) {
        super(PlatformType.DY.getDbCode(), dsConfigManager.chooseDyEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final VerifyInfoUpdate update = new VerifyInfoUpdate();
        update.setIndexId(PlatformType.DY.getDbCode() + "_" + sourceObj.get("uid"));
        update.setVerifyInfo(nullToDefault(sourceObj.getString("custom_verify"), EMPTY));
        update.setVerifyTypeV1(nullToDefault(sourceObj.getString("verify_label"), EMPTY));

        // 查找第一个元素的name字段
//        String name = Optional.ofNullable(sourceObj.getJSONArray("organization_certs"))
//                .map(array -> array.getJSONObject(0))
//                .map(first -> first.getString("name"))
//                .orElse(EMPTY);
//
//        update.setVerifyTypeV2(name);

        return update;
    }
}
