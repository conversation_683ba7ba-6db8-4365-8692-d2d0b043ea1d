package cn.newrank.niop.data.biz.callback.scheduler;

import cn.newrank.niop.data.biz.callback.service.CallbackRedirectRetryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Log4j2
@RequiredArgsConstructor
@RestController
@RequestMapping("callbackRedirectRetry")
public class CallbackRedirectRetryScheduler {

    private final CallbackRedirectRetryService redirectRetryService;

    @GetMapping("retryCallbackRedirectScheduler")
    @XxlJob("RetryCallbackRedirectScheduler")
    public ReturnT<String> retryCallbackRedirectScheduler(String ignore) {
        redirectRetryService.retryCallbackRedirect();
        return ReturnT.SUCCESS;
    }

}
