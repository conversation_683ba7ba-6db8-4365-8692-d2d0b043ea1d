package cn.newrank.niop.data.biz.biz.xhs.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/8/12 15:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XhsOpusClassify extends StorageEntity {
    private String opusId;
    private String title;
    private String desc;

    public static XhsOpusClassify build(XhsOpusFromMulti item) {
        XhsOpusClassify opus = new XhsOpusClassify();
        opus.opusId = item.getOpusId();
        opus.title = item.getTitle();
        opus.desc = item.getDesc();
        return opus;
    }

    @Override
    public String identifier() {
        return opusId;
    }
}
