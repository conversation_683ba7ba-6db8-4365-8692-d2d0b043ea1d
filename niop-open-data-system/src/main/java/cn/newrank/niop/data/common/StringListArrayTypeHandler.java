package cn.newrank.niop.data.common;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/17 14:58
 */
public class StringListArrayTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType)
            throws SQLException {
        if (parameter != null) {
            String[] array = parameter.toArray(new String[0]);
            // 使用 setArray 方法插入数组
            ps.setArray(i, ps.getConnection().createArrayOf("varchar", array));
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 从结果集中获取数组
        Array array = rs.getArray(columnName);
        if (array == null) {
            return new ArrayList<>();
        }
        // 将数组转换为 List<String>
        return new ArrayList<>(Arrays.asList((String[]) array.getArray()));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 从结果集中获取数组
        Array array = rs.getArray(columnIndex);
        if (array == null) {
            return new ArrayList<>();
        }
        // 将数组转换为 List<String>
        return new ArrayList<>(Arrays.asList((String[]) array.getArray()));
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        // 从 CallableStatement 中获取数组
        Array array = cs.getArray(columnIndex);
        if (array == null) {
            return new ArrayList<>();
        }
        // 将数组转换为 List<String>
        return new ArrayList<>(Arrays.asList((String[]) array.getArray()));
    }
}
