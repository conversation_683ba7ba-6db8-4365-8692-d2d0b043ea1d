package cn.newrank.niop.data.config;


import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 16:28
 */
@Configuration
public class EsConfiguration {

    private final ElasticsearchProperties elasticsearchProperties;

    public EsConfiguration(ElasticsearchProperties elasticsearchProperties) {
        this.elasticsearchProperties = elasticsearchProperties;
    }

    private static HttpHost getHttpHost(List<String> urls) {
        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("elasticsearch urls is empty");
        }

        final String url = urls.get(0);
        final String[] args = url.split("://");
        final int idx = args[1].indexOf(":");

        return new HttpHost(args[1].substring(0, idx)
                , Integer.parseInt(args[1].substring(idx + 1))
                , args[0]);

    }

    @Bean
    public RestHighLevelClient restHighLevelClient() {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(elasticsearchProperties.getUsername(),
                        elasticsearchProperties.getPassword())
        );

        return new RestHighLevelClient(RestClient.builder(getHttpHost(elasticsearchProperties.getUris()))
                .setHttpClientConfigCallback(callback -> {
                    callback.setDefaultCredentialsProvider(credentialsProvider);
                    return callback;
                })
        );
    }

    /**
     * 数据存储Es Client
     *
     * @param systemConfig 系统配置
     * @return RestHighLevelClient
     */
    @Bean
    public RestHighLevelClient storageEsClient(SystemConfig systemConfig) {
        final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials("niop_dc", "x3Cxl8dnlH0dut8Y")
        );


        final String host;
        if (systemConfig.isDevelop()) {
            host = "es-cn-cfn3vu6rj000pcbzy.public.elasticsearch.aliyuncs.com";
        } else {
            host = "es-cn-cfn3vu6rj000pcbzy.elasticsearch.aliyuncs.com";
        }

        return new RestHighLevelClient(RestClient.builder(new HttpHost(host, 9200, "http"))
                .setHttpClientConfigCallback(callback -> {
                    callback.setDefaultCredentialsProvider(credentialsProvider);
                    return callback;
                })
        );
    }
}
