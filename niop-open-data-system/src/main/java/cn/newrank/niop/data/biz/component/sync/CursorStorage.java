package cn.newrank.niop.data.biz.component.sync;

import java.util.List;

/**
 * 游标数据存储介质
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 17:48
 */
public interface CursorStorage<T, C extends Cursor<T>> {

    /**
     * 更新cursor
     *
     * @param cursor cursor
     */
    void update(C cursor);

    /**
     * 保存cursor
     *
     * @param cursor cursor
     */
    void save(C cursor);

    /**
     * 获取已启动的cursor
     *
     * @return cursor list
     */
    List<C> getStarted();

    /**
     * 获取未启动的cursor
     *
     * @param count count
     * @return cursor list
     */
    List<C> getUnstarted(int count);

    List<C> getAll();

    /**
     * 获取cursor
     *
     * @param key key
     * @return cursor
     */
    C get(String key);

    /**
     * 删除cursor
     *
     * @param key key
     */
    void remove(String key);
}
