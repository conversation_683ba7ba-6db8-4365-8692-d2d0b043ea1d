package cn.newrank.niop.data.biz.biz.ds.service.ks;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.RankHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 12:56
 */
@Service
public class KsRankHistorySync extends RankHistorySync {

    static final String RANK_QUERY = """
            GET search_kuaishou_user_rank_week/_search
            {
              "size": 10000,
              "_source": [
                "userId",
                "photoCount",
                "commentCount",
                "likeCount",
                "shareCount",
                "rankDate",
                "newrankIndex"
              ],
              "query": {
                "bool": {
                  "must": [
                    {
                      "terms": {
                        "userId": <foreach collection="userIds" item="userId" open="[" separator="," close="]">
                                    #{userId}
                                </foreach>
                      }
                    },
                    {
                      "range": {
                        "newrankIndex": {
                          "gte": 0
                        }
                      }
                    }
                  ]
                }
              },
              "sort": [
                {
                  "userId": {
                    "order": "desc"
                  }
                }
              ]
            }
            """;
    public static final String RANK_DATE = "rankDate";
    protected final Datasource datasource;

    protected KsRankHistorySync(RedissonClient redissonClient,
                                CommonEsService commonEsService,
                                DsConfigManager dsConfigManager) {
        super("ks", redissonClient, commonEsService);
        this.datasource = EsFactory.DEFAULT.create(dsConfigManager.chooseKsEsConfig());
    }

    @Override
    protected Map<String, JSONObject> rankIndexes(List<String> ids) {
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(RANK_QUERY)
                .addParam("userIds", ids);
        final JSONObject resp = datasource.query(queryBuilder).data();
        final List<JSONObject> items = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream()
                .map(json -> json.getJSONObject("_source"))
                .toList();
        final Map<String, JSONObject> map = new HashMap<>();
        for (JSONObject item : items) {
            map.compute(item.getString("userId"), (uid, rank) -> {
                if (rank == null) {
                    return item;
                }

                if (DateTimeUtil.toDateTime(rank.getString(RANK_DATE))
                        .isBefore(DateTimeUtil.toDateTime(item.getString(RANK_DATE)))) {
                    return item;
                }

                return rank;
            });
        }

        return map;
    }


    @Override
    protected EsEntity castOf(JSONObject rank, String docId) {
        final RankHistoryUpdate update = new RankHistoryUpdate();

        update.setIndexId(docId);
        update.setAccountId(rank.getLong("userId"));
        update.setNrIndexWeek(rank.getDouble("newrankIndex"));
        update.setLastWeekAwemeCount(rank.getLong("photoCount"));
        update.setLastWeekCommentCount(rank.getLong("commentCount"));
        update.setLastWeekLikeCount(rank.getLong("likeCount"));
        update.setLastWeekShareCount(rank.getLong("shareCount"));
        update.setNrIndexWeekDate(rank.getString(RANK_DATE));

        return update;
    }

    @Data
    public static class RankHistoryUpdate implements EsEntity {

        Long accountId;
        Double nrIndexWeek;
        Long lastWeekAwemeCount;
        Long lastWeekCommentCount;
        Long lastWeekLikeCount;
        Long lastWeekShareCount;
        String nrIndexWeekDate;

        String indexId;

        @Override
        public String docId() {
            return indexId;
        }
    }


}
