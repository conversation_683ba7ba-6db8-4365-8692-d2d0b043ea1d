package cn.newrank.niop.data.biz.export.pojo.po;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import lombok.Data;

@Data
public class DataExport {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 导数id
     */
    private String exportId;

    /**
     * 导数类型
     */
    private DataExportType exportType;

    /**
     * 导数源id
     */
    private String targetId;

    /**
     * 导数源名称 (不保存, 从接口查询)
     */
    private String targetName;

    /**
     * 导数名称
     */
    private String exportName;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;

}
