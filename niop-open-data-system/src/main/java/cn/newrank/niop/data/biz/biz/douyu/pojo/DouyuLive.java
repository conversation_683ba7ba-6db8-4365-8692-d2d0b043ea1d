package cn.newrank.niop.data.biz.biz.douyu.pojo;

import cn.newrank.niop.data.biz.component.biz.Calculation;
import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DouyuLive extends SampleVersionEntity implements Serializable {

    /**
     * 公会
     */
    private String clubOrgName;

    /**
     * 主播 id
     */
    private String uid;

    /**
     * 主播昵称
     */
    private String nickname;

    /**
     * 人气峰值/峰值热度
     */
    private Long hot;

    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 直播状态
     */
    private String liveStatus;

    /**
     * 开始时间
     */
    private Timestamp startTime;

    /**
     * 下播时间
     */
    private Timestamp endTime;

    /**
     * 平均热度
     */
    @Calculation("avg(hot)")
    private BigDecimal avgHot;

    /**
     * 最大热度
     */
    @Calculation("max(hot)")
    private Long maxHot;
    /**
     * 时长
     */
    @Calculation("(endTime - startTime) / 1000 (s)")
    private Long duration;


    /**
     * 是否计算过
     */
    @Calculation("self")
    private Boolean calculated;

}