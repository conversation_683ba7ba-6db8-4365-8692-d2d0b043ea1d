package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyShopCouponsMapper;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyShopCouponsSourceMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCoupons;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCouponsSource;
import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopCouponsDTO;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024/10/17
 * @description
 */
@Service
@RequiredArgsConstructor
public class DyShopCouponsService {


    private final DyShopCouponsMapper shopCouponsMapper;

    private final DyShopCouponsSourceMapper couponsSourceMapper;


    public void shopSkuData(JSONObject json, String deviceName) {
        String productId = json.getString("product_id");
        String partitionOffset = json.getString("kafka_partition") + "_" + json.getString("kafka_offset");
        JSONObject jsonData = json.getJSONObject("json_details").getJSONObject("data");
        List<DyShopCouponsDTO> resultList = new ArrayList<>();
        //JSONArray canUserArea = jsonData.getJSONObject("can_use_area").getJSONArray("card_list");
        //can_use_area中获取优惠券
        JSONArray canUserArea = jsonData.getJSONObject("can_use_area").getJSONArray("card_list");
        if (CollUtil.isNotEmpty(canUserArea)) {
            List<DyShopCouponsDTO> canUseAreaList = userAreaHandle("can_use_area", canUserArea, false);
            if (CollUtil.isNotEmpty(canUseAreaList)) {
                resultList.addAll(canUseAreaList);
            }
        }
        //calc_panel_area中获取优惠券
        JSONObject calcJson = jsonData.getJSONObject("calc_panel_area");
        if (calcJson != null) {
            JSONArray calcCanUserArea = calcJson.getJSONArray("card_list");
            if (CollUtil.isNotEmpty(calcCanUserArea)) {
                List<DyShopCouponsDTO> canUseAreaList = userAreaHandle("can_use_area", calcCanUserArea, true);
                if (CollUtil.isNotEmpty(canUseAreaList)) {
                    resultList.addAll(canUseAreaList);
                }
            }
        }
        //un_use_area中获取优惠券
        JSONArray unUserArea = jsonData.getJSONObject("un_use_area").getJSONArray("card_list");
        if (CollUtil.isNotEmpty(unUserArea)) {
            List<DyShopCouponsDTO> unUseAreaList = userAreaHandle("un_use_area", unUserArea, true);
            if (CollUtil.isNotEmpty(unUseAreaList)) {
                resultList.addAll(unUseAreaList);
            }
        }
        List<DyShopCoupons> saveLive = resultList.stream().map(item -> {
            item.setPartitionOffset(partitionOffset)
                    .setDeviceName(deviceName)
                    .setProductId(productId);
            return DyShopCoupons.createItem(item);
        }).toList();
        if (CollUtil.isNotEmpty(saveLive)) {
            shopCouponsMapper.batchSave(saveLive);
        }
        //保存数据源
        couponsSourceMapper.insertOne(new DyShopCouponsSource()
                .setProductId(productId)
                .setPartitionOffset(partitionOffset)
                .setSourceData(json.toJSONString()));

    }

    private List<DyShopCouponsDTO> userAreaHandle(String useAreaDesc, JSONArray userArea, boolean isExtra) {
        List<DyShopCouponsDTO> resultList = Lists.newArrayList();
        userArea.forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            JSONArray couponList = itemObj.getJSONArray("coupon_list");
            if (CollUtil.isEmpty(couponList)) {
                //如果是can_use_area中就不获取额外使用优惠券
                if (!isExtra) {
                    return;
                }
                List<DyShopCouponsDTO> dtoList = getExtraUseCoupons(itemObj,useAreaDesc);
                if (CollUtil.isEmpty(dtoList)) {
                    return;
                }
                resultList.addAll(dtoList);
                return;
            }
            couponList.forEach(couponItem -> {
                JSONObject couponJson = (JSONObject) couponItem;
                String couponsDesc = getCouponsDesc(couponJson);
                DyShopCouponsDTO dto = new DyShopCouponsDTO()
                        .setUseAreaDesc(useAreaDesc)
                        .setCouponTitle(couponJson.getString("coupon_title"))
                        .setWhatCoupon(couponJson.getString("what_coupon"))
                        .setCouponName(couponJson.getString("coupon_name"))
                        .setActivityId(couponJson.getJSONObject("track_data").getString("activity_id"))
                        .setActivityType(couponJson.getJSONObject("track_data").getInteger("activity_type"))
                        .setCredit(couponJson.getString("credit"))
                        .setThreshold(couponJson.getLong("threshold"))
                        .setDiscount(couponJson.getLong("discount"))
                        .setType(couponJson.getInteger("type"))
                        .setExpireTime(couponJson.getString("expire_time"))
                        .setCouponsDesc(couponsDesc);
                resultList.add(dto);
            });
        });
        return resultList;
    }


    private static String getCouponsDesc(JSONObject couponJson) {
        Long discount = couponJson.getLong("discount");
        String couponsDesc;
        if (discount == 0) {
            //满减
            long threshold = couponJson.getLong("threshold") / 100;
            Long credit = couponJson.getLong("credit") / 100;
            if (threshold == 0) {
                couponsDesc = String.format("立减%s", credit);
            } else {
                couponsDesc = String.format("满%s减%s", threshold, credit);
            }
        } else {
            couponsDesc = String.format("%s折", couponJson.getDouble("discount") / 10);
        }
        return couponsDesc;
    }

    private List<DyShopCouponsDTO> getExtraUseCoupons(JSONObject itemObj,String useAreaDesc) {
        Integer cardType = itemObj.getInteger("card_type");
        JSONObject activityCard = itemObj.getJSONObject("common_activity_card");
        if (activityCard == null) {
            return null;
        }
        String tagStr = (String) activityCard.getJSONArray("tag_headers").get(0);
        JSONArray tagsArray = activityCard.getJSONArray("tags");
        String activityId = activityCard.getJSONObject("track_data").getString("activity_id");
        Integer activityType = activityCard.getJSONObject("track_data").getInteger("activity_type");
        List<DyShopCouponsDTO> resultList = Lists.newArrayList();
        int num = 0;
        for (Object tag : tagsArray) {
            String tagS = (String) tag;
            DyShopCouponsDTO dto = new DyShopCouponsDTO()
                    .setUseAreaDesc(useAreaDesc)
                    .setActivityId(activityId)
                    .setActivityType(activityType)
                    .setCredit(null)
                    .setThreshold(null)
                    .setDiscount(null)
                    .setType(cardType)
                    .setExpireTime(null);
            if (tagStr.startsWith("官方立减")) {
                dto.setCouponTitle(tagStr)
                        .setWhatCoupon(tagStr)
                        .setCouponName(tagS)
                        .setCouponsDesc(tagStr);
            } else {
                dto.setCouponTitle(tagStr)
                        .setWhatCoupon(tagStr)
                        .setCouponName(tagS)
                        .setCouponsDesc(tagS);
            }
            resultList.add(dto);
            //单个活动包含多个优惠规则，处理活动id规则
            num++;
            if (activityId.contains("_")) {
                int index = activityId.indexOf("_");
                activityId = activityId.substring(0, index) + "_" + num;
            } else {
                activityId = activityId + "_" + num;
            }
        }
        return resultList;
    }

    private DyShopCouponsDTO getExtraCanUseCoupons(JSONObject calcJson, JSONObject itemObj) {
        JSONArray operatorList = calcJson.getJSONObject("calc_panel").getJSONObject("formula_area").getJSONArray("calc_operator_list");
        JSONObject activityCard = itemObj.getJSONObject("common_activity_card");
        String tagStr = (String) activityCard.getJSONArray("tag_headers").get(0);
        for (Object operator : operatorList) {
            JSONObject opeJson = (JSONObject) operator;
            if (opeJson.getString("tags").contains(tagStr)) {
                return new DyShopCouponsDTO()
                        .setCouponTitle(tagStr)
                        .setWhatCoupon(tagStr)
                        .setCouponName(activityCard.getString("tags"))
                        .setActivityId(activityCard.getJSONObject("track_data").getString("activity_id"))
                        .setActivityType(activityCard.getJSONObject("track_data").getInteger("activity_type"))
                        .setCredit(opeJson.getString("number"))
                        .setThreshold(null)
                        .setDiscount(null)
                        .setType(opeJson.getInteger("operator_type"))
                        .setExpireTime(null);
            }
        }
        return null;
    }


}
