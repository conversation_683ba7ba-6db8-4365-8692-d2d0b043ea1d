package cn.newrank.niop.data.biz.biz.ks.schedule;

import cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusSltFilterService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:12:11
 */
@RestController
@RequestMapping("/ksSlt")
public class KsSltSchedule {

    private final LmKsOpusSltFilterService lmKsOpusSltFilterService;

    public KsSltSchedule(LmKsOpusSltFilterService lmKsOpusSltFilterService) {
        this.lmKsOpusSltFilterService = lmKsOpusSltFilterService;
    }

    /**
     * 同步ES数据到lindorm
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/syncEsOpusData")
    @XxlJob("syncKsEsOpusDataSlt")
    public ReturnT<String> syncEsOpusData(String param) {
        lmKsOpusSltFilterService.runSchedule(param);
        return ReturnT.SUCCESS;
    }

}
