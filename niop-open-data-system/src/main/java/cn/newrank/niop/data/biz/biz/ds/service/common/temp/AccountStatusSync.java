package cn.newrank.niop.data.biz.biz.ds.service.common.temp;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 10:49
 */
@Service
public class AccountStatusSync extends DefaultHistorySynchronizer<String> {
    static final String START_QUERY = """
            GET search_nr_account/_search?scroll=5m
            {
              "_source": [
                "account_status"
              ],
              "size": 1000,
              "query": {
                "match_all": {}
              }
            }
            """;
    static final String SCROLL_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "5m",
                "scroll_id" : #{scrollId}
            }
            """;
    private final CommonEsService commonEsService;
    private final Datasource datasource;

    protected AccountStatusSync(RedissonClient redissonClient, CommonEsService commonEsService) {
        super("ds_account_status_sync", redissonClient, 1);
        this.commonEsService = commonEsService;
        this.datasource = EsFactory.DEFAULT.create(commonEsService.getRestClient());
        datasource.checkHealth();
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final String scrollId = cursor.getNext();
        final JSONObject resp;
        if (StringUtils.isBlank(scrollId)) {
            final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                    .template(START_QUERY);

            resp = datasource.query(queryBuilder).data();
            final Integer total = resp.getJSONObject("hits")
                    .getJSONObject("total")
                    .getInteger("value");
            cursor.setTotal(total);
            cursor.setNext(resp.getString("_scroll_id"));
        } else {
            final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                    .template(SCROLL_QUERY)
                    .addParam("scrollId", scrollId);

            resp = datasource.query(queryBuilder).data();
        }

        final List<JSONObject> hits = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class);

        final List<AccountStatusUpdate> updates = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream().map(item -> {
                    final AccountStatusUpdate update = new AccountStatusUpdate();
                    update.setId(item.getString("_id"));
                    final JSONObject source = item.getJSONObject("_source");
                    if (source == null || source.isEmpty()) {
                        update.setAccountStatus(1);
                        return update;
                    }

                    return null;
                })
                .filter(Objects::nonNull)
                .toList();

        commonEsService.update(updates);

        return hits.size();
    }

    @Data
    public static class AccountStatusUpdate implements EsEntity {

        String id;
        String indexId;
        Integer accountStatus;

        @Override
        public String docId() {
            return id;
        }
    }
}
