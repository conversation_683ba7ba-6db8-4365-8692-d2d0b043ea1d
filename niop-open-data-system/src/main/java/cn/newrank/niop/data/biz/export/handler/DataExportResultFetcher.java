package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportResultSource;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;

/**
 * <AUTHOR>
 */
public interface DataExportResultFetcher {

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    DataExportType getDataSourceType();

    /**
     * 获取导数任务结果查询来源
     *
     * @return 结果来源
     */
    DataExportResultSource getResultSource();

    /**
     * 查询任务结果
     *
     * @param resultTaskId 任务结果id
     * @param cursor 游标参数
     * @return 导数结果
     */
    ExportResult queryResult(String resultTaskId, String cursor);

    /**
     * 处理空数据
     *
     * @param resultTaskId 结果任务id
     */
    void handleNullData(String resultTaskId);

}
