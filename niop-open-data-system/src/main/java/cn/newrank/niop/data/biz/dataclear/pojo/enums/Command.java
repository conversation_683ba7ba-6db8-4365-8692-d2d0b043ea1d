package cn.newrank.niop.data.biz.dataclear.pojo.enums;


import cn.newrank.niop.data.common.enums.DsType;

/**
 * 指令
 *
 *<AUTHOR>
 *@since  2025/4/25 14:54
 *@version 1.0.0
 */
public interface Command {

     String JDBC_DELETE = "DELETE FROM %s WHERE %s LIMIT 500";
     String JDBC_COUNT = "SELECT COUNT(*) FROM %s WHERE %s LIMIT 500";
     String JDBC_CHECK = "SELECT COUNT(*) AS COUNT FROM %S WHERE %S  LIMIT 1";

    static Command delete(DsType dsType) {
        return switch (dsType) {
            case POSTGRES, MYSQL -> JDBC_DELETE::formatted;
            default -> throw new IllegalArgumentException("删除不支持的数据源类型：" + dsType);
        };
    }


    static Command count(DsType dsType) {
        return switch (dsType) {
            case POSTGRES, MYSQL -> JDBC_COUNT::formatted;
            default -> throw new IllegalArgumentException("统计不支持的数据源类型：" + dsType);
        };
    }


    static Command check(DsType dsType) {
        return switch (dsType) {
            case POSTGRES, MYSQL -> JDBC_CHECK::formatted;
            default -> throw new IllegalArgumentException("检查不支持的数据源类型：" + dsType);
        };
    }

    String args(Object... args);



}
