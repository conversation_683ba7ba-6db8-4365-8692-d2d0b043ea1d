package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.biz.callback.event.CbConfigRefreshTopic;
import cn.newrank.niop.data.biz.component.callback.Callback;
import cn.newrank.niop.data.biz.component.callback.CallbackFactory;
import cn.newrank.niop.data.biz.dao.CbConfigDao;
import cn.newrank.niop.data.biz.manager.CallbackFactoryManager;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.event.CbDeleteEvent;
import cn.newrank.niop.data.biz.pojo.event.CbUpdateEvent;
import cn.newrank.niop.data.biz.pojo.param.CallbackConfigCreate;
import cn.newrank.niop.data.biz.pojo.param.CallbackConfigUpdate;
import cn.newrank.niop.data.biz.pojo.param.CbConfigFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.CbConfigPageQuery;
import cn.newrank.niop.data.biz.service.CbConfigService;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.niop.util.U;
import cn.newrank.niop.web.model.PageView;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 *
 */
@Service
public class CbConfigServiceImpl implements CbConfigService, ApplicationEventPublisherAware {


    private final CbConfigDao cbConfigDao;
    private final CallbackFactoryManager callbackFactoryManager;
    private final CbConfigRefreshTopic cbConfigRefreshTopic;
    private ApplicationEventPublisher eventPublisher;


    public CbConfigServiceImpl(CbConfigDao cbConfigDao,
                               CallbackFactoryManager callbackFactoryManager,
                               RedissonClient redissonClient) {
        this.cbConfigDao = cbConfigDao;
        this.callbackFactoryManager = callbackFactoryManager;
        this.cbConfigRefreshTopic = new CbConfigRefreshTopic(redissonClient);
    }

    @Override
    public CbConfig get(String cbId) {
        return cbConfigDao.get(cbId);
    }

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    @Override
    public String create(CallbackConfigCreate callbackConfigCreate) {
        final CbConfig cbConfig = callbackConfigCreate.toDto();

        final CallbackFactory factory = callbackFactoryManager.getFactory(cbConfig.getType());

        try (final Callback callback = factory.newCallback(cbConfig.getConfig())) {
            if (callback.isActive()) {
                cbConfig.setCbId(Ids.create(8));

                cbConfig.setUuid(callback.getUUID());
                cbConfigDao.save(cbConfig);

                return cbConfig.getCbId();
            }
        }

        throw createParamError("回调配置链接不可用");
    }

    @Override
    public boolean update(CallbackConfigUpdate callbackUpdate) {
        final CbConfig cbConfig = callbackUpdate.toDto();
        final CallbackFactory factory = callbackFactoryManager.getFactory(cbConfig.getType());

        try (final Callback callback = factory.newCallback(cbConfig.getConfig())) {
            if (callback.isActive()) {
                cbConfig.setUuid(callback.getUUID());
                if (cbConfigDao.update(cbConfig)) {
                    cbConfigRefreshTopic.emitEvent(CbUpdateEvent.of(cbConfig.getCbId()));
                    return true;
                }
            }

            throw createParamError("回调配置链接不可用");
        }
    }

    @Override
    public boolean delete(String cbId) {
        eventPublisher.publishEvent(CbDeleteEvent.of(cbId));
        if (cbConfigDao.delete(cbId)) {
            cbConfigRefreshTopic.emitEvent(CbUpdateEvent.of(cbId));
            return true;
        }
        return false;
    }


    @Override
    public void checkCbId(String cbId) {
        if (cbConfigDao.get(cbId) == null) {
            throw createParamError("回调ID(cbId: {})不存在", cbId);
        }
    }

    @Override
    public List<CbConfig> fuzzyQuery(CbConfigFuzzyQuery fuzzyQuery) {
        return cbConfigDao.fuzzyQuery(fuzzyQuery);
    }

    @Override
    public PageView<CbConfig> page(CbConfigPageQuery pageQuery) {
        return cbConfigDao.page(pageQuery);
    }

    @Override
    public Map<String, CbConfig> group(List<String> cbIds) {
        return U.toMap(cbConfigDao.list(cbIds), CbConfig::getCbId, Function.identity());
    }

    @Override
    public List<String> listCbIds() {
        return cbConfigDao.listCbIds();
    }
}




