package cn.newrank.niop.data.biz.export.pojo.dto;

import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataExportConsumeTaskResult {

    /**
     * 导数任务id
     */
    private final String exportTaskId;

    /**
     * 是否已结束
     */
    private boolean isEnd = false;

    /**
     * 消费的任务数
     */
    private final AtomicInteger consumeTaskNum = new AtomicInteger(0);

    /**
     * 消费的结果数
     */
    private final AtomicInteger consumeResultNum = new AtomicInteger(0);

    private DataExportConsumeTaskResult(String exportTaskId) {
        this.exportTaskId = exportTaskId;
    }

    public static DataExportConsumeTaskResult of(String exportTaskId) {
        return new DataExportConsumeTaskResult(exportTaskId);
    }

    public void accumulateTaskNum(Integer taskNum) {
        Optional.ofNullable(taskNum).ifPresent(this.consumeTaskNum::addAndGet);
    }

    public void accumulateResultNum(Integer resultNum) {
        Optional.ofNullable(resultNum).ifPresent(this.consumeResultNum::addAndGet);
    }

    public Integer getConsumeTaskNum() {
        return this.consumeTaskNum.get();
    }

    public Integer getConsumeResultNum() {
        return this.consumeResultNum.get();
    }

    public boolean reachMaxResultLimit() {
        return this.consumeResultNum.get() >= DataExportConstant.FILE_MAX_RESULT_NUM;
    }

    public void end() {
        this.isEnd = true;
    }

    public boolean isEnd() {
        return this.isEnd;
    }

}
