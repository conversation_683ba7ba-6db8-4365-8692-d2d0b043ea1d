package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.biz.callback.event.CbTriggerEvent;
import cn.newrank.niop.data.biz.service.CbConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/30 9:39
 */
@Component
@Log4j2
public class CallbackScheduler {

    private final ApplicationEventPublisher eventPublisher;
    private final CbConfigService cbConfigService;
    private final CbHttpTaskService cbHttpTaskService;
    private final CbDataService cbDataService;

    public CallbackScheduler(ApplicationEventPublisher eventPublisher,
                             CbConfigService cbConfigService,
                             CbHttpTaskService cbHttpTaskService,
                             CbDataService cbDataService) {
        this.eventPublisher = eventPublisher;
        this.cbConfigService = cbConfigService;
        this.cbHttpTaskService = cbHttpTaskService;
        this.cbDataService = cbDataService;
    }

    @XxlJob("CallbackScheduleDaemon")
    public ReturnT<String> callbackScheduleDaemon(String ignore) {
        final List<String> cbIds = cbConfigService.listCbIds();
        for (String cbId : cbIds) {
            eventPublisher.publishEvent(CbTriggerEvent.of(cbId));
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("CallbackTableManager")
    public ReturnT<String> callbackTableManager(String ignore) {
        final LocalDate now = LocalDate.now();
        try {
            // 创建明日分区表
            cbDataService.createTable(now.plusDays(1));
            cbHttpTaskService.createTable(now.plusDays(1));

            // 清除表
            cleanTable();
        } catch (Exception e) {
            log.error("HTTP回调数据表定时任务执行失败, error", e);
        }

        return ReturnT.SUCCESS;
    }

    private void cleanTable() {
        final LocalDate cleanEndPartition = cbHttpTaskService.queryCleanEndPartition();

        final List<String> tables = cbHttpTaskService.queryDistributedTables();
        final List<LocalDate> partitions = getPartitions(tables);

        final List<LocalDate> cleaned = new ArrayList<>();
        for (LocalDate partition : partitions) {
            if (partition.isBefore(cleanEndPartition)) {
                cbDataService.dropTable(partition);
                cbHttpTaskService.dropTable(partition);

                cleaned.add(partition);
            }
        }

        log.info("clean table finished. cleaned: {}", cleaned);
    }

    private List<LocalDate> getPartitions(List<String> tables) {
        return tables.stream()
                .map(table -> table.substring(table.lastIndexOf("_") + 1))
                .map(partition -> LocalDate.parse(partition, DateTimeFormatter.ofPattern("yyyyMMdd")))
                .sorted()
                .toList();
    }
}
