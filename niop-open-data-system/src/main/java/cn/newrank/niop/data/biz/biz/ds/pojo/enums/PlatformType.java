package cn.newrank.niop.data.biz.biz.ds.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 平台类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PlatformType implements BizEnum {
    WEIBO("weibo", "微博"),
    SPH("sph", "视频号"),
    XHS("xhs", "小红书"),
    BILI("bili", "哔哩哔哩"),
    DY("dy", "抖音"),
    DYR("dyr", "抖音榜单"),
    KS("ks", "快手"),
    GZH("gzh", "公众号"),
    GZHR("gzhr", "公众号榜单"),
    SPHR("sphr", "视频号榜单"),
    WBR("wbr", "微博榜单"),
    KSR("ksr", "ks榜单"),
    BZR("bzr", "bz榜单"),
    ;

    final String code;
    final String description;

    PlatformType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PlatformType getByCode(boolean expression, String code) {
        for (PlatformType platformType : values()) {
            if (platformType.getDbCode().equals(code)) {
                return platformType;
            }
        }
        if (expression) {
            throw createParamError("未知PlatformType类型-{}", code);
        }
        return null;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
