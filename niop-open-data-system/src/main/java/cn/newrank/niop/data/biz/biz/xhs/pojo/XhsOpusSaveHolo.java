package cn.newrank.niop.data.biz.biz.xhs.pojo;



import cn.newrank.niop.data.util.DateTimeUtil;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 多回调源-小红书作品数据
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
public class XhsOpusSaveHolo {
    private String opusId;
    private String cover;
    private String title;
    private String desc;
    private String hashTags;
    private Timestamp createTime;
    private String type;
    private Long collectNum;
    private Long likes;
    private Long shareNum;
    private Long commentNum;
    private String uid;
    private String images;
    private String nickname;
    private String token;
    private Integer width;
    private Integer height;
    private String videoUrl;
    private Integer duration;

    private Long userFans;
    private Timestamp anaTime;
    private Integer isVisible;
    private Integer isDelete;
    private Long interactiveCount;
    private String noteCounterTypeV2;
    private String noteCounterTypeV1;
    private String ipLocationWeb;
    private String officialKeyword;
    private String videoInfo;
    private String shareInfoLink;
    private Integer isCooperate;
    private String cooperateName;
    private String cooperateId;
    private String poiName;
    private String officialWarnMsg;
    private String discernBusinessBrandId;
    private String discernBusinessBrandName;
    private String seedBrandId;
    private String seedBrandName;


    private String imagesList;
    private Timestamp time;
    private Timestamp lastUpdateTime;
    private Timestamp gmtTime;
    private Integer acqAwemeDetail;
    private Timestamp firstDetailAnaTime;
    private String poiId;

    private String ds;
    private String source;
    private String messageId;

    /**
     * 获取小红书作品数据
     * @param item 小红书作品数据
     * @return 小红书作品数据
     */
    public static @NotNull XhsOpusSaveHolo toXhsHoloOpus(XhsOpusFromMulti item) {
        XhsOpusSaveHolo holo = new XhsOpusSaveHolo();
        holo.setOpusId(item.getOpusId());
        holo.setCover(item.getCover());
        holo.setTitle(item.getTitle());
        holo.setDesc(item.getDesc());
        holo.setHashTags(getHashTags(item));
        holo.setCreateTime(getTimestamp(item.getCreateTime()));
        holo.setType(item.getType());
        holo.setCollectNum(item.getCollectNum());
        holo.setLikes(item.getLikes());
        holo.setShareNum(item.getShareNum());
        holo.setCommentNum(item.getCommentNum());
        holo.setUid(item.getUid());
        holo.setImages(item.getImages());
        holo.setNickname(item.getNickname());
        holo.setToken(item.getToken());
        holo.setWidth(item.getWidth());
        holo.setHeight(item.getHeight());
        holo.setVideoUrl(item.getVideoUrl());
        holo.setDuration(item.getDuration());

        holo.setUserFans(item.getUserFans());
        holo.setAnaTime(getTimestamp(item.getAnaTime()));
        holo.setIsVisible(item.getIsVisible());
        holo.setIsDelete(item.getIsDelete());
        holo.setInteractiveCount(item.getInteractiveCount());
        holo.setNoteCounterTypeV2(item.getNoteCounterTypeV2());
        holo.setNoteCounterTypeV1(item.getNoteCounterTypeV1());
        holo.setIpLocationWeb(item.getIpLocationWeb());
        holo.setOfficialKeyword(item.getOfficialKeyword());
        holo.setVideoInfo(Strings.isBlank(item.getVideoInfo()) ? null : item.getVideoInfo());
        holo.setShareInfoLink(item.getShareInfoLink());
        holo.setIsCooperate(item.getIsCooperate());
        holo.setCooperateName(item.getCooperateName());
        holo.setCooperateId(item.getCooperateId());
        holo.setPoiName(item.getPoiName());
        holo.setOfficialWarnMsg(item.getOfficialWarnMsg());
        holo.setDiscernBusinessBrandId(item.getDiscernBusinessBrandId());
        holo.setDiscernBusinessBrandName(item.getDiscernBusinessBrandName());
        holo.setSeedBrandId(item.getSeedBrandId());
        holo.setSeedBrandName(item.getSeedBrandName());


        holo.setImagesList(Strings.isBlank(item.getImagesList()) ? null : item.getImagesList());
        holo.setTime(getTimestamp(item.getTime()));
        holo.setLastUpdateTime(getTimestamp(item.getLastUpdateTime()));
        holo.setGmtTime(getTimestamp(item.getGmtTime()));
        holo.setAcqAwemeDetail(item.getAcqAwemeDetail());
        holo.setFirstDetailAnaTime(getTimestamp(item.getFirstDetailAnaTime()));
        holo.setPoiId(item.getPoiId());
        // 分区信息
        holo.setDs(item.getDs());
        holo.setSource(item.getSource());
        holo.setMessageId(item.getMessageId());
        return holo;
    }

    /**
     * 转换为保存holo列表
     * @param list ldm列表
     * @return 保存holo列表
     */
    public static @NotNull List<XhsOpusSaveHolo> convertSaveHoloList(List<XhsOpusFromMulti> list) {
        return list.stream()
                .map(XhsOpusSaveHolo::toXhsHoloOpus)
                .toList();
    }

    private static @Nullable String getHashTags(XhsOpusFromMulti item) {
        return Strings.isNotBlank(item.getHashTags()) ? item.getHashTags().replace("\\u0000", "") : null;
    }

    private static Timestamp getTimestamp(String dateTime) {
        if (Strings.isBlank(dateTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        LocalDateTime localDateTime;
        try {
            localDateTime = LocalDateTime.parse(dateTime, formatter);
        } catch (Exception e) {
            localDateTime = DateTimeUtil.toDateTime(dateTime);
        }
        return Timestamp.valueOf(localDateTime.atZone(ZoneId.systemDefault()).toLocalDateTime());
    }

}
