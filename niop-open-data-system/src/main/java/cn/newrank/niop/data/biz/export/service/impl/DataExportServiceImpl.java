package cn.newrank.niop.data.biz.export.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.export.factory.DataExportTemplateFileGeneratorFactory;
import cn.newrank.niop.data.biz.export.handler.DataExportTemplateFileGenerator;
import cn.newrank.niop.data.biz.export.mapper.DataExportMapper;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportCreate;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import cn.newrank.niop.data.biz.export.service.DataExportService;
import cn.newrank.niop.data.common.OpenConsoleRequest;
import cn.newrank.niop.web.model.PageView;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class DataExportServiceImpl implements DataExportService {

    private final DataExportMapper dataExportMapper;

    private final DataExportTemplateFileGeneratorFactory templateFileGeneratorFactory;

    public DataExportServiceImpl(DataExportMapper dataExportMapper,
                                 DataExportTemplateFileGeneratorFactory templateFileGeneratorFactory) {
        this.dataExportMapper = dataExportMapper;
        this.templateFileGeneratorFactory = templateFileGeneratorFactory;
    }

    @Override
    public DataExport create(DataExportCreate exportCreate) {
        final DataExport dataExport = exportCreate.init();

        final String exportId = dataExport.getExportId();
        dataExportMapper.insert(dataExport);
        return dataExportMapper.getByExportId(exportId);
    }

    @Override
    public PageView<DataExport> page(DataExportPageQuery pageQuery) {
        Page<DataExport> exportPage = dataExportMapper.page(pageQuery, pageQuery.toMybatisPlusPage());

        final Set<String> targetIds = exportPage.getRecords().stream().map(DataExport::getTargetId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(targetIds)) {
            final List<OpenConsoleRequest.AbilityInfo> abilityInfos = OpenConsoleRequest.listAbilities(targetIds);
            batchSetTargetNames(exportPage.getRecords(), abilityInfos);
        }

        return PageView.of(exportPage);
    }

    @Override
    public ResponseEntity<FileSystemResource> getTemplateFile(String exportId) {
        final DataExport dataExport = Optional.ofNullable(dataExportMapper.getByExportId(exportId))
            .orElseThrow(() -> createParamError("导数不存在"));

        final DataExportTemplateFileGenerator templateFileGenerator = templateFileGeneratorFactory.getTemplateFileGenerator(dataExport.getExportType());

        File file = null;

        try {
            file = templateFileGenerator.createFile(exportId);
            // 如果已有模板文件, 直接返回
            if (file.exists() && file.length() > 0) {
                return wrapTemplateFile(file);
            }

            boolean succeed = templateFileGenerator.writeTemplateFile(file, dataExport);
            if (succeed) {
                return wrapTemplateFile(file);
            }
        } catch (Exception e) {
            log.warn("模板文件创建异常, exportId: {}, e: ", exportId, e);
        } finally {
            // 系统退出时删除文件
            Optional.ofNullable(file).ifPresent(File::deleteOnExit);
        }

        throw createParamError("模板文件下载异常, 请重试");
    }

    private void batchSetTargetNames(List<DataExport> exports, List<OpenConsoleRequest.AbilityInfo> abilityInfos) {
        if (CollUtil.isEmpty(exports) || CollUtil.isEmpty(abilityInfos)) {
            return;
        }

        final Map<String, String> targetMap = abilityInfos.stream()
            .collect(Collectors.toMap(OpenConsoleRequest.AbilityInfo::getAbilityId, OpenConsoleRequest.AbilityInfo::getAbilityName));
        exports.forEach(e -> e.setTargetName(targetMap.getOrDefault(e.getTargetId(), "-")));
    }

    private ResponseEntity<FileSystemResource> wrapTemplateFile(File file) {
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName())
            // 前端访问需要
            .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION)
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(new FileSystemResource(file));
    }

}
