package cn.newrank.niop.data.biz.biz.xhs.mapper;

import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusExpand;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/8/27 16:42
 */
@Mapper
@DS("xhs")
public interface XhsOpusExpandMapper {
    /**
     * 批量写入数据
     *
     * @param list 数据列表
     * @return 受影响行数
     */
    int batchInsert(@Param("list") List<XhsOpusExpand> list);
}
