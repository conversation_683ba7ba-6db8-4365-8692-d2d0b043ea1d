package cn.newrank.niop.data.biz.biz.xhs.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/11 20:58:51
 */

@AllArgsConstructor
public enum XhsTopicUniIndexDataTypeEnum implements BizEnum {
    /**
     * 数据类型
     */
    TOPIC_CAL_RESULT("topic_cal_result", "topic_cal_result","话题计算结果数据"),
    TOPIC_BASE("topic_base", "topic_base","话题基础信息数据"),
    TOPIC_BRAND_REC("topic_brand_rec", "topic_brand_rec","话题品牌识别"),
    TOPIC_CATEGORY_REC("topic_category_rec", "topic_category_rec","话题品类识别"),
    ;

    private final String code;
    private final String dbCode;
    private final String description;

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return dbCode;
    }


    public static XhsTopicUniIndexDataTypeEnum getByJsonCode(String code) {
        for (XhsTopicUniIndexDataTypeEnum value : values()) {
            if (value.getJsonValue().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static XhsTopicUniIndexDataTypeEnum getByDbCode(String code) {
        for (XhsTopicUniIndexDataTypeEnum value : values()) {
            if (value.getDbCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
