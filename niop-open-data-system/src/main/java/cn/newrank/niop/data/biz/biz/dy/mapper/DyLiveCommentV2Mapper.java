package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyLiveCommentV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS("dy")
public interface DyLiveCommentV2Mapper{

    List<DyLiveCommentV2> list(@Param("roomId") String roomId,
                               @Param("msgId") String msgId);

    void update(@Param("lives") List<DyLiveCommentV2> lives);

    String nextRoomId(@Param("start") String start, @Param("end") String end);
}




