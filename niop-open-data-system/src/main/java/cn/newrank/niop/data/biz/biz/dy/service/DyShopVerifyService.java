package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyShopVerifyMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopVerify;
import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopVerifyDTO;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @createTime 2024/10/23
 * @description
 */
@Service
@RequiredArgsConstructor
public class DyShopVerifyService {

    private final DyShopVerifyMapper shopVerifyMapper;


    public void shopSkuData(JSONObject item, String deviceName) {
        String partitionOffset = item.getString("kafka_partition") + "_" + item.getString("kafka_offset");

        JSONObject promotionV3Json = item.getJSONObject("json_details").getJSONObject("promotion_v3");
        if (promotionV3Json == null) {
            return;
        }
        JSONObject pageMetaJson = promotionV3Json.getJSONObject("page_meta");
        //过滤这种商品详情数据
        if (pageMetaJson == null) {
            return;
        }
        DyShopVerifyDTO dto = new DyShopVerifyDTO();
        dto.setPartitionOffset(partitionOffset)
                .setDeviceName(deviceName);
        //获取商品id和是否在售
        JSONObject commonMetaJson = pageMetaJson.getJSONObject("common_meta");

        //商品被删除判断
        if (commonMetaJson.containsKey("product_deleted") && commonMetaJson.getBoolean("product_deleted")) {
            String productId = commonMetaJson.getString("product_id");
            if (StrUtil.isBlank(productId)) {
                return;
            }
            shopDeleteHandle(productId, dto);
            return;
        }
        Boolean onSale = commonMetaJson.getBoolean("on_sale");
        if (onSale) {
        //判断--商品已抢光
            onSale = Optional.ofNullable(promotionV3Json.getJSONObject("bottom_button"))
                    .map(buttonJson -> buttonJson.getJSONObject("vo"))
                    .map(voJson -> voJson.getJSONObject("display"))
                    .map(displayJson -> displayJson.getJSONObject("lynx_tip"))
                    .map(tipJson -> tipJson.getJSONObject("data"))
                    .map(dataJson -> dataJson.getString("title"))
                    .map(title -> !"商品已抢光".equals(title))
                    .orElse(true);

        }

        dto.setProductId(commonMetaJson.getString("product_id"))
                .setOnSale(onSale);
        //获取商品标题和价格信息
        JSONObject buyJson = promotionV3Json.getJSONObject("bottom_button").getJSONObject("vo").getJSONObject("meta").getJSONObject("buy");
        dto.setTitle(buyJson.getString("good_title"))
                .setPriceInfo(buyJson.getString("product_min_price"));
        //获取出售数量
        String saleStr = Optional.ofNullable(commonMetaJson.getJSONObject("activity_info"))
                .map(activityJson -> activityJson.getJSONObject("sales"))
                .map(salesJson -> salesJson.getString("text"))
                .orElse("");
        //获取已售的数量
        if (StrUtil.isNotBlank(saleStr) && saleStr.startsWith("已售")) {
            String saleInfo = saleStr.substring(2);
            dto.setSaleInfo(saleInfo);
        }
        shopVerifyMapper.saveOne(DyShopVerify.createItem(dto));
    }

    private void shopDeleteHandle(String productId, DyShopVerifyDTO dto) {
        dto.setProductId(productId)
                .setOnSale(false)
                .setTitle("商品已失效");
        shopVerifyMapper.saveOne(DyShopVerify.createItem(dto));
    }
}