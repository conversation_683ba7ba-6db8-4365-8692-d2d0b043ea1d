package cn.newrank.niop.data.biz.biz.athm.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 汽车之家-账号
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/31 09:59:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AthmAccount extends SampleVersionEntity {
    /**
     * uid
     */
    String uid;
    /**
     * 昵称
     */
    String nickname;
    /**
     * 头像
     */
    String avatar;
    /**
     * 认证类型
     */
    String verifyType;
    /**
     * 认证V
     */
    String verifyTypedes;
    /**
     * 认证主体
     */
    String enterpriseName;
    /**
     * 简介
     */
    String introduction;
    /**
     * 粉丝数
     */
    Integer fansNum;
    /**
     * 关注数
     */
    Integer followNum;
    /**
     * 作品数
     */
    Integer articleNum;
    /**
     * 认证信息
     */
    String verify;

    @Override
    public String identifier() {
        return uid;
    }
}