package cn.newrank.niop.data.biz.biz.ds.service.bz;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.BzEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_BZ_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * <AUTHOR>
 * @date 2024/9/6 上午9:48
 */
@Log4j2
@Service
public class BzInsertDataSyncService implements SyncBizService<BzEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public BzInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseBzEsConfig();
    }

    @Override
    public String getIndexName() {
        return "bili_user_data";
    }

    @Override
    public String getSortField() {
        return "mid";
    }

    @Override
    public String getRangField() {
        return "gmt_modify";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_BZ_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }

        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<BzEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), BzEsMetaData.class);
        });
    }

    @Override
    public List<BzEsMetaData> convertData(List<BzEsMetaData> dataList) {
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (BzEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_BZ_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", PlatformType.BILI.getDbCode());


            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.BILI.getDbCode() + "_" + mainMap.get("account_id"));
            }

            if (data.containsKey("sex") && "保密".equals(data.get("sex"))) {
                mainMap.put("gender", "未知");
            }

            if (data.containsKey("tags")) {
                ArrayList<Object> objects = new ArrayList<>();
                if (!"[]".equals(data.get("tags"))) {
                    objects.add(data.get("tags"));
                    mainMap.put("account_tag", objects);
                } else {
                    mainMap.put("account_tag", null);
                }
            }

            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 移除空值字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }

        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), BzEsMetaData.class);
    }
}
