package cn.newrank.niop.data.biz.dataclear.service;

import cn.newrank.niop.data.biz.dataclear.mapper.DataClearTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/12 下午1:28
 */
@Component
@Slf4j
public class AppLifecycleListener implements ApplicationListener<ApplicationEvent> {
    private final DataClearTaskMapper dataClearTaskMapper;

    public AppLifecycleListener(DataClearTaskMapper dataClearTaskMapper) {
        this.dataClearTaskMapper = dataClearTaskMapper;
    }

    @Override
    public void onApplicationEvent(@NotNull ApplicationEvent event) {
        if (event instanceof ContextClosedEvent) {
            handleContextClose((ContextClosedEvent) event);
        }
    }

    private void handleContextClose(ContextClosedEvent event) {
        int i = dataClearTaskMapper.updatePending();
        //当服务器启动的时候，将所有执行中的任务变更为待执行
        log.info("容器关闭,有{}个任务返回待执行状态", i);
        // 执行关闭时的逻辑
    }
}