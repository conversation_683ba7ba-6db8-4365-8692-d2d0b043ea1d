package cn.newrank.niop.data.biz.callback.pojo;

import cn.newrank.niop.web.model.BizEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/27 15:21
 */
public enum CbStatus implements BizEnum {
    SUCCESS("success"),
    RUNNING("running"),
    ERROR("error"),
    ;

    final String code;

    CbStatus(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return code;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
