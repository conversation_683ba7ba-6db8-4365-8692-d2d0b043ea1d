package cn.newrank.niop.data.biz.callback.util;

import cn.newrank.niop.data.biz.callback.pojo.DataType;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@UtilityClass
public class CallbackRedirectUtil {

    public static void buildRedirectResult(CallbackRedirect redirect, CallbackRedirect snapshot, JSONObject param, JSONObject result, SubSourceType sourceType) {
        redirect.setConsumerRecord(snapshot.getConsumerRecord());
        redirect.setSourceId(snapshot.getSourceId());
        redirect.setSourceKey(snapshot.getSourceKey());
        redirect.setAppId(snapshot.getAppId());
        redirect.setSourceType(sourceType.getJsonValue());


        final JSONObject data;
        Object payloadObject = snapshot.getPayload();
        if (payloadObject instanceof String payload) {
            data = new JSONObject(JSON.parseObject(payload));
        } else {
            data = new JSONObject((JSONObject) snapshot.getPayload());
        }

        data.put("extra", result.get("extra"));

        if (SubSourceType.ABILITY_PARSED_RESULT == sourceType) {

            data.put("dataType", DataType.PARSED_DATA.getType());
            Object rParsed;
            try {
                rParsed = result.getJSONObject("result");
            } catch (Exception e) {
                rParsed = result.getJSONArray("result");
            }
            data.put("data", rParsed);
        } else if (SubSourceType.ABILITY_RAW_RESULT == sourceType) {

            data.put("dataType", DataType.SOURCE_DATA.getType());
            data.put("data", result.get("source"));
        } else if (SubSourceType.ABILITY_RAW_RESULT_OLD == sourceType) {

            data.put("dataType", DataType.SOURCE_DATA.getType());
            data.put("data", result.get("source"));

            // v1-请求参数
            data.put("params", param.get("params"));
            redirect.setPayload(data);

            log(redirect, param);
            return;
        } else if (SubSourceType.ABILITY_PARSED_RESULT_OLD == sourceType) {

            data.put("dataType", DataType.PARSED_DATA.getType());
            Object rParsed;
            try {
                rParsed = result.getJSONObject("result");
            } catch (Exception e) {
                rParsed = result.getJSONArray("result");
            }
            data.put("data", rParsed);

            // v1-请求参数
            data.put("params", param.get("params"));
            redirect.setPayload(data);

            log(redirect, param);
            return;
        }

        final JSONObject newPayload = new JSONObject();
        newPayload.put("data", data);
        newPayload.put("sendTime", DateTimeUtil.format(LocalDateTime.now()));
        redirect.setPayload(newPayload);
    }

    private void log(CallbackRedirect redirect, JSONObject param) {
        try {
            String sourceId = redirect.getSourceId();
            if ("VMM48PZO".equals(sourceId)) {
                log.info("callback data:{}, params:{}", JSON.toJSONString(redirect), param);
            }
        } catch (Exception e) {
            log.error("callback", e);
        }
    }
}
