package cn.newrank.niop.data.biz.service.impl;


import cn.newrank.niop.data.biz.pojo.dto.Dictionary;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.ColumnAliasesUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryRemarkUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryTagUpdate;
import cn.newrank.niop.data.biz.service.DatasourceService;
import cn.newrank.niop.data.biz.service.DictionaryService;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ds.Collection;
import cn.newrank.niop.data.common.ds.Column;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.enums.DsType;
import cn.newrank.niop.data.common.event.DatasourceConfigRefreshEvent;
import cn.newrank.niop.data.config.SystemConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 14:53
 */
@Service
@Log4j2
public class DictionaryServiceImpl implements DictionaryService {

    private final DatasourceService datasourceService;
    private final RestHighLevelClient esClient;
    private final ObjectMapper objectMapper;
    private final String dictionaryIndex;
    private final DsConfigService dsConfigService;
    private final ThreadPoolExecutor commonIoThreadPool;

    public DictionaryServiceImpl(DatasourceService datasourceService,
                                 RestHighLevelClient restHighLevelClient,
                                 SystemConfig systemConfig,
                                 DsConfigService dsConfigService,
                                 ThreadPoolExecutor commonIoThreadPool) {
        this.datasourceService = datasourceService;
        this.esClient = restHighLevelClient;
        this.dictionaryIndex = systemConfig.getEnvironment().isProduct()
                ? "niop_dc_dictionary_product"
                : "niop_dc_dictionary_test";
        this.dsConfigService = dsConfigService;
        this.commonIoThreadPool = commonIoThreadPool;
        this.objectMapper = new ObjectMapper()
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }


    @Override
    public void startSync() {
        for (DsConfig dsConfig : dsConfigService.listAll()) {
            startDsSync(dsConfig.getDcId());
        }
    }

    /**
     * 监听数据源配置刷新事件
     *
     * @param event 事件
     */
    @EventListener(DatasourceConfigRefreshEvent.class)
    public void onDsConfigRefresh(DatasourceConfigRefreshEvent event) {
        CompletableFuture.runAsync(() -> {
            try {
                startDsSync(event.getDcId());
            } catch (Exception e) {
                log.error("刷新数据源配置失败", e);
            }
        }, commonIoThreadPool);
    }

    @Override
    public boolean startDsSync(String dcId) {
        try {
            log.info("开始同步数据源({})字典", dcId);
            final DsConfig dsConfig = dsConfigService.getConfig(dcId);
            final Datasource datasource = datasourceService.getDatasource(dcId);

            final List<Collection> collections = datasource.getCollections();
            int i = 0;
            for (Collection collection : collections) {
                log.info("开始同步数据源({}/{})集合{});", ++i, collections.size(), collection.getName());

                final List<Column> columns = datasource.getColumns(collection.getName());
                startCollectionSync(collection, dsConfig, columns);
            }

            return true;
        } catch (Exception e) {
            log.error("同步数据源({})字典失败", dcId, e);
            return false;
        }
    }

    @Override
    public boolean updateTags(DictionaryTagUpdate tagUpdate) {
        try {
            final String dicId = tagUpdate.getDicId();
            final Dictionary forUpdate = Dictionary.builder()
                    .tags(tagUpdate.getTags() == null ? List.of() : tagUpdate.getTags())
                    .dicId(dicId)
                    .gmtModified(LocalDateTime.now()).buildForUpdate();

            final UpdateRequest updateRequest = new UpdateRequest(dictionaryIndex, dicId)
                    .doc(objectMapper.writeValueAsString(forUpdate), XContentType.JSON);

            esClient.update(updateRequest, RequestOptions.DEFAULT).forcedRefresh();

            return true;
        } catch (Exception e) {
            log.error("更新标签失败", e);
            return false;
        }
    }

    @Override
    public boolean updateRemark(DictionaryRemarkUpdate remarkUpdate) {
        try {
            final String dicId = remarkUpdate.getDicId();
            final Dictionary forUpdate = Dictionary.builder()
                    .remark(remarkUpdate.getRemark())
                    .dicId(dicId)
                    .gmtModified(LocalDateTime.now()).buildForUpdate();

            final UpdateRequest updateRequest = new UpdateRequest(dictionaryIndex, dicId)
                    .doc(objectMapper.writeValueAsString(forUpdate), XContentType.JSON);

            esClient.update(updateRequest, RequestOptions.DEFAULT).forcedRefresh();

            return true;
        } catch (Exception e) {
            log.error("更新描述失败", e);
            return false;
        }
    }

    @Override
    public boolean updateColumnAliases(ColumnAliasesUpdate columnAliasesUpdate) {
        final String dicId = columnAliasesUpdate.getDicId();

        final Dictionary dictionary = get(dicId);
        if (dictionary == null) {
            throw createParamError("字典(ID: {})不存在", dicId);
        }

        try {
            final List<Dictionary.DictionaryColumn> columns = dictionary.getColumns();
            columns.forEach(c -> {
                if (c.getColumnName().equals(columnAliasesUpdate.getColumnName())) {
                    c.setColumnAliases(columnAliasesUpdate.getColumnAliases());
                }
            });

            final Dictionary forUpdate = Dictionary.builder()
                    .columns(columns)
                    .gmtModified(LocalDateTime.now())
                    .buildForUpdate();

            final UpdateRequest updateRequest = new UpdateRequest(dictionaryIndex, dicId)
                    .doc(objectMapper.writeValueAsString(forUpdate), XContentType.JSON);

            esClient.update(updateRequest, RequestOptions.DEFAULT).forcedRefresh();

            return true;
        } catch (Exception e) {
            log.error("更新描述失败", e);
            return false;
        }
    }

    private void startCollectionSync(Collection collection, DsConfig dsConfig, List<Column> columns) {
        try {
            final DsType dsType = dsConfig.getType();
            final String database = dsConfig.getConfig().getString(ConfigKey.DATABASE);
            final String id = Dictionary.newId(dsConfig.getDcId(), collection.getName());
            final Dictionary dictionary = get(id);

            final Dictionary.Builder builder = Dictionary.builder()
                    .dsType(dsType)
                    .database(database)
                    .collection(collection.getName())
                    .description(collection.getDescription())
                    .dcId(dsConfig.getDcId())
                    .columnsOf(columns)
                    .dicId(id)
                    .gmtModified(LocalDateTime.now())
                    .gmtCreate(LocalDateTime.now());

            if (dictionary != null) {
                // 已存在
                builder.oldColumns(dictionary.getColumns());
            }


            // 构建要更新的部分字段
            String docAsJson = objectMapper.writeValueAsString(builder.buildForUpdate());
            // 构建如果文档不存在时的插入内容
            String upsertAsJson = objectMapper.writeValueAsString(builder.buildForNew());

            final UpdateRequest updateRequest = new UpdateRequest(dictionaryIndex, id)
                    .doc(docAsJson, XContentType.JSON)
                    .upsert(upsertAsJson, XContentType.JSON);

            esClient.update(updateRequest, RequestOptions.DEFAULT).forcedRefresh();
        } catch (Exception e) {
            log.error("更新失败", e);
        }
    }

    @Override
    public Dictionary get(String dicId) {
        final GetRequest getRequest = new GetRequest(dictionaryIndex, dicId);

        try {
            final GetResponse response = esClient.get(getRequest, RequestOptions.DEFAULT);
            if (response.isExists()) {
                final String sourceAsString = response.getSourceAsString();

                return objectMapper.readValue(sourceAsString, Dictionary.class);
            }

            return null;
        } catch (Exception e) {
            throw createDbError(e, "获取字典数据(ID: {})失败", dicId);
        }
    }

    @Override
    public Dictionary get(String dcId, String collection) {
        final String dicId = Dictionary.newId(dcId, collection);
        return get(dicId);
    }
}

