package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.nrcore.json.JsonField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:32:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DyHaiHuiOpus extends StorageEntity {
    /**
     * ES字段
     */
    @JsonField("aweme_id")
    private String awemeId;
    @JsonField("aweme_desc")
    private String awemeDesc;
    @JsonField("ocr_content")
    private String ocrContent;
    @JsonField("aweme_type")
    private Integer awemeType;
    @JsonField("create_time")
    private String createTime;

    @JsonField("author_user_id")
    private String authorUserId;
    /**
     * holo达人数据
     */
    @J<PERSON><PERSON>ield("uid")
    private String uid;
    @JsonField("star_id")
    private String starId;
    @JsonField("nick_name")
    private String nickName;
    @JsonField("user_signature")
    private String userSignature;
    @JsonField("follower")
    private Long follower;
    @JsonField("all_like")
    private Long allLike;
    @JsonField("opus_num")
    private Integer opusNum;
    @JsonField("custom_verify")
    private String customVerify;
    @JsonField("e_commerce_enable")
    private Boolean eCommerceEnable;
    @JsonField("gender")
    private Integer gender;
    @JsonField("province")
    private String province;
    @JsonField("city")
    private String city;
    @JsonField("mcn_name")
    private String mcnName;
    @JsonField("tags_relation")
    private String tagsRelation;
    @JsonField("cover_num")
    private Long coverNum;
    @JsonField("inter_num")
    private Long interNum;
    @JsonField("industry_info")
    private String industryInfo;

    @JsonField("prospective_1_20_cpm")
    private Double prospective1To20Cpm;
    @JsonField("prospective_20_60_cpm")
    private Double prospective20To60Cpm;
    @JsonField("prospective_60_cpm")
    private Double prospective60Cpm;
    @JsonField("prospective_1_20_cpe")
    private Double prospective1To20Cpe;
    @JsonField("prospective_20_60_cpe")
    private Double prospective20To60Cpe;
    @JsonField("prospective_60_cpe")
    private Double prospective60Cpe;
    @JsonField("fans_increment_within_15d")
    private Integer fansIncrementWithin15D;
    @JsonField("fans_increment_rate_within_15d")
    private Double fansIncrementRateWithin15D;

    @JsonField("price_1")
    private Integer price1;
    @JsonField("price_2")
    private Integer price2;
    @JsonField("price_71")
    private Integer price71;
    @JsonField("star_index_value")
    private Double starIndexValue;
    @JsonField("personal_30d_item_num")
    private Integer personal30dItemNum;
    @JsonField("personal_30d_play_over_rate")
    private Double personal30dPlayOverRate;
    @JsonField("personal_30d_interact_rate")
    private Double personal30dInteractRate;
    @JsonField("personal_30d_play_mid")
    private Long personal30dPlayMid;
    @JsonField("personal_30d_like_avg")
    private Integer personal30dLikeAvg;
    @JsonField("personal_30d_comment_avg")
    private Integer personal30dCommentAvg;
    @JsonField("personal_30d_share_avg")
    private Integer personal30dShareAvg;
    @JsonField("personal_30d_avg_duration")
    private Double personal30dAvgDuration;
    
    @JsonField("star_30d_item_num")
    private Integer star30dItemNum;
    @JsonField("star_30d_play_over_rate")
    private Double star30dPlayOverRate;
    @JsonField("star_30d_interact_rate")
    private Double star30dInteractRate;
    
    @JsonField("star_30d_play_mid")
    private Long star30dPlayMid;
    @JsonField("star_30d_like_avg")
    private Integer star30dLikeAvg;
    @JsonField("star_30d_comment_avg")
    private Integer star30dCommentAvg;
    @JsonField("star_30d_share_avg")
    private Integer star30dShareAvg;
    @JsonField("star_30d_avg_duration")
    private Double star30dAvgDuration;
    
    @JsonField("convert_ability_30d_play_mid")
    private Long convertAbility30dPlayMid;
    @JsonField("convert_ability_30d_component_click_cnt_range")
    private String convertAbility30dComponentClickCntRange;
    @JsonField("convert_ability_30d_component_click_rate_range")
    private String convertAbility30dComponentClickRateRange;
    @JsonField("convert_ability_30d_related_cpc_range")
    private String convertAbility30dRelatedCpcRange;
    @JsonField("convert_ability_30d_rec_product_cnt")
    private Integer convertAbility30dRecProductCnt;
    @JsonField("convert_ability_30d_avg_sales_amount_range")
    private String convertAbility30dAvgSalesAmountRange;
    @JsonField("convert_ability_30d_rec_product_price_range")
    private String convertAbility30dRecProductPriceRange;
    @JsonField("convert_ability_30d_gpm_range")
    private String convertAbility30dGpmRange;
    @JsonField("expected_play_num")
    private Long expectedPlayNum;

    /**
     * 封面图像总结
     */
    private String coverSummary;

    /**
     * 向量引擎字段数据
     */
    private String textField;

    @Override
    public String identifier() {
        return awemeId;
    }
}
