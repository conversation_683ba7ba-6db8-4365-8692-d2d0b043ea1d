package cn.newrank.niop.data.config;

import cn.newrank.niop.data.common.event.DatasourceConfigRefreshTopic;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/20 10:03
 */
@Configuration
public class GlobalTopicConfig {

    /**
     * 数据源配置刷新事件
     *
     * @param redissonClient redisson客户端
     * @return {@link DatasourceConfigRefreshTopic}
     */
    @Bean
    public DatasourceConfigRefreshTopic datasourceConfigRefreshTopic(RedissonClient redissonClient) {
        return new DatasourceConfigRefreshTopic(redissonClient);
    }
}
