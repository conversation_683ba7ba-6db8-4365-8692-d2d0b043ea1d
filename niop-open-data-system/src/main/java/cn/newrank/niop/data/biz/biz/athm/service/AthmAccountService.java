package cn.newrank.niop.data.biz.biz.athm.service;

import cn.newrank.niop.data.biz.biz.athm.mapper.AthmAccountMapper;
import cn.newrank.niop.data.biz.biz.athm.pojo.AthmAccount;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 汽车之家-账号数据存储service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/31 10:17:26
 */
@Service
public class AthmAccountService implements StorageBizService<AthmAccount> {

    private final AthmAccountMapper athmAccountMapper;

    public AthmAccountService(AthmAccountMapper athmAccountMapper) {
        this.athmAccountMapper = athmAccountMapper;
    }

    @Override
    public void storeBatch(List<AthmAccount> items) {
        athmAccountMapper.insertBatch(items);
    }


    @Override
    public AthmAccount get(String identifier) {
        return athmAccountMapper.get(identifier);
    }

    @Override
    public AthmAccount castOf(JSONObject item) {
        return item.to(AthmAccount.class);
    }

}




