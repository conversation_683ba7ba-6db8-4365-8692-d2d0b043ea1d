package cn.newrank.niop.data.util;


import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * 可迭代得集合流式操作工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 14:12
 */
public class Iterables {

    private Iterables() {
    }

    /**
     * 实体映射转换
     *
     * @param entities 实体
     * @param mapper   映射
     * @return 结果集
     */
    public static <T, R> List<R> toList(Iterable<T> entities, Function<T, R> mapper) {
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).map(mapper).toList();
    }

    public static <T, E> List<E> flatList(Iterable<T> entities, Function<T, List<E>> mapper) {
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).map(mapper).flatMap(Collection::stream).distinct().toList();
    }

    /**
     * 实体映射转换, 结果去重
     *
     * @param entities 实体
     * @param mapper   映射
     * @return 结果集
     */
    public static <T, R> List<R> toDistinctList(Iterable<T> entities, Function<T, R> mapper) {
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).map(mapper).distinct().toList();
    }

    /**
     * 实体映射转换
     *
     * @param entities 实体
     * @param mapper   映射
     * @return 结果集
     */
    public static <T, R> Set<R> toSet(Iterable<T> entities, Function<T, R> mapper) {
        if (isEmpty(entities)) {
            return emptySet();
        }
        return streamOf(entities).map(mapper).collect(Collectors.toSet());
    }

    /**
     * 实体过滤
     *
     * @param entities  实体数据
     * @param predicate 过滤条件
     * @return 满足条件的值
     */
    public static <T> List<T> collectPart(Iterable<T> entities, Predicate<T> predicate) {
        Assert.notNull(predicate, "过滤条件不能为 null");
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).filter(predicate).toList();
    }

    /**
     * 实体过滤
     *
     * @param entities    实体数据
     * @param fieldMapper 字段映射
     * @param predicate   过滤条件
     * @return 满足条件的值
     */
    public static <T, F> List<F> collectPart(Iterable<T> entities, Function<T, F> fieldMapper, Predicate<T> predicate) {
        Assert.notNull(fieldMapper, "字段映射不能为 null");
        Assert.notNull(predicate, "过滤条件不能为 null");
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).filter(predicate).map(fieldMapper).toList();
    }

    /**
     * 转Map , key重复丢弃后续
     *
     * @param entities  实体
     * @param keyMapper key映射
     * @param valMapper val映射
     * @return map
     */
    public static <T, K, V> Map<K, V> toMap(Iterable<T> entities, Function<T, K> keyMapper, Function<T, V> valMapper) {
        Assert.notNull(keyMapper, "key映射不能为 null");
        Assert.notNull(keyMapper, "val映射不能为 null");
        if (isEmpty(entities)) {
            return emptyMap();
        }
        return streamOf(entities).collect(Collectors.toMap(keyMapper, valMapper, (oldVal, newVal) -> oldVal));
    }

    /**
     * 排序
     *
     * @param entities   实体
     * @param comparator 比较器
     * @return 排序后的list
     */
    public static <T> List<T> toSortedList(Iterable<T> entities, Comparator<T> comparator) {
        if (isEmpty(entities)) {
            return emptyList();
        }
        return streamOf(entities).sorted(comparator).toList();
    }

    /**
     * 分组
     *
     * @param entities   实体
     * @param classifier 分组键
     * @return 分组结果
     */
    public static <K, T> Map<K, List<T>> toGroup(Iterable<T> entities, Function<? super T, ? extends K> classifier) {
        if (isEmpty(entities)) {
            return emptyMap();
        }
        return streamOf(entities).collect(Collectors.groupingBy(classifier));
    }

    /**
     * 分组并转换实体
     *
     * @param entities   实体
     * @param classifier 分组键函数
     * @param mapper     实体转换函数
     * @return 分组并转换后的结果
     */
    public static <K, T, U> Map<K, List<U>> toGroup(Iterable<T> entities,
                                                    Function<? super T, ? extends K> classifier,
                                                    Function<? super T, ? extends U> mapper) {
        if (isEmpty(entities)) {
            return emptyMap();
        }
        return streamOf(entities)
                .collect(Collectors.groupingBy(
                        classifier,
                        Collectors.mapping(mapper, Collectors.toList())));
    }

    /**
     * Converts Iterable to stream
     */
    public static <T> Stream<T> streamOf(final Iterable<T> iterable) {
        if (iterable instanceof Collection) {
            return ((Collection<T>) iterable).stream();
        }
        return StreamSupport.stream(iterable.spliterator(), false);
    }

    /**
     * 集合是否为空
     *
     * @param entities 实体
     */
    public static boolean isEmpty(Iterable<?> entities) {
        if (entities == null) {
            return true;
        }
        if (entities instanceof Collection) {
            return ((Collection<?>) entities).isEmpty();
        }
        return !entities.iterator().hasNext();
    }

    /**
     * 集合是否不为空
     *
     * @param entities 实体集合
     */
    public static boolean isNotEmpty(Iterable<?> entities) {
        return !isEmpty(entities);
    }

    /**
     * 空的List
     */
    public static <T> List<T> emptyList() {
        return new ArrayList<>();
    }

    /**
     * 空的List
     */
    public static <E> Set<E> emptySet() {
        return new HashSet<>();
    }

    /**
     * 空的Map
     */
    public static <K, V> Map<K, V> emptyMap() {
        return new HashMap<>();
    }

    @SafeVarargs
    public static <T> List<T> asList(T... ts) {
        return ts == null ? emptyList() : new ArrayList<>(Arrays.asList(ts));
    }

    @SafeVarargs
    public static <T> Set<T> asSet(T... ts) {
        return ts == null ? emptySet() : new HashSet<>(Arrays.asList(ts));
    }

    @SafeVarargs
    public static <T extends Comparable<T>> SortedSet<T> asSortSet(T... ts) {
        return ts == null ? new TreeSet<>() : new TreeSet<>(Arrays.asList(ts));
    }

}

