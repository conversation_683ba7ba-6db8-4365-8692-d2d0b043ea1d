package cn.newrank.niop.data.biz.biz.bz.service;

import cn.newrank.niop.data.biz.biz.bz.mapper.BzLiveMapper;
import cn.newrank.niop.data.biz.biz.bz.pojo.BzLive;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 10:55
 */
@Service
public class BzLiveService implements StorageBizService<BzLive> {

    private final BzLiveMapper bzLiveMapper;

    public BzLiveService(BzLiveMapper bzLiveMapper) {
        this.bzLiveMapper = bzLiveMapper;
    }

    @Override
    public List<BzLive> list(List<String> identifiers) {
        return bzLiveMapper.list(identifiers);
    }

    @Override
    public void storeBatch(List<BzLive> items) {
        bzLiveMapper.saveAll(items);
    }

    @Override
    public BzLive castOf(JSONObject item) {
        return StorageBizService.format(BzLive.class, item);
    }

    public List<BzLive> listCalculateLives() {
        return bzLiveMapper.listCalculateLives();
    }

    public void updateCalculateProps(BzLive live) {
        bzLiveMapper.update(live);
    }
}
