package cn.newrank.niop.data.biz.pojo.dto;

import cn.newrank.nrcore.json.JsonField;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.util.Strings;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/7/11
 */
@Data
@Accessors(chain = true)
public class KafkaAbilityMessage {

    /**
     * ability result
     */
    private AbilityCallBackResult data;

    /**
     * kafka send time
     */
    @JsonField("sendTime")
    private String sendTime;

    /**
     * kafka partition
     */
    @JsonField("kafka_partition")
    private String kafkaPartition;

    /**
     * kafka offset
     */
    @JsonField("kafka_offset")
    private String kafkaOffset;


    /**
     * @param data ability result
     * @return kafka ability result
     */
    public static KafkaAbilityMessage convertAbilityResult(JSONObject data) {
        KafkaAbilityMessage result = JsonParser.parseObject(data, KafkaAbilityMessage.class);
        if(Objects.isNull(result)){
            throw new RuntimeException("parse kafka ability result is null");
        }
        JSONObject json = data.getJSONObject("data");
        if(Objects.isNull(json)){
            throw new RuntimeException("ability result json is null");
        }
        AbilityCallBackResult parsedData = JsonParser.parseObject(json, AbilityCallBackResult.class);
        if(Strings.isBlank(parsedData.getTaskId())){
            throw new RuntimeException("ability result parsed is null");
        }
        result.setData(parsedData);
        return result;
    }


    /**
     * 获取结果 (结果不是Array)
     * @param result kafka ability result
     * @return 结果
     */
    public static JSONObject getResult(KafkaAbilityMessage result) {
        return Optional.ofNullable(result.getData().getData())
                .filter(list -> !list.isEmpty())
                .map(items -> (JSONObject) items.get(0))
                .orElse(new JSONObject());
    }

    /**
     * 获取结果列表(结果是Array)
     * @param result kafka ability result
     * @return 结果列表
     */
    public static List<JSONObject> getResultList(KafkaAbilityMessage result) {
        return Optional.ofNullable(result.getData().getData())
                .map(items -> items.stream().map(ob -> (JSONObject) ob).toList())
                .orElse(Collections.emptyList());
    }
}
