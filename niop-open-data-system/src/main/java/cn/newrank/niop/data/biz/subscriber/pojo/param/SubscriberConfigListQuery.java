package cn.newrank.niop.data.biz.subscriber.pojo.param;

import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 订阅配置列表查询参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/19
 */
@Data
public class SubscriberConfigListQuery {
    
    /**
     * 订阅源ID
     */
    @NotBlank(message = "订阅源ID不能为空")
    private String sourceId;
    
    /**
     * 订阅源类型列表
     */
    @NotEmpty(message = "订阅源类型不能为空")
    private List<SubSourceType> sourceTypes;
}