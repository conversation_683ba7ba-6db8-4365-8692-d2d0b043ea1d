package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.param.CallbackConfigCreate;
import cn.newrank.niop.data.biz.pojo.param.CallbackConfigUpdate;
import cn.newrank.niop.data.biz.pojo.param.CbConfigFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.CbConfigPageQuery;
import cn.newrank.niop.web.model.PageView;

import java.util.List;
import java.util.Map;

/**
 * 回调配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 9:54
 */
public interface CbConfigService {

    /**
     * 创建回调
     *
     * @param callbackConfigCreate 回调信息
     * @return 回调id
     */
    String create(CallbackConfigCreate callbackConfigCreate);

    /**
     * 更新回调
     *
     * @param callbackUpdate 回调信息
     * @return 是否更新成功
     */
    boolean update(CallbackConfigUpdate callbackUpdate);

    /**
     * 删除回调
     *
     * @param cbId 回调id
     * @return 是否删除成功
     */
    boolean delete(String cbId);

    /**
     * 校验回调id
     *
     * @param cbId 回调id
     */
    void checkCbId(String cbId);

    /**
     * 获取回调配置
     *
     * @param cbId 回调id
     * @return 回调信息
     */
    CbConfig get(String cbId);

    /**
     * 模糊查询
     *
     * @param cbConfigFuzzyQuery 回调信息
     * @return 回调信息
     */
    List<CbConfig> fuzzyQuery(CbConfigFuzzyQuery cbConfigFuzzyQuery);

    /**
     * 分页查询
     *
     * @param pageQuery 回调信息
     * @return 回调信息
     */
    PageView<CbConfig> page(CbConfigPageQuery pageQuery);

    Map<String, CbConfig> group(List<String> cbIds);

    /**
     * 获取所有回调id
     *
     * @return 回调id列表
     */
    List<String> listCbIds();
}
