package cn.newrank.niop.data.biz.biz.xhs.pojo;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsSourceDataTypeEnum;
import cn.newrank.niop.data.biz.biz.xhs.pojo.source.*;
import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.data.biz.pojo.dto.KafkaAcqMessage;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 多回调源-小红书作品数据
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Slf4j
public class XhsOpusFromMulti extends StorageEntity {

    @JsonField("opus_id")
    private String opusId;
    private String cover;
    private String title;
    private String desc;
    private String hashTags;
    private String createTime;
    private String type;
    private Long collectNum;
    private Long likes;
    private Long shareNum;
    private Long commentNum;
    private String uid;
    private String images;
    private String nickname;
    private String lastUpdateTime;
    private Long userFans;
    private String gmtTime;
    private String anaTime;
    /**
     * 发布时间
     * publishTime : 话题
     * time: 大盘
     */
    private String time;
    private String imagesList;
    private Integer isVisible;
    private Integer isDelete;
    private Long interactiveCount;
    private String noteCounterTypeV2;
    private String noteCounterTypeV1;
    private String ipLocationWeb;
    private String officialKeyword;

    private String videoInfo;
    private Integer width;
    private Integer height;
    private String videoUrl;
    private Integer duration;
    private String shareInfoLink;
    private Integer isCooperate;
    private String cooperateName;
    private String cooperateId;
    private String poiName;
    private String officialWarnMsg;
    private String discernBusinessBrandId;
    private String discernBusinessBrandName;
    private String seedBrandId;
    private String seedBrandName;
    /**
     * 1.大盘新增字段
     * 2.话题能力默认第一次写入时间
     */
    private String firstDetailAnaTime;
    private String token;
    /**
     * 1.大盘新增字段
     * 2.话题能力默认 1
     */
    private Integer acqAwemeDetail;
    /**
     * 数据标签
     */
    private String dataLabel;
    private String videoId;
    private String poiId;

    /**
     * 用于log和分表字段
     */
    private String ds;
    private String source;
    private String messageId;
    /**
     * 数据来源标签
     */
    private String sourceTag;
    /**
     * 作品详情初次写入的时间
     */
    private String ldGmtCreate;
    /**
     * 最近数据更新时间
     */
    private String ldGmtModify;

    public static final List<String> TOPIC_UPDATE_FIELDS = List.of(new String[]{
            "opusId", "createTime", "cover", "title", "desc", "hashTags", "type", "uid", "images",
            "nickname", "token", "lastUpdateTime", "width", "height", "videoUrl", "duration", "videoInfo",
            "imagesList", "acqAwemeDetail", "time", "anaTime", "gmtTime"
    });


    /**
     * 互动数字段
     * 规则：根据anaTime更新
     */
    public static final List<String> INTERACTIVE_FIELDS = List.of(new String[]{
            "likes",
            "interactiveCount",
            "commentNum",
            "collectNum",
            "shareNum"
    });


    /**
     * 第一次获取时间
     * 规则：只存第一次获取的值字段
     */
    public static final List<String> NOT_UPDATE_FIELDS = List.of(new String[]{"firstDetailAnaTime"});


    /**
     * 大盘更新字段
     * 类型：AWEME_BASE
     * 规则：新值存在则更新
     */
    public static final List<String> AWEME_BASE_FIELDS = List.of(new String[]{
            "opusId", "createTime", "uid", "title", "cover", "type", "isVisible", "isDelete", "desc",
            "noteCounterTypeV1", "noteCounterTypeV2", "acqAwemeDetail", "ipLocationWeb", "officialKeyword",
            "shareInfoLink", "isCooperate", "cooperateName", "cooperateId", "poiName", "officialWarnMsg",
            "discernBusinessBrandId", "discernBusinessBrandName", "seedBrandId", "seedBrandName", "videoUrl",
            "videoInfo", "videoId", "lastUpdateTime", "gmtTime", "anaTime", "time", "imagesList", "hashTags", "poiId"
    });

    /**
     * 大盘更新字段
     * 类型：AWEME_ACCOUNT
     * 规则：新值存在则更新
     */
    public static final List<String> AWEME_ACCOUNT_FIELDS =
            List.of(new String[]{"opusId", "createTime", "uid", "userFans", "images", "nickname"});


    /**
     * 大盘更新字段
     * 类型：AWEME_FIRST_ACQ_ABNORMAL
     * 规则：新值存在则更新
     */
    public static final List<String> AWEME_FIRST_ACQ_ABNORMAL_FIELDS = AWEME_BASE_FIELDS;

    /**
     * 大盘更新字段
     * 类型：AWEME_FIRST_ACQ_DETAIL
     * 规则：新值存在则更新
     */
    public static final List<String> AWEME_FIRST_ACQ_DETAIL_FIELDS = List.of(new String[]{"opusId", "createTime"});

    /**
     * 获取标签，目前处于测试期，如果标签为空，则随机生成标签value
     */
    public String getDataLabel() {
        if (Strings.isBlank(dataLabel)) {
            JSONObject integrity = new JSONObject();
            int calc_1 = (int) (Math.random() * 2);
            integrity.put("calc_1", calc_1);

            int base = (int) (Math.random() * 2);
            integrity.put("base", base);

            int all = (calc_1 + base) == 2 ? 1 : 0;
            integrity.put("all", all);
            JSONObject label = new JSONObject();
            label.put("integrity", integrity);
            dataLabel = label.toJSONString();
        }
        return dataLabel;
    }

    /**
     * 获取小红书作品采集信息
     *
     * @param item item
     * @return XhsOpusFromMulti
     */
    public static @Nullable XhsOpusFromMulti getXhsOpusFromMultiFromAcq(JSONObject item) {
        if (Objects.isNull(item) || item.isEmpty()) {
            log.warn("item is null");
            return null;
        }
        final KafkaAcqMessage message = KafkaAcqMessage.convertAcqMessage(item);
        if (Objects.isNull(message.getData_type())) {
            log.warn("illegal message , partition :{}  offset: {}", message.getKafka_partition(), message.getKafka_offset());
            return null;
        }
        final String dataType = message.getData_type();
        final XhsSourceDataTypeEnum type = XhsSourceDataTypeEnum.getByJsonCode(dataType);
        if (Objects.isNull(type)) {
            log.error("数据类型不存在:{}", dataType);
            return null;
        }
        return XhsOpusFromMulti.convertToMultiByType(message, type);
    }

    /**
     * 转换视频信息到videoInfo
     *
     * @param multi 小红书话题能力数据
     */
    public static String getVideoInfo(XhsOpusFromMulti multi) {
        JSONObject videoInfo = new JSONObject();
        videoInfo.put("video_duration", multi.getDuration());
        videoInfo.put("video_height", multi.getHeight());
        videoInfo.put("video_width", multi.getWidth());
        videoInfo.put("video_url", multi.getVideoUrl());
        return videoInfo.toJSONString();
    }


    /**
     * 大盘数据-作品基础信息
     *
     * @return 大盘数据-作品基础信息
     */
    public static XhsOpusFromMulti convertToMultiByType(KafkaAcqMessage message, XhsSourceDataTypeEnum type) {
        final String data = message.getJson_details();
        if (Strings.isBlank(data)) {
            log.error("jsonDetails is blank part: {} offset:{}", message.getKafka_partition(), message.getKafka_offset());
            return null;
        }
        return switch (type) {
            case ABILITY_TOPIC -> getTopicOpus(message);
            case AWEME_ACCOUNT -> getAwemeAccount(message);
            case AWEME_BASE -> getAwemeBase(message);
            case AWEME_FIRST_ACQ_DETAIL -> getAwemeFirstAcqDetail(message);
            case AWEME_FIRST_ACQ_ABNORMAL -> getAwemeFirstAcqAbnormal(message);
            default -> throw new RuntimeException("未知数据类型");
        };
    }

    /**
     * 获取大盘采集信息
     *
     * @param message kafka消息
     * @return 大盘信息
     */
    private static XhsOpusFromMulti getAwemeFirstAcqAbnormal(KafkaAcqMessage message) {
        String data = message.getJson_details();
        JSONObject jsonObject = JSONObject.parseObject(data);
        final XhsDpFirstAcqAbnormal dpBase = XhsDpFirstAcqAbnormal.parse(jsonObject);
        final String opusId = dpBase.getOpusId();
        if (Strings.isBlank(opusId)) {
            throw new RuntimeException("opusId is null");
        }
        XhsOpusFromMulti opus = XhsOpusFromMulti.initXhsOpusFromMulti(opusId);
        opus.setUid(dpBase.getUserid());
        opus.setTitle(dpBase.getTitle());
        opus.setCover(dpBase.getCover());
        opus.setType(dpBase.getType());
        opus.setIsVisible(dpBase.getIsVisible());
        opus.setIsDelete(dpBase.getIsDelete());
        opus.setLikes(dpBase.getLikedCount());
        opus.setDesc(dpBase.getDesc());
        opus.setInteractiveCount(dpBase.getInteractiveCount());
        opus.setCommentNum(dpBase.getCommentsCount());
        opus.setCollectNum(dpBase.getCollectedCount());
        opus.setShareNum(dpBase.getSharedCount());
        opus.setNoteCounterTypeV1(dpBase.getNoteCounterTypeV1());
        opus.setNoteCounterTypeV2(dpBase.getNoteCounterTypeV2());
        opus.setAcqAwemeDetail(dpBase.getAcqAwemeDetail());
        opus.setIpLocationWeb(dpBase.getIpLocationWeb());
        opus.setOfficialKeyword(dpBase.getOfficialKeyword());
        opus.setShareInfoLink(dpBase.getShareInfoLink());
        opus.setIsCooperate(dpBase.getIsCooperate());
        opus.setCooperateName(dpBase.getCooperateName());
        opus.setCooperateId(dpBase.getCooperateId());
        opus.setPoiName(dpBase.getPoiName());
        opus.setOfficialWarnMsg(dpBase.getOfficialWarnMsg());
        opus.setDiscernBusinessBrandId(dpBase.getDiscernBusinessBrandId());
        opus.setDiscernBusinessBrandName(dpBase.getDiscernBusinessBrandName());
        opus.setSeedBrandId(dpBase.getSeedBrandId());
        opus.setSeedBrandName(dpBase.getSeedBrandName());
        opus.setVideoUrl(dpBase.getVideoUrl());
        opus.setVideoId(dpBase.getVideoId());
        // 额外解析字段
        opus.setVideoInfo(getVideoInfoFromDpBase(dpBase));
        opus.setLastUpdateTime(DateTimeUtil.formatUtcDateTime(dpBase.getLastUpdateTime()));
        opus.setGmtTime(DateTimeUtil.formatUtcDateTime(dpBase.getGmtCreate()));
        opus.setAnaTime(DateTimeUtil.formatUtcDateTime(dpBase.getAnaTime()));
        // 真实发布时间覆盖Time
        opus.setTime(DateTimeUtil.formatUtcDateTime(dpBase.getTime()));
        opus.setImagesList(getJsonArray(dpBase.getImagesList()));
        opus.setHashTags(getJsonArray(dpBase.getTopics()));
        opus.setPoiId(dpBase.getPoiId());

        opus.setDs(KafkaAcqMessage.getDs(message.getAna_time()));
        opus.setSource(XhsSourceDataTypeEnum.AWEME_FIRST_ACQ_ABNORMAL.getDbCode());
        opus.setMessageId(KafkaAcqMessage.getAcqMessageId(message));
        return opus;
    }


    /**
     * 获取大盘第一次采集时间
     *
     * @param message kafka消息
     * @return 时间
     */
    private static XhsOpusFromMulti getAwemeFirstAcqDetail(KafkaAcqMessage message) {
        String data = message.getJson_details();
        JSONObject jsonObject = JSONObject.parseObject(data);
        final XhsDpFirstDetailAnaTime dpBase = XhsDpFirstDetailAnaTime.parse(jsonObject);
        final String opusId = dpBase.getOpusId();
        if (Strings.isBlank(opusId)) {
            throw new RuntimeException("opusId is null");
        }
        XhsOpusFromMulti opus = XhsOpusFromMulti.initXhsOpusFromMulti(opusId);
        opus.setFirstDetailAnaTime(DateTimeUtil.formatUtcDateTime(dpBase.getFirstDetailAnaTime()));

        opus.setDs(KafkaAcqMessage.getDs(message.getAna_time()));
        opus.setSource(XhsSourceDataTypeEnum.AWEME_FIRST_ACQ_DETAIL.getDbCode());
        opus.setMessageId(KafkaAcqMessage.getAcqMessageId(message));
        return opus;
    }


    /**
     * 获取大盘基础信息
     *
     * @param message kafka信息
     * @return 大盘基础信息
     */
    private static XhsOpusFromMulti getAwemeBase(KafkaAcqMessage message) {
        String data = message.getJson_details();
        JSONObject jsonData = JSON.parseObject(data);
        final XhsDpBase dpBase = XhsDpBase.parse(jsonData);
        final String opusId = dpBase.getOpusId();
        if (Strings.isBlank(opusId)) {
            throw new RuntimeException("opusId is null");
        }
        XhsOpusFromMulti opus = XhsOpusFromMulti.initXhsOpusFromMulti(opusId);
        opus.setUid(dpBase.getUserid());
        opus.setTitle(dpBase.getTitle());
        opus.setCover(dpBase.getCover());
        opus.setType(dpBase.getType());
        opus.setIsVisible(dpBase.getIsVisible());
        opus.setIsDelete(dpBase.getIsDelete());
        opus.setLikes(dpBase.getLikedCount());
        opus.setDesc(dpBase.getDesc());
        opus.setInteractiveCount(dpBase.getInteractiveCount());
        opus.setCommentNum(dpBase.getCommentsCount());
        opus.setCollectNum(dpBase.getCollectedCount());
        opus.setShareNum(dpBase.getSharedCount());
        opus.setNoteCounterTypeV1(dpBase.getNoteCounterTypeV1());
        opus.setNoteCounterTypeV2(dpBase.getNoteCounterTypeV2());
        opus.setAcqAwemeDetail(dpBase.getAcqAwemeDetail());
        opus.setIpLocationWeb(dpBase.getIpLocationWeb());
        opus.setOfficialKeyword(dpBase.getOfficialKeyword());
        opus.setShareInfoLink(dpBase.getShareInfoLink());
        opus.setIsCooperate(dpBase.getIsCooperate());
        opus.setCooperateName(dpBase.getCooperateName());
        opus.setCooperateId(dpBase.getCooperateId());
        opus.setPoiName(dpBase.getPoiName());
        opus.setOfficialWarnMsg(dpBase.getOfficialWarnMsg());
        opus.setDiscernBusinessBrandId(dpBase.getDiscernBusinessBrandId());
        opus.setDiscernBusinessBrandName(dpBase.getDiscernBusinessBrandName());
        opus.setSeedBrandId(dpBase.getSeedBrandId());
        opus.setSeedBrandName(dpBase.getSeedBrandName());
        opus.setVideoUrl(dpBase.getVideoUrl());
        opus.setVideoId(dpBase.getVideoId());
        // 额外解析字段
        opus.setVideoInfo(getVideoInfoFromDpBase(dpBase));
        opus.setLastUpdateTime(DateTimeUtil.formatUtcDateTime(dpBase.getLastUpdateTime()));
        opus.setGmtTime(DateTimeUtil.formatUtcDateTime(dpBase.getGmtCreate()));
        opus.setAnaTime(DateTimeUtil.formatUtcDateTime(dpBase.getAnaTime()));
        // 真实发布时间覆盖Time
        opus.setTime(DateTimeUtil.formatUtcDateTime(dpBase.getTime()));
        opus.setImagesList(getJsonArray(dpBase.getImagesList()));
        opus.setHashTags(getJsonArray(dpBase.getTopics()));
        opus.setPoiId(dpBase.getPoiId());
        // 获取kafka信息
        opus.setDs(KafkaAcqMessage.getDs(message.getAna_time()));
        opus.setSource(XhsSourceDataTypeEnum.AWEME_BASE.getDbCode());
        opus.setMessageId(KafkaAcqMessage.getAcqMessageId(message));
        return opus;
    }


    /**
     * 获取jsonArray
     *
     * @param list 列表
     * @return 列表
     */
    public static String getJsonArray(String list) {
        if (Objects.isNull(list)) {
            return null;
        }
        JSONArray array = JSON.parseObject(list, JSONArray.class);
        if (CollectionUtil.isNotEmpty(array) && array.get(0) instanceof String) {
            JSONArray jsonArray = new JSONArray();
            array.stream().filter(Objects::nonNull)
                    .forEach(item -> jsonArray.add(JSON.parseObject((String) item, JSONObject.class)));
            return Strings.isBlank(list) ? "" : jsonArray.toJSONString();
        }
        return Strings.isBlank(list) ? "" : array.toJSONString();
    }


    /**
     * 获取视频信息
     *
     * @param dpBase dpBase
     * @return videoInfo
     */
    private static @NotNull String getVideoInfoFromDpBase(XhsDpBase dpBase) {
        JSONObject videoInfo = new JSONObject();
        videoInfo.put("video_duration", dpBase.getVideoDuration());
        videoInfo.put("video_height", dpBase.getVideoHeight());
        videoInfo.put("video_played_count", dpBase.getVideoPlayedCount());
        videoInfo.put("video_width", dpBase.getVideoWidth());
        videoInfo.put("video_url", dpBase.getVideoUrl());
        return videoInfo.toJSONString();
    }

    /**
     * 获取视频信息
     *
     * @param dpBase dpBase
     * @return videoInfo
     */
    private static @NotNull String getVideoInfoFromDpBase(XhsDpFirstAcqAbnormal dpBase) {
        JSONObject videoInfo = new JSONObject();
        videoInfo.put("video_duration", dpBase.getVideoDuration());
        videoInfo.put("video_height", dpBase.getVideoHeight());
        videoInfo.put("video_played_count", dpBase.getVideoPlayedCount());
        videoInfo.put("video_width", dpBase.getVideoWidth());
        videoInfo.put("video_url", dpBase.getVideoUrl());
        return videoInfo.toJSONString();
    }


    /**
     * @param message kafka信息
     * @return XhsOpusFromMulti
     */
    private static XhsOpusFromMulti getAwemeAccount(KafkaAcqMessage message) {
        String data = message.getJson_details();
        JSONObject jsonObject = JSONObject.parseObject(data);
        final XhsDpAccount dpAccount = XhsDpAccount.parse(jsonObject);
        final String opusId = dpAccount.getOpusId();
        if (Strings.isBlank(opusId)) {
            throw new RuntimeException("opusId is null");
        }
        // 初始化opus
        final XhsOpusFromMulti opus = initXhsOpusFromMulti(opusId);
        opus.setUid(dpAccount.getUserId());
        opus.setUserFans(dpAccount.getFans());
        opus.setImages(dpAccount.getImages());
        opus.setNickname(dpAccount.getNickname());

        // 获取kafka信息
        opus.setDs(KafkaAcqMessage.getDs(message.getAna_time()));
        opus.setSource(XhsSourceDataTypeEnum.AWEME_ACCOUNT.getDbCode());
        opus.setMessageId(KafkaAcqMessage.getAcqMessageId(message));
        return opus;
    }


    /**
     * 初始化opus
     * 初始化笔记创建时间：作品ID反解时间
     *
     * @param opusId 作品Id
     * @return opus
     */
    private static XhsOpusFromMulti initXhsOpusFromMulti(String opusId) {
        final XhsOpusFromMulti opus = new XhsOpusFromMulti();
        final String createTime = getNoteCreateTime(opusId);
        opus.setOpusId(opusId);
        opus.setCreateTime(createTime);
        return opus;
    }

    /**
     * 笔记创建时间, 作品ID反解时间,笔记创建时间
     *
     * @param opusId 作品ID
     * @return str
     */
    public static String getNoteCreateTime(String opusId) {
        LocalDateTime dateTime = LocalDateTimeUtil.of(Long.parseLong(opusId.substring(0, 8), 16) * 1000);
        return dateTime.format(DatePattern.NORM_DATETIME_FORMATTER);
    }

    /**
     * 解析话题作品
     *
     * @return opus
     */
    private static XhsOpusFromMulti getTopicOpus(KafkaAcqMessage message) {
        String data = message.getJson_details();
        JSONObject jsonObject = JSONObject.parseObject(data);
        final XhsOpusTopic topic = XhsOpusTopic.parse(jsonObject);
        if (Strings.isBlank(topic.getOpusId())) {
            return null;
        }
        XhsOpusFromMulti opus = XhsOpusFromMulti.initXhsOpusFromMulti(topic.getOpusId());
        opus.setCover(topic.getCover());
        opus.setTitle(topic.getTitle());
        opus.setDesc(topic.getDesc());
        opus.setHashTags(getJsonArray(topic.getHashTags()));
        opus.setType(topic.getType());
        opus.setCollectNum(topic.getCollectNum());
        opus.setLikes(topic.getLikes());
        opus.setShareNum(topic.getShareNum());
        opus.setCommentNum(topic.getCommentNum());
        opus.setUid(topic.getUid());
        opus.setImages(topic.getImages());
        opus.setNickname(topic.getNickname());
        opus.setToken(topic.getToken());
        opus.setLastUpdateTime(convertToDateTime(topic.getEditTime()));
        opus.setWidth(topic.getWidth());
        opus.setHeight(topic.getHeight());
        opus.setVideoUrl(topic.getVideoUrl());
        opus.setDuration(topic.getDuration());
        opus.setVideoInfo(XhsOpusFromMulti.getVideoInfo(opus));
        opus.setImagesList(getJsonArray(topic.getImagesList()));
        // 添加标识
        opus.setAcqAwemeDetail(1);
        // 真实发布时间
        opus.setTime(convertToDateTime(topic.getPublishTime()));
        opus.setAnaTime(message.getAna_time());
        // 对应话题任务完成时间
        opus.setGmtTime(message.getAna_time());
        opus.setFirstDetailAnaTime(message.getAna_time());

        opus.setDs(KafkaAcqMessage.getDs(message.getAna_time()));
        opus.setSource(XhsSourceDataTypeEnum.ABILITY_TOPIC.getDbCode());
        opus.setMessageId(KafkaAcqMessage.getAcqMessageId(message));
        return opus;
    }


    public static String convertToDateTime(String iosTime) {
        DateTimeFormatter isoFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        DateTimeFormatter normFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(iosTime, isoFormatter);
        return dateTime.format(normFormatter);
    }

    @Override
    public String identifier() {
        return opusId;
    }


    /**
     * 旧值不存在则赋新值
     *
     * @param fields     字段
     * @param oldXhsOpus 旧值
     */
    public void mergeIfOldNull(List<String> fields, XhsOpusFromMulti oldXhsOpus) {
        if (oldXhsOpus == null) {
            return;
        }
        fields.forEach(field -> {
            final Field f = ReflectionUtils.findField(XhsOpusFromMulti.class, field);
            assert f != null;
            ReflectionUtils.makeAccessible(f);
            final Object oldValue = ReflectionUtils.getField(f, oldXhsOpus);
            if (oldValue != null) {
                return;
            }
            final Object newValue = ReflectionUtils.getField(f, this);
            ReflectionUtils.setField(f, oldXhsOpus, newValue);
        });
    }


    /**
     * 合并字段，新值存在则直接赋值
     *
     * @param fields     字段
     * @param oldXhsOpus 旧值
     */
    public void mergeIfNewExist(List<String> fields, XhsOpusFromMulti oldXhsOpus) {
        if (oldXhsOpus == null) {
            return;
        }
        fields.forEach(field -> {
            final Field f = ReflectionUtils.findField(XhsOpusFromMulti.class, field);
            assert f != null;
            ReflectionUtils.makeAccessible(f);
            final Object newValue = ReflectionUtils.getField(f, this);
            if (newValue == null) {
                return;
            }
            ReflectionUtils.setField(f, oldXhsOpus, newValue);
        });
    }

    @Data
    public static class SourceTag {
        @JSONField(name = "source_list")
        private Set<String> sourceList;
        @JSONField(name = "is_from_es")
        private int fromEs;
        @JSONField(name = "last_source_modify")
        private String lastSourceModify;

        public void refresh(XhsSourceDataTypeEnum source) {
            if (Objects.isNull(sourceList)) {
                sourceList = new HashSet<>();
            }
            sourceList.add(source.getDbCode());
            lastSourceModify = source.getDbCode();
            if (XhsSourceDataTypeEnum.isFromEs(source)) {
                fromEs = 1;
            }
        }
    }
}
