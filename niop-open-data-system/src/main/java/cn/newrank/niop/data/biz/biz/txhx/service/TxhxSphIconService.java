package cn.newrank.niop.data.biz.biz.txhx.service;

import cn.newrank.niop.data.biz.biz.txhx.mapper.TxhxSphIconMapper;
import cn.newrank.niop.data.biz.biz.txhx.pojo.TxhxSphIcon;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 腾讯互选-详情-service操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/06/05 10:49:53
 */
@Service
public class TxhxSphIconService implements StorageBizService<TxhxSphIcon> {

    private final TxhxSphIconMapper txhxSphIconMapper;

    public TxhxSphIconService(TxhxSphIconMapper txhxSphIconMapper) {
        this.txhxSphIconMapper = txhxSphIconMapper;
    }

    @Override
    public TxhxSphIcon get(String identifier) {
        return txhxSphIconMapper.get(identifier);
    }

    @Override
    public void storeBatch(List<TxhxSphIcon> items) {
        txhxSphIconMapper.insertBatch(items);
    }

    @Override
    public TxhxSphIcon castOf(JSONObject item) {
        return item.to(TxhxSphIcon.class);
    }
}
