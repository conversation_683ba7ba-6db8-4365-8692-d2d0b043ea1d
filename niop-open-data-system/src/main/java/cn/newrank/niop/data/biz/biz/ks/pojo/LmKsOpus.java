package cn.newrank.niop.data.biz.biz.ks.pojo;


import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * lindorm ks opus
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/23 16:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LmKsOpus extends StorageEntity {
    /**
     *
     */
    private String photoId;

    /**
     *
     */
    private Integer anaDel;

    /**
     *
     */
    private String anaTags;

    /**
     *
     */
    private String awemePortrait;
    private String awemeAcqDataType;
    private Timestamp anaTime;
    private Timestamp awemePortraitAcqTime;
    private Timestamp flinkAwemeSyncTime;
    private Timestamp mcAwemeSyncTime;
    private Integer inDaily;
    /**
     *
     */
    private Timestamp awemePortraitUpdateTime;

    /**
     *
     */
    private Timestamp baseInsertTime;

    /**
     *
     */
    private String caption;

    /**
     *
     */
    private Long collectCount;

    /**
     *
     */
    private Long commentCount;

    /**
     *
     */
    private String cover;

    /**
     *
     */
    private String coverType;

    /**
     *
     */
    private Long duration;

    /**
     *
     */
    private String extParams;

    /**
     *
     */
    private Long forwardCount;

    /**
     *
     */
    private String headurls;

    /**
     *
     */
    private String headurl;

    /**
     *
     */
    private String imageUrls;

    /**
     *
     */
    private Timestamp insertTime;

    /**
     *
     */
    private Integer isHotAweme;

    /**
     *
     */
    private Integer isLowHot;

    /**
     *
     */
    private Integer isPromotion;

    /**
     *
     */
    private String kwaiId;

    /**
     *
     */
    private Long likeCount;

    /**
     *
     */
    private String mainMvUrls;

    /**
     *
     */
    private String merchant;

    /**
     *
     */
    private Integer mtype;

    /**
     *
     */
    private String music;

    /**
     *
     */
    private String ownerCount;

    /**
     *
     */
    private String poi;

    /**
     *
     */
    private String screenType;

    /**
     *
     */
    private Long shareCount;

    /**
     *
     */
    private String shareInfo;

    /**
     *
     */
    private String soundTrack;

    /**
     *
     */
    private String standardSerial;

    /**
     *
     */
    private String streamManifest;

    /**
     *
     */
    private String subMtype;

    /**
     *
     */
    private Timestamp time;

    /**
     *
     */
    private Long type;

    /**
     *
     */
    private Long unlikeCount;

    /**
     *
     */
    private Timestamp updateTime;

    /**
     *
     */
    private Long userFan;

    /**
     *
     */
    private String userId;

    /**
     *
     */
    private String userName;

    /**
     *
     */
    private String userType;

    /**
     *
     */
    private String verifiedDetail;

    /**
     *
     */
    private Long viewCount;

    /**
     *
     */
    private Double vpf;

    /**
     * 是否带货
     */
    private Boolean hasMerchant;
    /**
     * 原始作品id
     */
    private String originalPhotoId;

    private String disclaimerMessage;

    private String merchantAdType;
    private String searchTags;
    private String soundTrackId;
    private String musicId;
    private Long userIdL;
    private String categoryLv1;

    /**
     * 创建时间
     */
    private String gmtTime;
    private String ldGmtCreate;


    @Override
    public String identifier() {
        return photoId;
    }

    public boolean isDelete() {
        return anaDel != null && anaDel == -1;
    }


   static final List<Field> FIELDS;

    static {
        FIELDS = Arrays.stream(LmKsOpus.class.getDeclaredFields())
                .filter(field -> !Modifier.isStatic(field.getModifiers()))
                .toList();
        FIELDS.forEach(ReflectionUtils::makeAccessible);
    }

    public void copyIfExistNull(LmKsOpus oldKsOpus) {
        if (oldKsOpus == null) {
            return;
        }
        FIELDS.forEach(field -> {
            final Object newValue = ReflectionUtils.getField(field, this);
            if (newValue != null) {
                return;
            }

            final Object oldValue = ReflectionUtils.getField(field, oldKsOpus);
            ReflectionUtils.setField(field, this, oldValue);
        });
    }

}
