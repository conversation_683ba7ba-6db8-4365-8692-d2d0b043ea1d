package cn.newrank.niop.data.biz.biz.athm.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 汽车之家
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 9:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AthmOpusList extends SampleVersionEntity {
    /**
     * 标题
     */
    private String title;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 时长
     */
    private Integer duration;

    /**
     * 作品id
     */
    private String opusId;

    /**
     * url
     */
    private String url;

    /**
     * 封面
     */
    private String cover;

    /**
     * uid
     */
    private String uid;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 阅读数
     */
    private Integer readNum;

    /**
     * 点赞数
     */
    private Integer likeNum;

    /**
     * 描述
     */
    private String description;

    /**
     * 内容
     */
    private String content;

    /**
     * 播放数
     */
    private Integer playNum;

    /**
     * 发布时间
     */
    private String publishTime;

    @Override
    public String identifier() {
        return opusId;
    }
}