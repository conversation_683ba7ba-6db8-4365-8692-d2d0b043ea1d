package cn.newrank.niop.data.biz.export.mapper;

import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS("niop-ldm")
public interface DataExportTableStorageMapper {

    /**
     * 查询表格存储数据
     *
     * @param taskId   任务id
     * @param uniqueId 唯一id
     * @param limit    查询条数
     * @return 表格存储数据
     */
    List<DaTaExportTableStorage> queryTableStorageContents(@Param("taskId") String taskId,
                                                           @Param("uniqueId") String uniqueId,
                                                           @Param("limit") int limit);

    /**
     * 是否存在对应的数据
     *
     * @param taskIds 任务id列表
     * @return 是否存在
     */
    Boolean existsData(@Param("taskIds") List<String> taskIds);
}
