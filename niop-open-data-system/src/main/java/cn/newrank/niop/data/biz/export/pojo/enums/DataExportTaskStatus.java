package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

public enum DataExportTaskStatus implements BizEnum {

    /**
     * 导数任务状态
     */
    FAILED("-1", "失败"),
    PARAM_FILE_UPLOADED("0", "参数文件已上传"),
    TASK_INITIALIZED("1", "任务初始化"),
    TASK_RUNNING("2", "任务执行中"),
    SUCCEED("3", "任务成功"),
    RESULT_FILE_IS_READY("4", "结果文件已准备好"),
    ;

    private final String code;
    private final String description;

    DataExportTaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

}
