package cn.newrank.niop.data.config;

import com.alibaba.nacos.common.executor.NameThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/15 15:43
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 线程池
     *
     * @return ThreadPoolExecutor
     */
    @Bean
    public ThreadPoolExecutor commonIoThreadPool() {
        return new ThreadPoolExecutor(1, 20,
                30, TimeUnit.SECONDS
                , new LinkedBlockingQueue<>(50)
                , new NameThreadFactory("common-io-")
                , new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
