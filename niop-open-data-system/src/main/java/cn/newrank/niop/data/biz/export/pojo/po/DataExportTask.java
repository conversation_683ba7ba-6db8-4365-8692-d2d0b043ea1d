package cn.newrank.niop.data.biz.export.pojo.po;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class DataExportTask {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 导数id
     */
    private String exportId;

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 任务参数文件地址
     */
    private String taskParamFile;

    /**
     * 导数交付类型
     */
    private DataExportDeliveryType deliveryType;

    /**
     * 运行状态
     */
    private DataExportRunningStatus runningStatus;

    /**
     * 任务状态
     */
    private DataExportTaskStatus taskStatus;

    /**
     * 参数总数
     */
    private Integer paramTotalNum;

    /**
     * 成功数
     */
    private Integer succeedTotalNum;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 任务完成时间
     */
    private Timestamp taskFinishedTime;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;

    public static DataExportTask initialized(Integer id, Integer paramTotalNum) {
        final DataExportTask task = new DataExportTask();
        task.setId(id);
        task.setParamTotalNum(paramTotalNum);
        task.setTaskStatus(DataExportTaskStatus.TASK_INITIALIZED);
        return task;
    }

    public static DataExportTask failed(Integer id) {
        final DataExportTask task = new DataExportTask();
        task.setId(id);
        task.setRunningStatus(DataExportRunningStatus.FINISHED);
        task.setTaskStatus(DataExportTaskStatus.FAILED);
        task.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));
        return task;
    }

    public static DataExportTask running(Integer id) {
        final DataExportTask task = new DataExportTask();
        task.setId(id);
        task.setTaskStatus(DataExportTaskStatus.TASK_RUNNING);
        return task;
    }

    public static DataExportTask resultFileReady(Integer id) {
        final DataExportTask task = new DataExportTask();
        task.setId(id);
        task.setRunningStatus(DataExportRunningStatus.FINISHED);
        task.setTaskStatus(DataExportTaskStatus.RESULT_FILE_IS_READY);
        return task;
    }

}
