package cn.newrank.niop.data.biz.biz.ds.pojo.dto;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import lombok.Data;


/**
 * 收藏数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class CollectCount implements EsEntity {

    Integer collectCount;
    String indexId;

    public static CollectCount of(String indexId, Integer collectCount) {
        final CollectCount c = new CollectCount();

        c.setCollectCount(collectCount);
        c.setIndexId(indexId);

        return c;
    }

    @Override
    public String docId() {
        return indexId;
    }
}
