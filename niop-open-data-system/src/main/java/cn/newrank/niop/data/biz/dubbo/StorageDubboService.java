package cn.newrank.niop.data.biz.dubbo;

import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.pojo.dto.StorageResult;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.biz.storage.pojo.dto.Storage;
import cn.newrank.niop.data.biz.storage.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.storage.pojo.params.StorageGetHistoryRequest;
import cn.newrank.niop.data.biz.storage.pojo.params.StorageGetRequest;
import cn.newrank.niop.data.biz.storage.service.IStorageDubboService;
import cn.newrank.niop.data.util.Iterables;
import org.apache.dubbo.config.annotation.DubboService;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/08/14 15:09:16
 */
@DubboService
public class StorageDubboService implements IStorageDubboService {

    private final StorageService storageService;

    public StorageDubboService(StorageService storageService) {
        this.storageService = storageService;
    }

    @Override
    public Storage get(StorageGetRequest request) {
        final StorageResult storageResult = storageService.get(StorageBiz.ofJSONValue(request.getStorageBiz()),
                request.getIdentifier(),
                request.getVersion()
        );

        if (storageResult == null) {
            throw createParamError("数据(identifier: {})不存在", request.getIdentifier());
        }

        final Storage storage = new Storage();
        storage.setIdentifier(storageResult.getIdentifier());
        storage.setVersion(storageResult.getVersion());
        storage.setUpdateTime(storageResult.getUpdateTime());
        storage.setData(storageResult.getData());
        return storage;
    }

    @Override
    public StorageHistory getHistory(StorageGetHistoryRequest request) {
        final cn.newrank.niop.data.biz.pojo.dto.StorageHistory history = storageService.getHistory(
                StorageBiz.ofJSONValue(request.getStorageBiz()),
                request.getIdentifier()
        );

        final StorageHistory storageHistory = new StorageHistory();
        storageHistory.setHistories(
                Iterables.toList(
                        history.getHistories(),
                        hs -> {
                            final StorageHistory.History returnHistory = new StorageHistory.History();
                            returnHistory.setVersion(hs.getVersion());
                            returnHistory.setUpdateTime(hs.getUpdateTime());
                            returnHistory.setData(hs.getData());
                            return returnHistory;
                        }
                )
        );
        storageHistory.setIdentifier(history.getIdentifier());
        storageHistory.setPages(history.getPages());
        storageHistory.setTotal(history.getTotal());
        return storageHistory;
    }
}
