package cn.newrank.niop.data.biz.biz.tiktok;

import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokAccountMapper;
import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokHoloUserMapper;
import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokOpusHoloMapper;
import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokOpusMapper;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokAccount;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokHoloOpus;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokHoloUser;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokOpus;
import cn.newrank.niop.data.biz.biz.tiktok.service.TiktokDailyRankService;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.newrank.niop.util.U.toList;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 19:11
 */
@Component
@Log4j2
public class TiktokScheduler {

    private final TiktokOpusMapper tiktokOpusMapper;
    private final TiktokAccountMapper tiktokAccountMapper;
    private final TiktokOpusHoloMapper tiktokOpusHoloMapper;
    private final TiktokHoloUserMapper tiktokHoloUserMapper;
    private final TiktokDailyRankService tiktokDailyRankService;

    public TiktokScheduler(TiktokOpusMapper tiktokOpusMapper,
                           TiktokAccountMapper tiktokAccountMapper,
                           TiktokOpusHoloMapper tiktokOpusHoloMapper,
                           TiktokHoloUserMapper tiktokHoloUserMapper,
                           TiktokDailyRankService tiktokDailyRankService) {
        this.tiktokOpusMapper = tiktokOpusMapper;
        this.tiktokAccountMapper = tiktokAccountMapper;
        this.tiktokOpusHoloMapper = tiktokOpusHoloMapper;
        this.tiktokHoloUserMapper = tiktokHoloUserMapper;
        this.tiktokDailyRankService = tiktokDailyRankService;
    }

    @XxlJob("CalculateTiktokRank")
    public ReturnT<String> calculateTiktokRank(String rankDateStr) {
        final LocalDate rankDate = StringUtils.isBlank(rankDateStr) ? LocalDate.now() : DateTimeUtil.toDate(rankDateStr);
        log.info("[TiktokRank] 开始榜单计算-{}", rankDate);
        log.info("[TiktokRank] 开始同步账号数据");
        syncAccount();

        log.info("[TiktokRank] 开始同步作品数据");
        syncOpus(rankDate);

        log.info("[TiktokRank] 开始计算榜单数据");
        tiktokDailyRankService.clear(rankDate);
        tiktokDailyRankService.calculate(rankDate);

        log.info("[TiktokRank] 完成计算榜单数据");
        return ReturnT.SUCCESS;
    }


    public void syncOpus(LocalDate rankDate) {
        try {
            final LocalDateTime startTime = rankDate.atStartOfDay();
            final LocalDateTime endTime = rankDate.plusDays(1).atStartOfDay();

            long cursor = 0;
            while (true) {
                final List<TiktokOpus> opuses = tiktokOpusMapper.list(cursor,
                        Timestamp.valueOf(startTime),
                        Timestamp.valueOf(endTime));

                if (opuses.isEmpty()) {
                    return;
                }

                cursor = opuses.get(opuses.size() - 1).getId();

                final List<TiktokHoloOpus> holoOpuses = toList(opuses, TiktokHoloOpus::of);
                tiktokOpusHoloMapper.save(DateTimeUtil.format(rankDate, "yyyyMM"), holoOpuses);
            }
        } catch (Exception e) {
            log.error("同步tiktok 作品数据失败", e);
        }
    }

    public void syncAccount() {
        try {
            long cursor = 0;
            while (true) {
                final List<TiktokAccount> accounts = tiktokAccountMapper.list(cursor);

                if (accounts.isEmpty()) {
                    return;
                }

                cursor = accounts.get(accounts.size() - 1).getId();

                final List<TiktokHoloUser> tiktokHoloUsers = toList(accounts, TiktokHoloUser::of);
                tiktokHoloUserMapper.save(tiktokHoloUsers);
            }
        } catch (Exception e) {
            log.error("同步tiktok 账号数据失败", e);
        }
    }

    public void calculate(LocalDate rankDate) {
        tiktokDailyRankService.clear(rankDate);
        tiktokDailyRankService.calculate(rankDate);
    }
}
