package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import lombok.Data;

/**
 * 综合服务数据回调
 */
@Data
public class HubServiceCallback {

    /**
     * 源id
     */
    private String sourceId;

    /**
     * 源类型
     */
    private String sourceType;

    /**
     * 负载
     */
    private Payload payload;


    @Data
    static class Payload {

        /**
         * 任务id
         */
        private String taskId;

        /**
         * 应用id
         */
        private String appId;

        /**
         * 能力id
         */
        private String abilityId;

        /**
         * 总数据量
         */
        private Integer total;

        /**
         * 当前数据序列
         */
        private Integer seqNum;

        /**
         * 任务状态
         */
        private Integer status;

        /**
         * 任务完成时间
         */
        private String finishTime;

        /**
         * Json 格式参数
         */
        private String params;

    }

    public String getTaskId() {
        return this.payload.getTaskId();
    }

    public String getAppId() {
        return this.payload.getAppId();
    }

    public Integer getTotal() {
        return this.payload.getTotal();
    }

    public Integer getSeqNum() {
        return this.payload.getSeqNum();
    }

    public Integer getStatus() {
        return this.payload.getStatus();
    }

    public String getFinishTime() {
        return this.payload.getFinishTime();
    }

    /**
     * 是否是导数应用的任务
     *
     * @return 是否导数应用的任务
     */
    public boolean fromExportApp() {
        return DataExportConstant.EXPORT_APP_ID.equals(this.getAppId());
    }

    /**
     * 任务状态是否成功
     *
     * @return 状态是否成功
     */
    public boolean statusSucceed() {
        return 1 == this.getStatus();
    }

    /**
     * 任务是否已完成
     *
     * @return 是否完成
     */
    public boolean taskFinished() {
        // seqNum 为 0 表示结果为空
        return 0 == this.getSeqNum() || this.getSeqNum().equals(this.getTotal());
    }

}
