package cn.newrank.niop.data.biz.export.excel.handler;

import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.pojo.dto.HeaderField;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import java.util.Map;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 */
public class ColumnFormatCellWriteHandler implements CellWriteHandler {

    private final Map<String, HeaderField> fieldMap;

    public ColumnFormatCellWriteHandler(Map<String, HeaderField> fieldMap) {
        this.fieldMap = fieldMap;
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        final Cell cell = context.getCell();
        final String cellValue = cell.getStringCellValue();
        if (CharSequenceUtil.isBlank(cellValue)) {
            return;
        }

        Sheet sheet = context.getWriteSheetHolder().getSheet();

        HeaderField field = this.fieldMap.get(cellValue);

        ColumnDataType dataType = ColumnDataType.parseByType(field.getType());
        Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
        // 单元格格式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setDataFormat(dataType.getFormatIndex());

        // 设置当前列默认格式
        sheet.setDefaultColumnStyle(cell.getColumnIndex(), cellStyle);
    }

    enum ColumnDataType {

        /**
         * 数据类型
         */
        STRING("string", 49),
        NUMBER("number", 1),
        INTEGER("integer", 1),
        ;

        final String type;

        /**
         * 所有格式参考
         *
         * @see BuiltinFormats
         */
        final int formatIndex;

        ColumnDataType(String type, int formatIndex) {
            this.type = type;
            this.formatIndex = formatIndex;
        }

        public String getType() {
            return this.type;
        }

        public short getFormatIndex() {
            return (short) this.formatIndex;
        }

        public static ColumnDataType parseByType(String type) {
            if (CharSequenceUtil.isNotBlank(type)) {
                for (ColumnDataType dataType : values()) {
                    if (dataType.getType().equals(type)) {
                        return dataType;
                    }
                }
            }
            throw createParamError("不支持的数据类型");
        }
    }

}
