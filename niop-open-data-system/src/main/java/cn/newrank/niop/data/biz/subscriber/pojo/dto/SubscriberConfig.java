package cn.newrank.niop.data.biz.subscriber.pojo.dto;

import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.pojo.po.SubscriberConfigPo;
import cn.newrank.niop.util.U;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 10:52
 */
@Data
public class SubscriberConfig {
    /**
     * 订阅者id
     */
    private String subscriberId;

    /**
     * 应用id
     */
    private List<App> apps;
    /**
     * 应用名称
     */
    private String appName;

    /**
     * 回调源id
     */
    private String cbId;

    private String cbName;

    /**
     * 源id
     */
    private String sourceId;
    /**
     * 源名称
     */
    private String sourceName;

    /**
     * 源类型
     */
    private SubSourceType sourceType;

    /**
     * 是否启用参数(针对v1能力结果)
     */
    private Boolean enableParams;

    /**
     * 发送策略
     */
    private SendStrategy sendStrategy;

    /**
     * 标签
     */
    private List<Tag> tags;
    /**
     * 负责人
     */
    private List<String> maintainers;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;


    public SubscriberConfigPo toPo() {
        final SubscriberConfigPo configPo = new SubscriberConfigPo();

        configPo.setAppIds(JSON.toJSONString(appIds()));
        configPo.setCbId(cbId);
        configPo.setSubscriberId(subscriberId);
        configPo.setSourceId(sourceId);
        configPo.setSourceName(sourceName);
        configPo.setSourceType(sourceType);
        configPo.setEnableParams(enableParams);
        if (sendStrategy != null) {
            configPo.setSendStrategy(JSON.toJSONString(sendStrategy));
        }
        if (tags != null) {
            configPo.setTags(JSON.toJSONString(tags));
        }
        configPo.setMaintainers(JSON.toJSONString(maintainers));

        return configPo;
    }

    public List<String> appIds() {
        if (apps == null) {
            return new ArrayList<>();
        }
        return U.toList(apps, App::getAppId);
    }
}