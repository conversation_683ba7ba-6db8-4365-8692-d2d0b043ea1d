package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class DataExportAbilityResultHandler extends BaseDataExportResultHandler {

    protected DataExportAbilityResultHandler(DataExportSubtaskService subtaskService) {
        super(subtaskService);
    }

    @Override
    public DataExportType getDataSourceType() {
        return DataExportType.ABILITY;
    }

}
