package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.util.Ids;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:48
 */
@Data
public class CallbackRedirect {
    /**
     * 存储业务
     */
    String storageBiz;
    /**
     * 源唯一键
     */
    String sourceKey;
    /**
     * 源id
     */
    String sourceId;
    /**
     * 源类型
     */
    String sourceType;
    /**
     * 应用id
     */
    String appId;
    /**
     * 消费者原始记录
     */
    @JSONField(serialize = false)
    ConsumerRecord<String, String> consumerRecord;

    /**
     * 消费者原始记录载体
     */
    ConsumerRecordCarrier consumerRecordCarrier;

    /**
     * 数据
     */
    Object payload;

    /**
     * 源唯一键, 如果为空则随机生成
     *
     * @return 源唯一键
     */
    public String getSourceKey() {
        return sourceKey == null ? Ids.create(32) : sourceKey;
    }

    public String toPayloadJSONString() {
        if (payload instanceof String json) {
            return json;
        } else {
            return JSON.toJSONString(payload);
        }
    }

    public Object payload() {
        return payload;
    }

    public String getAppId() {
        if (StringUtils.isNotBlank(appId)) {
            return appId;
        }

        if (payload instanceof JSONObject jsonObject) {
            return jsonObject.getString("appId");
        }

        return null;
    }

    /**
     * 是否为回调消息
     *
     * @return true:是，false:否
     */
    public boolean isCb() {
        return StringUtils.isNotBlank(sourceType) && StringUtils.isNotBlank(sourceId);
    }

}
