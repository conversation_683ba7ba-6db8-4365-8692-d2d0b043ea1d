package cn.newrank.niop.data.biz.biz.xhs.schedule;

import cn.newrank.niop.data.biz.biz.xhs.service.exp.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/3/31 17:09:11
 */
@RestController
@RequestMapping("/xhsExpSchedule")
public class XhsExpSchedule {

    private final XhsExpParentTaskService xhsExpParentTaskService;
    private final XhsExpAtspTaskService xhsExpAtspTaskService;
    private final XhsExpTopicWeightService xhsExpTopicWeightService;
    private final XhsExpNewTopicService xhsExpNewTopicService;
    private final XhsExpTestService xhsExpTestService;
    private final XhsExpTaskStoreService xhsExpTaskStoreService;

    public XhsExpSchedule(XhsExpParentTaskService xhsOpusTopicMonitorService,
                          XhsExpAtspTaskService xhsExpAtspTaskService,
                          XhsExpTopicWeightService xhsExpTopicWeightService,
                          XhsExpNewTopicService xhsExpNewTopicService,
                          XhsExpTestService xhsExpTestService,
                          XhsExpTaskStoreService xhsExpTaskStoreService) {
        this.xhsExpParentTaskService = xhsOpusTopicMonitorService;
        this.xhsExpAtspTaskService = xhsExpAtspTaskService;
        this.xhsExpTopicWeightService = xhsExpTopicWeightService;
        this.xhsExpNewTopicService = xhsExpNewTopicService;
        this.xhsExpTestService = xhsExpTestService;
        this.xhsExpTaskStoreService = xhsExpTaskStoreService;
    }

    /**
     * 1.创建话题监测任务
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/createXhsExpTopicTask")
    @XxlJob("createXhsExpTopicTask")
    public ReturnT<String> createXhsExpTopicTask(String param) {
        xhsExpParentTaskService.runSchedule(param);
        return ReturnT.SUCCESS;
    }


    /**
     * 2.提交话题监测任务
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/submitXhsExpTopicTask")
    @XxlJob("submitXhsExpTopicTask")
    public ReturnT<String> submitXhsExpTopicTask(String param) {
        xhsExpAtspTaskService.runSubmit(param);
        return ReturnT.SUCCESS;
    }


    /**
     * 3.更新话题权重
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/updateXhsExpTopicWeight")
    @XxlJob("updateXhsExpTopicWeight")
    public ReturnT<String> updateXhsExpTopicWeight(String param) {
        xhsExpTopicWeightService.runSchedule(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 4.新话题入监控表
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/newTopicAddToMonitorTopic")
    @XxlJob("newTopicAddToMonitorTopic")
    public ReturnT<String> newTopicAddToMonitorTopic(String param) {
        xhsExpNewTopicService.runSchedule(param);
        return ReturnT.SUCCESS;
    }


    /**
     * 5.获取结果
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/xhsExpGetAtspResult")
    @XxlJob("xhsExpGetAtspResult")
    public ReturnT<String> xhsExpGetAtspResult(String param) {
        xhsExpAtspTaskService.runResult(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 6.测试提交
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/xhsExpSubmitTest")
    @XxlJob("xhsExpSubmitTest")
    public ReturnT<String> xhsExpSubmitTest(String param) {
        xhsExpTestService.runSubmitTest(param);
        return ReturnT.SUCCESS;
    }



    /**
     * 7.表删除
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/xhsExpTaskTableDrop")
    @XxlJob("xhsExpTaskTableDrop")
    public ReturnT<String> xhsExpTaskTableDrop(String param) {
        xhsExpTaskStoreService.dropTaskTable(param);
        return ReturnT.SUCCESS;
    }


}
