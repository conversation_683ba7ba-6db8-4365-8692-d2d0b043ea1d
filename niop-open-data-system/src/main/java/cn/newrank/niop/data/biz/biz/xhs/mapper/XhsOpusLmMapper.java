package cn.newrank.niop.data.biz.biz.xhs.mapper;


import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多回调源存-小红书作品数据-覆盖写lindorm
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
@DS("xhs")
public interface XhsOpusLmMapper {

    /**
     * 批量存储
     * @param items 存储对象
     * @return 存储结果
     */
    boolean saveBatch(@Param("items") List<XhsOpusFromMulti> items);

    /**
     * 批量获取作品是否存在
     * @param opusIds 作品ID
     * @return 存在返回对象，不存在返回null
     */
    List<XhsOpusFromMulti> getOpusExistBatch(@Param("items") List<String> opusIds);


    /**
     * 批量获取作品是否存在
     * @param opusIds 作品ID
     * @return 存在返回对象，不存在返回null
     */
    List<String> getOpusExistIdsBatch(@Param("items") List<String> opusIds);


    /**
     * 批量存储
     * @param items 存储对象
     * @return 存储结果
     */
    boolean fillFieldSaveBatch(@Param("items") List<XhsOpusFromMulti> items);

}




