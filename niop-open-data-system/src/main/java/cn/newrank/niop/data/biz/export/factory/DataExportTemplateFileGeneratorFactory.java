package cn.newrank.niop.data.biz.export.factory;

import cn.newrank.niop.data.biz.export.handler.DataExportTemplateFileGenerator;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DataExportTemplateFileGeneratorFactory implements ApplicationContextAware {

    private final Map<DataExportType, DataExportTemplateFileGenerator> generatorMap = new EnumMap<>(DataExportType.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        final Map<String, DataExportTemplateFileGenerator> fileGeneratorMap = applicationContext.getBeansOfType(DataExportTemplateFileGenerator.class);

        for (Map.Entry<String, DataExportTemplateFileGenerator> generator : fileGeneratorMap.entrySet()) {
            final DataExportTemplateFileGenerator value = generator.getValue();
            this.generatorMap.put(value.getExportType(), value);
        }
    }

    public DataExportTemplateFileGenerator getTemplateFileGenerator(DataExportType exportType) {
        return Optional.ofNullable(this.generatorMap.get(exportType))
            .orElseThrow(() -> new IllegalArgumentException("未实现的导数类型: " + exportType.getDescription()));
    }

}
