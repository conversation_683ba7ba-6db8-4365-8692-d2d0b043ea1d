package cn.newrank.niop.data.biz.component.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 带版本的数据存储
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 14:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class StorageVersionEntity extends StorageEntity {

    /**
     * 版本: 从1开始
     */
    protected Integer version;

    /**
     * 获取版本更新时间戳
     *
     * @return 版本更新时间戳
     */
    public abstract long versionUpdateTime();

    /**
     * 设置版本更新时间戳
     *
     * @param versionUpdateTime 版本更新时间戳
     */
    public abstract void setVersionUpdateTime(long versionUpdateTime);
}
