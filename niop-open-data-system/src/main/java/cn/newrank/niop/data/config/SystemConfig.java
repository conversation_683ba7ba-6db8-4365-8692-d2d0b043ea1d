package cn.newrank.niop.data.config;


import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.config.property.SystemProperties;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/12/21 10:25
 */
@Log4j2
@Data
@Configuration
public class SystemConfig {


    private static SystemConfig config;
    private final SystemProperties systemProperties;
    private final Environment environment;

    public SystemConfig(SystemProperties systemProperties) {
        this.systemProperties = systemProperties;
        this.environment = Environment.getEnvironment(systemProperties.getActiveProfile());
        SystemConfig.config = this;
        log.info("当前应用运行环境为: {}", environment);
    }

    public static SystemConfig getConfig() {
        if (config == null) {
            throw createParamError("系统配置未初始化");
        }
        return config;
    }

    /**
     * 是否是生产环境
     *
     * @return true: 生产环境 false: 非生产环境
     */
    public boolean isProduct() {
        return environment.isProduct();
    }

    /**
     * 是否是测试环境
     *
     * @return true: 测试环境 false: 非测试环境
     */
    public boolean isTest() {
        return environment.isTest();
    }

    /**
     * 开发环境
     *
     * @return true: 开发环境 false: 非开发环境
     */
    public boolean isDevelop() {
        return environment.isDevelop();
    }

}
