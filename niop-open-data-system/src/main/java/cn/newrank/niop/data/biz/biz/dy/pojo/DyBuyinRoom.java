package cn.newrank.niop.data.biz.biz.dy.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName niop_data_biz_dy_buyin_room
 */
@Data
@Accessors(chain = true)
public class DyBuyinRoom implements Serializable {
    /**
     * 
     */
    private Timestamp gmtModified;

    /**
     * 
     */
    private Timestamp gmtCreate;

    /**
     * 
     */
    private String roomId;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String startTime;

    /**
     * 
     */
    private String endTime;

    /**
     * 
     */
    private String cover;

    /**
     * 
     */
    private Long status;

    /**
     * 
     */
    private String streamUrl;

    /**
     * 
     */
    private String viewerNums;

    /**
     * 
     */
    private String maxViewerNum;

    /**
     * 
     */
    private String totalSaleNum;

    /**
     * 
     */
    private String totalGmv;

    /**
     * 
     */
    private String productNum;

    /**
     * 
     */
    private String deviceName;

    /**
     * 
     */
    private String partitionOffset;

    private static final long serialVersionUID = 1L;
}