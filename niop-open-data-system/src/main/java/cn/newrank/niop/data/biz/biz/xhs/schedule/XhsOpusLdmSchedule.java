package cn.newrank.niop.data.biz.biz.xhs.schedule;

import cn.newrank.niop.data.biz.biz.xhs.service.XhsOpusBackfillService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/3/31 17:09:11
 */
@Slf4j
@RestController
@RequestMapping("/xhsOpusLdmSchedule")
public class XhsOpusLdmSchedule {

    private final XhsOpusBackfillService xhsOpusSaveService;

    public XhsOpusLdmSchedule(XhsOpusBackfillService xhsOpusSaveService) {
        this.xhsOpusSaveService = xhsOpusSaveService;
    }


    /**
     * test
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/fillLdmOpus")
    @XxlJob("fillLdmOpus")
    public ReturnT<String> fillLdmOpus(String param) {
        try {
            xhsOpusSaveService.fillLdmOpus(param);
        } catch (Exception e) {
            log.warn("fillLdmOpus error", e);
        }
        return ReturnT.SUCCESS;
    }

}
