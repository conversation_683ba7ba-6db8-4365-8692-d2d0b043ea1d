package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.common.ServiceNames;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.storage.service.IDatasourceDubboService;
import cn.newrank.niop.data.config.property.KafkaProperties;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.nrcore.web.response.RespJson;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 13:52
 */
@RestController
@Log4j2
@RequestMapping("test")
public class TestController {

    @Resource
    private KafkaProperties kafkaProperties;

    @DubboReference(providedBy = ServiceNames.DUBBO_OPEN_DATA)
    private IDatasourceDubboService datasourceDubboService;

    /**
     * 创建线程池
     *
     * @param core 核心线程数
     * @param max  最大线程数
     * @param name 线程名
     * @return 线程池实例
     */
    public static ThreadPoolTaskExecutor create(int core, int max, String name) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(core);
        executor.setMaxPoolSize(max);
        executor.setQueueCapacity(0);
        executor.setThreadNamePrefix(name + "-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @RequestMapping
    public void test() {
        log.info(JSON.toJSONString(datasourceDubboService.getDatasourceConfig("F84AA")));
    }

    @PostMapping("redirect-callback/{type}")
    public String callbackRedirect(@RequestBody CallbackRedirect callbackRedirect, @PathVariable String type) {
        final KafkaProperties.Config config = getConfig(type);
        final CbConfig cbConfig = new CbConfig();
        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> """
                    alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093
                    """;
            case TOPIC -> config.getTopic();
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> config.getUsername();
            case PASSWORD -> config.getPassword();
            default -> null;
        });

        try (final KafkaCallback kafkaCallback = new KafkaCallback(cbConfig.getConfig())) {
            if (!kafkaCallback.isActive()) {
                throw createParamError("not active");
            }

            kafkaCallback.callback(JSON.toJSONString(callbackRedirect));
        }

        return "success";
    }

    public KafkaProperties.Config getConfig(String type) {
        return switch (type) {
            case "sample" -> kafkaProperties.getSampleRedirect();
            case "scheduler" -> kafkaProperties.getSchedulerRedirect();
            case "xhs_hot_word" -> kafkaProperties.getAnalysisHotWord();
            case "gzh_enterprise_info" -> kafkaProperties.getGzhEnterpriseCertificationInfo();
            case "gzh_fans_data" -> kafkaProperties.getGzhFansData();
            case "scheduler_task" -> kafkaProperties.getSchedulerTask();
            case "ks_holo_opus" -> kafkaProperties.getAnalysisKs();
            case "ability_callback" -> kafkaProperties.getAbilityCallback();
            default -> throw createParamError("type error");
        };
    }

    AtomicInteger count = new AtomicInteger();


    @RespJson(code = 0)
    @PostMapping("callback")
    public void testCallback(@RequestBody Object data) {
        //log.info(JSON.toJSONString(data, JSONWriter.Feature.PrettyFormat));
        log.info("rev {}", count.incrementAndGet());
    }


    @GetMapping("send-sample")
    void callbackRedirect() {
        final KafkaProperties.Config config = getConfig("sample");
        final CbConfig cbConfig = new CbConfig();
        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> """
                    alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093
                    """;
            case TOPIC -> config.getTopic();
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> config.getUsername();
            case PASSWORD -> config.getPassword();
            default -> null;
        });

        try (final KafkaCallback kafkaCallback = new KafkaCallback(cbConfig.getConfig())) {
            if (!kafkaCallback.isActive()) {
                throw createParamError("not active");
            }

            for (int i = 0; i < 12; i++) {
                kafkaCallback.callback(getBody());
            }
        }
    }

    int id;

    String getBody() {
        return """
                {
                  "sourceId":"D4HA",
                  "sourceKey": "%s",
                  "sourceType": "sample",
                  "payload": {"appId":"27U51I1Y","data":{"musicUrl":"","oriUrl":"http://mp.weixin.qq.com/s?__biz=MzA5MTQ5MjkyNw==&mid=**********&idx=1&sn=dacb31ed0d281727ec8212c564a6a78a&chksm=8a0ccc2ce6d3368c948a627caa52c13a3a4cba9dc1b04514de039a1b63407363f48a9f4d5b0e#rd","description":"四川省教育厅唯一官方公众号","indexUrl":"http://wx.qlogo.cn/mmhead/Q3auHgzwzM7qZR16EeaxmUDpN76DSM1O5fWBvU3r5qnWeHaMVNvM5Q/0","memo":"","mid":"**********","title":"广元教师赵怀勇：不能让任何一名学生掉队！| 致敬40年","shareNum":165,"content":"致敬40年·四川教师风采                              为深入贯彻落实习近平总书记关于教育的重要论述，大力弘扬教育家精神，充分展示四川教师敬业立学、崇德尚美的精神风貌，营造全社会尊师重教的良好氛围，在第40个教师节来临之际，“四川教育发布”特开设教师节专栏——“致敬40年·四川教师风采”系列报道，讲述四川教师育人故事，让“尊师”成为风尚，让“重教”温暖每位老师，激发全省教师为党育人、为国育才的使命担当，以实际行动投身教育强省建设。今天跟川川一起了解四川省广元中学副校长赵怀勇——\\n人物简介\\n赵怀勇，四川省广元中学副校长、正高级教师，曾获全国教育系统先进工作者、四川省劳动模范、四川省优秀教师等荣誉。\\n从大山中走出的赵怀勇深知，大山里的孩子更希望通过读书改变未来。35年来，一万多个日日夜夜里，他备课、磨题、面批面改、谈心谈话……无数个孩子的人生轨迹因此而改变，无数个家庭的面貌因此而变化。\\n他曾经在校长会上说道，广元属于西部地区，广中学子很多来自四县三区的广大农村，要加大扶持力度，不能让任何一名学生掉队。\\n赵怀勇时刻惦记着分管年级建档立卡贫困户的每一位学生，在生活与学业上，竭尽所能地给他们提供帮助。赵怀勇说：“一个学生就是一个点，一个家庭就是一条线，无数条线形成了平面。”35年来，他始以涓涓细流一路奔腾向前，汇聚成汪洋大海。\\n在他的教导下，数千位学子成功圆梦。仅2017年，他所指导的学生中，就有150多人获得国家级、省级奥赛等级奖，400多人在各类作文赛、创新赛、电视大赛获国家、省市级奖。\\n无论是作为一名普通教师，还是年级分管领导，赵怀勇都坚持着一个朴素的追求——在师生中留个好口碑。为了这个朴素的追求，该同志连续20多年战斗在毕业班教学岗位上，连续18年担任高中毕业班化学教学工作。\\n他时刻提醒自己：教师是立教之本、兴教之源。面对新课改，他不断探索新的教学方法，拓宽了创新拔尖培养途径，积极响应高校自主招生、多元录取政策，引进新百年教育资源，高三高二年级自主招生培训进入常态化。\\n近年来，他指导20余名青年教师获省、市级赛课奖项，带领化学组先后承担20余项国家、省、市级科研课题并屡获表彰。\\n赵怀勇坚持研学合一，他的《直接证据视角下的“氨气性质”教学设计》《创新意识在化学教学中的培养》等文章在国家、省、市级刊物上发表；《基础教育信息化初探》《普通高中分层教育实施策略》《全面准确的把握概念的内涵与外延》等文章在省市级学术活动中交流。\\n赵怀勇秉持为学在实、大爱无疆的行事准则，从1989年至今，无论是做教师还是副校长，赵怀勇始终坚持把平凡的工作做真、做实、做久作为他内心恪守的信条。\\n▌本文来源：人民网、广元市教育局                                                                                                                                                                                                                 更多新闻\\n9月四川考试招生月历来了\\n四川教育要闻（8月24日-30日）\\n卡莎莎，曲别阿布老师！\\n 43条安全提示转给师生家长\\n","countryId":"156","likeNum":79,"sourceUrl":"","audioUrl":"","biz":"MzA5MTQ5MjkyNw==","videoUrl":"http://mpvideo.qpic.cn/0bc3geaasaaaouagp6wwivtfamodbeyqacia.f10002.mp4?dis_k=8b764baf0181312b3e3655332b24e1a9&dis_t=1725589148&play_scene=10120&auth_info=Vd7F7clXbVFK682exnZ7bV95bEMFRn5wOlwpSUM3RHhkdB1EIVswPig9QWNlUmxXVQ==&auth_key=008ac83e862874cc663e1797f6b5b2f7","imageUrl":"https://mmbiz.qpic.cn/sz_mmbiz_jpg/ia0IsgbTKx4DNcdQ6IIVEX5IvzfEK2GIiaysxMGCfrdCvIe4TYfuAGh68BlWDZaicwicFfHls1wv2Y9fTzXjiaEaH0Q/0?wx_fmt=jpeg","nickname":"四川教育发布","isAds":false,"viewingNum":18,"gifs":[],"summary":"","publishTime":"2024-09-05 21:21:42","images":["https://mmbiz.qpic.cn/sz_mmbiz_png/ia0IsgbTKx4Cv7jl0hibDU8OXXRdib0JKVAweHuGNxHuhPasc9AhMg5qrbz9znsLceMgI6xtVuLVcNQ37yPeCRSfA/640?wx_fmt=png&from=appmsg","https://mmbiz.qpic.cn/sz_mmbiz_png/ia0IsgbTKx4DNcdQ6IIVEX5IvzfEK2GIiaPa2hTiahw5PSmMB6F2QibXibDdiaI9OibebIVFsERdhNvRTMnYd719xlLlg/640?wx_fmt=png&from=appmsg","https://mmbiz.qpic.cn/sz_mmbiz_png/ia0IsgbTKx4DNcdQ6IIVEX5IvzfEK2GIiaO11GvPod1HdsnuMc5xUyLwfGV7ukiaXbkkqj8V0gcWibIgOa7mtvB8Hg/640?wx_fmt=png&from=appmsg","https://mmbiz.qpic.cn/sz_mmbiz_png/ia0IsgbTKx4DNcdQ6IIVEX5IvzfEK2GIiax89icKOVphXC3vE46hDAMmHtdib9pgHvhQ2E83zp1fNicibh0dOBF6icO5Q/640?wx_fmt=png&from=appmsg"],"oriAuthor":"","author":"四川教育发布","topics":[{"topicId":"3589306396945973258","topicName":"#第40个教师节"},{"topicId":"3589374703485091843","topicName":"#致敬40年"}],"avatar":"http://mmbiz.qpic.cn/mmbiz_png/ia0IsgbTKx4Cg4XJATcNUrqUa2t98pOxZMPHePDKUiaN3GOLicxc4gVKzoAJfFuJVPpVp6aXEnspPPvxHslHwvs9g/0?wx_fmt=png","wxId":"gh_a6b5da9f59a1","url":"http://mp.weixin.qq.com/s?__biz=MzA5MTQ5MjkyNw==&mid=**********&idx=1&sn=dacb31ed0d281727ec8212c564a6a78a&chksm=8b975499bce0dd8fe63ccd9a52ecf1dba793ec85d94413284139fb121af1166c925c3a277f29#rd","tags":[],"opusId":"61EF9E8C3EC8478B51B794888FE1CD14","viewNum":11593,"commentsCount":0,"oriFlag":false,"publishOrder":"0","countryName":"中国","provinceName":"四川","account":"scsjyt"},"ruleId":"AWMK","sampleId":"MzA5MTQ5MjkyNw==","sampleStatus":"normal","updateTime":*************}
                }
                """.formatted(Ids.create(32));
    }

    @GetMapping("send-ability-callback")
    void abilityCallback() {
        final KafkaProperties.Config config = getConfig("ability_callback");
        final CbConfig cbConfig = new CbConfig();
        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> """
                    alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093
                    """;
            case TOPIC -> config.getTopic();
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> config.getUsername();
            case PASSWORD -> config.getPassword();
            default -> null;
        });

        try (final KafkaCallback callback = new KafkaCallback(cbConfig.getConfig())) {
            callback.callback("{\"abilityId\":\"OOXDJOWX\",\"appId\":\"8IKWK5MJ\",\"bizCode\":0,\"bizMsg\":\"成功\",\"finishTime\":\"2024-11-18 10:02:00.006\",\"sceneIds\":[\"Y1VBMM6J\"],\"status\":1,\"taskId\":\"ooxdjowx8ikwk5mj673aa0130000000000000093\"}");
        }
    }

}
