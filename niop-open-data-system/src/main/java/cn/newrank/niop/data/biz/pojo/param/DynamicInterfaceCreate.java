package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.model.Arg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceCreate {
    /**
     * 数据源ID
     */
    @NotBlank(message = "数据源配置ID(dcId)不能为空")
    private String dcId;

    /**
     * 接口名称
     */
    @NotBlank(message = "接口名称(name)不能为空")
    private String name;

    /**
     * 查询语句
     */
    @NotBlank(message = "查询语句(query)不能为空")
    private String query;

    /**
     * 查询参数
     */
    private List<@Valid Arg> args;

    /**
     * QPS 时间间隔
     */
    @NotNull(message = "QPS 时间间隔(refreshSeconds)不能为空")
    private Integer refreshPermits;

    /**
     *
     */
    @NotNull(message = "QPS 时间间隔(refreshSeconds)不能为空")
    private Integer refreshSeconds;

    /**
     * 负责人ID
     */
    @NotBlank(message = "负责人ID(maintainerId)不能为空")
    private String maintainerId;

    /**
     * 负责人名
     */
    @NotBlank(message = "负责人名(maintainerName)不能为空")
    private String maintainerName;

    private String description;

    public DynamicInterface toDto() {
        final DynamicInterface dynamicInterface = new DynamicInterface();
        dynamicInterface.setDcId(dcId);
        dynamicInterface.setName(name);
        dynamicInterface.setQuery(query);
        dynamicInterface.setArgs(args);
        dynamicInterface.setRefreshPermits(refreshPermits);
        dynamicInterface.setRefreshSeconds(refreshSeconds);
        dynamicInterface.setMaintainerId(maintainerId);
        dynamicInterface.setMaintainerName(maintainerName);
        dynamicInterface.setDescription(description);

        return dynamicInterface;
    }
}