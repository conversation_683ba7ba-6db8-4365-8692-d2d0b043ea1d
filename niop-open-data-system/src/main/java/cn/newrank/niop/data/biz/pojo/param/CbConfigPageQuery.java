package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.web.model.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/23 15:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CbConfigPageQuery extends PageQuery {

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 搜索类型
     */
    private String searchType;

    /**
     * 负责人
     */
    private String maintainer;

}
