package cn.newrank.niop.data.biz.export.factory;

import cn.newrank.niop.data.biz.export.handler.DataExportResultHandler;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DataExportResultHandlerFactory implements ApplicationContextAware {

    private final Map<DataExportType, DataExportResultHandler> handlerMap = new EnumMap<>(DataExportType.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        final Map<String, DataExportResultHandler> resultHandlerMap = applicationContext.getBeansOfType(DataExportResultHandler.class);

        for (Map.Entry<String, DataExportResultHandler> handler : resultHandlerMap.entrySet()) {
            final DataExportResultHandler value = handler.getValue();
            this.handlerMap.put(value.getDataSourceType(), value);
        }
    }

    public DataExportResultHandler getResultHandler(DataExportType exportType) {
        return Optional.ofNullable(this.handlerMap.get(exportType))
            .orElseThrow(() -> new IllegalArgumentException("未实现的导数类型: " + exportType.getDescription()));
    }

}
