package cn.newrank.niop.data.biz.biz.xhs.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/11 20:58:51
 */

@AllArgsConstructor
public enum XhsExpTaskEnum implements BizEnum {
    /**
     * 扩量任务状态
     */
    CREATE("0", "创建"),
    RUNNING("1", "执行中"),
    SUCCESS("2", "成功"),
    FAILED("-1", "失败"),
    TIMEOUT("-2", "超时"),
    ;

    private final String code;
    private final String description;

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
