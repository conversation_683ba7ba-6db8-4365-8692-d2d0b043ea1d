package cn.newrank.niop.data.biz.component.callback;


import cn.newrank.niop.data.biz.component.trace.Tracers;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.config.property.KafkaProperties;
import cn.newrank.niop.data.util.Hash;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.record.CompressionType;

import java.io.File;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/15 20:45
 */
@Log4j2
public class KafkaCallback implements Callback {

    public static final String KAFKA_REV_TOPIC = "kafka.rev.topic";
    public static final String KAFKA_REV_PARTITION = "kafka.rev.partition";
    public static final String KAFKA_REV_OFFSET = "kafka.rev.offset";
    private static final String PROTOCOL_SASL_PLAINTEXT = "SASL_PLAINTEXT";
    private static final String PROTOCOL_PLAINTEXT = "PLAINTEXT";
    private final ConfigProperties configProperties;
    private final String topic;
    private final Properties props;
    private final String cbId;
    // TODO: KafkaProducer 实例集中管理复用，不宜初始化过多
    private volatile KafkaProducer<String, String> producer;

    public KafkaCallback(ConfigProperties configProperties) {
        this.configProperties = configProperties;
        this.topic = configProperties.getCheckedString(ConfigKey.TOPIC);
        this.props = initProperties(configProperties);
        this.cbId = configProperties.getString(ConfigKey.ID);
    }

    public static KafkaCallback build(KafkaProperties.Config config) {
        ConfigProperties properties = config(config);
        return  new KafkaCallback(properties);
    }

    private static void setPassword(Properties properties, String username, String password) {
        String prefix = "org.apache.kafka.common.security.plain.PlainLoginModule";
        String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";"
                , prefix, username, password);
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
    }

    private static Properties initProperties(ConfigProperties config) {
        final Properties properties = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点
        String servers = config.getCheckedString(ConfigKey.KAFKA_BOOTSTRAP_SERVERS);
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);

        final String protocol = getProtocol(config);
        if (PROTOCOL_SASL_PLAINTEXT.equals(protocol)) {
            properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, PROTOCOL_SASL_PLAINTEXT);
            //Plain方式
            properties.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            setPassword(properties, config.getCheckedString(ConfigKey.USERNAME),
                    config.getCheckedString(ConfigKey.PASSWORD));
        }

        if (SystemConfig.getConfig().isDevelop()) {
            final File file = new File("kafka/api.kafka.client.truststore.jks");

            properties.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, file.getAbsolutePath());
            properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            properties.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, "KafkaOnsClient");
            setPassword(properties, config.getCheckedString(ConfigKey.USERNAME), config.getCheckedString(ConfigKey.PASSWORD));
            properties.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            properties.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");
        }

        //Kafka消息的序列化方式
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间
        properties.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 3 * 1000);
        //设置客户端内部重试次数
        properties.put(ProducerConfig.RETRIES_CONFIG, 5);
        //设置客户端内部重试间隔
        properties.put(ProducerConfig.RECONNECT_BACKOFF_MS_CONFIG, 3000);
        //构造Producer对象，注意，该对象是线程安全的，一般来说，一个进程内一个Producer对象即可；
        //如果想提高性能，可以多构造几个对象，但不要太多，最好不要超过5个
        properties.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10 * 1024 * 1024);

        // 设置消息压缩
        if (config.getBoolean(ConfigKey.COMPRESSION_ENABLED)) {
            properties.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, CompressionType.LZ4.name);
        }
        return properties;
    }

    private static String getProtocol(ConfigProperties config) {
        final String protocol = config.getCheckedString(ConfigKey.PROTOCOL);
        if (PROTOCOL_SASL_PLAINTEXT.equals(protocol) || PROTOCOL_PLAINTEXT.equals(protocol)) {
            return protocol;
        }

        throw createParamError("不支持协议{}", protocol);
    }

    private KafkaProducer<String, String> getProducer() {
        if (this.producer == null) {
            synchronized (this) {
                if (this.producer == null) {
                    try {
                        this.producer = new KafkaProducer<>(props);
                    } catch (Exception e) {
                        log.warn("kafka init error: ", e);
                        throw createDbError(e, "Kafka(topic: {})初始化失败", topic);
                    }
                }
            }
        }
        return this.producer;
    }

    @Override
    public ConfigProperties getConfig() {
        return configProperties;
    }

    @Override
    public String getUUID() {
        return Hash.sha256(configProperties.getCheckedString(ConfigKey.KAFKA_BOOTSTRAP_SERVERS) + topic);
    }

    @Override
    public boolean isActive() {
        try {
            return getProducer().partitionsFor(topic) != null;
        } catch (Exception e) {
            log.warn("Kafka topic {} isActive error", topic, e);
            close();
            return false;
        }
    }

    @Override
    public boolean callback(String data) {
        return callback(null, data);
    }


    public boolean callback(String key, String data) {
        return send(
                StringUtils.isBlank(key)
                        ? new ProducerRecord<>(topic, data)
                        : new ProducerRecord<>(topic, key, data)
        ) != null;
    }

    @Override
    public boolean callback(Collection<CallbackRedirect> callbackRedirects) {
        if (callbackRedirects == null || callbackRedirects.isEmpty()) {
            return true;
        }
        for (CallbackRedirect callbackRedirect : callbackRedirects) {
            if (!callback(callbackRedirect)) {
                return false;
            }
        }

        return true;
    }

    @Override
    public List<CallbackRedirect> callbackReturnError(Collection<CallbackRedirect> callbackRedirects) {
        if (callbackRedirects == null || callbackRedirects.isEmpty()) {
            return Collections.emptyList();
        }

        List<CallbackRedirect> errorList = new ArrayList<>();

        for (CallbackRedirect callbackRedirect : callbackRedirects) {
            try {
                if (!callback(callbackRedirect)) {
                    errorList.add(callbackRedirect);
                }
            } catch (Exception e) {
                errorList.add(callbackRedirect);
            }
        }

        // 返回异常的回调
        return errorList;
    }

    private RecordMetadata send(ProducerRecord<String, String> producerRecord) {
        final Future<RecordMetadata> future = getProducer().send(producerRecord);

        try {
            return future.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Kafka topic {} callback interrupt", topic, e);
        } catch (ExecutionException e) {
            log.warn("Kafka topic {} callback error", topic, e);
        }

        return null;
    }

    @Override
    public boolean callback(CallbackRedirect callbackRedirect) {
        final Span span = Tracers.getKafkaCbSpan(callbackRedirect, cbId);
        try {
            final ConsumerRecord<String, String> consumerRecord = callbackRedirect.getConsumerRecord();
            final String dataJSONString = callbackRedirect.toPayloadJSONString();

            final ProducerRecord<String, String> producerRecord;
            if (consumerRecord != null) {
                producerRecord = StringUtils.isBlank(consumerRecord.key())
                        ? new ProducerRecord<>(topic, dataJSONString)
                        : new ProducerRecord<>(topic, consumerRecord.key(), dataJSONString);
            } else {
                producerRecord = new ProducerRecord<>(topic, dataJSONString);
            }

            final RecordMetadata metadata = send(producerRecord);
            if (metadata == null) {
                span.setStatus(StatusCode.ERROR);
                return false;
            }

            span.setAttribute(KAFKA_REV_TOPIC, metadata.topic())
                    .setAttribute(KAFKA_REV_PARTITION, metadata.partition())
                    .setAttribute(KAFKA_REV_OFFSET, metadata.offset());

            return true;
        } catch (Exception e) {
            log.error("Kafka topic {} callback error", topic, e);
            span.setStatus(StatusCode.ERROR)
                    .recordException(e);
            throw e;
        } finally {
            span.setStatus(StatusCode.OK)
                    .end();
        }
    }


    @Override
    public void close() {
        if (producer != null) {
            try {
                producer.close(Duration.ofSeconds(3));
            } catch (Exception e) {
                log.warn("Kafka topic {} close error", topic, e);
            }
        }
    }

    /**
     * 将配置转为
     *
     * @param config producer 可用的config
     * @return 可用的config
     */
    public static ConfigProperties config(KafkaProperties.Config config) {
        return key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> config.getBootstrapServers();
            case TOPIC -> config.getTopic();
            case PROTOCOL -> config.isPlaintext() ? PROTOCOL_PLAINTEXT : PROTOCOL_SASL_PLAINTEXT;
            case USERNAME -> config.getUsername();
            case PASSWORD -> config.getPassword();
            default -> null;
        };
    }
}
