package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.analysis.callback.BusinessKafkaCallback;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyOpus;
import cn.newrank.niop.data.biz.biz.dy.pojo.YsDyOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.newrank.niop.util.U.isEmpty;
import static cn.newrank.niop.util.U.toList;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Log4j2
public class DyOpusService {
    public static final String QUERY_DY_NULL_INIT_BIZ_OPUS_TML = """
            SELECT
                 aweme_id,
                 title,
                 pub_time,
                 task_category,
                 brand_id,
                 brand_name,
                 blogger_uid,
                 update_status,
                 update_time,
                 play_cnt,
                 like_cnt,
                 comment_cnt,
                 share_cnt,
                 collect_cnt,
                 aweme_type
            FROM
                 dws_dy_works_info
                      
            ORDER BY
                 aweme_id
            LIMIT %s OFFSET %s
            """;

    public static final String QUERY_DY_BIZ_OPUS_TML = """
            SELECT
                 aweme_id,
                 title,
                 pub_time,
                 task_category,
                 brand_id,
                 brand_name,
                 blogger_uid,
                 update_status,
                 update_time,
                 play_cnt,
                 like_cnt,
                 comment_cnt,
                 share_cnt,
                 collect_cnt,
                 aweme_type
            FROM
                 dws_dy_works_info
            WHERE
                 update_time >= '%s'
            ORDER BY
                 aweme_id
            LIMIT %s OFFSET %s
            """;

    final BeanPropertyRowMapper<YsDyOpus> dyOpusRowMapper;
    private final HoloFactory holoFactory;
    private final DsConfigManager dsConfigManager;
    private final BusinessKafkaCallback businessKafkaCallback;

    public DyOpusService(
            DsConfigManager dsConfigManager,
            BusinessKafkaCallback businessKafkaCallback) {
        this.holoFactory = HoloFactory.DEFAULT;
        this.dsConfigManager = dsConfigManager;
        this.dyOpusRowMapper = BeanPropertyRowMapper.newInstance(YsDyOpus.class);
        this.businessKafkaCallback = businessKafkaCallback;
    }

    public void save(List<DyOpus> opuses) {
        businessKafkaCallback.sendBatch(opuses);
    }


    public ReturnT<String> syncBizOpus(String startTime) {
        boolean init = true;
        if (StrUtil.isBlank(startTime)) {
            init = false;
            startTime = DateTimeUtil.format(LocalDate.now().minusDays(1));
        }

        int total = 0;
        try (final HoloFactory.Holo holo = holoFactory.create(dsConfigManager.chooseYsHoloConfig())) {
            final NamedParameterJdbcTemplate jdbcTemplate = holo.availableJdbcTemplate();

            int offset = 0;
            AtomicInteger updateCount = new AtomicInteger();
            AtomicInteger deltaCount = new AtomicInteger();
            while (true) {
                final String sql = init ? QUERY_DY_NULL_INIT_BIZ_OPUS_TML.formatted(200, offset)
                        : QUERY_DY_BIZ_OPUS_TML.formatted(startTime, 200, offset);
                final List<YsDyOpus> dyOpuses = jdbcTemplate.query(sql, dyOpusRowMapper);

                if (isEmpty(dyOpuses)) {
                    break;
                }
                dyOpuses.stream()
                        .collect(Collectors.
                                partitioningBy(ksOpus -> Objects.nonNull(ksOpus.getAweme_type())
                                        && ksOpus.getAweme_type() == 1))
                        .forEach((isBusiness, opusList) -> {
                                    if (!opusList.isEmpty() && isBusiness) {
                                            List<DyOpus> list = toList(opusList, YsDyOpus::toDyOpus);
                                            // 统一设置更新时间为 now
                                            Timestamp now = Timestamp.from(Instant.now());
                                            list.forEach(item ->
                                                    {
                                                        if (Objects.isNull(item.getSUpdateTime())) {
                                                            item.setSUpdateTime(now);
                                                        }
                                                    }
                                            );
                                            save(list);
                                            updateCount.addAndGet(opusList.size());
                                        }
                                }
                        );

                offset += dyOpuses.size();
                total += dyOpuses.size();
                XxlJobLogger.log("已经同步获取了{},删除{},更新{}", total, deltaCount, updateCount);
            }
        } catch (Exception e) {
            log.error("Dy商业作品数据同步失败, e: ", e);
        }


        return ReturnT.SUCCESS;
    }
}
