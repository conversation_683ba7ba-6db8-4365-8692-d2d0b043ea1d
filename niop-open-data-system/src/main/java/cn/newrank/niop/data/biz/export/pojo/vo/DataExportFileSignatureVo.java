package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.oss.pojo.dto.OssSignature;
import cn.newrank.nrcore.utils.UuidUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DataExportFileSignatureVo extends OssSignature {

    /**
     * 临时文件名前缀
     */
    private String fileNamePrefix;

    public static DataExportFileSignatureVo buildBy(OssSignature ossSignature) {
        DataExportFileSignatureVo vo = new DataExportFileSignatureVo();
        vo.setAccessKey(ossSignature.getAccessKey());
        vo.setPolicy(ossSignature.getPolicy());
        vo.setSignature(ossSignature.getSignature());
        vo.setHost(ossSignature.getHost());
        vo.setDir(ossSignature.getDir());
        vo.setExpireTime(ossSignature.getExpireTime());
        vo.setFileNamePrefix(UuidUtils.getUuid(12).toLowerCase());
        return vo;
    }

}
