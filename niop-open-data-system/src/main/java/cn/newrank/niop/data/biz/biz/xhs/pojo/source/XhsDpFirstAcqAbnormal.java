package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.nrcore.json.JsonField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
public class XhsDpFirstAcqAbnormal {

    @JsonField("gmt_create")
    private String gmtCreate;
    @JsonField("video_duration")
    private Integer videoDuration;
    @JsonField("video_height")
    private Integer videoHeight;
    @JsonField("liked_count")
    private Long likedCount;
    @JsonField("share_info_link")
    private String shareInfoLink;
    @JsonField("video_width")
    private Integer videoWidth;
    @JsonField("title")
    private String title;
    @JsonField("type")
    private String type;
    @JsonField("userid")
    private String userid;
    @JsonField("cover")
    private String cover;
    @JsonField("ip_location_web")
    private String ipLocationWeb;
    @<PERSON>sonField("video_url")
    private String videoUrl;
    @J<PERSON><PERSON>ield("id")
    private String opusId;
    @JsonField("images_list")
    private String imagesList;
    @JsonField("note_counter_type_v2")
    private String noteCounterTypeV2;
    @JsonField("note_counter_type_v1")
    private String noteCounterTypeV1;
    @JsonField("is_visible")
    private Integer isVisible;
    @JsonField("create_time")
    private String createTime;
    @JsonField("topics")
    private String topics;
    @JsonField("acq_aweme_detail")
    private Integer acqAwemeDetail;
    @JsonField("official_keyword")
    private String officialKeyword;
    @JsonField("ana_time")
    private String anaTime;
    @JsonField("interactive_count")
    private Long interactiveCount;
    @JsonField("is_delete")
    private Integer isDelete;
    @JsonField("collected_count")
    private Long collectedCount;
    @JsonField("comments_count")
    private Long commentsCount;
    @JsonField("shared_count")
    private Long sharedCount;
    @JsonField("time")
    private String time;
    @JsonField("desc")
    private String desc;
    @JsonField("video_id")
    private String videoId;
    @JsonField("is_cooperate")
    private Integer isCooperate;
    @JsonField("cooperate_name")
    private String cooperateName;
    @JsonField("cooperate_id")
    private String cooperateId;
    @JsonField("poi_name")
    private String poiName;
    @JsonField("official_warn_msg")
    private String officialWarnMsg;
    @JsonField("discern_business_brand_id")
    private String discernBusinessBrandId;
    @JsonField("discern_business_brand_name")
    private String discernBusinessBrandName;
    @JsonField("seed_brand_id")
    private String seedBrandId;
    @JsonField("seed_brand_name")
    private String seedBrandName;
    @JsonField("video_played_count")
    private Long videoPlayedCount;
    @JsonField("last_update_time")
    private String lastUpdateTime;

}
