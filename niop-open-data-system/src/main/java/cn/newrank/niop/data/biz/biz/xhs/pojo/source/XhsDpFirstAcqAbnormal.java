package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
public class XhsDpFirstAcqAbnormal {

    @JsonField("gmt_create")
    private String gmtCreate;
    @JsonField("video_duration")
    private Integer videoDuration;
    @JsonField("video_height")
    private Integer videoHeight;
    @JsonField("liked_count")
    private Long likedCount;
    @JsonField("share_info_link")
    private String shareInfoLink;
    @JsonField("video_width")
    private Integer videoWidth;
    @JsonField("title")
    private String title;
    @JsonField("type")
    private String type;
    @JsonField("userid")
    private String userid;
    @JsonField("cover")
    private String cover;
    @JsonField("ip_location_web")
    private String ipLocationWeb;
    @J<PERSON><PERSON>ield("video_url")
    private String videoUrl;
    @JsonField("id")
    private String opusId;
    @JsonField("images_list")
    private String imagesList;
    @JsonField("note_counter_type_v2")
    private String noteCounterTypeV2;
    @JsonField("note_counter_type_v1")
    private String noteCounterTypeV1;
    @JsonField("is_visible")
    private Integer isVisible;
    @JsonField("create_time")
    private String createTime;
    @JsonField("topics")
    private String topics;
    @JsonField("acq_aweme_detail")
    private Integer acqAwemeDetail;
    @JsonField("official_keyword")
    private String officialKeyword;
    @JsonField("ana_time")
    private String anaTime;
    @JsonField("interactive_count")
    private Long interactiveCount;
    @JsonField("is_delete")
    private Integer isDelete;
    @JsonField("collected_count")
    private Long collectedCount;
    @JsonField("comments_count")
    private Long commentsCount;
    @JsonField("shared_count")
    private Long sharedCount;
    @JsonField("time")
    private String time;
    @JsonField("desc")
    private String desc;
    @JsonField("video_id")
    private String videoId;
    @JsonField("is_cooperate")
    private Integer isCooperate;
    @JsonField("cooperate_name")
    private String cooperateName;
    @JsonField("cooperate_id")
    private String cooperateId;
    @JsonField("poi_name")
    private String poiName;
    @JsonField("official_warn_msg")
    private String officialWarnMsg;
    @JsonField("discern_business_brand_id")
    private String discernBusinessBrandId;
    @JsonField("discern_business_brand_name")
    private String discernBusinessBrandName;
    @JsonField("seed_brand_id")
    private String seedBrandId;
    @JsonField("seed_brand_name")
    private String seedBrandName;
    @JsonField("video_played_count")
    private Long videoPlayedCount;
    @JsonField("last_update_time")
    private String lastUpdateTime;
    @JsonField("poi_id")
    private String poiId;

    public static XhsDpFirstAcqAbnormal parse(JSONObject item){
        XhsDpFirstAcqAbnormal dpBase = new XhsDpFirstAcqAbnormal();
        dpBase.setGmtCreate(item.getString("gmt_create"));
        dpBase.setVideoDuration(item.getInteger("video_duration"));
        dpBase.setVideoHeight(item.getInteger("video_height"));
        dpBase.setLikedCount(item.getLong("liked_count"));
        dpBase.setShareInfoLink(item.getString("share_info_link"));
        dpBase.setVideoWidth(item.getInteger("video_width"));
        dpBase.setTitle(item.getString("title"));
        dpBase.setType(item.getString("type"));
        dpBase.setUserid(item.getString("userid"));
        dpBase.setCover(item.getString("cover"));
        dpBase.setIpLocationWeb(item.getString("ip_location_web"));
        dpBase.setVideoUrl(item.getString("video_url"));
        dpBase.setOpusId(item.getString("id"));
        dpBase.setImagesList(item.getString("images_list"));
        dpBase.setNoteCounterTypeV2(item.getString("note_counter_type_v2"));
        dpBase.setNoteCounterTypeV1(item.getString("note_counter_type_v1"));
        dpBase.setIsVisible(item.getInteger("is_visible"));
        dpBase.setCreateTime(item.getString("create_time"));
        dpBase.setTopics(item.getString("topics"));
        dpBase.setAcqAwemeDetail(item.getInteger("acq_aweme_detail"));
        dpBase.setOfficialKeyword(item.getString("official_keyword"));
        dpBase.setAnaTime(item.getString("ana_time"));
        dpBase.setInteractiveCount(item.getLong("interactive_count"));
        dpBase.setIsDelete(item.getInteger("is_delete"));
        dpBase.setCollectedCount(item.getLong("collected_count"));
        dpBase.setCommentsCount(item.getLong("comments_count"));
        dpBase.setSharedCount(item.getLong("shared_count"));
        dpBase.setTime(item.getString("time"));
        dpBase.setDesc(item.getString("desc"));
        dpBase.setVideoId(item.getString("video_id"));
        dpBase.setIsCooperate(item.getInteger("is_cooperate"));
        dpBase.setCooperateName(item.getString("cooperate_name"));
        dpBase.setCooperateId(item.getString("cooperate_id"));
        dpBase.setPoiName(item.getString("poi_name"));
        dpBase.setOfficialWarnMsg(item.getString("official_warn_msg"));
        dpBase.setDiscernBusinessBrandId(item.getString("discern_business_brand_id"));
        dpBase.setDiscernBusinessBrandName(item.getString("discern_business_brand_name"));
        dpBase.setSeedBrandId(item.getString("seed_brand_id"));
        dpBase.setSeedBrandName(item.getString("seed_brand_name"));
        dpBase.setVideoPlayedCount(item.getLong("video_played_count"));
        dpBase.setLastUpdateTime(item.getString("last_update_time"));
        dpBase.setPoiId(item.getString("poi_id"));
        return dpBase;
    }

}
