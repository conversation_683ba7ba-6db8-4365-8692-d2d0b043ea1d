package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.dto.Dictionary;
import cn.newrank.niop.data.biz.pojo.param.ColumnAliasesUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryRemarkUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryTagUpdate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 14:53
 */
public interface DictionaryService {

    /**
     * 启动同步
     */
    void startSync();

    /**
     * 启动同步数据源
     *
     * @param dcId 数据源ID
     * @return 是否成功
     */
    boolean startDsSync(String dcId);

    /**
     * 编辑字典的标签
     *
     * @return 是否成功
     */
    boolean updateTags(DictionaryTagUpdate tagUpdate);

    /**
     * 编辑字典的备注
     *
     * @return 是否成功
     */
    boolean updateRemark(DictionaryRemarkUpdate remarkUpdate);

    /**
     * 编辑字典的列别名
     *
     * @return 是否成功
     */
    boolean updateColumnAliases(ColumnAliasesUpdate columnAliasesUpdate);

    /**
     * 获取字典信息
     *
     * @param dicId 字典ID
     * @return 字典信息
     */
    Dictionary get(String dicId);

    /**
     * 获取集合字典信息
     *
     * @param dcId       数据源ID
     * @param collection 集合名称
     * @return 是否成功
     */
    Dictionary get(String dcId, String collection);
}
