package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.biz.component.biz.*;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.pojo.dto.StorageResult;
import cn.newrank.niop.data.biz.pojo.param.StorageHistoryPageQuery;
import cn.newrank.niop.data.biz.pojo.param.StorageQuery;
import cn.newrank.niop.data.biz.service.StorageService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 19:15
 */
@Service
@Log4j2
public class StorageServiceImpl implements StorageService {

    private final HistoryStorageService historyStorageService;
    private final StorageBizServiceContext storageBizServiceContext;

    public StorageServiceImpl(HistoryStorageService historyStorageService,
                              StorageBizServiceContext storageBizServiceContext) {
        this.historyStorageService = historyStorageService;
        this.storageBizServiceContext = storageBizServiceContext;
    }


    @Override
    public void storeBatch(StorageBiz storageBiz, List<JSONObject> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        final StorageBizService<? extends StorageEntity> storageBizService = storageBizServiceContext.getService(storageBiz);

        // fix: 样本数据必须深拷贝
        String jsonString = items.get(0).toJSONString();
        final JSONObject sample = JSONObject.parseObject(jsonString);
        if (storageBizService.castOf(sample) instanceof StorageVersionEntity) {
            // 存储版本数据
            for (JSONObject item : items) {
                final StorageVersionEntity storageVersionEntity = (StorageVersionEntity) storageBizService.castOf(item);
                final StorageEntity lastEntity = storageBizService.get(storageVersionEntity.identifier());

                int lastVersion = 1;
                if (lastEntity instanceof StorageVersionEntity lastVersionEntity) {
                    // 如果数据没有更新，则不存储
                    if (lastVersionEntity.versionUpdateTime() == storageVersionEntity.versionUpdateTime()) {
                        continue;
                    } else {
                        lastVersion = lastVersionEntity.getVersion() + 1;
                    }
                }

                // 存储历史数据
                historyStorageService.storeHistory(storageBiz, lastEntity);

                // 设置版本号
                storageVersionEntity.setVersion(lastVersion);
                storageBizService.store(storageVersionEntity);
            }
        } else {
            storageBizService.storeJSONBatch(items);
        }
    }

    @Override
    public void store(List<CallbackRedirect> storages) {
        if (storages == null || storages.isEmpty()) {
            return;
        }

        final EnumMap<StorageBiz, List<JSONObject>> category = new EnumMap<>(StorageBiz.class);
        for (CallbackRedirect storage : storages) {
            final Object payload = storage.payload();
            if (payload instanceof JSONObject json) {
                category.computeIfAbsent(StorageBiz.ofJSONValue(storage.getStorageBiz()), storageBiz -> new ArrayList<>())
                        .add(json);
            }
        }

        category.forEach(this::storeBatch);
    }


    @Override
    public StorageResult get(StorageBiz storageBiz, String identifier, int version) {
        final StorageBizService<? extends StorageEntity> service = storageBizServiceContext.getService(storageBiz);
        final StorageEntity entity = service.get(identifier);
        if (entity == null) {
            return null;
        }

        if (entity instanceof StorageVersionEntity versionEntity) {
            if (version < 1 || versionEntity.getVersion() == version) {
                final StorageResult result = new StorageResult();

                result.setData(versionEntity.toJSONString());
                result.setIdentifier(versionEntity.identifier());
                result.setVersion(versionEntity.getVersion());
                result.setUpdateTime(versionEntity.versionUpdateTime());

                return result;
            }

            return historyStorageService.get(storageBiz, identifier, version);
        }

        final StorageResult result = new StorageResult();

        result.setData(entity.toJSONString());
        result.setIdentifier(entity.identifier());
        result.setVersion(1);
        result.setUpdateTime(0);

        return result;
    }

    @Override
    public StorageHistory getHistory(StorageBiz storageBiz, String identifier) {
        final StorageBizService<? extends StorageEntity> service = storageBizServiceContext.getService(storageBiz);
        final StorageEntity entity = service.get(identifier);


        final StorageHistory history = historyStorageService.getHistory(storageBiz, identifier);

        if (entity instanceof StorageVersionEntity versionEntity) {
            history.getHistories().addFirst(new StorageHistory.History(versionEntity.getVersion(),
                    versionEntity.versionUpdateTime(), versionEntity.toJSONObject()));
        } else {
            history.getHistories().addFirst(new StorageHistory.History(1,
                    0L, entity.toJSONObject()));
        }

        return history;
    }


    @Override
    public List<? extends StorageEntity> listLastedEntities(StorageQuery storageQuery) {
        return storageBizServiceContext.getService(storageQuery.getStorageBiz())
                .list(storageQuery.getIdentifiers());
    }

    @Override
    public StorageHistory getHistories(StorageHistoryPageQuery pageQuery) {
        return historyStorageService.getHistories(pageQuery);
    }
}
