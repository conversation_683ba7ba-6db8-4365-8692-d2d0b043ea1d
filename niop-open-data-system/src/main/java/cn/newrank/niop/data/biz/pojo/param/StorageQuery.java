package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/15 9:48
 */
@Data
public class StorageQuery {
    /**
     * 存储标识符
     */
    @NotEmpty(message = "存储标识符(identifiers)不能为空")
    @Size(max = 200, message = "存储标识符(identifiers)长度不能超过200")
    List<String> identifiers;
    /**
     * 存储业务
     */
    @NotNull(message = "存储业务(storageBiz)不能为空")
    StorageBiz storageBiz;
}
