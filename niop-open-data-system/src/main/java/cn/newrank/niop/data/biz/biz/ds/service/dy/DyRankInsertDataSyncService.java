package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DyEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_DY_RANK_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class DyRankInsertDataSyncService implements SyncBizService<DyEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public DyRankInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseDyEsConfig();
    }

    @Override
    public String getIndexName() {
        return "xd-lower:search_douyin_user_rank_week";
    }


    @Override
    public String getRangField() {
        return "newrank_index";
    }


    @Override
    public String getRangIndex() {
        return "rank_date";
    }

    @Override
    public String getUniqueIndex() {
        return MAIN_DY_RANK_FIELD_MAPPING.get("account_id");
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_DY_RANK_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        sourceFields.add("unique_id");
        sourceFields.add("short_id");
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<DyEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), DyEsMetaData.class);
        });
    }

    @Override
    public List<DyEsMetaData> convertData(List<DyEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (DyEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_DY_RANK_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", "dy");

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", "dy" + "_" + mainMap.get("account_id"));
            }
            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), DyEsMetaData.class);
    }
}
