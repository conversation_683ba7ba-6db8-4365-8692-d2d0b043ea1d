package cn.newrank.niop.data.biz.component.sync;

import cn.newrank.niop.data.biz.component.sync.model.State;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.data.util.IPv4;
import com.alibaba.nacos.common.executor.NameThreadFactory;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 同步器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 15:24
 */
@Log4j2
public abstract class AbstractHistorySynchronizer<T, C extends Cursor<T>>
        implements HistorySynchronizer {
    protected static volatile ThreadPoolExecutor executor;
    protected final String currentAddress;
    protected final CursorStorage<T, C> cursorStorage;
    protected final int maxRunningCount;
    protected final Map<String, CursorSync<T, C>> runningCursors;
    protected final AtomicBoolean closed = new AtomicBoolean(false);
    protected final String name;

    protected AbstractHistorySynchronizer(String name, CursorStorage<T, C> cursorStorage, int maxRunningCount) {
        this.maxRunningCount = maxRunningCount;
        this.name = name;
        this.currentAddress = IPv4.local();
        this.cursorStorage = cursorStorage;
        this.runningCursors = new ConcurrentHashMap<>();
    }

    protected static ThreadPoolExecutor getExecutor() {
        if (executor != null) {
            return executor;
        }

        synchronized (AbstractHistorySynchronizer.class) {
            if (executor == null) {
                executor = new ThreadPoolExecutor(0, 100,
                        2, TimeUnit.MINUTES,
                        new SynchronousQueue<>(),
                        new NameThreadFactory("history-sync-"),
                        new ThreadPoolExecutor.AbortPolicy()
                );
            }
        }

        return executor;
    }

    /**
     * 新建一个同步游标
     *
     * @return 同步游标
     */
    protected abstract C newCursor();

    @Override
    public boolean newSync(LocalDateTime startTime, LocalDateTime endTime, Duration partitionInterval) {
        LocalDateTime cursor = startTime;
        while (cursor.isBefore(endTime)) {
            LocalDateTime start = cursor;

            cursor = cursor.plusSeconds(partitionInterval.toSeconds());
            if (cursor.isAfter(endTime)) {
                newSync(start, endTime);
            } else {
                newSync(start, cursor);
            }
        }

        return true;
    }

    @Override
    public boolean newSync(LocalDateTime startTime, LocalDateTime endTime) {
        final C cursor = newCursor();
        cursor.setStartTime(DateTimeUtil.format(startTime));
        cursor.setEndTime(DateTimeUtil.format(endTime));
        cursor.setState(Cursor.STATE_UNSTARTED);
        cursorStorage.save(cursor);

        return true;
    }

    @Override
    public boolean newSync(String start, String end) {
        final C cursor = newCursor();
        cursor.setStart(start);
        cursor.setEnd(end);
        cursor.setState(Cursor.STATE_UNSTARTED);
        cursorStorage.save(cursor);

        return true;
    }

    @Override
    public List<C> getProgress() {
        return cursorStorage.getAll();
    }

    @Override
    public boolean operate(String key, State state) {
        final C c = cursorStorage.get(key);
        if (c == null) {
            return false;
        }

        switch (state) {
            case STOP -> {
                c.setState(Cursor.STATE_FINISHED);
                c.setError("手动停止");

                cursorStorage.update(c);
            }
            case RESET -> {
                if (!shouldStart(c)) {
                    return false;
                }

                c.setState(Cursor.STATE_UNSTARTED);
                cursorStorage.update(c);
            }

            case DELETE -> {
                if (Cursor.STATE_FINISHED.equals(c.getState())) {
                    cursorStorage.remove(c.getKey());
                }
            }
        }

        return true;
    }

    private boolean shouldStart(C cursor) {
        final boolean timeout = DateTimeUtil.toDateTime(cursor.getUpdateTime())
                .isBefore(LocalDateTime.now().plusMinutes(5));
        return timeout || !cursor.isStarted();
    }

    @Override
    public synchronized void schedule() {
        final List<C> cs = cursorStorage.getStarted();

        int runningCount = 0;
        for (C c : cs) {
            if (c.isPaused()) {
                startSync(c);
                log.info("继续同步({})...", c.getKey());
            }

            if (++runningCount > maxRunningCount) {
                return;
            }
        }

        final int i = maxRunningCount - runningCount;
        if (i < 1) {
            return;
        }

        for (C c : cursorStorage.getUnstarted(i)) {
            startSync(c);
            log.info("({}->{})开始同步...", getName(), c.getKey());
        }
    }

    @Override
    public String getName() {
        return name;
    }

    private void startSync(C cursor) {
        runningCursors.computeIfAbsent(cursor.getKey(), key -> {
            cursor.setAddress(currentAddress);
            cursor.setState(Cursor.STATE_STARTED);
            cursor.setError(null);
            cursorStorage.update(cursor);

            final CursorSync<T, C> cursorSync = new CursorSync<>(name,cursor, cursorStorage) {
                @Override
                protected int sync(C cursor) {
                    return AbstractHistorySynchronizer.this.sync(cursor);
                }

                @Override
                protected void afterSync() {
                    runningCursors.remove(cursor.getKey());
                }
            };

            getExecutor().execute(cursorSync);

            return cursorSync;
        });
    }

    /**
     * 数据同步
     *
     * @param cursor 游标
     * @return 本次同步数据量(0 : 同步完成, > 0 : 继续同步, < 0 : 忽略 ， 执行下次)
     */
    protected abstract int sync(C cursor);

    @Override
    public final void close() {
        if (closed.compareAndSet(false, true)) {
            runningCursors.values().forEach(CursorSync::stop);
        }
    }
}
