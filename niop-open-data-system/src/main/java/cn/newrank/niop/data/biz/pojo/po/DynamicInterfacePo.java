package cn.newrank.niop.data.biz.pojo.po;

import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.model.Arg;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfacePo {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private Timestamp gmtModified;

    /**
     *
     */
    private Timestamp gmtCreate;

    /**
     * 接口ID
     */
    private String interfaceId;

    /**
     * 数据源ID
     */
    private String dcId;

    /**
     * 接口名称
     */
    private String name;

    /**
     * 查询语句
     */
    private String query;

    /**
     * 查询参数
     */
    private String args;

    /**
     * QPS 时间间隔
     */
    private Integer refreshPermits;

    /**
     *
     */
    private Integer refreshSeconds;

    /**
     * 负责人ID
     */
    private String maintainerId;

    /**
     *
     */
    private String maintainerName;
    private String description;

    public static DynamicInterfacePo fromDto(DynamicInterface dynamicInterface) {
        final DynamicInterfacePo interfacePo = new DynamicInterfacePo();
        interfacePo.setInterfaceId(dynamicInterface.getInterfaceId());
        interfacePo.setDcId(dynamicInterface.getDcId());
        interfacePo.setName(dynamicInterface.getName());
        interfacePo.setQuery(dynamicInterface.getQuery());
        interfacePo.setRefreshPermits(dynamicInterface.getRefreshPermits());
        interfacePo.setRefreshSeconds(dynamicInterface.getRefreshSeconds());
        interfacePo.setMaintainerId(dynamicInterface.getMaintainerId());
        interfacePo.setMaintainerName(dynamicInterface.getMaintainerName());
        interfacePo.setDescription(dynamicInterface.getDescription());
        interfacePo.setArgs(dynamicInterface.getArgs() == null ? "[]" : JSON.toJSONString(dynamicInterface.getArgs()));

        return interfacePo;
    }

    public DynamicInterface toDto() {
        final DynamicInterface dynamicInterface = new DynamicInterface();

        dynamicInterface.setArgs(JSON.parseArray(this.args, Arg.class));
        dynamicInterface.setDcId(this.dcId);
        dynamicInterface.setDescription(this.description);
        dynamicInterface.setInterfaceId(this.interfaceId);
        dynamicInterface.setMaintainerId(this.maintainerId);
        dynamicInterface.setMaintainerName(this.maintainerName);
        dynamicInterface.setName(this.name);
        dynamicInterface.setQuery(this.query);
        dynamicInterface.setRefreshPermits(this.refreshPermits);
        dynamicInterface.setRefreshSeconds(this.refreshSeconds);
        dynamicInterface.setGmtCreate(this.gmtCreate.toLocalDateTime());
        dynamicInterface.setGmtModified(this.gmtModified.toLocalDateTime());

        return dynamicInterface;
    }
}