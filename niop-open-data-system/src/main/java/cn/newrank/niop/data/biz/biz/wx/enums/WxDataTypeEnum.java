package cn.newrank.niop.data.biz.biz.wx.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/11 20:58:51
 */

@AllArgsConstructor
public enum WxDataTypeEnum implements BizEnum {
    /**
     * 数据类型
     */
    AWEME_BASE("aweme_base", "wx_aweme_base_short_long","作品短长链基础信息")
    ;

    private final String code;
    private final String dbCode;
    private final String description;

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return dbCode;
    }


    public static WxDataTypeEnum getByJsonCode(String code) {
        for (WxDataTypeEnum value : values()) {
            if (value.getJsonValue().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static WxDataTypeEnum getByDbCode(String code) {
        for (WxDataTypeEnum value : values()) {
            if (value.getDbCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
