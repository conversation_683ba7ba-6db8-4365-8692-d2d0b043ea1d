package cn.newrank.niop.data.biz.biz.huya.service;

import cn.newrank.niop.data.biz.biz.huya.mapper.HuyaLiveMapper;
import cn.newrank.niop.data.biz.biz.huya.pojo.HuyaLive;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 14:30
 */
@Service
public class HuyaLiveService implements StorageBizService<HuyaLive> {

    private final HuyaLiveMapper huyaLiveMapper;

    public HuyaLiveService(HuyaLiveMapper huyaLiveMapper) {
        this.huyaLiveMapper = huyaLiveMapper;
    }

    @Override
    public List<HuyaLive> list(List<String> identifiers) {
        return huyaLiveMapper.list(identifiers);
    }

    @Override
    public void storeBatch(List<HuyaLive> items) {
        huyaLiveMapper.saveAll(items);
    }

    @Override
    public HuyaLive castOf(JSONObject item) {
        final JSONObject data = item.getJSONObject("data");
        final HuyaLive huyaLive = data.to(HuyaLive.class);

        huyaLive.setSampleId(item.getString("sampleId"));
        huyaLive.setSampleStatus(item.getString("sampleStatus"));
        huyaLive.setUpdateTime(item.getLongValue("updateTime"));

        return huyaLive;
    }

    public List<HuyaLive> listCalculateLives() {
        return huyaLiveMapper.listCalculateLives();
    }

    public void updateCalculateProps(HuyaLive live) {
        huyaLiveMapper.update(live);
    }
}
