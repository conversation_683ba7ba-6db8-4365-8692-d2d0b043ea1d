package cn.newrank.niop.data;


import cn.newrank.niop.sdk.http.configuration.HttpAutoConfiguration;
import cn.newrank.niop.web.config.WebAutoConfiguration;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 10:38
 */
@EnableDiscoveryClient
@SpringBootApplication
@ImportAutoConfiguration(WebAutoConfiguration.class)
@Import(HttpAutoConfiguration.class)
@EnableDubbo
@EnableAspectJAutoProxy(exposeProxy = true)
public class DataApplication {


    public static void main(String[] args) {
        SpringApplication.run(DataApplication.class, args);
    }
}