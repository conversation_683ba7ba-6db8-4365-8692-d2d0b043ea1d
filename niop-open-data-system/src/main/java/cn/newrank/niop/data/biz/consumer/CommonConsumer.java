package cn.newrank.niop.data.biz.consumer;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.common.DestroyWatcher;
import cn.newrank.niop.data.common.KafkaConsumers;
import cn.newrank.niop.data.config.property.KafkaProperties;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.errors.WakeupException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.Ordered;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * kafka 消费者基类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:26
 */
@Log4j2
public class CommonConsumer implements CommandLineRunner, DestroyWatcher.Destructible, Ordered {

    public static final String TOPIC_KEY = "_kafka_topic";
    public static final String PARTITION_KEY = "_kafka_partition";
    private final List<KafkaConsumer<String, String>> kafkaConsumers;
    private final AtomicBoolean isRunning = new AtomicBoolean(true);
    private final ThreadPoolExecutor executor;
    private final KafkaProperties.Config config;
    private final Environment environment;
    private final CountDownLatch runningCount;
    private final ConsumeService consumeService;

    public CommonConsumer(KafkaProperties.Config config,
                          Environment environment,
                          ConsumeService consumeService,
                          ThreadPoolExecutor executor) {
        this.kafkaConsumers = new CopyOnWriteArrayList<>();
        this.consumeService = consumeService;
        this.executor = executor;
        this.config = config;
        this.environment = environment;
        this.runningCount = new CountDownLatch(executor.getCorePoolSize());
    }

    /**
     * 重置消费偏移量
     *
     * @param kafkaConsumer   kafkaConsumer
     * @param consumerRecords 消费者记录
     */
    private static void resetOffset(KafkaConsumer<String, String> kafkaConsumer, ConsumerRecords<String, String> consumerRecords) {
        // 处理失败，回滚到这批消息的开始位置
        final Map<TopicPartition, Long> partitionOffsets = new HashMap<>();

        for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
            final TopicPartition tp = new TopicPartition(consumerRecord.topic(), consumerRecord.partition());
            partitionOffsets.merge(tp, consumerRecord.offset(), Math::min);
        }

        // 遍历所有涉及的分区并重置偏移量
        partitionOffsets.forEach(kafkaConsumer::seek);
    }

    public static String getStackTrace(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        return stringWriter.toString();
    }

    @Override
    public void run(String... args) {
        final int corePoolSize = executor.getCorePoolSize();

        for (int i = 0; i < corePoolSize; i++) {
            executor.execute(this::start);
        }

        log.info("CommonKafkaConsumer started");
    }

    private KafkaConsumer<String, String> createKafkaConsumer() {
        final KafkaConsumer<String, String> kafkaConsumer = KafkaConsumers.createKafkaConsumer(config, environment);
        kafkaConsumers.add(kafkaConsumer);
        return kafkaConsumer;
    }

    @Override
    public DestroyWatcher.Callable destroy() {
        isRunning.set(false);
        kafkaConsumers.forEach(KafkaConsumer::wakeup);
        executor.shutdown();

        return () -> runningCount == null || runningCount.getCount() == 0;
    }

    private void start() {
        try (KafkaConsumer<String, String> kafkaConsumer = createKafkaConsumer()) {
            while (isRunning.get()) {
                try {
                    final ConsumerRecords<String, String> consumerRecords = kafkaConsumer.poll(Duration.ofSeconds(5));
                    if (consumerRecords.isEmpty()) {
                        ThreadUtil.sleep(200);
                        continue;
                    }

                    boolean success = consumeWithoutException(consumerRecords);
                    if (success) {
                        kafkaConsumer.commitAsync();
                    } else {
                        if (config.isResetOffsetAfterFailed()) {
                            resetOffset(kafkaConsumer, consumerRecords);
                        }
                        ThreadUtil.sleep(30, TimeUnit.SECONDS);
                    }

                    waitBatchOps(consumerRecords.count());
                } catch (Exception e) {
                    if (e instanceof WakeupException) {
                        return;
                    }
                    ThreadUtil.sleep(10, TimeUnit.SECONDS);
                    log.error("Kafka消费失败", e);
                }
            }
        }

        runningCount.countDown();
        log.info("CommonKafkaConsumer stopped...");
    }

    /**
     * 消费，异常时返回false
     *
     * @param consumerRecords 消费者记录
     * @return true 消费成功
     */
    private boolean consumeWithoutException(ConsumerRecords<String, String> consumerRecords) {
        try {
            return consumeService.consume(consumerRecords);
        } catch (Exception e) {
            String message = e.getMessage();
            if (message == null && e.getCause() != null) {
                message = e.getCause().getMessage();
            }

            log.error("数据消费失败, 异常信息: {}, stackTrace: {}", message, getStackTrace(e));
            return false;
        }
    }

    private void waitBatchOps(int count) {
        int defaultPoll = 30;
        int maxPoll = Math.max(config.getMaxPoll(), defaultPoll);
        int max = Math.min(20, maxPoll / 2);
        int sleepTime = config.getSleepTime();
        if (count < max) {
            ThreadUtil.sleep(sleepTime, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
