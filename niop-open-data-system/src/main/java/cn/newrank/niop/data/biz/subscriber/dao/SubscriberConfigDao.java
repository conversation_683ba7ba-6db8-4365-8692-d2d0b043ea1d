package cn.newrank.niop.data.biz.subscriber.dao;

import cn.newrank.niop.data.biz.subscriber.dao.mapper.SubscriberConfigMapper;
import cn.newrank.niop.data.biz.subscriber.event.SubUpdateEvent;
import cn.newrank.niop.data.biz.subscriber.event.SubUpdateRefreshTopic;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.*;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigPageQuery;
import cn.newrank.niop.data.biz.subscriber.pojo.po.SubscriberConfigPo;
import cn.newrank.niop.web.model.PageView;
import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static cn.newrank.niop.util.U.toList;
import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 11:07
 */
@Component
@Log4j2
public class SubscriberConfigDao {

    public static final String NOOP_CB_ID = "noop_cb_id";
    private final SubscriberConfigMapper subscriberConfigMapper;
    private final RedissonClient redissonClient;
    private final SubUpdateRefreshTopic subUpdateRefreshTopic;
    /**
     * 本地缓存
     */
    ConcurrentMap<String, List<String>> subCbIdCache = new ConcurrentHashMap<>(128);
    ConcurrentMap<String, List<CbEnableParamSubCache>> subCbEnableParamCache = new ConcurrentHashMap<>(128);


    public SubscriberConfigDao(SubscriberConfigMapper subscriberConfigMapper,
                               RedissonClient redissonClient,
                               SubUpdateRefreshTopic subUpdateRefreshTopic) {
        this.subscriberConfigMapper = subscriberConfigMapper;
        this.redissonClient = redissonClient;
        this.subUpdateRefreshTopic = subUpdateRefreshTopic;
        this.subUpdateRefreshTopic.addListener((channel, msg) -> {
            clearCache(SubSourceType.ofJSONValue(msg.getSourceType()), msg.getSourceId());
            log.info("回调源刷新事件监听到, 刷新缓存");
        });

    }

    static String formatKey(String... items) {
        return String.join(":", items);
    }

    public void save(SubscriberConfig config) {
        final SubscriberConfigPo configPo = config.toPo();

        final SubscriberConfig dbConfig = getBy(config.getCbId(), config.getSourceType(), config.getSourceId());
        if (dbConfig != null) {
            throw createParamError("该配置信息(cbId: {}, sourceId:{})已存在", config.getCbId(), config.getSourceId());
        }
        try {
            subscriberConfigMapper.insert(configPo);
            subUpdateRefreshTopic.emitEvent(SubUpdateEvent.of(config.getSourceType(), config.getSourceId()));
        } catch (Exception e) {
            throw createDbError(e, "保存配置信息失败");
        }
    }

    public SubscriberConfig getBy(String cbId, SubSourceType sourceType, String sourceId) {
        final SubscriberConfigPo configPo = subscriberConfigMapper.getBy(cbId, sourceType, sourceId);
        if (configPo == null) {
            return null;
        }

        return configPo.toDto();
    }

    public SubscriberConfig get(String subscriberId) {
        final SubscriberConfigPo configPo = subscriberConfigMapper.get(subscriberId);

        return configPo == null ? null : configPo.toDto();
    }

    //删除数据
    @Transactional(rollbackFor = Exception.class)
    public void delete(SubscriberConfig config) {
        try {
            //数据库内删除
            subscriberConfigMapper.delete(config.getSubscriberId());
            subUpdateRefreshTopic.emitEvent(SubUpdateEvent.of(config.getSourceType(), config.getSourceId()));
        } catch (Exception e) {
            throw createDbError(e, "删除配置信息失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(SubscriberConfig config) {
        final SubscriberConfigPo configPo = config.toPo();
        final SubscriberConfigPo oldConfig = subscriberConfigMapper.get(config.getSubscriberId());
        if (oldConfig == null) {
            throw createParamError("该配置信息不存在");
        }

        final SubscriberConfig dbConfig = getBy(config.getCbId(), config.getSourceType(), config.getSourceId());
        if (dbConfig != null && !config.getSubscriberId().equals(dbConfig.getSubscriberId())) {
            throw createParamError("该配置信息(cbId: {}, sourceId:{})已存在", config.getCbId(), config.getSourceId());
        }

        try {
            subscriberConfigMapper.update(configPo);

            subUpdateRefreshTopic.emitEvent(SubUpdateEvent.of(config.getSourceType(), config.getSourceId()));
            subUpdateRefreshTopic.emitEvent(SubUpdateEvent.of(oldConfig.getSourceType(), oldConfig.getSourceId()));
        } catch (Exception e) {
            throw createDbError(e, "编辑配置信息失败");
        }
    }

    public PageView<SubscriberConfig> page(SubscriberConfigPageQuery pageQuery) {
        return PageView.of(subscriberConfigMapper.page(pageQuery, pageQuery.toMybatisPlusPage()))
                .convert(SubscriberConfigPo::toDto);
    }

    private void clearCache(SubSourceType sourceType, String sourceId) {
        // 删除redis缓存
        getCbSubCache(sourceType, sourceId).delete();

        final Set<String> keys = subCbIdCache.keySet();
        for (String key : keys) {
            if (key.startsWith(formatKey(sourceType.getJsonValue(), sourceId))) {
                subCbIdCache.remove(key);
            }
        }

        // 删除redis缓存
        getCbEnableParamSubCache(sourceType, sourceId).delete();

        final Set<String> sceKeys = subCbEnableParamCache.keySet();
        for (String key : sceKeys) {
            if (key.startsWith(formatKey(sourceType.getJsonValue(), sourceId))) {
                subCbEnableParamCache.remove(key);
            }
        }
    }

    /**
     * 搜索订阅关系, 二级缓存
     *
     * @param sourceType 源类型
     * @param sourceId   源ID
     * @param appId      应用ID
     * @return 回调ID列表
     */
    public List<String> searchCbIds(SubSourceType sourceType, String sourceId, String appId) {
        final String key = formatKey(sourceType.getJsonValue(), sourceId, appId);
        final List<String> ids = subCbIdCache.computeIfAbsent(key, ignore -> {
            final List<String> cbIds = getSubscriberCbIds(sourceType, sourceId, appId);

            if (cbIds.isEmpty()) {
                return List.of(NOOP_CB_ID);
            }

            return cbIds;
        });

        if (ids.size() == 1 && NOOP_CB_ID.equals(ids.get(0))) {
            return List.of();
        }

        return ids;
    }

    public List<CbEnableParamSubCache> searchCbEnableParam(SubSourceType sourceType, String sourceId, String appId) {
        final String key = formatKey(sourceType.getJsonValue(), sourceId, appId);
        List<CbEnableParamSubCache> list = subCbEnableParamCache.computeIfAbsent(key, ignore -> {
            List<CbEnableParamSubCache> cdEnableParams = getSubscriberCb(sourceType, sourceId, appId);

            if (cdEnableParams.isEmpty()) {
                return List.of(CbEnableParamSubCache.NOOP);
            }

            return cdEnableParams;
        });

        if (list.size() == 1 && CbEnableParamSubCache.NOOP.equals(list.get(0))) {
            return List.of();
        }

        return list;
    }

    /**
     * 根据sourceType, sourceId, appId查询订阅关系
     *
     * @param sourceType 源类型
     * @param sourceId   源ID
     * @param appId      应用ID
     * @return 回调ID列表
     */
    private List<String> getSubscriberCbIds(SubSourceType sourceType, String sourceId, String appId) {
        final RList<CbSubCache> cbSubCache = getCbSubCache(sourceType, sourceId);

        List<CbSubCache> caches = cbSubCache.readAll();
        if (caches == null || caches.isEmpty()) {
            caches = toList(subscriberConfigMapper.searchSbConfig(sourceType, sourceId), config -> {
                final CbSubCache cache = new CbSubCache();
                cache.setCbId(config.getCbId());
                cache.setAppIds(new HashSet<>(config.appIds()));

                return cache;
            });

            if (caches.isEmpty()) {
                cbSubCache.add(CbSubCache.NOOP);
            } else {
                cbSubCache.addAll(caches);
            }
            cbSubCache.expire(Duration.ofDays(30));
        }

        return caches.stream()
                .filter(cbSub -> {
                    if (CbSubCache.NOOP.equals(cbSub)) {
                        return false;
                    }
                    final Set<String> appIds = cbSub.getAppIds();
                    if (StringUtils.isNotBlank(cbSub.getAppId())) {
                        appIds.add(cbSub.getAppId());
                    }
                    // 没有指定appId, 都会回调
                    if (appIds.isEmpty()) {
                        return true;
                    }

                    return appIds.contains(appId);
                })
                .map(CbSubCache::getCbId)
                .toList();
    }

    /**
     * 根据sourceType, sourceId查询订阅关系
     *
     * @param sourceType 源类型
     * @param sourceId   源ID
     * @param appId      应用ID
     * @return 回调ID列表
     */
    private List<CbEnableParamSubCache> getSubscriberCb(SubSourceType sourceType, String sourceId, String appId) {
        final RList<CbEnableParamSubCache> cbSubCache = getCbEnableParamSubCache(sourceType, sourceId);

        List<CbEnableParamSubCache> caches = cbSubCache.readAll();
        if (caches == null || caches.isEmpty()) {
            caches = toList(subscriberConfigMapper.searchSbConfig(sourceType, sourceId), config -> {
                final CbEnableParamSubCache cache = new CbEnableParamSubCache();
                cache.setCbId(config.getCbId());
                cache.setEnableParams(config.getEnableParams());
                cache.setAppIds(new HashSet<>(config.appIds()));

                if (StringUtils.isNotBlank(config.getSendStrategy())) {
                    cache.setSendStrategy(JSON.parseObject(config.getSendStrategy(), SendStrategy.class));
                }
                if (StringUtils.isNotBlank(config.getTags())) {
                    cache.setTags(JSON.parseArray(config.getTags(), Tag.class));
                }

                return cache;
            });

            if (caches.isEmpty()) {
                cbSubCache.add(CbEnableParamSubCache.NOOP);
            } else {
                cbSubCache.addAll(caches);
            }
            cbSubCache.expire(Duration.ofDays(30));
        }

        return caches.stream()
                .filter(cbSub -> {
                    if (CbEnableParamSubCache.NOOP.equals(cbSub)) {
                        return false;
                    }
                    final Set<String> appIds = cbSub.getAppIds();
                    // 没有指定appId, 都会回调
                    if (appIds.isEmpty()) {
                        return true;
                    }
                    return appIds.contains(appId);
                })
                .toList();
    }


    private RList<CbSubCache> getCbSubCache(SubSourceType sourceType, String sourceId) {
        return redissonClient.getList("subscriber:cb:"
                + sourceType.getJsonValue() + ":" + sourceId, new JsonJacksonCodec());
    }

    private RList<CbEnableParamSubCache> getCbEnableParamSubCache(SubSourceType sourceType, String sourceId) {
        return redissonClient.getList("subscriber:cb:enableParam"
                + sourceType.getJsonValue() + ":" + sourceId, new JsonJacksonCodec());
    }

    public List<SubscriberConfig> listByCbId(String cbId) {
        return subscriberConfigMapper.listByCbId(cbId).stream().map(SubscriberConfigPo::toDto).toList();
    }
}
