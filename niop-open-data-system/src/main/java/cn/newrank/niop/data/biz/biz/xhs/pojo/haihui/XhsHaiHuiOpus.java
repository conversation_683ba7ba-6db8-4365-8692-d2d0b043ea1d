package cn.newrank.niop.data.biz.biz.xhs.pojo.haihui;

import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:32:42
 */
@Data
public class XhsHaiHuiOpus {
    /**
     * ES字段
     */
    @JsonField("id")
    private String opusId;
    @JsonField("user.userid")
    private String userId;
    @JsonField("title")
    private String title;
    @JsonField("desc")
    private String desc;
    @JsonField("official_keyword")
    private String officialKeyword;
    @JsonField("cooperate_binds_name")
    private String cooperateBindsName;
    private String topicsName;
    @JsonField({"create_time", "timestamp", "time"})
    private String publishTime;
    @JsonField("type")
    private String type;


    /**
     * holo字段
     */
    @Json<PERSON>ield("gender")
    private Integer gender;
    @JsonField("location")
    private String location;
    @JsonField("ip_location")
    private String ipLocation;
    @JsonField("mcn_name")
    private String mcnName;
    @JsonField("fans_num")
    private Long fansNum;
    @JsonField("personal_tags")
    private String personalTags;
    @JsonField("feature_tags")
    private String featureTags;
    @JsonField("content_tags")
    private String contentTags;
    @JsonField("picture_price")
    private BigDecimal picturePrice;
    @JsonField("video_price")
    private BigDecimal videoPrice;
    private BigDecimal price;
    @JsonField("picture_read_cost")
    private BigDecimal pictureReadCost;
    @JsonField("video_read_cost")
    private BigDecimal videoReadCost;
    @JsonField("fans30_growth_rate")
    private Double fans30GrowthRate;

    @JsonField("normal_pv_all_imp_median")
    private Long normalPvAllImpMedian;
    @JsonField("normal_pv_all_read_median")
    private Long normalPvAllReadMedian;
    @JsonField("normal_pv_all_interaction_median")
    private Long normalPvAllInteractionMedian;
    @JsonField("normal_pv_all_like_median")
    private Long normalPvAllLikeMedian;
    @JsonField("normal_pv_all_collect_median")
    private Long normalPvAllCollectMedian;
    @JsonField("normal_pv_all_comment_median")
    private Long normalPvAllCommentMedian;
    @JsonField("normal_pv_all_share_median")
    private Long normalPvAllShareMedian;
    @JsonField("normal_pv_all_interaction_rate")
    private Double normalPvAllInteractionRate;
    @JsonField("high_percent_fans_age")
    private String highPercentFansAge;
    @JsonField("high_percent_fans_device")
    private String highPercentFansDevice;
    @JsonField("high_percent_fans_area")
    private String highPercentFansArea;


    /**
     * 2025年6月19日 新增
     */
    @JsonField("normal_pv_all_note_number")
    private Integer normalPvAllNoteNumber;
    @JsonField("normal_pv_all_note_type")
    private String normalPvAllNoteType;
    @JsonField("normal_pv_all_thousand_like_percent")
    private Double normalPvAllThousandLikePercent;
    @JsonField("normal_pv_all_hundred_like_percent")
    private Double normalPvAllHundredLikePercent;
    @JsonField("cooperate_pv_all_note_number")
    private Integer cooperatePvAllNoteNumber;
    @JsonField("cooperate_pv_all_note_type")
    private String cooperatePvAllNoteType;
    @JsonField("cooperate_pv_all_imp_median")
    private Integer cooperatePvAllImpMedian;
    @JsonField("cooperate_pv_all_read_median")
    private Integer cooperatePvAllReadMedian;
    @JsonField("cooperate_pv_all_interaction_median")
    private Integer cooperatePvAllInteractionMedian;
    @JsonField("cooperate_pv_all_like_median")
    private Integer cooperatePvAllLikeMedian;
    @JsonField("cooperate_pv_all_collect_median")
    private Integer cooperatePvAllCollectMedian;
    @JsonField("cooperate_pv_all_comment_median")
    private Integer cooperatePvAllCommentMedian;
    @JsonField("cooperate_pv_all_share_median")
    private Integer cooperatePvAllShareMedian;
    @JsonField("cooperate_pv_all_interaction_rate")
    private Double cooperatePvAllInteractionRate;
    @JsonField("cooperate_pv_all_thousand_like_percent")
    private Double cooperatePvAllThousandLikePercent;
    @JsonField("cooperate_pv_all_hundred_like_percent")
    private Double cooperatePvAllHundredLikePercent;


    /**
     * 封面图像总结
     */
    private String coverSummary;

    /**
     * 向量引擎字段数据
     */
    private String textField;


    public static void fillUserData(XhsHaiHuiOpus opus, XhsHaiHuiOpus holo) {
        opus.setLocation(holo.getLocation());
        opus.setIpLocation(holo.getIpLocation());
        opus.setMcnName(holo.getMcnName());
        opus.setFansNum(holo.getFansNum());
        opus.setPersonalTags(holo.getPersonalTags());
        opus.setFeatureTags(holo.getFeatureTags());
        opus.setContentTags(holo.getContentTags());
        opus.setGender(holo.getGender());

        JSONArray contentTags = JSON.parseArray(holo.getContentTags());
        if(CollectionUtil.isNotEmpty(contentTags)){
            JSONArray tagList = new JSONArray();
            contentTags.forEach(item -> {
                if (item instanceof JSONObject jsonObject) {
                    tagList.add(jsonObject.getString("taxonomy1Tag"));
                    JSONArray tag2s = jsonObject.getJSONArray("taxonomy2Tags");
                    if(CollectionUtil.isNotEmpty(tag2s)){
                        tagList.addAll(tag2s);
                    }
                }
            });
            if(CollectionUtil.isNotEmpty(tagList)){
                opus.setContentTags(tagList.toJSONString());
            }
        }else{
            opus.setContentTags(null);
        }

        if("video".equals(opus.getType())){
            opus.setPrice(holo.getVideoPrice());
        }else{
            opus.setPrice(holo.getPicturePrice());
        }

        opus.setPicturePrice(holo.getPicturePrice());
        opus.setVideoPrice(holo.getVideoPrice());
        opus.setPictureReadCost(holo.getPictureReadCost());
        opus.setVideoReadCost(holo.getVideoReadCost());
        opus.setFans30GrowthRate(holo.getFans30GrowthRate());
        opus.setNormalPvAllImpMedian(holo.getNormalPvAllImpMedian());
        opus.setNormalPvAllReadMedian(holo.getNormalPvAllReadMedian());
        opus.setNormalPvAllInteractionMedian(holo.getNormalPvAllInteractionMedian());
        opus.setNormalPvAllLikeMedian(holo.getNormalPvAllLikeMedian());
        opus.setNormalPvAllCollectMedian(holo.getNormalPvAllCollectMedian());
        opus.setNormalPvAllCommentMedian(holo.getNormalPvAllCommentMedian());
        opus.setNormalPvAllShareMedian(holo.getNormalPvAllShareMedian());
        opus.setNormalPvAllInteractionRate(holo.getNormalPvAllInteractionRate());
        opus.setHighPercentFansAge(holo.getHighPercentFansAge());
        opus.setHighPercentFansDevice(holo.getHighPercentFansDevice());
        opus.setHighPercentFansArea(holo.getHighPercentFansArea());

        opus.setNormalPvAllNoteNumber(holo.getNormalPvAllNoteNumber());
        opus.setNormalPvAllNoteType(holo.getNormalPvAllNoteType());
        opus.setNormalPvAllThousandLikePercent(holo.getNormalPvAllThousandLikePercent());
        opus.setNormalPvAllHundredLikePercent(holo.getNormalPvAllHundredLikePercent());
        opus.setCooperatePvAllNoteNumber(holo.getCooperatePvAllNoteNumber());
        opus.setCooperatePvAllNoteType(holo.getCooperatePvAllNoteType());
        opus.setCooperatePvAllImpMedian(holo.getCooperatePvAllImpMedian());
        opus.setCooperatePvAllReadMedian(holo.getCooperatePvAllReadMedian());
        opus.setCooperatePvAllInteractionMedian(holo.getCooperatePvAllInteractionMedian());
        opus.setCooperatePvAllLikeMedian(holo.getCooperatePvAllLikeMedian());
        opus.setCooperatePvAllCollectMedian(holo.getCooperatePvAllCollectMedian());
        opus.setCooperatePvAllCommentMedian(holo.getCooperatePvAllCommentMedian());
        opus.setCooperatePvAllShareMedian(holo.getCooperatePvAllShareMedian());
        opus.setCooperatePvAllInteractionRate(holo.getCooperatePvAllInteractionRate());
        opus.setCooperatePvAllThousandLikePercent(holo.getCooperatePvAllThousandLikePercent());
        opus.setCooperatePvAllHundredLikePercent(holo.getCooperatePvAllHundredLikePercent());
    }

    public static String parseFields(Object field) {
        return JSON.toJSONString(field);
    }
}
