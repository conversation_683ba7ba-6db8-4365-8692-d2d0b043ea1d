package cn.newrank.niop.data.biz.export.pojo.po;

import cn.newrank.niop.data.biz.consumer.AbilityFinishTask;
import cn.newrank.niop.data.biz.consumer.HubServiceCallback;
import cn.newrank.niop.data.biz.export.atsp.AtspSubmitResult;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportSubtaskStatus;
import com.alibaba.fastjson2.JSON;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

@Data
public class DataExportSubtask {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 结果任务id
     */
    private String resultTaskId;

    /**
     * 参数 Json
     */
    private String paramJson;

    /**
     * 业务code
     */
    private Integer bizCode;

    /**
     * 业务消息
     */
    private String bizMsg;

    /**
     * 子任务状态
     */
    private DataExportSubtaskStatus subtaskStatus;

    /**
     * 数据量
     */
    private Integer dataNum;

    /**
     * 任务完成时间
     */
    private Timestamp taskFinishedTime;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;

    public static DataExportSubtask init(DataExportTask task, Map<String, ?> paramMap) {
        final DataExportSubtask subtask = new DataExportSubtask();
        subtask.setExportTaskId(task.getExportTaskId());
        subtask.setSubtaskStatus(DataExportSubtaskStatus.NOT_SUBMITTED);
        subtask.setParamJson(JSON.toJSONString(paramMap));
        return subtask;
    }

    public DataExportSubtask failed(AtspSubmitResult submitResult) {
        if (Objects.isNull(submitResult)) {
            return this;
        }
        this.setBizCode(submitResult.getCode());
        this.setBizMsg(submitResult.getMsg());
        this.setDataNum(0);
        this.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));
        return this;
    }

    public static DataExportSubtask buildBy(AbilityFinishTask finishTask) {
        final DataExportSubtask subtask = new DataExportSubtask();
        subtask.setResultTaskId(finishTask.getTaskId());
        subtask.setBizCode(finishTask.getBizCode());
        subtask.setBizMsg(finishTask.getBizMsg());
        subtask.setSubtaskStatus(finishTask.statusSucceed() ? DataExportSubtaskStatus.SUCCEED : DataExportSubtaskStatus.FAILED);
        // 默认设置为 1
        subtask.setDataNum(1);
        subtask.setTaskFinishedTime(Timestamp.valueOf(finishTask.getFinishTime()));
        return subtask;
    }

    public static DataExportSubtask buildBy(HubServiceCallback hubCallback) {
        final DataExportSubtask subtask = new DataExportSubtask();
        subtask.setResultTaskId(hubCallback.getTaskId());
        // 综合服务这些字段为空
        subtask.setBizCode(null);
        subtask.setBizMsg(null);
        subtask.setSubtaskStatus(hubCallback.statusSucceed() ? DataExportSubtaskStatus.SUCCEED : DataExportSubtaskStatus.FAILED);
        subtask.setDataNum(hubCallback.getTotal());
        subtask.setTaskFinishedTime(Timestamp.valueOf(hubCallback.getFinishTime()));
        return subtask;
    }

}
