package cn.newrank.niop.data.biz.biz.xhs.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsTopicUniIndexDataTypeEnum;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsTopicUniIndexLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsTopicUniIndex;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 小红书作品存储ldm服务
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
@Service
public class XhsTopicUniIndexStoreServiceImpl implements StorageBizService<XhsTopicUniIndex> {

    private final XhsTopicUniIndexLmMapper xhsTopicUniIndexLmMapper;

    public XhsTopicUniIndexStoreServiceImpl(XhsTopicUniIndexLmMapper xhsTopicUniIndexLmMapper) {
        this.xhsTopicUniIndexLmMapper = xhsTopicUniIndexLmMapper;
    }


    @Override
    public XhsTopicUniIndex castOf(JSONObject item) {
        if (Objects.isNull(item)) {
            return null;
        }
        String dataType = item.getString("data_type");
        XhsTopicUniIndexDataTypeEnum typeEnum = XhsTopicUniIndexDataTypeEnum.getByJsonCode(dataType);
        if (Objects.isNull(typeEnum)) {
            return null;
        }
        return XhsTopicUniIndex.convertToTopicUniIndex(item, typeEnum);
    }


    @Override
    public void storeBatch(List<XhsTopicUniIndex> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Map<String, List<XhsTopicUniIndex>> map = list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(XhsTopicUniIndex::getSource));
        for (Map.Entry<String, List<XhsTopicUniIndex>> entry : map.entrySet()) {
            storeBatchByDateType(entry.getValue(), XhsTopicUniIndexDataTypeEnum.getByJsonCode(entry.getKey()));
        }
    }

    /**
     * 根据数据类型存储
     *
     * @param list         数据
     * @param dataTypeEnum 数据类型
     */
    public void storeBatchByDateType(List<XhsTopicUniIndex> list,
                                     XhsTopicUniIndexDataTypeEnum dataTypeEnum) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        if (Objects.isNull(dataTypeEnum)) {
            return;
        }
        switch (dataTypeEnum) {
            case TOPIC_CAL_RESULT:
                xhsTopicUniIndexLmMapper.storeCalResultBatch(list);
                break;
            case TOPIC_BASE:
                xhsTopicUniIndexLmMapper.storeBaseBatch(list);
                break;
            case TOPIC_BRAND_REC:
                xhsTopicUniIndexLmMapper.storeBrandRecBatch(list);
                break;
            case TOPIC_CATEGORY_REC:
                xhsTopicUniIndexLmMapper.storeCategoryRecBatch(list);
                break;
            default:
                break;
        }
    }
}
