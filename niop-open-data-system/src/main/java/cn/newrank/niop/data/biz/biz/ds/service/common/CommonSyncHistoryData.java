package cn.newrank.niop.data.biz.biz.ds.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class CommonSyncHistoryData {
    public static final String DS_ES_HISTORY_DATA_SEARCH_QUERY = """
                GET /search_nr_account/_search?scroll=5m
                {
                    "size": #{size},
                    "query": {
                      "term": {
                        "platform_type": {
                          "value": #{platformType}
                        }
                      }
                    },
                    "sort": [
                      "_doc"
                    ]
                }
            """;

    public static final String DS_ES_HISTORY_DATA_SCROLL_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "5m",
                "scroll_id" : #{scrollId}
            }
            """;
    private final EsFactory esFactory;
    private final DsConfigManager dsConfigManager;
    private final CommonEsService commonEsService;

    public CommonSyncHistoryData(DsConfigManager dsConfigManager, CommonEsService commonEsService) {
        this.esFactory = EsFactory.DEFAULT;
        this.dsConfigManager = dsConfigManager;
        this.commonEsService = commonEsService;
    }

    /**
     * 大搜es历史数据同步
     *
     * @param platformType 平台类型 {@link  PlatformType}
     */
    public void syncHistoryData(String platformType) {
        XxlJobLogger.log("大搜es开始同步平台{}", platformType);

        if (StrUtil.isBlank(platformType) || Objects.isNull(PlatformType.getByCode(false, platformType))) {
            XxlJobLogger.log("大搜es数据-同步失败, 平台类型(platformType: {})有误", platformType);
            return;
        }

        try (final EsFactory.Es es = esFactory.create(dsConfigManager.chooseDsEsConfig())) {
            int total = 0;
            Resp query = es.query(
                    es.newQueryBuilder()
                            .template(DS_ES_HISTORY_DATA_SEARCH_QUERY)
                            .addParam("size", 1000)
                            .addParam("platformType", platformType)
            );
            Resp.DataView dataView = query.getDataView();
            if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
                JSONObject resultObj = esDataView.resp();
                String scrollId = resultObj.getString(EsUtil.SCROLL_ID);
                List<DataCenterEsMetaData> esDataList = EsUtil.listHitsToEntity(resultObj, json -> {
                    JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
                    if (sourceObj != null && sourceObj.get("account_tag") != null) {
                        sourceObj.put("account_tag", sourceObj.getJSONArray("account_tag"));
                    }
                    return EsCodec.deserialize(JSON.toJSONString(sourceObj), DataCenterEsMetaData.class);
                });

                if (CollUtil.isEmpty(esDataList)) {
                    return;
                }

                // 保存到es
                commonEsService.upsert(esDataList);
                total += esDataList.size();
                XxlJobLogger.log("first query completed, scrollId: {}", scrollId);

                while (true) {
                    query = es.query(
                            es.newQueryBuilder().template(DS_ES_HISTORY_DATA_SCROLL_QUERY)
                                    .addParam("scrollId", scrollId)
                    );

                    dataView = query.getDataView();
                    if (dataView instanceof EsFactory.Es.RespImpl.EsDataView scrollEsDataView) {
                        resultObj = scrollEsDataView.resp();
                        scrollId = resultObj.getString(EsUtil.SCROLL_ID);
                        esDataList = EsUtil.listHitsToEntity(resultObj, json -> {
                            JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
                            try {
                                if (sourceObj != null && sourceObj.get("account_tag") != null) {
                                    sourceObj.put("account_tag", sourceObj.getJSONArray("account_tag"));
                                }
                                return EsCodec.deserialize(JSON.toJSONString(sourceObj), DataCenterEsMetaData.class);
                            } catch (Exception e) {
                                // 如果解析失败，则跳过当前数据，后续做手动补偿
                                log.error("es数据解析失败, json: {} e: {}", JSON.toJSONString(sourceObj), e.getMessage());
                                XxlJobLogger.log("es数据解析失败, e: {}", e.getMessage());
                                return null;
                            }
                        });

                        if (CollUtil.isEmpty(esDataList)) {
                            // 理论上没有数据后需要删除scrollId，但目前es这种调用方式只支持get，所以这里让scrollId到期自动删除，或者通过kibana入口手动清除
                            XxlJobLogger.log("同步全部数据完成, scrollId: {}", scrollId);
                            break;
                        }

                        // 保存到es
                        commonEsService.upsert(esDataList);
                        total += esDataList.size();

                        try {
                            // 控制 400ms 查询一次
                            TimeUnit.MILLISECONDS.sleep(200);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            XxlJobLogger.log("大搜es数据-[({})平台数据] 睡眠异常, e: ", platformType, e);
                            return;
                        }
                    }
                }
            }
            XxlJobLogger.log("大搜es数据-[({})平台数据]同步成功, total: {}", platformType, total);
        } catch (Exception e) {
            log.error("大搜es数据-[({})平台数据]同步失败, e: ", platformType, e);
            XxlJobLogger.log("大搜es数据-[({})平台数据]同步失败, e: ", platformType, e);
        }
    }
}
