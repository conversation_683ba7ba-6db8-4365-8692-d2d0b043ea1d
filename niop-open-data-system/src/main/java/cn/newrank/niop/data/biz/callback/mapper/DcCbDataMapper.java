package cn.newrank.niop.data.biz.callback.mapper;

import cn.newrank.niop.data.biz.callback.pojo.CbData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/30 9:48
 */
@Mapper
public interface DcCbDataMapper {


    int insertBatch(@Param("dataList") List<CbData> dataList, @Param("partition") String partition);

    List<CbData> list(@Param("cIds") List<String> cIds);

    void createTable(@Param("partition") String partition,
                     @Param("startDate") Date startDate,
                     @Param("endDate") Date endDate);

    void dropTable(String partition);
}




