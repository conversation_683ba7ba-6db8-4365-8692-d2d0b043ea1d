package cn.newrank.niop.data.biz.subscriber.pojo.param;

import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SendStrategy;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.Tag;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.util.U;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 11:04
 */
@Data
public class SubscriberConfigCreate {
    /**
     * 应用id
     */
    private String appId;

    private List<String> appIds = new ArrayList<>();
    /**
     * 回调源id
     */
    @NotBlank(message = "回调源ID(cbId)不能为空")
    private String cbId;

    /**
     * 源id
     */
    @NotBlank(message = "源ID(sourceId)不能为空")
    private String sourceId;
    /**
     * 源名称
     */
    @NotBlank(message = "源名称(sourceName)不能为空")
    private String sourceName;

    /**
     * 源类型
     */
    @NotNull(message = "源类型(sourceType)不能为空")
    private SubSourceType sourceType;

    /**
     * 是否启用参数(针对v1能力结果)
     */
    private boolean enableParams;
    /**
     * 发送策略
     */
    private SendStrategy sendStrategy;

    List<Tag> tags;

    /**
     * 负责人
     */
    @NotEmpty(message = "负责人(maintainers)不能为空")
    private List<String> maintainers;

    public SubscriberConfig toDto() {
        if (sendStrategy != null) {
            sendStrategy.validate();
        }
        final SubscriberConfig subscriberConfig = new SubscriberConfig();


        if (appId != null && !appIds.contains(appId)) {
            appIds.add(appId);
        }

        final List<App> apps = U.toList(appIds, id -> {
            final App app = new App();
            app.setAppId(id);
            return app;
        });

        subscriberConfig.setApps(apps);
        subscriberConfig.setCbId(cbId);
        subscriberConfig.setMaintainers(maintainers);
        subscriberConfig.setSourceId(sourceId);
        subscriberConfig.setSourceName(sourceName);
        subscriberConfig.setSourceType(sourceType);
        subscriberConfig.setEnableParams(enableParams);
        subscriberConfig.setSendStrategy(sendStrategy);
        subscriberConfig.setTags(tags);

        return subscriberConfig;
    }
}
