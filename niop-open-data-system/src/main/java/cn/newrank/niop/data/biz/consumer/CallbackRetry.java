package cn.newrank.niop.data.biz.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import cn.newrank.niop.data.biz.pojo.po.CallbackRetryPo;
import cn.newrank.niop.data.util.Ids;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;

/**
 * 回调重试实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/13 11:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CallbackRetry extends CallbackRedirect {
    /**
     * 回调id
     */
    String cbId;

    /**
     * 重试次数
     */
    Integer retryNums;

    /**
     * 重试状态
     */
    private RetryStatus retryStatus;

    /**
     * 下次重试时间
     */
    private Timestamp nextRetryTime;

    /**
     * 失败信息
     */
    private String errorInfo;

    /**
     * 重试id
     */
    private String retryId;

    public static CallbackRetry of(CallbackRedirect redirect, String errorInfo) {
        if (redirect instanceof CallbackRetry retry) {
            return retry;
        }

        // 初始化重试
        return init(redirect, errorInfo);
    }

    public static CallbackRetry init(CallbackRedirect redirect, String errorInfo) {
        final CallbackRetry retry = new CallbackRetry();
        retry.setRetryId(Ids.create(32, false));
        retry.setRetryStatus(RetryStatus.PROCESS);
        retry.setRetryNums(0);
        retry.setNextRetryTime(Timestamp.from(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8))));
        retry.setErrorInfo(errorInfo);
        retry.setStorageBiz(redirect.getStorageBiz());
        retry.setSourceKey(redirect.getSourceKey());
        retry.setSourceId(redirect.getSourceId());
        retry.setSourceType(redirect.getSourceType());
        retry.setAppId(redirect.getAppId());
        retry.setConsumerRecord(redirect.getConsumerRecord());
        retry.setConsumerRecordCarrier(redirect.getConsumerRecordCarrier());
        retry.setPayload(redirect.getPayload());

        return retry;
    }

    public static List<CallbackRetry> buildBy(List<CallbackRetryPo> poList) {
        if (CollUtil.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream().map(CallbackRetryPo::toDto).toList();
    }

}
