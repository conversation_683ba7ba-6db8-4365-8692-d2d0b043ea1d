package cn.newrank.niop.data.util;

import lombok.experimental.UtilityClass;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/15 10:56
 */
@UtilityClass
public class FieldVisitor {

    static final ConcurrentMap<Class<?>, List<Field>> TIMESTAMP_FIELDS = new ConcurrentHashMap<>();

    public static void visitTimestamp(Object target, BiConsumer<String, Timestamp> timestampConsumer) {
        if (target == null) {
            return;
        }

        final List<Field> targetFields = TIMESTAMP_FIELDS.computeIfAbsent(target.getClass(), clazz -> {
            final Field[] fields = clazz.getDeclaredFields();

            final List<Field> tem = new ArrayList<>();
            for (Field field : fields) {
                final Class<?> type = field.getType();
                if (type == Timestamp.class) {
                    tem.add(field);
                }
            }

            return tem;
        });

        targetFields.forEach(field -> {
            ReflectionUtils.makeAccessible(field);
            final Object value = ReflectionUtils.getField(field, target);

            if (value == null) {
                timestampConsumer.accept(field.getName(), null);
                return;
            }

            if (value instanceof Timestamp timestamp) {
                timestampConsumer.accept(field.getName(), timestamp);
            }
        });
    }
}
