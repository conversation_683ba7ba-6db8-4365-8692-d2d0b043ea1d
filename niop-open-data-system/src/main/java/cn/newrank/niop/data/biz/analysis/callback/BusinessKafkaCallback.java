package cn.newrank.niop.data.biz.analysis.callback;

import cn.newrank.niop.data.biz.analysis.pojo.AnalysisMessage;
import cn.newrank.niop.data.biz.analysis.pojo.DyBusinessAweme;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyOpus;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.component.callback.KafkaCallbackFactory;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.data.util.Jackson;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/3 9:40
 */
@Log4j2
@Component
public class BusinessKafkaCallback {

    public static final String YZ_BUSSINESS_AWEME = "yz_bussiness_aweme";
    private final KafkaCallback analysisBussinessCallback;

    public BusinessKafkaCallback(KafkaCallbackFactory kafkaCallbackFactory, SystemConfig systemConfig) {
        analysisBussinessCallback = getCallback(kafkaCallbackFactory, systemConfig);
    }

    private @Nullable KafkaCallback getCallback(KafkaCallbackFactory kafkaCallbackFactory, SystemConfig systemConfig) {
        if (systemConfig.isProduct()) {
            ConfigProperties configProperties = key -> switch (key) {
                case KAFKA_BOOTSTRAP_SERVERS -> "192.168.8.217:9092,192.168.8.216:9092,192.168.8.218:9092";
                case TOPIC -> "dy_aweme_yz_bussiness_source";
                case PROTOCOL -> "PLAINTEXT";
                default -> "";
            };

            final KafkaCallback analysisBussinessCallback = kafkaCallbackFactory.newCallback(configProperties);
            if (analysisBussinessCallback.isActive()) {
                return analysisBussinessCallback;
            }

            throw new IllegalStateException("分析商业数据推送 kafka is not active");
        }

        return null;
    }

    /**
     * 发送抖音商业数据
     *
     * @param dyOpus 抖音作品
     */
    protected void send(DyOpus dyOpus) {
        if (analysisBussinessCallback == null) {
            return;
        }

        try {
            final DyBusinessAweme dyBusinessAweme = DyBusinessAweme.of(dyOpus);

            final AnalysisMessage<Object> message = new AnalysisMessage<>();
            message.setDataType(YZ_BUSSINESS_AWEME);
            message.setJsonDetails(dyBusinessAweme);
            message.setLogicId(dyOpus.getAwemeId());
            message.setGmtCreate(DateTimeUtil.format(dyOpus.getSUpdateTime()));

            final String json = Jackson.snackWrite(message);

            analysisBussinessCallback.callback(message.getLogicId(), json);
        } catch (Exception e) {
            log.error("send dy bussiness aweme error", e);
        }
    }

    /**
     * 批量发送抖音商业数据
     *
     * @param opuses 抖音作品列表
     */
    public void sendBatch(List<DyOpus> opuses) {
        if (opuses == null) {
            return;
        }


        for (DyOpus opus : opuses) {
            send(opus);
        }
    }
}
