package cn.newrank.niop.data.biz.biz.dy.mapper;


import cn.newrank.niop.data.biz.biz.dy.pojo.DyHaiHuiOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 海汇抖音存储达人数据
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
@DS("haihui")
public interface DyHaiHuiLmMapper {

    /**
     * 批量存储-无上次更新时间-新表
     *
     * @param items 存储对象
     * @return 存储结果
     */
    boolean saveBatch(@Param("items") List<DyHaiHuiOpus> items);

}




