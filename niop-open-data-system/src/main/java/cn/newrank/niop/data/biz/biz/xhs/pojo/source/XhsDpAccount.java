package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
public class XhsDpAccount {

    @JsonField("id")
    private String opusId;

    @JsonField("userid")
    private String userId;

    @JsonField("fans")
    private Long fans;

    @JsonField("images")
    private String images;

    @JsonField("nickname")
    private String nickname;

    public static XhsDpAccount parse(JSONObject item){
        XhsDpAccount dpAccount = new XhsDpAccount();
        dpAccount.setOpusId(item.getString("id"));
        dpAccount.setUserId(item.getString("userid"));
        dpAccount.setFans(item.getLong("fans"));
        dpAccount.setImages(item.getString("images"));
        dpAccount.setNickname(item.getString("nickname"));
        return  dpAccount;
    }

}
