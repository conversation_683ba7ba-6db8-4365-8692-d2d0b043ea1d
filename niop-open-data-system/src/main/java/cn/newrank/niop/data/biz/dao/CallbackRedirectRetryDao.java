package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.dao.mapper.CallbackRedirectRetryMapper;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import cn.newrank.niop.data.biz.pojo.po.CallbackRetryPo;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:51
 */
@Slf4j
@Component
public class CallbackRedirectRetryDao {

    private final CallbackRedirectRetryMapper redirectRetryMapper;

    public CallbackRedirectRetryDao(CallbackRedirectRetryMapper redirectRetryMapper) {
        this.redirectRetryMapper = redirectRetryMapper;
    }

    public void saveBatch(List<CallbackRetry> retryList) {
        List<CallbackRetryPo> retryPoList = new ArrayList<>();
        for (CallbackRetry retry : retryList) {
            final CallbackRetryPo retryPo = CallbackRetryPo.fromDto(retry);
            retryPoList.add(retryPo);
        }

        try {
            redirectRetryMapper.saveBatch(retryPoList);
        } catch (Exception e) {
            throw createDbError(e, "批量保存回调重定向重试失败， {}", JSON.toJSONString(retryList));
        }
    }

    public void updateBatch(List<CallbackRetry> retryList) {
        List<CallbackRetryPo> retryPoList = new ArrayList<>();
        for (CallbackRetry retry : retryList) {
            final CallbackRetryPo retryPo = CallbackRetryPo.fromDto(retry);
            retryPoList.add(retryPo);
        }

        try {
            redirectRetryMapper.updateBatch(retryPoList);
        } catch (Exception e) {
            throw createDbError(e, "批量保存回调重定向重试失败");
        }

    }

    public List<CallbackRetry> listByRetryStatus(RetryStatus retryStatus, Integer size) {
        return CallbackRetry.buildBy(redirectRetryMapper.listByRetryStatus(retryStatus.getIntDbCode(), size));
    }

}
