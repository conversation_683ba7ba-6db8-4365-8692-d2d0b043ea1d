package cn.newrank.niop.data.biz.oss.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

public final class OssUtil {

    private OssUtil() {}

    private static final String OSS_HOST = "aliyuncs.com";

    public static String getOssFileKey(String fileUrl) {
        checkFileUrl(fileUrl);
        // key 从 / 之后开始算
        return fileUrl.split(OSS_HOST + StrPool.SLASH)[1];
    }

    private static void checkFileUrl(String fileUrl) {
        if (CharSequenceUtil.isBlank(fileUrl) || !fileUrl.contains(OSS_HOST)) {
            throw createParamError("OSS文件链接异常");
        }
    }

}
