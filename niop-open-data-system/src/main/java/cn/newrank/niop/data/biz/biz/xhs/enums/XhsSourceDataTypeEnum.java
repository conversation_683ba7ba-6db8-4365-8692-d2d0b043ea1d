package cn.newrank.niop.data.biz.biz.xhs.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/11 20:58:51
 */

@AllArgsConstructor
public enum XhsSourceDataTypeEnum implements BizEnum {
    /**
     * 数据类型
     */
    ABILITY_TOPIC("ab-topic","ab-topic", "话题作品能力【CWWEVCGP】"),
    ABILITY_POC("ab-poc", "ab-poc","POC能力【QQB1BRAW】"),
    AWEME_BASE("aweme_base", "dp_aweme_base","作品基础信息"),
    AWEME_ACCOUNT("aweme_account", "dp_aweme_account","用户信息"),
    AWEME_FIRST_ACQ_DETAIL("aweme_first_acq_detail", "dp_aweme_first_acq_detail","详情首次采集时间"),
    AWEME_FIRST_ACQ_ABNORMAL("aweme_first_acq_abnormal","dp_aweme_first_acq_abnormal","作品首次采集异常数据")
    ;

    private final String code;
    private final String dbCode;
    private final String description;

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return dbCode;
    }


    public static XhsSourceDataTypeEnum getByJsonCode(String code) {
        for (XhsSourceDataTypeEnum value : values()) {
            if (value.getJsonValue().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static XhsSourceDataTypeEnum getByDbCode(String code) {
        for (XhsSourceDataTypeEnum value : values()) {
            if (value.getDbCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
