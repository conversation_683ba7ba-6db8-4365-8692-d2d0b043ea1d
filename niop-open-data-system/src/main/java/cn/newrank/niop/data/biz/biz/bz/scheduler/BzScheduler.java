package cn.newrank.niop.data.biz.biz.bz.scheduler;

import cn.newrank.niop.data.biz.biz.bz.pojo.BzLive;
import cn.newrank.niop.data.biz.biz.bz.service.BzLiveService;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.common.lock.DistributeLock;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 18:56
 */
@Log4j2
@Component
public class BzScheduler {

    private final BzLiveService bzLiveService;
    private final StorageService storageService;

    public BzScheduler(BzLiveService bzLiveService, StorageService storageService) {
        this.bzLiveService = bzLiveService;
        this.storageService = storageService;
    }


    @DistributeLock(abortException = false)
    @Scheduled(cron = "0 0/5 * * * ?")
    public void run() {
        String failedSampleId = null;
        while (true) {
            final List<BzLive> lives = bzLiveService.listCalculateLives();
            if (lives.isEmpty()) {
                return;
            }

            for (BzLive live : lives) {
                if (StringUtils.equals(failedSampleId, live.getSampleId())) {
                    return;
                }

                try {
                    calculate(live);
                } catch (Exception e) {
                    log.error("计算失败，e: ", e);
                    failedSampleId = live.getSampleId();
                }
            }
        }
    }

    private void calculate(BzLive live) {
        final LinkedList<StorageHistory.History> histories =
                storageService.getHistory(StorageBiz.BZ_LIVE, live.getSampleId()).getHistories();

        final Timestamp endTime = live.getEndTime();
        final Timestamp startTime = live.getStartTime();
        final int total = histories.size() + 1;
        if (endTime != null && startTime != null) {
            final Duration between = Duration.between(startTime.toLocalDateTime(), endTime.toLocalDateTime());

            // 直播
            live.setDuration(between.toSeconds());
        }


        long maxHot = live.getHot() == null ? 0L : live.getHot();
        long maxLike = live.getTotalLikeNum() == null ? 0L : live.getTotalLikeNum();
        long maxWatchedNum = live.getWatchedNum() == null ? 0L : live.getWatchedNum();
        long maxAudienceNum = live.getAudienceNum() == null ? 0L : live.getAudienceNum();
        long hotSum = maxHot;
        long audienceSum = maxAudienceNum;
        for (StorageHistory.History history : histories) {
            final BzLive bzLive = history.getData().to(BzLive.class);

            if ("2".equalsIgnoreCase(bzLive.getLiveStatus())) {
                // 轮播不计算
                continue;
            }

            final Long hot = bzLive.getHot();
            if (hot != null && hot > maxHot) {
                maxHot = hot;

            }

            final Long totalLikeNum = bzLive.getTotalLikeNum();
            if (totalLikeNum != null && totalLikeNum > maxLike) {
                maxLike = totalLikeNum;
            }


            final Long watchedNum = bzLive.getWatchedNum();
            if (watchedNum != null && watchedNum > maxWatchedNum) {
                maxWatchedNum = watchedNum;
            }

            final Long audienceNum = bzLive.getAudienceNum();
            if (audienceNum != null && audienceNum > maxAudienceNum) {
                maxAudienceNum = audienceNum;
            }

            hotSum += hot == null ? 0 : hot;
            audienceSum += audienceNum == null ? 0 : audienceNum;
        }
        live.setMaxHot(maxHot);
        live.setTotalLikeNum(maxLike);
        live.setWatchedNum(maxWatchedNum);
        live.setAvgHot(new BigDecimal(hotSum).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP));
        live.setAvgAudienceNum(new BigDecimal(audienceSum).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP).longValue());
        live.setMaxAudienceNum(maxAudienceNum);

        bzLiveService.updateCalculateProps(live);
    }
}
