package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import groovy.util.logging.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import static cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusBasicService.SCROLL_QUERY;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/13 16:25
 */
@Slf4j
@Log4j2
@Component
public class DyHistorySync extends DefaultHistorySynchronizer<String> {

   private static final String E_IDS = """
           GET search_nr_account_20241015/_search?scroll=15m
           {
             "query": {
               "bool": {
                 "filter": [
                   {
                     "term": {
                       "platform_type": "dy"
                     }
                   },
           
                   {
                     "range": {
                       "nr_index_week_date": {
                         "gte": "2025-05-05 00:00:00"
                       }
                     }
                   }
                 ]
               }
             },
             "size": 200,
             "track_total_hits": true
           }
           """;

    private static final String DETAIL = """
            GET /search_douyin_user/_search/
            {
              "size": 500,
              "_source": [
                "crawl_time",
                "account_classify_first",
                "gender",
                "city_new",
                "signature",
                "new_article_follower_count",
                "account_classify_second",
                "mplatform_followers_count",
                "ana_xd_tags",
                "custom_verify",
                "ana_time",
                "uid",
                "province_new",
                "avatar",
                "total_favorited",
                "nickname",
                "aweme_count_30",
                "aweme_count",
                "live_stream_follower_count",
                "sec_uid",
                "verify_label",
                "mcn_name",
                "follower_count",
                "unique_id",
                "short_id"
              ],
              "query": {
               "terms": {
                 "_id": %s
               }
              }
            }
            """;
    final EsFactory.Es dy;
    final EsFactory.Es ds;
    final CommonEsService commonEsService;
    final DyInsertDataSyncService dataSyncService;
    final AtomicBoolean first = new AtomicBoolean(false);
    protected DyHistorySync(RedissonClient redissonClient,
                            DsConfigManager dsConfigManager,
                            CommonEsService commonEsService,
                            DyInsertDataSyncService dataSyncService) {
        super("ds_dy_tem_sync", redissonClient, 1);
        this.dy = EsFactory.DEFAULT.create(dsConfigManager.chooseDyEsConfig());
        this.commonEsService = commonEsService;
        this.ds = EsFactory.DEFAULT.create(commonEsService.getRestClient());
        this.dataSyncService = dataSyncService;
    }



    @Override
    protected int sync(Cursor<String> cursor) {
        final String scrollId = cursor.getNext();
        final JSONObject resp;
        if (StringUtils.isBlank(scrollId)) {
           resp = ds.newQueryBuilder()
                    .template(E_IDS)
                    .query()
                    .data();

            final Integer total = resp.getJSONObject("hits")
                    .getJSONObject("total").getInteger("value");
            cursor.setTotal(total);
            cursor.setNext(resp.getString("_scroll_id"));
        } else {
            final QueryBuilder queryBuilder = ds.newQueryBuilder()
                    .template(SCROLL_QUERY)
                    .addParam("scrollId", scrollId);

            resp = ds.query(queryBuilder).data();
        }

        final List<JSONObject> hits = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class);
        if (hits.isEmpty()) {
            return 0;
        }

        final List<String> ids = hits.stream()
                .map(item -> item.getJSONObject("_source").getString("account_id"))
                .toList();
        if (first.compareAndSet(false, true)) {
            log.info("同步dy数据: {}", JSON.toJSONString(ids));
        }

        final JSONObject detailResp = dy.newQueryBuilder()
                .template(DETAIL.formatted(JSON.toJSONString(ids)))
                .query()
                .data();

        final List<DataCenterEsMetaData> dataList = dataSyncService.parseData(detailResp, DataCenterEsMetaData.class);
        if (CollUtil.isEmpty(dataList)) {
            return hits.size();
        }

        // 同步到数据中心es
        commonEsService.upsert(dataList);


        return hits.size();
    }
}
