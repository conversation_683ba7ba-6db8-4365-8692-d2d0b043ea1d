package cn.newrank.niop.data.biz.biz.dy.service.haihui;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyHaiHuiLmMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyHaiHuiOpus;
import cn.newrank.niop.data.biz.biz.xhs.service.haihui.XhsHaihuiLdmService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基础数据存储
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:50
 */
@Service
@Log4j2
public class DyHaihuiBusinessOpusServiceImpl extends DefaultHistorySynchronizer<String> implements StorageBizService<DyHaiHuiOpus> {

    private final XhsHaihuiLdmService xhsHaihuiLdmService;
    private final DyHaiHuiLmMapper dyHaiHuiLmMapper;
    private final EsFactory.Es dyEs;
    private final HoloFactory.Holo holo;
    public static final String DY_ES_OPUS_SYNC = """
               GET search_douyin_aweme/_search
               {
                 "size": #{size},
                 "query": {
                   "bool": {
                     "filter": [
                       {
                         "range": {
                           "create_time": {
                             "gte": #{startTime},
                             "lte": #{endTime}
                           }
                         }
                       }
                     ],
                     "should": [
                       {
                         "terms": {
                           "author_user_id": #{uids},
                            "boost": 1
                         }
                       }
                     ],
                     "minimum_should_match": 1
                   }
                 },
                 "sort": [ { "aweme_id": "asc" } ],
                 "search_after": [#{searchAfter}],
                 "_source": [
                   "aweme_id",
                   "uid",
                   "author_user_id",
                   "aweme_desc",
                   "ocr_content",
                   "aweme_type",
                   "create_time"
                 ]
               }
            """;
    public static final String HOLO_PAGE_QUERY = """
            select
                uid,
                star_id,
                nick_name,
                user_signature,
                follower,
                all_like,
                opus_num,
                custom_verify,
                e_commerce_enable,
                gender,
                province,
                city,
                mcn_name,
                tags_relation,
                cover_num,
                inter_num,
                industry_info,
                prospective_1_20_cpm,
                prospective_20_60_cpm,
                prospective_60_cpm,
                prospective_1_20_cpe,
                prospective_20_60_cpe,
                prospective_60_cpe,
                fans_increment_within_15d,
                fans_increment_rate_within_15d,
                price_1,
                price_2,
                price_71,
                star_index_value,
                personal_30d_item_num,
                personal_30d_play_over_rate,
                personal_30d_interact_rate,
                personal_30d_play_mid,
                personal_30d_like_avg,
                personal_30d_comment_avg,
                personal_30d_share_avg,
                personal_30d_avg_duration,
                star_30d_play_mid,
                star_30d_like_avg,
                star_30d_comment_avg,
                star_30d_share_avg,
                star_30d_avg_duration,
                convert_ability_30d_play_mid,
                convert_ability_30d_component_click_cnt_range,
                convert_ability_30d_component_click_rate_range,
                convert_ability_30d_related_cpc_range,
                convert_ability_30d_rec_product_cnt,
                convert_ability_30d_avg_sales_amount_range,
                convert_ability_30d_rec_product_price_range,
                convert_ability_30d_gpm_range,
                expected_play_num
            from dws_dy_video_user_info_main
            where is_business = true
                and star_id != '-999'
                and user_status != 4
                and uid > '%s'
            order by uid
            limit %s
            """;

    public DyHaihuiBusinessOpusServiceImpl(RedissonClient redissonClient, XhsHaihuiLdmService xhsHaihuiLdmService,
                                           DyHaiHuiLmMapper dyHaiHuiLmMapper,
                                           DsConfigManager dsConfigManager) {
        super("haihui_dy_business_opus_sync", redissonClient, 2);
        this.xhsHaihuiLdmService = xhsHaihuiLdmService;
        this.dyHaiHuiLmMapper = dyHaiHuiLmMapper;
        this.dyEs = EsFactory.DEFAULT.create(dsConfigManager.chooseDyEsConfig());
        this.holo = HoloFactory.DEFAULT.create(dsConfigManager.chooseYsHoloConfig());
    }


    @Override
    public void storeBatch(List<DyHaiHuiOpus> items) {
        if (items.isEmpty()) {
            return;
        }
        List<CompletableFuture<Void>> futures = Lists.partition(items, 1000).parallelStream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    try {
                        dyHaiHuiLmMapper.saveBatch(batch);
                    } catch (Exception e) {
                        throw new CompletionException(e);
                    }
                })).toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    @Override
    public DyHaiHuiOpus castOf(JSONObject item) {
        return item.to(DyHaiHuiOpus.class);
    }

    @Override
    public int sync(Cursor<String> cursor) {
        final String startTime = LocalDate.now().atStartOfDay().minusDays(1).format(DatePattern.NORM_DATETIME_FORMATTER);
        final String endTime = LocalDate.now().atStartOfDay().format(DatePattern.NORM_DATETIME_FORMATTER);
        final JSONArray userArray = getUser(cursor.getNext());
        final Map<String, JSONObject> userMap = getUserMap(userArray);
        if (CollectionUtil.isEmpty(userMap.keySet())) {
            return 0;
        }
        final List<String> userIds = userMap.keySet().stream().filter(Strings::isNotBlank).toList();
        if (CollectionUtil.isEmpty(userIds)) {
            return 0;
        }
        // 翻页获取作品
        String opusCursor = "";
        while (true) {
            List<DyHaiHuiOpus> opuses = getHaihuiDyOpus(getOpus(opusCursor, startTime, endTime, userIds));
            if (CollectionUtil.isEmpty(opuses)) {
                break;
            }
            opusCursor = opuses.get(opuses.size() - 1).getAwemeId();
            storeBatch(getHaihuiDyOpus(opuses, userMap));
        }
        cursor.setNext(userArray.getJSONObject(userArray.size() - 1).getString("uid"));
        return userMap.size();
    }

    private static @NotNull Map<String, JSONObject> getUserMap(JSONArray dataView) {
        if (Objects.isNull(dataView)) {
            return Map.of();
        }
        return dataView.stream()
                .map(item -> (JSONObject) item)
                .filter(user -> Strings.isNotBlank(user.getString("uid")))
                .collect(Collectors.toMap(user -> user.getString("uid"), Function.identity()));
    }

    private JSONArray getUser(String next) {
        if (Strings.isBlank(next)) {
            next = "";
        }
        final QueryBuilder queryBuilder = holo.newQueryBuilder()
                .template(String.format(HOLO_PAGE_QUERY, next, 200));
        return holo.query(queryBuilder).data();
    }

    private JSONObject getOpus(String cursor, String beginTime, String stopTime, List<String> uids) {
        if (CollectionUtil.isEmpty(uids)) {
            return null;
        }
        if (Strings.isBlank(cursor)) {
            cursor = "";
        }
        return dyEs.query(dyEs.newQueryBuilder().template(DY_ES_OPUS_SYNC)
                .addParam("size", 1000)
                .addParam("uids", uids)
                .addParam("searchAfter", cursor)
                .addParam("startTime", beginTime)
                .addParam("endTime", stopTime)).data();
    }


    private static List<DyHaiHuiOpus> getHaihuiDyOpus(JSONObject object) {
        return EsUtil.listHitsToEntity(object, json -> {
            JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
            if (Objects.isNull(sourceObj)) {
                return null;
            }
            return JsonParser.parseObject(sourceObj, DyHaiHuiOpus.class);
        });
    }

    private List<DyHaiHuiOpus> getHaihuiDyOpus(List<DyHaiHuiOpus> opuses, Map<String, JSONObject> userMap) {
        final List<String> opusIds = opuses.stream().map(DyHaiHuiOpus::getAwemeId).toList();
        Map<String, String> summaryMap = xhsHaihuiLdmService.getCoverSummaryMap(opusIds);
        return Optional.of(opuses).map(list -> list.stream()
                .filter(opus -> Strings.isNotBlank(opus.getUid()))
                .map(opus -> getDyHaiHuiFinalOpus(userMap, opus, summaryMap)).toList()
        ).orElse(Collections.emptyList());
    }

    private static @NotNull DyHaiHuiOpus getDyHaiHuiFinalOpus(Map<String, JSONObject> userMap,
                                                              DyHaiHuiOpus opus,
                                                              Map<String, String> summaryMap) {
        JSONObject object = userMap.get(opus.getUid());
        if (Objects.isNull(object)) {
            object = userMap.get(opus.getAuthorUserId());
        }
        DyHaiHuiOpus finalOpus = JsonParser.parseObject(object, DyHaiHuiOpus.class);

        finalOpus.setTagsRelation(getTagsRelation(object));
        finalOpus.setIndustryInfo(getIndustryInfo(object));

        finalOpus.setAwemeId(opus.getAwemeId());
        finalOpus.setAwemeDesc(opus.getAwemeDesc());
        finalOpus.setOcrContent(opus.getOcrContent());
        finalOpus.setAwemeType(opus.getAwemeType());
        finalOpus.setCreateTime(opus.getCreateTime());

        finalOpus.setCoverSummary(Optional.ofNullable(summaryMap.get(opus.getAwemeId())).orElse(""));
        finalOpus.setTextField(getTextField(finalOpus));
        return finalOpus;
    }

    private static String getIndustryInfo(JSONObject object) {
        return Optional.ofNullable(object.getJSONArray("industry_info"))
                .map(array -> array.toJSONString()).orElse("");
    }

    private static String getTagsRelation(JSONObject object) {
        List<String> tagsRelation = Optional.ofNullable(object.getJSONArray("tags_relation"))
                .map(tags -> tags.stream()
                        .map(tag -> JSON.parseObject((String) tag))
                        .flatMap(tagJson -> tagJson.keySet().stream()
                                .flatMap(key -> tagJson.getJSONArray(key).stream()
                                        .map(value -> key + "-" + value)
                                ))
                        .toList())
                .orElse(Collections.emptyList());
        return JSON.toJSONString(tagsRelation);
    }


    private static String getTextField(DyHaiHuiOpus opus) {
        String interval = "。\n";
        List<String> fields = Arrays.asList(
                opus.getAwemeDesc(),
                opus.getOcrContent(),
                opus.getCoverSummary()
        );
        return fields.stream().filter(Strings::isNotBlank).collect(Collectors.joining(interval));
    }
}
