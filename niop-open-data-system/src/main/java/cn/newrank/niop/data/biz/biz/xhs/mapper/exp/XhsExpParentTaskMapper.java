package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;

import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpParentTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1 10:20:07
 */
@Mapper
public interface XhsExpParentTaskMapper {

    /**
     * 创建表
     * @param partition 分区
     * @param execDate 执行日期
     * @return 创建结果
     */
    boolean createTable(@Param("partition") String partition,
                        @Param("execDate") LocalDate execDate);


    /**
     * 删除表
     * @param partition 分区
     * @return 删除结果
     */
    boolean dropTable(@Param("partition") String partition);

    /**
     * 创建任务
     * @param items 任务列表
     * @return 创建结果
     */
    boolean storeBatch(@Param("items") List<XhsExpParentTask> items,
                       @Param("partition") String partition);


    /**
     * 根据任务ID获取任务
     * @param parentIds 任务ID
     * @return 任务
     */
    List<XhsExpParentTask> getParentTasksByIds(@Param("execDate") LocalDate execDate,
                                               @Param("parentIds") List<String> parentIds);


    /**
     * 根据任务ID获取任务
     * @param parentIds 任务ID
     * @return 任务
     */
    List<XhsExpParentTask> getRunningParentTasksByIds(@Param("execDate") LocalDate execDate,
                                               @Param("parentIds") List<String> parentIds);


    /**
     * 更新任务状态
     * @param task 任务
     * @return 更新结果
     */
    boolean updateParentTaskStatus(@Param("task") XhsExpParentTask task);


    /**
     * 更新任务作品数
     * @return 更新结果
     */
    boolean updateParentTaskValue(@Param("task") XhsExpParentTask task);

    /**
     * 根据话题ID获取任务ID
     * @param topicId 话题ID
     * @param execDate 日期
     * @return 任务ID
     */
    String getParentIdByTopicId(@Param("execDate") LocalDate execDate,
                                @Param("topicId") String topicId);
}
