package cn.newrank.niop.data.biz.component.biz;

import cn.newrank.niop.data.util.FieldVisitor;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;

/**
 * 存储对象父类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/31 10:34
 */
@Getter
@Setter
public abstract class StorageEntity {
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * id
     */
    protected Integer id;
    /**
     * 创建时间
     */
    protected Timestamp gmtCreate;
    /**
     * 修改时间
     */
    protected Timestamp gmtModified;

    /**
     * 唯一标识
     *
     * @return String
     */
    public abstract String identifier();

    public String toJSONString() {
        return JSON.toJSONString(toJSONObject());
    }

    public String toJSONString(boolean ignoreCalculation) {
        return JSON.toJSONString(toJSONObject(ignoreCalculation));
    }

    public JSONObject toJSONObject(boolean ignoreCalculation) {
        final JSONObject json = (JSONObject) JSON.toJSON(this);
        json.remove("id");
        json.remove("gmtCreate");
        json.remove("gmtModified");
        json.remove("version");

        if (ignoreCalculation) {
            Calculations.getCalculationFields(this).forEach(json::remove);
        }

        FieldVisitor.visitTimestamp(this, (name, timestamp) -> {
            if (timestamp != null) {
                // 时间格式化
                json.put(name, DATETIME_FORMATTER.format(timestamp.toLocalDateTime()));
            }
        });

        return json;
    }

    public JSONObject toJSONObject() {
        return toJSONObject(false);
    }
}
