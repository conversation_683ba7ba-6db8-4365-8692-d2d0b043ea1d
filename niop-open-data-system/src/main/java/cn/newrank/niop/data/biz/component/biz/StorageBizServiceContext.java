package cn.newrank.niop.data.biz.component.biz;

import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 10:00
 */
@Log4j2
@Component
public class StorageBizServiceContext implements ApplicationContextAware {
    private final ConcurrentMap<StorageBiz, StorageBizService<? extends StorageEntity>> storageBizServiceMap
            = new ConcurrentHashMap<>();


    ApplicationContext ioc;

    public StorageBizService<? extends StorageEntity> getService(StorageBiz storageBiz) {
        return storageBizServiceMap.get(storageBiz);
    }


    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.ioc = applicationContext;
        for (StorageBiz storageBiz : StorageBiz.values()) {
            storageBizServiceMap.put(storageBiz, ioc.getBean(storageBiz.storageBizServiceClass));
        }

        log.info("StorageBizServiceContext init completely");
    }
}
