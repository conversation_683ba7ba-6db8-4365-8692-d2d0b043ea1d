package cn.newrank.niop.data.biz.biz.xhs.pojo.exp;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsExpTaskEnum;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.nrcore.utils.UuidUtils;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2025/3/31 14:24:03
 */
@Data
public class XhsExpParentTask {
    /**
     * 话题任务ID
     */
    String parentId;
    /**
     * 话题ID
     */
    String topicId;
    /**
     * 话题权重
     */
    Integer topicWeight;
    /**
     * 执行时间
     */
    LocalDate execDate;
    /**
     * 任务状态
     */
    Integer taskStatus;
    /**
     * 任务触发时间
     */
    LocalDateTime triggerTime;

    /**
     * 最小作品发布时间
     */
    LocalDateTime minPublishTime;

    /**
     * 当前页码
     */
    Integer currentPageNum;


    /**
     * 当前作品数量
     */
    Integer currentOpusNum;




    public static XhsExpParentTask initTask(XhsExpTopic topic,
                                            LocalDateTime triggerTime,
                                            LocalDateTime minPublishTime) {
        final LocalDate execDate = triggerTime.toLocalDate();
        XhsExpParentTask main = new XhsExpParentTask();
        main.setParentId(getParentId(topic.getTopicId(), triggerTime));
        main.setTopicId(topic.getTopicId());
        main.setTopicWeight(topic.getTopicWeight());
        main.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.CREATE.getDbCode()));
        main.setExecDate(execDate);
        main.setCurrentPageNum(1);
        main.setCurrentOpusNum(0);
        main.setMinPublishTime(minPublishTime);
        main.setTriggerTime(triggerTime);
        return main;
    }

    public static String getParentId(String topicId, LocalDateTime triggerTime) {
        return topicId + "-" + triggerTime.format(DatePattern.PURE_DATETIME_FORMATTER);
    }
}
