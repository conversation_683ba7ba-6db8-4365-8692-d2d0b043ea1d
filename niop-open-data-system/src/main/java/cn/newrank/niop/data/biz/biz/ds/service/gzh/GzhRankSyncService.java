package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.GzhEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_GZH_RANK_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class GzhRankSyncService implements SyncBizService<GzhEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public GzhRankSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseGzhEsConfig();
    }

    @Override
    public String getIndexName() {
        return "weixin_rank_week";
    }

    @Override
    public String getRangIndex() {
        return "rankDate";
    }

    @Override
    public String getUniqueIndex() {
        return MAIN_GZH_RANK_FIELD_MAPPING.get("uid");
    }

    @Override
    public String getRangField() {
        return "log1pMark";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_GZH_RANK_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        sourceFields.add("account");
        sourceFields.add("wxId");
        sourceFields.add("uid");
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }

    @Override
    public List<GzhEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), GzhEsMetaData.class);
        });
    }

    @Override
    public List<GzhEsMetaData> convertData(List<GzhEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (GzhEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_GZH_RANK_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", "gzh");
            mainMap.put("index_id", "gzh" + "_" + mainMap.get("uid"));
            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), GzhEsMetaData.class);
    }
}
