package cn.newrank.niop.data.biz.biz.dy.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @createTime 2024/10/17
 * @description
 */
@Data
@Accessors(chain = true)
public class DyShopCouponsDTO {

    private String useAreaDesc;
    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String couponTitle;

    /**
     *
     */
    private String whatCoupon;

    /**
     *
     */
    private String couponName;

    /**
     *
     */
    private String activityId;

    /**
     *
     */
    private Integer activityType;

    /**
     *
     */
    private String credit;

    /**
     *
     */
    private Long threshold;

    /**
     *
     */
    private Long discount;

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String expireTime;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     * 打折描述
     */
    private String couponsDesc;

    /**
     * 分区位点
     */
    private String partitionOffset;
}
