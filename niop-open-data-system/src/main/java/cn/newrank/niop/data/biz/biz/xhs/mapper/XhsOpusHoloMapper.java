package cn.newrank.niop.data.biz.biz.xhs.mapper;


import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusSaveHolo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多回调源存-小红书作品数据-直接写holo
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
@DS("holo")
public interface XhsOpusHoloMapper {


    /**
     * 批量存-新表
     *
     * @param items 批量数据
     * @return 存结果
     */
    boolean storeBatch(@Param("items") List<XhsOpusSaveHolo> items);
}




