package cn.newrank.niop.data.biz.biz.xhs.mapper;


import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsTopicUniIndex;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多回调源存-小红书作品数据-覆盖写lindorm
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
@DS("xhs")
public interface XhsTopicUniIndexLmMapper {

    /**
     * 批量存储基础数据
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeBaseBatch(@Param("items") List<XhsTopicUniIndex> items);
    /**
     * 批量存储话题计算结果数据
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeCalResultBatch(@Param("items") List<XhsTopicUniIndex> items);
    /**
     * 批量存储话题计算结果数据
     * @param item 存储对象
     * @return 存储结果
     */
    boolean storeCalResult(@Param("item") XhsTopicUniIndex item);
    /**
     * 批量存储话题品牌识别
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeBrandRecBatch(@Param("items") List<XhsTopicUniIndex> items);
    /**
     * 批量存储话题品类识别
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeCategoryRecBatch(@Param("items") List<XhsTopicUniIndex> items);

}




