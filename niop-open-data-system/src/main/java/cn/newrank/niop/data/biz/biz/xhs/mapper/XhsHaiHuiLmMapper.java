package cn.newrank.niop.data.biz.biz.xhs.mapper;


import cn.newrank.niop.data.biz.biz.xhs.pojo.haihui.XhsHaiHuiOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多回调源存-小红书作品数据-覆盖写lindorm
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
@DS("haihui")
public interface XhsHaiHuiLmMapper {

    /**
     * 批量存储-无上次更新时间-新表
     *
     * @param items 存储对象
     * @return 存储结果
     */
    boolean saveBatch(@Param("items") List<XhsHaiHuiOpus> items);

    /**
     * 批量存储-无上次更新时间-新表
     *
     * @param items 存储对象
     * @return 存储结果
     */
    boolean addFieldSaveBatch(@Param("items") List<XhsHaiHuiOpus> items);


    /**
     * 批量存储-无上次更新时间-新表
     *
     * @param items 存储对象
     * @return 存储结果
     */
    List<XhsHaiHuiOpus> searchOpusBatch(@Param("items") List<String> items);


    List<XhsHaiHuiOpus> pageXhsOpus(@Param("cursor") String cursor, @Param("size") int size);

    void saveBatchUser(@Param("items") List<XhsHaiHuiOpus> items);

}




