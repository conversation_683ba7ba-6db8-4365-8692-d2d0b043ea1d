package cn.newrank.niop.data.biz.pojo.enums;

public enum AppEnvEnum {

    /**
     * 应用环境
     */
    DEV("dev"),
    TEST("test"),
    PRODUCT("product");

    final String envName;

    AppEnvEnum(String envName) {
        this.envName = envName;
    }

    public static AppEnvEnum parse(String envName) {
        for (AppEnvEnum value : values()) {
            if (value.envName.equalsIgnoreCase(envName)) {
                return value;
            }
        }
        return null;
    }

    public String getEnvName() {
        return envName;
    }

}