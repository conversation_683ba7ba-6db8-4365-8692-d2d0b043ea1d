package cn.newrank.niop.data.biz.dao.mapper;

import cn.newrank.niop.data.biz.pojo.param.DynamicInterfacePageQuery;
import cn.newrank.niop.data.biz.pojo.po.DynamicInterfacePo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:50
 */
@Mapper
public interface DynamicInterfaceMapper {


    void save(DynamicInterfacePo interfacePo);

    void delete(@Param("interfaceId") String interfaceId);

    int update(DynamicInterfacePo interfacePo);

    Page<DynamicInterfacePo> page(@Param("mybatisPlusPage") Page<DynamicInterfacePo> mybatisPlusPage,
                                  @Param("pageQuery") DynamicInterfacePageQuery pageQuery);

    DynamicInterfacePo get(String interfaceId);

    DynamicInterfacePo getByName(String name);
}




