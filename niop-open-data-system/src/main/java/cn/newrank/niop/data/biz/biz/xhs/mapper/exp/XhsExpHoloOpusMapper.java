package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;

import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopicOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/1 10:20:07
 */
@DS("holo")
@Mapper
public interface XhsExpHoloOpusMapper {


    /**
     * 存储作品列表
     *
     * @param taskId   任务ID
     * @param parentId 父任务ID
     * @param topicId  话题ID
     * @param items    作品列表
     * @return 存储结果
     */
    boolean storeBatch(@Param("taskId") String taskId,
                       @Param("parentId") String parentId,
                       @Param("topicId") String topicId,
                       @Param("acqTime") LocalDateTime acqTime,
                       @Param("items") List<XhsExpTopicOpus> items,
                       @Param("ds") String ds,
                       @Param("dateTime") LocalDateTime dateTime);

    /**
     * 存储作品列表
     *
     * @param taskId   任务ID
     * @param parentId 父任务ID
     * @param topicId  话题ID
     * @param items    作品列表
     * @return 存储结果
     */
    boolean storeBatchTest(@Param("taskId") String taskId,
                           @Param("parentId") String parentId,
                           @Param("topicId") String topicId,
                           @Param("acqTime") LocalDateTime acqTime,
                           @Param("items") List<XhsExpTopicOpus> items,
                           @Param("ds") String ds,
                           @Param("dateTime") LocalDateTime dateTime);


    /**
     * 根据父任务ID获取作品数量
     *
     * @param parentId 父任务ID
     * @return 作品数量
     */
    Integer getOpusNumByParentTaskId(@Param("parentId") String parentId);

    /**
     * 根据作品ID列表获取已经存在作品数量
     *
     * @param opusIds 作品ID列表
     * @return 作品数量
     */
    Integer getOpusNumIsExist(@Param("items") Set<String> opusIds);


    /**
     * 根据作品ID列表获取已经存在作品数量
     *
     * @param cursor 作品ID列表
     * @return 作品数量
     */
    List<XhsExpTopicOpus> listOpusIds(@Param("cursor") String cursor,
                                      @Param("size") Integer size,
                                      @Param("ds") String ds);

    /**
     * 判断用户是否存在
     *
     * @return 用户是否存在
     */
    List<String> isUserExist(@Param("ds") String ds, @Param("items") Set<String> opusIds);
}
