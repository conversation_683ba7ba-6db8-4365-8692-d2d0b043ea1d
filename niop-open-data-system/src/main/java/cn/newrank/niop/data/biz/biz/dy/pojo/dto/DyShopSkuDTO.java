package cn.newrank.niop.data.biz.biz.dy.pojo.dto;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024/10/16
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DyShopSkuDTO extends StorageEntity {

    /**
     * 商品id(logic_id)
     */
    private String productId;

    /**
     * sku长id
     */
    private String skuLongId;

    /**
     * 规格价格
     */
    private String skuPrice;

    /**
     * 规格库存
     */
    private Long skuStockNum;

    /**
     * 活动sku编码
     */
    private String skuId;

    /**
     * 常规sku编码
     */
    private String skuExtra;

    /**
     * 颜色分类
     */
    private List<String> specsName;

    /**
     * name：规格类型名称
     * id:规格类型id
     */
    private List<JSONObject> specsItems;

    /**
     * 规格图片
     */
    private String pic;

    /**
     * 数据来源
     */
    private String deviceName;


    /**
     * 分区位点
     */
    private String partitionOffset;


    @Override
    public String identifier() {
        return "skuLongId";
    }
}
