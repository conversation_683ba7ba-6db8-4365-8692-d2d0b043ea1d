package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceSchemaMapper;
import cn.newrank.niop.data.biz.pojo.po.DynamicInterfaceSchemaPo;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import com.alibaba.nacos.api.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:51
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicInterfaceSchemaDao {

    private final RedissonClient redissonClient;
    private final DynamicInterfaceSchemaMapper dynamicInterfaceSchemaMapper;


    public void create(DynamicInterfaceSchema dynamicInterfaceSchema) {
        final DynamicInterfaceSchemaPo interfaceSchemaPo = DynamicInterfaceSchemaPo.fromDto(dynamicInterfaceSchema);

        try {
            dynamicInterfaceSchemaMapper.create(interfaceSchemaPo);

            getCachedInterface(interfaceSchemaPo.getInterfaceId()).delete();
        } catch (Exception e) {
            throw createDbError(e, "创建动态接口Schema失败");
        }
    }

    public void update(DynamicInterfaceSchema dynamicInterfaceSchema) {
        final DynamicInterfaceSchemaPo interfaceSchemaPo = DynamicInterfaceSchemaPo.fromDto(dynamicInterfaceSchema);

        try {
            dynamicInterfaceSchemaMapper.update(interfaceSchemaPo);

            getCachedInterface(interfaceSchemaPo.getInterfaceId()).delete();
        } catch (Exception e) {
            throw createDbError(e, "更新动态接口Schema失败");
        }
    }

    private RBucket<String> getCachedInterface(String interfaceId) {
        return redissonClient.getBucket("dynamic:interface:schema" + interfaceId);
    }

    public DynamicInterfaceSchema getByInterfaceId(String interfaceId) {
        if (StringUtils.isBlank(interfaceId)) {
            return null;
        }

        final RBucket<String> cachedInterface = getCachedInterface(interfaceId);

        if (cachedInterface.isExists()) {
            return DynamicInterfaceSchema.fromJson(cachedInterface.get());
        }

        final DynamicInterfaceSchemaPo interfaceSchemaPo = dynamicInterfaceSchemaMapper.get(interfaceId);
        if (interfaceSchemaPo == null) {
            cachedInterface.set("");
            cachedInterface.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterfaceSchema dynamicInterfaceSchema = interfaceSchemaPo.toDto();

        cachedInterface.set(dynamicInterfaceSchema.toJSONString());
        cachedInterface.expire(Duration.ofDays(15));

        return dynamicInterfaceSchema;
    }

}
