package cn.newrank.niop.data.biz.callback.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.callback.pojo.CdTaskConfig;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.CbEnableParamSubCache;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SendStrategy;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class BaseRedirectService<T extends CallbackRedirect> {

    protected final CallbackService callbackService;
    private final CommonRedirectService commonRedirectService;
    protected final SubscriberConfigService subscriberConfigService;

    public BaseRedirectService(CallbackService callbackService,
                               CommonRedirectService commonRedirectService,
                               SubscriberConfigService subscriberConfigService) {
        this.callbackService = callbackService;
        this.commonRedirectService = commonRedirectService;
        this.subscriberConfigService = subscriberConfigService;
    }

    // 结果为空异常重试 + 结果解析异常重试 + 回调业务异常重试
    public List<CallbackRetry> redirectCallback(List<T> redirectList) {
        if (CollUtil.isEmpty(redirectList)) {
            return Collections.emptyList();
        }

        Map<String /*taskId*/, T> redirectMap = redirectList.stream()
                .collect(Collectors.toMap(T::getSourceKey, Function.identity(), (v1, v2) -> v2));

        // 构建cbId-taskId的映射
        CdTaskConfig taskConfig = commonRedirectService.buildCbIdTaskIdMap(new HashMap<>(redirectMap));

        // 通过taskId查询原始和解析的回调结果
        final Map<String, JSONObject> rs = commonRedirectService.getResults(
                taskConfig.getAllTaskIds()
        );
        // 查询能力请求参数(针对[开启了携带参数的]才查询)
        final Map<String, JSONObject> params = commonRedirectService.getParams(
                taskConfig.getEnabledTaskIds()
        );

        // 重试列表
        List<CallbackRetry> retryList = new ArrayList<>();

        taskConfig.getCbWithParsed().forEach((cbId, taskIds) -> {
            // 处理过的回调列表
            final List<T> redirects = taskIds.stream()
                    .distinct()
                    .map(taskId -> {
                        final T redirect = redirectMap.get(taskId);
                        final JSONObject result = rs.get(taskId);

                        // 创建解析回调重定向
                        return newCallbackRedirect(redirect, JSONObject.of(), result, SubSourceType.ABILITY_PARSED_RESULT, retryList);
                    })
                    // 过滤空回调结果
                    .filter(Objects::nonNull)
                    .toList();

            // 回调
            if (CollUtil.isNotEmpty(redirects)) {
                commonRedirectService.callback(cbId, new ArrayList<>(redirects), new HashMap<>(redirectMap), retryList);
            }

        });

        taskConfig.getCbWithRaw().forEach((cbId, taskIds) -> {
            // 处理过的回调列表
            final List<T> redirects = taskIds.stream()
                    .distinct()
                    .map(taskId -> {
                        final T redirect = redirectMap.get(taskId);
                        final JSONObject result = rs.get(taskId);

                        // 创建原始回调重定向
                        return newCallbackRedirect(redirect, JSONObject.of(), result, SubSourceType.ABILITY_RAW_RESULT, retryList);
                    })
                    // 过滤空回调重定向
                    .filter(Objects::nonNull)
                    .toList();

            // 回调
            if (CollUtil.isNotEmpty(redirects)) {
                commonRedirectService.callback(cbId, new ArrayList<>(redirects), new HashMap<>(redirectMap), retryList);
            }

        });

        taskConfig.getCbOldWithRaw().forEach((cbId, taskIds) -> {
            final List<T> redirects = taskIds.stream()
                    .distinct()
                    .map(taskId -> {
                        final T redirect = redirectMap.get(taskId);
                        final JSONObject param = params.getOrDefault(taskId, JSONObject.of());
                        final JSONObject result = rs.get(taskId);

                        // 创建原始回调重定向
                        return newCallbackRedirect(redirect, param, result, SubSourceType.ABILITY_RAW_RESULT_OLD, retryList);
                    })
                    .filter(Objects::nonNull)
                    .toList();
            if (CollUtil.isNotEmpty(redirects)) {
                commonRedirectService.callback(cbId, new ArrayList<>(redirects), new HashMap<>(redirectMap), retryList);
            }
        });

        taskConfig.getCbOldWithParsed().forEach((cbId, taskIds) -> {
            final List<T> redirects = taskIds.stream()
                    .distinct()
                    .map(taskId -> {
                        final T redirect = redirectMap.get(taskId);
                        final JSONObject param = params.getOrDefault(taskId, JSONObject.of());
                        final JSONObject result = rs.get(taskId);

                        // 创建原始回调重定向
                        return newCallbackRedirect(redirect, param, result, SubSourceType.ABILITY_PARSED_RESULT_OLD, retryList);
                    })
                    .filter(Objects::nonNull)
                    .toList();
            if (CollUtil.isNotEmpty(redirects)) {
                commonRedirectService.callback(cbId, new ArrayList<>(redirects), new HashMap<>(redirectMap), retryList);
            }
        });

        // 批量加入回调重定向重试队列 最后执行
        if (CollUtil.isNotEmpty(retryList)) {
            callbackService.sendRetryQueue(retryList);

            // 移除失败的重试
            retryList.removeIf(retry -> {
                RetryStatus retryStatus = retry.getRetryStatus();
                return RetryStatus.ERROR.equals(retryStatus);
            });
        }

        return retryList;
    }

    /**
     * 任务有结果
     *
     * @param redirect 重定向信息
     * @return 是否有结果
     */
    protected boolean hasResult(CallbackRedirect redirect) {
        final Object payload = redirect.getPayload();
        if (payload instanceof JSONObject json) {
            final Integer status = json.getInteger("status");
            final Integer bizCode = json.getInteger("bizCode");
            return status != null && status == 1 && bizCode != null && bizCode == 0;
        }
        return false;
    }

    protected abstract T newCallbackRedirect(T snapshot, JSONObject param, JSONObject result, SubSourceType sourceType, List<CallbackRetry> retryList);

}
