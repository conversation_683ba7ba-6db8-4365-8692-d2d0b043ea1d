package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportResultSource;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.biz.export.service.DataExportTableStorageService;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class AbilityResultTableStoreFetcher extends BaseDataExportResultFetcher {

    private final DataExportTableStorageService exportTableStorageService;

    public AbilityResultTableStoreFetcher(DataExportSubtaskService subtaskService,
                                          DataExportTableStorageService exportTableStorageService) {
        super(subtaskService);
        this.exportTableStorageService = exportTableStorageService;
    }

    @Override
    public DataExportType getDataSourceType() {
        return DataExportType.ABILITY;
    }

    @Override
    public DataExportResultSource getResultSource() {
        return DataExportResultSource.TABLE_STORE_NODE;
    }

    @Override
    public ExportResult queryResult(String resultTaskId, String cursor) {
        final List<DaTaExportTableStorage> storages = exportTableStorageService.queryTableStorageContents(resultTaskId, cursor);
        // 首次查询就是空数据
        if (CollUtil.isEmpty(storages) && CharSequenceUtil.isBlank(cursor)) {
            handleNullData(resultTaskId);
        }
        return CursorExportResult.of(resultTaskId, storages);
    }

}
