package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 16:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DynamicInterfaceAuthUpdate extends DynamicInterfaceAuthCreate {

    @NotBlank(message = "授权ID(authId)不能为空")
    String authId;

    @Override
    public DynamicInterfaceAuth toDto() {
        final DynamicInterfaceAuth interfaceAuth = super.toDto();

        interfaceAuth.setAuthId(authId);

        return interfaceAuth;
    }
}
