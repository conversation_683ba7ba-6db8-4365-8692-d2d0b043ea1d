package cn.newrank.niop.data.common.lock;

import cn.hutool.core.lang.Assert;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.util.Hash;
import cn.newrank.nrcore.exception.BizException;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分布式锁切面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/27 14:24
 */
@Aspect
@Log4j2
@Order(Ordered.HIGHEST_PRECEDENCE)
public class DistributeLockAspect {

    RedissonClient redissonClient;

    public DistributeLockAspect(RedissonClient redissonClient) {
        Assert.notNull(redissonClient, "redissonClient 不能为空");
        this.redissonClient = redissonClient;
    }

    @Pointcut("@annotation(DistributeLock)")
    private void distributeLock() {
        //endpoint
    }

    @Around("distributeLock()")
    public Object lock(ProceedingJoinPoint joinPoint) throws Throwable {

        final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        final DistributeLock distributeLock = signature.getMethod().getAnnotation(DistributeLock.class);

        final String methodKey = getInvokeMethodKey(joinPoint, signature, distributeLock);
        final RLock invokeMethodLock = redissonClient.getLock(methodKey);

        if (invokeMethodLock.tryLock()) {
            try {
                return joinPoint.proceed();
            } catch (Exception e) {
                if (e instanceof BizException) {
                    throw e;
                }

                log.error("方法执行异常， e: ", e);
                throw e;
            } finally {
                invokeMethodLock.unlock();
            }
        }

        final String msg = "DistributeLock[" + compressPackagePath(methodKey) + "] 当前方法正被调用";
        if (distributeLock.abortException()) {
            throw new BizException(BizErr.UNSUPPORTED_ERROR, msg);
        }

        return null;
    }

    private String getInvokeMethodKey(ProceedingJoinPoint joinPoint, MethodSignature signature, DistributeLock distributeLock) {
        final String methodKey = joinPoint.getTarget().getClass().getName() + "#" + signature.getMethod().getName();
        //false: 指定的方法的锁
        //true : 指定到方法参数值的锁(不同的参数值, 获取的锁不同)
        String argKey = "";
        if (distributeLock.includeArgs()) {
            final Object[] args = joinPoint.getArgs();


            if (args != null && args.length > 0) {
                argKey = Stream.of(args)
                        .map(arg -> arg == null ? "" : Hash.sha256(arg.toString()))
                        .collect(Collectors.joining(","));
            }
        }

        return methodKey + "(" + argKey + ")";
    }

    private String compressPackagePath(String reference) {
        if (reference == null) {
            return null;
        }
        final String[] packages = reference.split("\\.");

        final int len = packages.length;
        final int num = 2;
        StringBuilder referenceBuilder = new StringBuilder(packages[len - 1])
                .insert(0, ".")
                .insert(0, packages[len - num]);

        for (int i = len - num; i >= 0; i--) {
            referenceBuilder.insert(0, ".")
                    .insert(0, packages[i].charAt(0));
        }

        return referenceBuilder.toString();
    }
}
