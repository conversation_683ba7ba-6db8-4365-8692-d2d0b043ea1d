package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:40
 */
@Data
public class DynamicInterfaceAuthCreate {
    /**
     * 接口ID
     */
    @NotBlank(message = "接口ID(interfaceId)不能为空")
    private String interfaceId;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID(appId)不能为空")
    private String appId;

    /**
     * QPS 时间间隔
     */
    @NotNull(message = "QPS 时间间隔(refreshSeconds)不能为空")
    private Integer refreshPermits;

    /**
     * QPS 次数
     */
    @NotNull(message = "QPS 次数(refreshSeconds)不能为空")
    private Integer refreshSeconds;

    public DynamicInterfaceAuth toDto() {
        final DynamicInterfaceAuth interfaceAuth = new DynamicInterfaceAuth();

        interfaceAuth.setAppId(this.appId);
        interfaceAuth.setInterfaceId(this.interfaceId);
        interfaceAuth.setRefreshPermits(this.refreshPermits);
        interfaceAuth.setRefreshSeconds(this.refreshSeconds);

        return interfaceAuth;
    }
}
