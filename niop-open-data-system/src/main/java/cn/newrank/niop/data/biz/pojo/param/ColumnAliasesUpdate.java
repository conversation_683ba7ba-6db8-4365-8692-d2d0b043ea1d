package cn.newrank.niop.data.biz.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/9 10:38
 */
@Data
public class ColumnAliasesUpdate {
    @NotBlank(message = "字典ID(dicId)不能为空")
    String dicId;

    @NotBlank(message = "列名(columnName)不能为空")
    String columnName;

    List<String> columnAliases;
}
