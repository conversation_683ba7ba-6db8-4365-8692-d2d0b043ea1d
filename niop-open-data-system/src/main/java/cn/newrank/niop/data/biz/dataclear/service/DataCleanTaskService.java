package cn.newrank.niop.data.biz.dataclear.service;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanRuleCreateQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskPageQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskUpdateStatus;
import cn.newrank.niop.web.model.PageView;

import java.util.List;

public interface DataCleanTaskService {

    /**
     * 创建数据清理
     *
     * @param callbackConfigCreate 数据清理信息
     * @return 数据清理id
     */
    String create(DataCleanRuleCreateQuery dataCleanRuleCreate);


    /**
     * 分页查询
     *
     * @param pageQuery 数据清理信息
     * @return 数据清理信息
     */
    PageView<DataCleanTask> page(DataCleanTaskPageQuery pageQuery);

    /**
     * 更新数据清理
     *
     * @param cleanRuleUpadteQuery 数据清理信息
     * @return 是否更新成功
     */
    boolean update(DataCleanTask cleanRuleUpadteQuery);

    /**
     * 删除数据清理
     *
     * @param cbId 数据清理id
     * @return 是否删除成功
     */
    boolean delete(String cbId);


    /**
     * 获取数据清理配置
     *
     * @param cbId 数据清理id
     * @return 数据清理信息
     */
    DataCleanTask get(String cbId);

    /**
     * 模糊查询
     *
     * @param fuzzyQuery 数据清理信息
     * @return 数据清理信息
     */
    List<DataCleanRuleCreate> fuzzyQuery(DataCleanTask fuzzyQuery);


    boolean checkLastIsExecuted(String ruleId);

    /**
     * 获取5个待执行的任务修改任务为执行中
     *
     * @return List<DataCleanRuleCreate>
     */
    List<DataCleanTask> listWaitingTaskAndLock();

    /**
     * 更新数据清理任务表状态
     *
     * @param cleanTaskUpdateStatus 数据清理信息
     * @return 是否更新成功
     */
    boolean updateStatus(DataCleanTaskUpdateStatus cleanTaskUpdateStatus);

}
