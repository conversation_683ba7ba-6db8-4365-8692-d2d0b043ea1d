package cn.newrank.niop.data.biz.oss.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.oss.pojo.dto.OssSignature;
import cn.newrank.niop.data.biz.oss.service.OssService;
import cn.newrank.niop.data.biz.oss.util.OssUtil;
import cn.newrank.niop.data.config.OssConfig;
import cn.newrank.niop.data.context.AppContext;
import cn.newrank.niop.data.util.Builders;
import cn.newrank.nrcore.web.oss.NrOssService;
import cn.newrank.nrcore.web.oss.enums.ObjectTagEnum;
import cn.newrank.nrcore.web.oss.enums.ObjectTagValueEnum;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PolicyConditions;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

@Log4j2
@Service
public class OssServiceImpl implements OssService {

    private final OSS ossClient;

    private final OssConfig ossConfig;

    private final NrOssService niopOssService;

    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final String OSS_HOST = "https://open-obj.newrank.cn";

    private static final String PATH_SPLIT_LINE = "aliyuncs.com/";

    private static final HashMap<ObjectTagEnum, ObjectTagValueEnum> DEFAULT_TAG = new HashMap<>(2);

    public OssServiceImpl(OSS ossClient, OssConfig ossConfig, NrOssService niopOssService) {
        this.ossClient = ossClient;
        this.ossConfig = ossConfig;
        this.niopOssService = niopOssService;
    }

    @Override
    public OssSignature getSignature(String dir, Duration duration) {
        return getSignature(true, dir, duration);
    }

    @Override
    public OssSignature getSignature(boolean appendBaseDir, String dir, Duration duration) {
        check(dir, duration);

        long expireEndTime = System.currentTimeMillis() + duration.toMillis();
        Date expiration = new Date(expireEndTime);
        PolicyConditions conditions = new PolicyConditions();
        // 文件最大 50M
        conditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 50 * 1024 * 1024L);
        conditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, "");

        String postPolicy = ossClient.generatePostPolicy(expiration, conditions);
        String encodePolicy = BinaryUtil.toBase64String(postPolicy.getBytes(StandardCharsets.UTF_8));
        String postSignature = ossClient.calculatePostSignature(postPolicy);
        final String finalPath = appendBaseDir ? ossConfig.getBasePath() + dir : dir;

        return Builders.of(OssSignature::new)
            .with(OssSignature::setSignature, postSignature)
            .with(OssSignature::setPolicy, encodePolicy)
            .with(OssSignature::setAccessKey, ossConfig.getAk())
            .with(OssSignature::setHost, ossConfig.getPublicOssHost())
            .with(OssSignature::setDir, finalPath)
            .with(OssSignature::setExpireTime, FORMATTER.format(expiration))
            .build();
    }

    @Override
    public OSSObject getObjectFromUrl(String fileUrl) {
        final String key = OssUtil.getOssFileKey(fileUrl);
        return ossClient.getObject(ossConfig.getBucket(), key);
    }

    @Override
    public void deleteObjectByUrl(String fileUrl) {
        final String key = OssUtil.getOssFileKey(fileUrl);
        ossClient.deleteObject(ossConfig.getBucket(), key);
    }

    @Override
    public boolean existObject(String fileUrl) {
        final String key = OssUtil.getOssFileKey(fileUrl);
        return ossClient.doesObjectExist(ossConfig.getBucket(), key);
    }

    @Override
    public String multipartUploadFile(String dir, File file) throws IOException {
        return multipartUploadFile(true, dir, file);
    }

    @Override
    public String multipartUploadFile(boolean appendBaseDir, String dir, File file) throws IOException {
        if (Objects.isNull(file)) {
            throw createParamError("上传文件不能为空");
        }

        checkDir(dir);
        final String finalPath = appendBaseDir ? ossConfig.getBasePath() + dir : dir;
        final String filePath = finalPath + file.getName();
        return niopOssService.multipartUploadFile(ossConfig.getBucket(), filePath, file, CannedAccessControlList.Private, DEFAULT_TAG);
    }

    @Override
    public String authorize(String fileUrl) {
        final String bucket = ossConfig.getBucket();
        final String path = OssUtil.getOssFileKey(fileUrl);
        try {
            // 设置文件的访问权限为私有读
            ossClient.setObjectAcl(bucket, path, CannedAccessControlList.Private);
            // 设置链接过期时间
            Date expiration = new Date(System.currentTimeMillis() + Duration.ofHours(1).toMillis());
            URL url = ossClient.generatePresignedUrl(bucket, path, expiration);
            String urlPath = url.toString();
            String result = AppContext.isProduct() ? OSS_HOST + StrPool.SLASH + CharSequenceUtil.subAfter(urlPath, PATH_SPLIT_LINE, false)
                : urlPath;

            // 去除内网标识
            String publicUrl = result.replace("-internal", "");
            return AppContext.isProduct() ? publicUrl : publicUrl.replace("http", "https");
        } catch (OSSException e) {
            if (Objects.equals("NoSuchKey", e.getErrorCode())) {
                throw createParamError("文件不存在");
            } else {
                throw e;
            }
        }
    }

    private void check(String dir, Duration duration) {
        checkDir(dir);

        if (Objects.isNull(duration) || duration.isNegative() || duration.isZero()) {
            throw createParamError("OSS签名时效参数异常");
        }
    }

    private void checkDir(String dir) {
        if (StrUtil.isBlank(dir) || !dir.endsWith(StrPool.SLASH)) {
            throw createParamError("OSS目录参数异常");
        }
    }

    private String getDir(String dir) {
        return ossConfig.getBasePath() + dir;
    }

}
