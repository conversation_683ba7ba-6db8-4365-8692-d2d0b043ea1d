package cn.newrank.niop.data.biz.export.controller;

import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskReset;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportSubtaskVo;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.web.model.PageView;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/export-subtask")
public class DataExportSubtaskController {

   private final DataExportSubtaskService dataExportSubtaskService;

    public DataExportSubtaskController(DataExportSubtaskService dataExportSubtaskService) {
        this.dataExportSubtaskService = dataExportSubtaskService;
    }

    /**
     * 分页查询导数子任务信息
     *
     * @param query 查询参数
     * @return 分页导数子任务数据
     */
    @GetMapping("/page")
    public PageView<DataExportSubtaskVo> page(@Validated DataExportSubtaskPageQuery query) {
        return dataExportSubtaskService.page(query).convert(DataExportSubtaskVo::buildBy);
    }

    /**
     * 子任务重置
     *
     * @param reset 重置参数
     * @return 是否成功
     */
    @PostMapping("/reset")
    public Boolean taskReset(@Validated @RequestBody DataExportSubtaskReset reset) {
        return dataExportSubtaskService.subtaskReset(reset);
    }

}
