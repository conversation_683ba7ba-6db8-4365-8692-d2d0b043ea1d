package cn.newrank.niop.data.biz.component.biz;

import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.config.SystemConfig;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 16:20
 */
@Component
public class CbConfigManager {
    private final SystemConfig systemConfig;

    public CbConfigManager(SystemConfig systemConfig) {
        this.systemConfig = systemConfig;
    }

    /**
     * 采集分析kafka
     *
     * @param topic kafka topic
     * @return kafka config
     */
    public ConfigProperties acqCallBackConfig(String topic) {
        return key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> "192.168.8.217:9092,192.168.8.216:9092,192.168.8.218:9092";
            case TOPIC -> topic;
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> "newrankcn";
            case PASSWORD -> "Newrank123456";
            default -> null;
        };
    }

    /**
     * api回调kafka
     *
     * @return kafka config
     */
    public ConfigProperties apiCallBackConfig(String topic) {
        if (Strings.isBlank(topic)) {
            throw new IllegalArgumentException("topic can not be blank");
        }
        if (systemConfig.isDevelop()) {
            return key -> switch (key) {
                case KAFKA_BOOTSTRAP_SERVERS -> """
                        alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093,
                        alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093,
                        alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093
                        """;
                case TOPIC -> topic;
                case PROTOCOL -> "PLAINTEXT";
                case USERNAME -> "niop_ds";
                case PASSWORD -> "wKb8LjOGmqxQJowM";
                default -> null;
            };
        }
        return key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS ->  """
                        alikafka-pre-cn-4xl3io1a0002-1-vpc.alikafka.aliyuncs.com:9092,
                        alikafka-pre-cn-4xl3io1a0002-2-vpc.alikafka.aliyuncs.com:9092,
                        alikafka-pre-cn-4xl3io1a0002-3-vpc.alikafka.aliyuncs.com:9092
                        """;
            case TOPIC -> topic;
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> "niop_ds";
            case PASSWORD -> "wKb8LjOGmqxQJowM";
            default -> null;
        };
    }
}
