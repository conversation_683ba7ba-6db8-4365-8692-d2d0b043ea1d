package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.web.model.BizEnum;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

public enum DataExportRunningStatus implements BizEnum {

    /**
     * 导数运行状态
     */
    FINISHED("2", "完成"),
    RUNNING("1", "运行中"),
    PAUSED("0", "已暂停"),
    ;

    private final String code;
    private final String description;

    DataExportRunningStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

    public static DataExportRunningStatus parseByCode(String code) {
        if (StrUtil.isNotBlank(code)) {
            for (DataExportRunningStatus runningStatus : values()) {
                if (runningStatus.getDbCode().equals(code)) {
                    return runningStatus;
                }
            }
        }
        throw createParamError("不支持的运行状态");
    }

}
