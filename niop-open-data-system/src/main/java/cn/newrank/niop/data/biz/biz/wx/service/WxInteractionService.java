package cn.newrank.niop.data.biz.biz.wx.service;

import cn.newrank.niop.data.biz.biz.wx.mapper.WxInteractionMapper;
import cn.newrank.niop.data.biz.biz.wx.pojo.WxInteraction;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/24 16:26
 */
@Service
public class WxInteractionService implements StorageBizService<WxInteraction> {

    private final WxInteractionMapper wxInteractionMapper;

    public WxInteractionService(WxInteractionMapper wxInteractionMapper) {
        this.wxInteractionMapper = wxInteractionMapper;
    }

    @Override
    public void storeBatch(List<WxInteraction> items) {
        wxInteractionMapper.save(items);
    }

    @Override
    public WxInteraction get(String identifier) {
        return wxInteractionMapper.get(identifier);
    }

    @Override
    public WxInteraction castOf(JSONObject item) {
        return StorageBizService.format(WxInteraction.class, item);
    }
}
