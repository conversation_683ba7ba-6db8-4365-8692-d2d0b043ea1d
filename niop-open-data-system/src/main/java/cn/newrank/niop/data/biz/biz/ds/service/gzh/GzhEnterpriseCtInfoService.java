package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.GzhEnterpriseCtInfo;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.TEST_YOU_DU_MAIN_ES_INDEX_20241015;
import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.YOU_DU_MAIN_ES_INDEX_20241015;

/**
 * 大搜Es-公众号企业认证数据同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class GzhEnterpriseCtInfoService implements StorageBizService<GzhEnterpriseCtInfo> {

    private final RestHighLevelClient storageEsClient;
    private final String index;

    public GzhEnterpriseCtInfoService(RestHighLevelClient storageEsClient, SystemConfig systemConfig) {
        this.storageEsClient = storageEsClient;
        this.index = systemConfig.isProduct() ? YOU_DU_MAIN_ES_INDEX_20241015 : TEST_YOU_DU_MAIN_ES_INDEX_20241015;
    }


    @Override
    public void storeBatch(List<GzhEnterpriseCtInfo> items) {
        List<String> indexIds = Iterables.toList(items, GzhEnterpriseCtInfo::getIndexId);

        final SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(indexIds.size());
        searchSourceBuilder.query(QueryBuilders.termsQuery("index_id", indexIds));
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.fetchSource(new String[]{"index_id"}, null);

        try {
            Map<String, String> docIdMap = Maps.newHashMapWithExpectedSize(indexIds.size());
            SearchResponse searchResponse = storageEsClient.search(searchRequest, RequestOptions.DEFAULT);
            for (SearchHit hit : searchResponse.getHits()) {
                String indexId = hit.getSourceAsMap().get("index_id").toString();
                String docId = hit.getId();
                docIdMap.put(indexId, docId);
            }

            final BulkRequest bulkRequest = new BulkRequest(index);
            for (GzhEnterpriseCtInfo item : items) {
                final String docId = docIdMap.get(item.getIndexId());
                if (StrUtil.isBlank(docId)) {
                    continue;
                }

                final UpdateRequest updateRequest = new UpdateRequest();
                updateRequest
                        .id(docId)
                        .doc(EsCodec.serialize(item), XContentType.JSON);

                bulkRequest.add(updateRequest);
            }

            if (bulkRequest.numberOfActions() == 0) {
                return;
            }

            storageEsClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        } catch (Exception e) {
            log.error("[GzhEnterpriseCtInfoService] e:", e);
        }
    }

    @Override
    public GzhEnterpriseCtInfo castOf(JSONObject item) {
        return EsCodec.deserialize(item.toJSONString(), GzhEnterpriseCtInfo.class);
    }
}
