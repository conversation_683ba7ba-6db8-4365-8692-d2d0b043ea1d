package cn.newrank.niop.data.biz.dao.mapper;

import cn.newrank.niop.data.biz.pojo.po.DynamicInterfaceAuthPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:48
 */
@Mapper
public interface DynamicInterfaceAuthMapper {


    void insert(DynamicInterfaceAuthPo authPo);

    int count(@Param("interfaceId") String interfaceId, @Param("appId") String appId);

    int delete(String authId);

    int update(DynamicInterfaceAuthPo authPo);

    List<DynamicInterfaceAuthPo> list(String interfaceId);

    DynamicInterfaceAuthPo get(String authId);

    DynamicInterfaceAuthPo getByInterfaceIdAndAppId(@Param("interfaceId") String interfaceId,
                                                    @Param("appId") String appId);
}




