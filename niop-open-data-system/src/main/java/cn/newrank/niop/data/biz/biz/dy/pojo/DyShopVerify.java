package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopVerifyDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_shop_verify
 */
@Accessors(chain = true)
@Data
public class DyShopVerify implements Serializable {
    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String title;

    /**
     *
     */
    private String priceInfo;

    /**
     *
     */
    private Boolean onSale;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     *
     */
    private String partitionOffset;

    /**
     * 售出描述
     */
    private String saleInfo;

    private static final long serialVersionUID = 1L;


    public static DyShopVerify createItem(DyShopVerifyDTO dto) {
        return new DyShopVerify()
                .setProductId(dto.getProductId())
                .setTitle(dto.getTitle())
                .setPriceInfo(dto.getPriceInfo())
                .setOnSale(dto.getOnSale())
                .setDeviceName(dto.getDeviceName())
                .setPartitionOffset(dto.getPartitionOffset())
                .setSaleInfo(dto.getSaleInfo());
    }
}