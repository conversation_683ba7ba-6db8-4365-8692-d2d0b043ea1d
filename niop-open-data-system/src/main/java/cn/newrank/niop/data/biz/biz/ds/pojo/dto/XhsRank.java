package cn.newrank.niop.data.biz.biz.ds.pojo.dto;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * xhs 榜单更新数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 10:46
 */
@Data
public class XhsRank implements EsEntity {
    String accountId;
    Double nrIndexWeek;
    String nrIndexWeekDate;
    String indexId;

    public static XhsRank of(JSONObject item) {
        final XhsRank xhsRank = new XhsRank();

        xhsRank.setAccountId(item.getString("account_id"));
        xhsRank.setNrIndexWeek(item.getDouble("nr_index_week"));
        xhsRank.setNrIndexWeekDate(item.getString("nr_index_week_date"));
        xhsRank.setIndexId(item.getString("index_id"));
        return xhsRank;
    }

    @Override
    public String docId() {
        return indexId;
    }
}
