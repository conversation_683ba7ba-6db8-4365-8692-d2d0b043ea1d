package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.biz.callback.service.CallbackRedirectService;
import cn.newrank.niop.data.biz.service.CallbackService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.util.List;
import java.util.stream.StreamSupport;

/**
 * 能力回调消费者服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 20:13
 */
@Slf4j
public class AbilityCallbackConsumeService implements ConsumeService {

    protected final CallbackRedirectService redirectService;
    protected final CallbackService callbackService;

    public AbilityCallbackConsumeService(CallbackRedirectService redirectService,
                                         CallbackService callbackService) {
        this.redirectService = redirectService;
        this.callbackService = callbackService;
    }

    @Override
    public boolean consume(ConsumerRecords<String, String> consumerRecords) {
        List<CallbackRedirect> redirectList = StreamSupport.stream(consumerRecords.spliterator(), false)
                .map(consumerRecord -> {
                    final CallbackRedirect redirect = new CallbackRedirect();
                    redirect.setConsumerRecord(consumerRecord);

                    // 序列化载体
                    ConsumerRecordCarrier carrier = ConsumerRecordCarrier.buildBy(consumerRecord);
                    redirect.setConsumerRecordCarrier(carrier);

                    final JSONObject json = JSON.parseObject(consumerRecord.value());
                    redirect.setAppId(json.getString("appId"));
                    redirect.setSourceId(json.getString("abilityId"));
                    redirect.setSourceKey(json.getString("taskId"));
                    redirect.setPayload(json);

                    return redirect;
                }).toList();

        redirectService.redirectCallback(redirectList);

        return true;
    }

}
