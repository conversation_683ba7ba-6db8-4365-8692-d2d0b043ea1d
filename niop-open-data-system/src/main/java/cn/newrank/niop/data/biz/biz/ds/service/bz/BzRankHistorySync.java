package cn.newrank.niop.data.biz.biz.ds.service.bz;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.RankHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 12:56
 */
@Service
public class BzRankHistorySync extends RankHistorySync {

    static final String RANK_QUERY = """
            GET bili_user_detail_rank_week/_search
              {
                "size": 10000,
                "_source": [
                  "mid",
                  "newrank_index",
                  "opus_count",
                  "video_review_count",
                  "coin_count",
                  "comment_count",
                  "like_count",
                  "share_count",
                  "rank_date"
                ],
                "query": {
                  "bool": {
                    "must": [
                      {
                        "terms": {
                          "mid": <foreach collection="mids" item="mid" open="[" separator="," close="]">
                                    #{mid}
                                </foreach>
                        }
                      },
                      {
                        "range": {
                          "newrank_index": {
                            "gt": 0
                          }
                        }
                      }
                    ]
                  }
                },
                "sort": [
                  {
                    "mid": {
                      "order": "desc"
                    }
                  }
                ]
              }
            """;
    public static final String RANK_DATE = "rank_date";
    protected final Datasource datasource;

    protected BzRankHistorySync(RedissonClient redissonClient,
                                CommonEsService commonEsService,
                                DsConfigManager dsConfigManager) {
        super("bili", redissonClient, commonEsService);
        this.datasource = EsFactory.DEFAULT.create(dsConfigManager.chooseBzEsConfig());
    }

    @Override
    protected Map<String, JSONObject> rankIndexes(List<String> ids) {
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(RANK_QUERY)
                .addParam("mids", ids);
        final JSONObject resp = datasource.query(queryBuilder).data();
        final List<JSONObject> items = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream()
                .map(json -> json.getJSONObject("_source"))
                .toList();
        final Map<String, JSONObject> map = new HashMap<>();
        for (JSONObject item : items) {
            map.compute(item.getString("mid"), (uid, rank) -> {
                if (rank == null) {
                    return item;
                }

                if (DateTimeUtil.toDateTime(rank.getString(RANK_DATE))
                        .isBefore(DateTimeUtil.toDateTime(item.getString(RANK_DATE)))) {
                    return item;
                }

                return rank;
            });
        }

        return map;
    }


    @Override
    protected EsEntity castOf(JSONObject rank, String docId) {
        final RankHistoryUpdate update = new RankHistoryUpdate();
        update.setIndexId(docId);
        update.setAccountId(rank.getLong("mid"));
        update.setNrIndexWeek(rank.getDouble("newrank_index"));
        update.setLastWeekAwemeCount(rank.getLong("opus_count"));
        update.setLastWeekBarrageCount(rank.getLong("video_review_count"));
        update.setLastWeekCoinCount(rank.getLong("coin_count"));
        update.setLastWeekCommentCount(rank.getLong("comment_count"));
        update.setLastWeekLikeCount(rank.getLong("like_count"));
        update.setLastWeekShareCount(rank.getLong("share_count"));
        update.setNrIndexWeekDate(rank.getString(RANK_DATE));


        return update;
    }

    @Data
    public static class RankHistoryUpdate implements EsEntity {

        Long accountId;
        Double nrIndexWeek;
        Long lastWeekAwemeCount;
        Long lastWeekBarrageCount;
        Long lastWeekCoinCount;
        Long lastWeekCommentCount;
        Long lastWeekLikeCount;
        String nrIndexWeekDate;
        Long lastWeekShareCount;
        String indexId;

        @Override
        public String docId() {
            return indexId;
        }
    }
}
