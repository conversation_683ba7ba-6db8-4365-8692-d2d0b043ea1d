package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_buyin_room】的数据库操作Mapper
 * @createDate 2024-10-29 16:00:34
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoom
 */
@Mapper
public interface DyBuyinRoomMapper {

    /**
     * 插入一条数据
     *
     * @param item
     */
    void insertOne(@Param("item") DyBuyinRoom item);
}




