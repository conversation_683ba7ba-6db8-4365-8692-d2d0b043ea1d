package cn.newrank.niop.data.biz.biz.ds.service.common.temp;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 12:27
 */
public abstract class RankHistorySync extends DefaultHistorySynchronizer<String> {
    static final String SCROLL_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "15m",
                "scroll_id" : #{scrollId}
            }
            """;
    static final String START_QUERY = """
            GET search_nr_account_20241015/_search
            {
              "size": 50,
              "query": {
                "bool": {
                  "must_not": [
                    {
                      "exists": {
                        "field": "nr_index_week"
                      }
                    }
                  ],
                  "filter": [
                    {
                      "term": {
                        "platform_type": #{platform}
                      }
                    }
                  ]
                }
              },
              "track_total_hits": true
            }
            """;
    protected final CommonEsService commonEsService;
    protected final Datasource dsEs;
    protected final String platform;

    protected RankHistorySync(String platform, RedissonClient redissonClient, CommonEsService commonEsService) {
        super("rank_sync_" + platform, redissonClient, 1);
        this.dsEs = EsFactory.DEFAULT.create(commonEsService.getRestClient());
        this.commonEsService = commonEsService;
        this.platform = platform;
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final String scrollId = cursor.getNext();
        final JSONObject resp;
        if (StringUtils.isBlank(scrollId)) {
            final QueryBuilder queryBuilder = dsEs.newQueryBuilder()
                    .template(START_QUERY)
                    .addParam("platform", platform);

            resp = dsEs.query(queryBuilder).data();
            final Integer total = resp.getJSONObject("hits")
                    .getJSONObject("total")
                    .getInteger("value");
            cursor.setTotal(total);
            cursor.setNext(resp.getString("_scroll_id"));
        } else {
            final QueryBuilder queryBuilder = dsEs.newQueryBuilder()
                    .template(SCROLL_QUERY)
                    .addParam("scrollId", scrollId);

            resp = dsEs.query(queryBuilder).data();
        }

        final List<String> ids = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream()
                .map(json -> json.getString("_id"))
                .toList();

        if (ids.isEmpty()) {
            return 0;
        }

        final Map<String, JSONObject> ranks = rankIndexes(ids.stream()
                .map(id -> id.split("_")[1]).toList());
        final List<EsEntity> entities = ids.stream()
                .map(id -> {
                    final JSONObject rank = ranks.get(id.split("_")[1]);
                    if (rank == null) {
                        return null;
                    }

                    return castOf(rank, id);
                })
                .filter(Objects::nonNull)
                .toList();

        commonEsService.update(entities);

        return ids.size();
    }

    protected abstract Map<String, JSONObject> rankIndexes(List<String> ids);

    protected abstract EsEntity castOf(JSONObject rank, String docId);
}
