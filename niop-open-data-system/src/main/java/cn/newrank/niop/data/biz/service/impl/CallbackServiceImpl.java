package cn.newrank.niop.data.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.callback.event.CbConfigRefreshTopic;
import cn.newrank.niop.data.biz.component.callback.Callback;
import cn.newrank.niop.data.biz.component.callback.CallbackFactory;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.dao.CallbackRedirectRetryDao;
import cn.newrank.niop.data.biz.manager.CallbackFactoryManager;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import cn.newrank.niop.data.biz.pojo.event.CbUpdateEvent;
import cn.newrank.niop.data.biz.pojo.param.CallbackConnectTest;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.service.CbConfigService;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.CbEnableParamSubCache;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SendStrategy;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.Tag;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.RedisEnableManager;
import cn.newrank.niop.data.util.TriggerUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/16 11:49
 */
@Service
@Log4j2
public class CallbackServiceImpl implements CallbackService {
    /**
     * 默认重试间隔
     */
    private static final int DEFAULT_RETRY_SECONDS = 30;
    private static final int START_DELAY_TIMES = 8;
    private final ConcurrentMap<String/*dcId*/, Callback> callbackCache;
    private final CallbackFactoryManager callbackFactoryManager;
    private final CbConfigService cbConfigService;
    private final RedisEnableManager redisEnableManager;
    private final SubscriberConfigService subscriberConfigService;
    private final CallbackRedirectRetryDao redirectRetryDao;

    public CallbackServiceImpl(CallbackFactoryManager callbackFactoryManager,
                               CbConfigService cbConfigService,
                               RedissonClient redissonClient,
                               SubscriberConfigService subscriberConfigService,
                               CbConfigRefreshTopic cbConfigRefreshTopic,
                               CallbackRedirectRetryDao redirectRetryDao) {
        this.callbackFactoryManager = callbackFactoryManager;
        this.cbConfigService = cbConfigService;
        this.redisEnableManager = new RedisEnableManager(redissonClient, "cb");
        this.subscriberConfigService = subscriberConfigService;
        this.callbackCache = new ConcurrentHashMap<>();
        cbConfigRefreshTopic.addListener((channel, msg) -> {
            final Callback removed = callbackCache.remove(msg.getCbId());
            if (removed != null) {
                removed.close();
            }
        });
        this.redirectRetryDao = redirectRetryDao;
    }

    @Override
    public SubscriberConfigService getSubscriberConfigService() {
        return subscriberConfigService;
    }

    @Override
    public boolean testConnect(CallbackConnectTest connectTest) {
        final CallbackFactory factory = callbackFactoryManager.getFactory(connectTest.getCbType());

        try (final Callback callback = factory.newCallback(connectTest.toDto().getConfig())) {
            return callback.isActive();
        }
    }

    @Override
    public Callback get(String cbId) {
        return callbackCache.computeIfAbsent(cbId, id -> {
            if (redisEnableManager.isEnable(cbId)) {
                final CbConfig cbConfig = cbConfigService.get(id);
                if (cbConfig == null) {
                    throw createParamError("回调配置(id: {})不存在", cbId);
                }

                final CallbackFactory factory = callbackFactoryManager.getFactory(cbConfig.getType());
                final ConfigProperties configProperties = cbConfig.getConfig()
                        .append(ConfigKey.ID, cbId);
                final Callback callback = factory.newCallback(configProperties);
                if (callback.isActive()) {
                    return callback;
                } else {
                    callback.close();
                }

                redisEnableManager.disable(cbId, Duration.ofMinutes(1));
            }

            throw createParamError("回调配置(id: {}) 连接失败", cbId);
        });
    }

    @EventListener()
    public void onCbConfigChange(CbUpdateEvent cbUpdateEvent) {
        final Callback callback = callbackCache.remove(cbUpdateEvent.getCbId());
        if (callback != null) {
            callback.close();
        }
    }

    @Override
    public boolean callback(String cbId, String data) {
        return get(cbId).callback(data);
    }


    @Override
    public void callback(List<CallbackRedirect> callbacks) {
        if (callbacks == null || callbacks.isEmpty()) {
            return;
        }

        final Map<String, Set<CallbackRedirect>> group = new HashMap<>();
        for (CallbackRedirect callback : callbacks) {
            final List<String> cbIds = subscriberConfigService.searchCbIds(
                    SubSourceType.ofJSONValue(callback.getSourceType())
                    , callback.getSourceId()
                    , callback.getAppId());
            if (cbIds.isEmpty()) {
                continue;
            }

            cbIds.forEach(cbId -> group.computeIfAbsent(cbId, key -> new HashSet<>()).add(callback));
        }

        group.forEach(this::callback);
    }

    @Override
    public List<CallbackRetry> callback(String cbId, Collection<CallbackRedirect> redirects) {
        setBeforeCallback(redirects);
        Callback callback;
        try {
            callback = get(cbId);
        } catch (Exception e) {
            log.error("<UNK>id<UNK>{}<UNK>", cbId, e);
            // 回调配置异常全部重试
            return forEachRetryCallback(redirects);
        }

        List<CallbackRedirect> redirectList = callback.callbackReturnError(redirects);

        // 回调业务异常重试
        return forEachRetryCallback(redirectList);
    }

    private void setBeforeCallback(Collection<CallbackRedirect> redirects) {
        for (CallbackRedirect redirect : redirects) {
            subscriberConfigService.searchCdEnableParam(
                            SubSourceType.ofJSONValue(redirect.getSourceType()),
                            redirect.getSourceId(),
                            redirect.getAppId())
                    .forEach(cache -> {
                        setTags(redirect, cache);

                        sendStrategy(redirect, cache);
                    });
        }
    }

    private static void setTags(CallbackRedirect redirect, CbEnableParamSubCache cache) {
        try {
            final List<Tag> tags = cache.getTags();
            if (tags == null || tags.isEmpty()) {
                return;
            }
            if (redirect.getPayload() instanceof JSONObject json) {
                json.putIfAbsent("labels", tags);
            }
        } catch (Exception e) {
            log.error("设置回调参数tags 失败", e);
        }
    }

    private static void sendStrategy(CallbackRedirect redirect, CbEnableParamSubCache cbEnableParams) {
        final SendStrategy sendStrategy = cbEnableParams.getSendStrategy();
        if (sendStrategy != null) {
            sendStrategy.strategy(redirect);
        }
    }

    private List<CallbackRetry> forEachRetryCallback(Collection<CallbackRedirect> redirects) {
        if (redirects == null || redirects.isEmpty()) {
            return Collections.emptyList();
        }

        List<CallbackRetry> retryList = new ArrayList<>();

        for (CallbackRedirect redirect : redirects) {
            if (redirect instanceof CallbackRetry redirectRetry) {
                redirectRetry.setErrorInfo("回调业务方kafka失败");
                retryList.add(redirectRetry);
            } else {
                CallbackRetry retry = CallbackRetry.init(redirect, "回调业务方kafka失败");
                retryList.add(retry);
            }
        }

        return retryList;
    }

    @Override
    public void sendRetryQueue(List<CallbackRetry> retryList) {
        if (CollUtil.isEmpty(retryList)) {
            return;
        }

        // 批量保存
        for (CallbackRetry retry : retryList) {
            // 重试次数
            final int retryNums = retry.getRetryNums() + 1;
            retry.setRetryNums(retryNums);

            // 重试达到最大值
            if (retryNums > 15) {
                retry.setRetryStatus(RetryStatus.ERROR);
                return;
            }

            // 下次重试时间 指数退避
            LocalDateTime nextRetryTime = TriggerUtil.nextExpBackTime(retryNums,
                    START_DELAY_TIMES,
                    DEFAULT_RETRY_SECONDS);
            retry.setNextRetryTime(Timestamp.from(nextRetryTime.toInstant(ZoneOffset.ofHours(8))));
        }

        redirectRetryDao.saveBatch(retryList);
    }


}
