package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 更新数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:50
 */
@Service
@Log4j2
public class LmKsOpusCategoryService implements StorageBizService<LmKsOpus> {

    private final LmKsOpusMapper lmKsOpusMapper;

    public LmKsOpusCategoryService(LmKsOpusMapper lmKsOpusMapper) {
        this.lmKsOpusMapper = lmKsOpusMapper;
    }

    @Override
    public void storeBatch(List<LmKsOpus> items) {
        if (items.isEmpty()) {
            return;
        }

        lmKsOpusMapper.storeCategoryBatch(items);
    }

    @Override
    public LmKsOpus castOf(JSONObject item) {
        final LmKsOpus ksOpus = new LmKsOpus();
        ksOpus.setPhotoId(item.getString("photoId"));
        ksOpus.setCategoryLv1(item.getString("category_lv1"));

        return ksOpus;
    }
}
