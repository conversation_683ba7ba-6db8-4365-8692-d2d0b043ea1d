package cn.newrank.niop.data.biz.dataclear.mapper;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskPageQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskUpdateStatus;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface DataClearTaskMapper {

    void insert(DataCleanTask dataCleanTask);

    int delete(String taskId);

    DataCleanTask get(String taskId);

    int update(DataCleanTask cleanRuleUpadteQuery);

    int updateByRuleId(DataCleanTask cleanRuleUpadteQuery);

    int updateStatus(DataCleanTaskUpdateStatus cleanRuleUpadteQuery);

    List<DataCleanTask> fuzzyQuery(@Param("fuzzyQuery") DataCleanTask fuzzyQuery);

    Page<DataCleanTask> page(@Param("pageQuery") DataCleanTaskPageQuery pageQuery,
                             Page<DataCleanRuleCreate> mybatisPlusPage);

    List<DataCleanTask> list();

    DataCleanTask getByRuleId(String ruleId);

    void updateIds(String taskId);

    int updatePending();
//    List<String> listCbIds();
}




