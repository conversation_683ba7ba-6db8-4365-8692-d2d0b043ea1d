package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import lombok.Data;

@Data
public class DataExportTaskRunningStatusVo {

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 运行状态
     */
    private String runningStatus;

    public static DataExportTaskRunningStatusVo buildBy(String exportTaskId, DataExportRunningStatus runningStatus) {
        DataExportTaskRunningStatusVo vo = new DataExportTaskRunningStatusVo();
        vo.setExportTaskId(exportTaskId);
        vo.setRunningStatus(runningStatus.getDescription());
        return vo;
    }

}
