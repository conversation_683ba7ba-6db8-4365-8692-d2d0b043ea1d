package cn.newrank.niop.data.biz.export.pojo.param;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import cn.newrank.nrcore.utils.UuidUtils;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class DataExportCreate {

    /**
     * 导数类型
     */
    @NotBlank(message = "导数类型不能为空")
    private String exportType;

    /**
     * 导数对象id
     */
    @NotBlank(message = "导数对象id不能为空")
    private String targetId;

    /**
     * 导数名称
     */
    @NotBlank(message = "导数名称不能为空")
    private String exportName;

    /**
     * 导数描述
     */
    private String description;

    public DataExport init() {
        final DataExport export = new DataExport();
        export.setExportType(DataExportType.parseByCode(this.getExportType()));
        export.setExportId(UuidUtils.getUuid(8));
        export.setTargetId(this.getTargetId());
        export.setExportName(this.getExportName());
        export.setDescription(this.getDescription());
        return export;
    }

}
