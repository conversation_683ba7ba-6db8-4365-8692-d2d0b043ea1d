package cn.newrank.niop.data.config.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetAddress;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 11:44
 */
@Component
@ConfigurationProperties(prefix = "newrank.aliyun")
@Data
public class AliyunProperties {
    public static final String SLS_PROJECT = "nr-niop";
    static final String INNER_HOST = "cn-hangzhou-intranet.log.aliyuncs.com";
    static final String OUT_HOST = "cn-hangzhou.log.aliyuncs.com";
    static boolean isInner;

    static {
        try {
            isInner = InetAddress.getByName(INNER_HOST).isReachable(3000);
        } catch (IOException e) {
            isInner = false;
        }
    }

    /**
     * Access key
     */
    private String ak;
    /**
     * Secret key
     */
    private String sk;
    /**
     * Logstore
     */
    private String logstore = "niop_data_storage_history";

    public static String getSlsHost() {
        return isInner ? INNER_HOST : OUT_HOST;
    }

}
