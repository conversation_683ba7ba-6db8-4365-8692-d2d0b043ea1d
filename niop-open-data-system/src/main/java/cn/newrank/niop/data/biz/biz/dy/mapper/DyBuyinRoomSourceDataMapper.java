package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoomSourceData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_buyin_room_source_data】的数据库操作Mapper
 * @createDate 2024-10-29 16:00:44
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoomSourceData
 */
@Mapper
public interface DyBuyinRoomSourceDataMapper {

    /**
     * 插入一条数据
     *
     * @param item
     */
    void insertOne(@Param("item") DyBuyinRoomSourceData item);

}




