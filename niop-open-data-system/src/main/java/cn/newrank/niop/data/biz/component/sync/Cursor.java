package cn.newrank.niop.data.biz.component.sync;

import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 * 数据同步游标： 保存数据同步的进度和状态信息
 * <p>
 * Unstart → started(paused|running) → finished(ok|error)
 * when state is started if the address is null it is paused else it is running.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 14:52
 */
@Data
public class Cursor<T> implements Comparable<Cursor<T>> {
    public static final String STATE_UNSTARTED = "UNSTARTED";
    public static final String STATE_STARTED = "STARTED";
    public static final String STATE_FINISHED = "FINISHED";
    /**
     * 总数
     */
    protected int total = -1;
    /**
     * 已完成数量
     */
    protected int completed = 0;
    /**
     * 状态
     */
    protected String state = STATE_UNSTARTED;
    /**
     * 执行地址
     */
    protected String address;
    /**
     * 错误信息
     */
    protected String error;
    /**
     * 开始时间
     */
    @Deprecated
    protected String startTime;
    /**
     * 结束时间
     */
    @Deprecated
    protected String endTime;
    /**
     * 开始
     */
    protected String start;
    /**
     * 结束
     */
    protected String end;
    /**
     * 更新时间
     */
    protected String updateTime;
    /**
     * 同步游标
     */
    protected T next;


    @Override
    public int compareTo(@NotNull Cursor<T> o) {
        return getKey().compareTo(o.getKey());
    }


    public void addCompleted(int count) {
        this.completed += count;
    }

    /**
     * 游标标识
     *
     * @return 游标标识
     */
    public String getKey() {
        if (start == null) {
            return startTime + "~" + endTime;
        }else {
            return start + "~" + end;
        }
    }

    /**
     * 是否暂停
     *
     * @return 是否暂停
     */
    public boolean isPaused() {
        return isStarted() && (address == null || address.isBlank());

    }

    /**
     * 是否已开始执行
     *
     * @return 是否已开始执行
     */
    public boolean isStarted() {
        return STATE_STARTED.equals(state);
    }

    /**
     * 是否未开始执行
     *
     * @return 是否未开始执行
     */
    public boolean isUnstarted() {
        return STATE_UNSTARTED.equals(state);
    }
}
