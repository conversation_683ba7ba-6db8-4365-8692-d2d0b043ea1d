package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShop;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_buyin_shop】的数据库操作Mapper
 * @createDate 2024-10-29 11:40:26
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShop
 */
@Mapper
public interface DyBuyinShopMapper {


    /**
     * 批量数据插入
     * @param itemList
     */
    void batchInsert(@Param("itemList") List<DyBuyinShop> itemList);
}




