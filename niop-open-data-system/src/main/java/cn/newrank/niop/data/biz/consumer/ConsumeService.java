package cn.newrank.niop.data.biz.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.util.List;
import java.util.function.Function;
import java.util.stream.StreamSupport;

/**
 * 消费服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/25 14:07
 */
public interface ConsumeService {

    /**
     * 消费
     *
     * @param consumerRecords 消费者记录
     * @return true 消费成功
     */
    boolean consume(ConsumerRecords<String, String> consumerRecords);


    /**
     * record value转实体工具方法
     *
     * @param consumerRecords 消费者记录
     * @return 实体
     */
    default <T> List<T> toEntities(ConsumerRecords<String, String> consumerRecords, Function<String, T> mapper) {
        return StreamSupport.stream(consumerRecords.spliterator(), false)
                .map(consumerRecord -> mapper.apply(consumerRecord.value()))
                .toList();
    }
}
