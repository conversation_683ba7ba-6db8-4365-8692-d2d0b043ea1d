package cn.newrank.niop.data.biz.biz.dy.service.haihui;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.component.sync.HistorySyncContext;
import cn.newrank.niop.data.biz.component.sync.HistorySynchronizer;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
@Log4j2
@Service
@AllArgsConstructor
public class DyHaihuiSyncService {

    private final RedissonClient redissonClient;
    private static final String SYNC_NAME = "haihui_dy_business_opus_sync";
    private static final String HAIHUI_DY_SYNC_TIME_CACHE = "haihui:dy:sync";
    private final HistorySyncContext historySyncContext;


    /**
     * 同步
     */
    public void sync(String param) {
        RBucket<String> bucket = redissonClient.getBucket(HAIHUI_DY_SYNC_TIME_CACHE);
        if (Strings.isBlank(bucket.get())) {
            bucket.set(LocalDateTime.now().minusDays(1).format(DatePattern.NORM_DATETIME_FORMATTER));
        }
        final LocalDateTime cachedTime = LocalDateTime.parse(bucket.get(), DatePattern.NORM_DATETIME_FORMATTER);
        if (cachedTime.isAfter(LocalDateTime.now()) && !"skip".equals(param)) {
            XxlJobLogger.log("未到同步时间：{}", cachedTime);
            return;
        }
        final LocalDateTime begin = LocalDate.now().atStartOfDay();
        final String start = begin.minusDays(1).format(DatePattern.NORM_DATETIME_FORMATTER);
        final String end = begin.format(DatePattern.NORM_DATETIME_FORMATTER);
        runSync(start, end, SYNC_NAME);
        bucket.set(begin.plusDays(1).plusHours(6).format(DatePattern.NORM_DATETIME_FORMATTER));
        XxlJobLogger.log("创建同步任务成功：{} - {}", start, end);
    }

    /**
     * 创建同步任务
     *
     * @param start 开始时间
     * @param end   结束时间
     */
    private void runSync(String start, String end, String syncName) {
        HistorySynchronizer synchronizer = historySyncContext.getSynchronizer(syncName);
        if (Objects.nonNull(synchronizer)) {
            historySyncContext.getSynchronizer(syncName).newSync(start, end);
        } else {
            log.warn("同步器不存在 {}", syncName);
            XxlJobLogger.log("同步器不存在 {}", syncName);
        }
    }
}
