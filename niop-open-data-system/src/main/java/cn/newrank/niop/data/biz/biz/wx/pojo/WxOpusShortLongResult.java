package cn.newrank.niop.data.biz.biz.wx.pojo;

import cn.newrank.niop.data.biz.biz.wx.enums.WxDataTypeEnum;
import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.data.biz.pojo.dto.KafkaAbilityMessage;
import cn.newrank.nrcore.json.JsonField;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;

import java.net.URL;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/12 14:46:24
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class WxOpusShortLongResult extends StorageEntity {


    @JsonField({"aweme_id", "uuid"})
    private String awemeId;

    @JsonField({"long_url", "oriUrl"})
    private String longUrl;

    @JsonField({"short_url", "url"})
    private String shortUrl;

    @JsonField({"comment_id", "commentId"})
    private String commentId;

    @JsonField({"acq_time", "updateTime"})
    private String acqTime;

    @Override
    public String identifier() {
        return awemeId;
    }


    public static WxOpusShortLongResult covertByType(JSONObject data, WxDataTypeEnum type) {
        if (Objects.isNull(type)) {
            return null;
        }
        JSONObject detail = data.getJSONObject("json_details");
        if (type == WxDataTypeEnum.AWEME_BASE) {
            WxOpusShortLongResult result = JsonParser.parseObject(detail, WxOpusShortLongResult.class);
            if (isRightResult(result)) {
                return result;
            }
        }
        return null;
    }

    public static WxOpusShortLongResult covertByAbilityResult(JSONObject data) {
        if (Objects.isNull(data)) {
            return null;
        }
        KafkaAbilityMessage kafkaAbilityMessage = KafkaAbilityMessage.convertAbilityResult(data);
        JSONObject jsonResult = KafkaAbilityMessage.getResult(kafkaAbilityMessage);
        WxOpusShortLongResult result = JsonParser.parseObject(jsonResult, WxOpusShortLongResult.class);
        if (isRightResult(result)) {
            result.setAcqTime(kafkaAbilityMessage.getData().getFinishTime());
            return result;
        }
        return null;
    }

    private static Boolean isRightResult(WxOpusShortLongResult result) {
        if(Objects.isNull(result)){
            return false;
        }
        if(Strings.isBlank(result.getShortUrl()) || Strings.isBlank(result.getLongUrl())){
            return false;
        }
        return getShortUrlIsNotLong(result) && parseLongUrl(result);
    }

    private static final String CHKSM = "&chksm";
    public static Boolean parseLongUrl(WxOpusShortLongResult result){
        String longUrl = result.getLongUrl();
        if(longUrl.contains(CHKSM)){
            result.setLongUrl(longUrl.substring(0,longUrl.indexOf(CHKSM)));
            return true;
        }
        return true;
    }

    /**
     * 验证短链字段 不是长链
     * @param result
     * @return
     */
    private static @NotNull Boolean getShortUrlIsNotLong(WxOpusShortLongResult result) {
        try {
            URL shortUrl = new URL(result.getShortUrl());
            URL longUrl = new URL(result.getLongUrl());
            // 短链的Path长
            if(shortUrl.getPath().length() <= longUrl.getPath().length()){
                result.setShortUrl(null);
            }
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
