package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
public class XhsOpusTopic {
    @JsonField("title")
    private String title;
    @JsonField("desc")
    private String desc;
    @JsonField("uid")
    private String uid;
    @JsonField("nickname")
    private String nickname;
    @JsonField("images")
    private String images;
    @JsonField("imagesList")
    private String imagesList;
    @JsonField("hashTags")
    private String hashTags;
    @JsonField("opusId")
    private String opusId;
    @JsonField("type")
    private String type;
    @JsonField("token")
    private String token;
    @JsonField("collectNum")
    private Long collectNum;
    @JsonField("commentNum")
    private Long commentNum;
    @<PERSON><PERSON><PERSON>ield("likes")
    private Long likes;
    @JsonField("shareNum")
    private Long shareNum;
    @JsonField("publishTime")
    private String publishTime;
    @JsonField("editTime")
    private String editTime;
    @JsonField("videoUrl")
    private String videoUrl;
    @JsonField("cursor")
    private String cursor;
    @JsonField("width")
    private Integer width;
    @JsonField("height")
    private Integer height;
    @JsonField("videoId")
    private String videoId;
    @JsonField("duration")
    private Integer duration;
    @JsonField("cover")
    private String cover;

    public static XhsOpusTopic parse(JSONObject item) {
        XhsOpusTopic data = new XhsOpusTopic();
        data.setTitle(item.getString("title"));
        data.setDesc(item.getString("desc"));
        data.setUid(item.getString("uid"));
        data.setNickname(item.getString("nickname"));
        data.setImages(item.getString("images"));
        data.setImagesList(item.getString("imagesList"));
        data.setHashTags(item.getString("hashTags"));
        data.setOpusId(item.getString("opusId"));
        data.setType(item.getString("type"));
        data.setToken(item.getString("token"));
        data.setCollectNum(item.getLong("collectNum"));
        data.setCommentNum(item.getLong("commentNum"));
        data.setLikes(item.getLong("likes"));
        data.setShareNum(item.getLong("shareNum"));
        data.setPublishTime(item.getString("publishTime"));
        data.setEditTime(item.getString("editTime"));
        data.setVideoUrl(item.getString("videoUrl"));
        data.setCursor(item.getString("cursor"));
        data.setWidth(item.getInteger("width"));
        data.setHeight(item.getInteger("height"));
        data.setVideoId(item.getString("videoId"));
        data.setDuration(item.getInteger("duration"));
        data.setCover(item.getString("cover"));
        return data;
    }
}
