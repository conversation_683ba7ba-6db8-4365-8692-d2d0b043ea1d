package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.nrcore.json.JsonField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
public class XhsOpusTopic {
    @JsonField("title")
    private String title;
    @JsonField("desc")
    private String desc;
    @JsonField("uid")
    private String uid;
    @Json<PERSON>ield("nickname")
    private String nickname;
    @JsonField("images")
    private String images;
    @JsonField("imagesList")
    private String imagesList;
    @JsonField("hashTags")
    private String hashTags;
    @JsonField("opusId")
    private String opusId;
    @JsonField("type")
    private String type;
    @JsonField("token")
    private String token;
    @JsonField("collectNum")
    private Long collectNum;
    @JsonField("commentNum")
    private Long commentNum;
    @JsonField("likes")
    private Long likes;
    @JsonField("shareNum")
    private Long shareNum;
    @JsonField("publishTime")
    private String publishTime;
    @JsonField("editTime")
    private String editTime;
    @JsonField("videoUrl")
    private String videoUrl;
    @JsonField("cursor")
    private String cursor;
    @JsonField("width")
    private Integer width;
    @JsonField("height")
    private Integer height;
    @JsonField("videoId")
    private String videoId;
    @JsonField("duration")
    private Integer duration;
    @JsonField("cover")
    private String cover;
}
