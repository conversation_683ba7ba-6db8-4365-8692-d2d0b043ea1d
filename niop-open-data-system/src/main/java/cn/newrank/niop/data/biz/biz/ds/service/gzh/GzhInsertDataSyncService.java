package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.GzhEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_GZH_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * 公众号增量数据同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class GzhInsertDataSyncService implements SyncBizService<GzhEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public GzhInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseGzhEsConfig();
    }

    @Override
    public String getRangField() {
        return "updateTime";
    }

    @Override
    public String getIndexName() {
        return "weixin_user";
    }

    @Override
    public String getSortField() {
        return "wxId";
    }

    @Override
    public String getRangIndex() {
        return "updateTime";
    }


    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_GZH_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }

        sourceFields.add("account");
        sourceFields.add("wxId");
        sourceFields.add("id");

        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }

    @Override
    public List<GzhEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), GzhEsMetaData.class);
        });
    }

    @Override
    public List<GzhEsMetaData> convertData(List<GzhEsMetaData> dataList) {
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (GzhEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_GZH_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }

            mainMap.put("officail_display_id", data.get("account") != null && StrUtil.isNotBlank((String)
                    data.get("account")) ? data.get("account") : data.get("wxId"));

            if (data.get("status") != null && data.getInteger("status") < 1) {
                mainMap.put("account_status", -1);
            }

            if (mainMap.containsKey("officail_display_id")) {
                final String officailDisplayId = String.valueOf(mainMap.get("officail_display_id"));
                if (officailDisplayId.contains("@dongjie") || officailDisplayId.contains("@zx") ||
                        officailDisplayId.contains("@tingzhi") || officailDisplayId.contains("@qianyi")) {
                    mainMap.put("account_status", -1);
                }
            }

            mainMap.put("platform_type", PlatformType.GZH.getDbCode());

            if (data.containsKey("id")) {
                mainMap.put("index_id", PlatformType.GZH.getDbCode() + "_" + data.get("id"));
            }

            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 移除空值字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }

        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), GzhEsMetaData.class);
    }
}
