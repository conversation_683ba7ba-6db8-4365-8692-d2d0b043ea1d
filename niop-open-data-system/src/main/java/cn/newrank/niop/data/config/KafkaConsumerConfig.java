package cn.newrank.niop.data.config;

import cn.newrank.niop.common.Environment;
import cn.newrank.niop.condition.ConditionOnEnvironment;
import cn.newrank.niop.data.biz.callback.service.CallbackRedirectService;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.consumer.*;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.config.property.KafkaProperties;
import com.alibaba.nacos.common.executor.NameThreadFactory;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:18
 */
@EnableConfigurationProperties(KafkaProperties.class)
@Configuration
@Log4j2
public class KafkaConsumerConfig {

    public static ThreadPoolExecutor createExecutor(String prefix, int threads) {
        return new ThreadPoolExecutor(threads, threads
                , 0L, TimeUnit.MILLISECONDS
                , new SynchronousQueue<>()
                , new NameThreadFactory(prefix)
                , new ThreadPoolExecutor.AbortPolicy());
    }

    /**
     * 样本中心数据回调
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置
     * @param storageService  存储服务
     * @param callbackService 回调服务
     * @return CallbackRedirectConsumer
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public CallbackRedirectConsumer sampleCallbackRedirectConsumer(KafkaProperties kafkaProperties,
                                                                   SystemConfig systemConfig,
                                                                   StorageService storageService,
                                                                   CallbackService callbackService) {
        return new CallbackRedirectConsumer(kafkaProperties.getSampleRedirect()
                , systemConfig.getEnvironment()
                , storageService
                , callbackService
                , createExecutor("kc-sample", 1));
    }

    /**
     * 工作流数据回调
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置
     * @param storageService  存储服务
     * @param callbackService 回调服务
     * @return CallbackRedirectConsumer
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public CallbackRedirectConsumer schedulerCallbackRedirectConsumer(KafkaProperties kafkaProperties,
                                                                      SystemConfig systemConfig,
                                                                      StorageService storageService,
                                                                      CallbackService callbackService) {
        return new CallbackRedirectConsumer(kafkaProperties.getSchedulerRedirect()
                , systemConfig.getEnvironment()
                , storageService
                , callbackService
                , createExecutor("kc-scheduler", 4));
    }

    /**
     * 综合服务数据回调
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置
     * @param storageService  存储服务
     * @param callbackService 回调服务
     * @return CallbackRedirectConsumer
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public CallbackRedirectConsumer serviceHubCallbackRedirectConsumer(KafkaProperties kafkaProperties,
                                                                       SystemConfig systemConfig,
                                                                       StorageService storageService,
                                                                       CallbackService callbackService) {
        return new CallbackRedirectConsumer(kafkaProperties.getServiceHubRedirect()
                , systemConfig.getEnvironment()
                , storageService
                , callbackService
                , createExecutor("kc-hub", 2));
    }

    /**
     * 数据方舟服务数据回调
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置
     * @param storageService  存储服务
     * @param callbackService 回调服务
     * @return CallbackRedirectConsumer
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public CallbackRedirectConsumer dataArkRedirectConsumer(KafkaProperties kafkaProperties,
                                                            SystemConfig systemConfig,
                                                            StorageService storageService,
                                                            CallbackService callbackService) {
        return new CallbackRedirectConsumer(kafkaProperties.getDataArkRedirect()
                , systemConfig.getEnvironment()
                , storageService
                , callbackService
                , createExecutor("kc-ark", 1));
    }

    /**
     * 能力导出回调
     *
     * @param kafkaProperties          kafka配置
     * @param systemConfig             系统配置
     * @param dataExportSubtaskService 数据导数子任务服务
     * @return ExportAbilityCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public ExportAbilityCallbackConsumer dataExportAbilityCallbackConsumer(KafkaProperties kafkaProperties,
                                                                           SystemConfig systemConfig,
                                                                           DataExportSubtaskService dataExportSubtaskService) {
        return new ExportAbilityCallbackConsumer(kafkaProperties.getExportAbilityCallback()
                , systemConfig.getEnvironment()
                , dataExportSubtaskService
                , createExecutor("ability-data-export", 1)
        );
    }

    /**
     * 综合服务导出回调
     *
     * @param kafkaProperties          kafka配置
     * @param systemConfig             系统配置
     * @param dataExportSubtaskService 数据导数子任务服务
     * @return ExportHubServiceCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public ExportHubServiceCallbackConsumer dataExportHubServiceCallbackConsumer(KafkaProperties kafkaProperties,
                                                                                 SystemConfig systemConfig,
                                                                                 DataExportSubtaskService dataExportSubtaskService) {
        return new ExportHubServiceCallbackConsumer(kafkaProperties.getExportHubServiceCallback()
                , systemConfig.getEnvironment()
                , dataExportSubtaskService
                , createExecutor("hub-service-data-export", 1)
        );
    }


    /**
     * 分析快手数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer analysisKsStorageSourceConsumer(KafkaProperties kafkaProperties,
                                                                 SystemConfig systemConfig,
                                                                 StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getAnalysisKs(),
                systemConfig.getEnvironment(),
                StorageBiz.LM_KS_OPUS_BASIC,
                storageService,
                createExecutor("analysis-ks-basic", 6));
    }


    /**
     * 分析lm 快手扩展数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer analysisLmKsExtStorageSourceConsumer(KafkaProperties kafkaProperties,
                                                                      SystemConfig systemConfig,
                                                                      StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getAnalysisKsExt(),
                systemConfig.getEnvironment(),
                StorageBiz.LM_KS_OPUS_EXT,
                storageService,
                createExecutor("analysis-lm-ks-ext", 2));
    }

    /**
     * 分析lm 快手 分类数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer analysisLmKsCategoryStorageSourceConsumer(KafkaProperties kafkaProperties,
                                                                           SystemConfig systemConfig,
                                                                           StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getAnalysisKsCategory(),
                systemConfig.getEnvironment(),
                StorageBiz.LM_KS_OPUS_CATEGORY,
                storageService,
                createExecutor("analysis-lm-ks-category", 1));
    }


    /**
     * 分析热词数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public StorageSourceConsumer analysisHotWordSSConsumer(KafkaProperties kafkaProperties,
                                                           SystemConfig systemConfig,
                                                           StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getAnalysisHotWord(),
                systemConfig.getEnvironment(),
                StorageBiz.XHS_HOT_WORD,
                storageService,
                createExecutor("analysis-hot-word", 4));
    }


    /**
     * 抖音直播小黄车
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment(Environment.PRODUCT)
    public StorageSourceConsumer analysisDyLittleYellowCarConsumer(KafkaProperties kafkaProperties,
                                                                   SystemConfig systemConfig,
                                                                   StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getAnalysisDyLittleYellowCar(),
                systemConfig.getEnvironment(),
                StorageBiz.DY_LITTLE_YELLOW_CAR,
                storageService,
                createExecutor("analysis-dy-little-yellow-car", 2));
    }

    /**
     * 公众号-企业认证信息
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer gzhEnterpriseCtInfoSSConsumer(KafkaProperties kafkaProperties,
                                                               SystemConfig systemConfig,
                                                               StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getGzhEnterpriseCertificationInfo(),
                systemConfig.getEnvironment(),
                StorageBiz.GZH_ENTERPRISE_CT_INFO,
                storageService,
                createExecutor("gzh-enterprise-certification-info", 1));
    }

    /**
     * 公众号-粉丝数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer gzhFansDataSSConsumer(KafkaProperties kafkaProperties,
                                                       SystemConfig systemConfig,
                                                       StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getGzhFansData(),
                systemConfig.getEnvironment(),
                StorageBiz.GZH_FANS_DATA,
                storageService,
                createExecutor("gzh-fans-data", 1));
    }


    /**
     * 能力中心回调消费
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置0
     * @param callbackService 回调服务
     * @return AbilityCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public AbilityCallbackConsumer abilityCallbackConsumer(KafkaProperties kafkaProperties,
                                                           SystemConfig systemConfig,
                                                           CallbackRedirectService redirectService,
                                                           CallbackService callbackService) {
        return new AbilityCallbackConsumer(kafkaProperties.getAbilityCallback(),
                systemConfig.getEnvironment(),
                redirectService,
                callbackService,
                createExecutor("ability-cb", 6));
    }


    /**
     * 分析快手数据
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer xhsTokenStorageSourceConsumer(KafkaProperties kafkaProperties,
                                                               SystemConfig systemConfig,
                                                               StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getXhsToken(),
                systemConfig.getEnvironment(),
                StorageBiz.XHS_OPUS_TOKEN,
                storageService,
                createExecutor("xhs-token", 6));
    }


    /**
     * 小红书-大盘作品存ldm结果回调
     *
     * @param kafkaProperties 配置
     * @param systemConfig    系统
     * @param storageService  存储
     * @return 数据消费
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer xhsDapanOpusStorageSourceConsumer(KafkaProperties kafkaProperties,
                                                                   SystemConfig systemConfig,
                                                                   StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getXhsDapanOpusStore(),
                systemConfig.getEnvironment(),
                StorageBiz.XHS_DAPAN_OPUS_STORE,
                storageService,
                createExecutor("xhs-dapan-opus-store", 12));
    }


    /**
     * 小红书扩量-能力中心回调消费
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置0
     * @param storageService  存储
     * @return AbilityCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer xhsExpAbilityCallbackConsumer(KafkaProperties kafkaProperties,
                                                               SystemConfig systemConfig,
                                                               StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getXhsExpAbilityCallback(),
                systemConfig.getEnvironment(),
                StorageBiz.XHS_EXP_TASK_CB,
                storageService,
                createExecutor("xhs-exp-ability-cb", 6));
    }

    /**
     * 小红书-话题唯一索引
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置0
     * @param storageService  存储
     * @return AbilityCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer xhsTopicUniqueCallbackConsumer(KafkaProperties kafkaProperties,
                                                                SystemConfig systemConfig,
                                                                StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getXhsTopicUniqueStore(),
                systemConfig.getEnvironment(),
                StorageBiz.XHS_TOPIC_UNIQUE_CB,
                storageService,
                createExecutor("xhs-topic-unique-cb", 1));
    }

    /**
     * 公众号-转链
     *
     * @param kafkaProperties kafka配置
     * @param systemConfig    系统配置0
     * @param storageService  存储
     * @return AbilityCallbackConsumer
     */
    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer wxOpusShortLongUrlCallbackConsumer(KafkaProperties kafkaProperties,
                                                                    SystemConfig systemConfig,
                                                                    StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getWxOpusShortLongUrl(),
                systemConfig.getEnvironment(),
                StorageBiz.WX_OPUS_SHORT_LONG_OPUS,
                storageService,
                createExecutor("wx_opus_short_long_opus", 3));
    }

    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer wxOpusShortLongUrlAbilityCallbackConsumer(KafkaProperties kafkaProperties,
                                                                           SystemConfig systemConfig,
                                                                           StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getWxOpusShortLongAbilityUrl(),
                systemConfig.getEnvironment(),
                StorageBiz.WX_OPUS_SHORT_LONG_ABILITY_OPUS,
                storageService,
                createExecutor("wx_opus_short_long_ability_opus", 3));
    }


    @Bean
    @ConditionOnEnvironment({Environment.PRODUCT})
    public StorageSourceConsumer ksHistorySyncLindormConsumer(KafkaProperties kafkaProperties,
                                                              SystemConfig systemConfig,
                                                              StorageService storageService) {
        return new StorageSourceConsumer(kafkaProperties.getKsHistorySyncLindorm(),
                systemConfig.getEnvironment(),
                StorageBiz.LM_KS_OPUS_HISTORY,
                storageService,
                createExecutor("ks_history_sync_lindorm", 6));
    }

}
