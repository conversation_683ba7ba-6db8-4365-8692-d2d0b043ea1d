package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.pojo.vo.CbConfigVo;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.service.CbConfigService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/21 9:38
 */
@Validated
@RestController
@RequestMapping("callback-config")
public class CbConfigController {

    private final CbConfigService cbConfigService;
    private final CallbackService callbackService;

    public CbConfigController(CbConfigService cbConfigService, CallbackService callbackService) {
        this.cbConfigService = cbConfigService;
        this.callbackService = callbackService;
    }

    /**
     * 新建配置
     *
     * @param configCreate 配置
     * @return id
     */
    @PostMapping("create")
    public String create(@Valid @RequestBody CallbackConfigCreate configCreate) {
        return cbConfigService.create(configCreate);
    }

    /**
     * 测试连接
     *
     * @param connectTest 测试连接
     * @return true
     */
    @PostMapping("connect/test")
    public boolean delete(@Valid @RequestBody CallbackConnectTest connectTest) {
        return callbackService.testConnect(connectTest);
    }

    /**
     * 分页查询
     *
     * @param pageQuery 分页查询
     * @return 分页
     */
    @GetMapping("page")
    public PageView<CbConfigVo> page(@Valid CbConfigPageQuery pageQuery) {
        return cbConfigService.page(pageQuery).convert(CbConfigVo::fromDto);
    }

    /**
     * 更新
     *
     * @param configUpdate 更新
     * @return 成功
     */
    @PostMapping("update")
    public boolean update(@Valid @RequestBody CallbackConfigUpdate configUpdate) {
        return cbConfigService.update(configUpdate);
    }

    /**
     * 模糊查询
     *
     * @param fuzzyQuery 模糊查询
     * @return 配置
     */
    @GetMapping("fuzzy-search")
    public List<CbConfigVo> fuzzySearch(@Valid CbConfigFuzzyQuery fuzzyQuery) {
        return cbConfigService.fuzzyQuery(fuzzyQuery).stream().map(CbConfigVo::fromDto).toList();
    }

    /**
     * 删除
     *
     * @param cbDelete 删除
     * @return 成功
     */
    @PostMapping("delete")
    public boolean delete(@Valid @RequestBody CallbackDelete cbDelete) {
        return cbConfigService.delete(cbDelete.getCbId());
    }


    /**
     * 根据回调源id查询
     *
     * @param cbId
     * @return CbConfigVo
     * <AUTHOR>
     * @date 2024/8/7 下午12:38
     */
    @GetMapping("details")
    public CbConfigVo getDetails(@NotBlank(message = "回调源ID不能为空") String cbId) {
        return CbConfigVo.fromDto(cbConfigService.get(cbId));
    }
}
