package cn.newrank.niop.data.biz.dataclear.service.impl;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.RuleStatus;
import cn.newrank.niop.data.biz.dataclear.pojo.TaskStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TaskDataCleanTaskExecuteService {


    // --- 可配置常量 ---
    // 限制单次执行的总时长（例如：60秒）
    private static final long EXECUTION_TIME_LIMIT_SECONDS = 80;
    // 状态检查间隔（例如：每 1 秒检查一次）
    private static final long STATUS_CHECK_INTERVAL_MS = 5000;
    // 初始、最小、最大睡眠时间（毫秒）
    private static final int INITIAL_SLEEP_TIME_MS = 75;
    private static final int MIN_SLEEP_TIME_MS = 50;
    private static final int MAX_SLEEP_TIME_MS = 1000;
    // 睡眠时间调整因子
    private static final double SLEEP_ADJUSTMENT_FACTOR = 0.5;
    // EWMA (指数加权移动平均) 权重，用于平滑睡眠时间
    private static final double EWMA_ALPHA = 0.8;
    // SQL执行时间阈值（毫秒），用于调整睡眠时间
    private static final long SLOW_EXECUTION_THRESHOLD_MS = 70;
    private static final long FAST_EXECUTION_THRESHOLD_MS = 20;

    private final ThreadPoolTaskExecutor dataClearTaskExecutor;
    private final SqlService sqlService;
    private final DataCleanTaskServiceImpl dataCleanTaskService;
    private final DataCleanRulerServiceImpl dataCleanRulerService;

    public TaskDataCleanTaskExecuteService(ThreadPoolTaskExecutor dataClearTaskExecutor, SqlService sqlService, DataCleanTaskServiceImpl dataCleanTaskService, DataCleanRulerServiceImpl dataCleanRulerService) {
        this.dataClearTaskExecutor = dataClearTaskExecutor;
        this.sqlService = sqlService;
        this.dataCleanTaskService = dataCleanTaskService;
        this.dataCleanRulerService = dataCleanRulerService;
    }

    public void submitTask(List<DataCleanTask> dataCleanTasks) throws ExecutionException, InterruptedException {
        List<CompletableFuture<String>> futures = new ArrayList<>();
        // todo 改成信號量
        for (DataCleanTask dataCleanTask : dataCleanTasks) {
            CompletableFuture<String> future = submitTask(dataCleanTask);
            futures.add(future);
        }
        // 等待所有异步任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();
        // 输出所有任务的结果
        for (CompletableFuture<String> future : futures) {
            log.info("执行成功{}", future.get());
        }

    }

    public DataCleanTask executeTask(DataCleanTask dataCleanTask) {
        long totalActiveDurationMillis = dataCleanTask.getDuration();
        long lastStatusCheckTime = System.currentTimeMillis();
        int currentSleepTime = INITIAL_SLEEP_TIME_MS;

        try {
            do {
                // 1. 检查是否需要从数据库获取最新状态（降低频率）
                long now = System.currentTimeMillis();
                if (now - lastStatusCheckTime > STATUS_CHECK_INTERVAL_MS) {
                    DataCleanTask latestTaskStatus = dataCleanTaskService.get(dataCleanTask.getTaskId());
                    // 更新检查时间
                    lastStatusCheckTime = now;
                    // 当需要紧急停止的时候
                    if (Objects.nonNull(latestTaskStatus) && latestTaskStatus.getTaskStatus() == TaskStatus.STOPPED) {
                        log.info("任务：{}：接收到停止指令，执行停止。", dataCleanTask.getTaskId());
                        // 更新传入任务的状态并返回
                        dataCleanTask.setTaskStatus(TaskStatus.STOPPED);
                        return dataCleanTask;
                    }
                }

                // 2. 执行核心 SQL 操作
                long loopStart = System.currentTimeMillis();
                long affectedRows = sqlService.querySql(dataCleanTask);
                long loopEnd = System.currentTimeMillis();
                long currentLoopDurationMillis = (loopEnd - loopStart);

                // 3. 更新任务统计数据 (直接修改传入的对象)
                totalActiveDurationMillis += currentLoopDurationMillis;
                // 更新总活跃时长
                dataCleanTask.setDuration((int) totalActiveDurationMillis);
                // 更新总删除数
                dataCleanTask.setRealDeleteNum(dataCleanTask.getRealDeleteNum() + (int) affectedRows);

                // 4. 检查是否完成
                if (affectedRows == 0) {
                    log.info("任务：{}：删除完成 (影响行数为 0)，状态设置为 SUCCESS。", dataCleanTask.getTaskId());
                    dataCleanTask.setTaskStatus(TaskStatus.SUCCESS);
                    return dataCleanTask;
                }

                // 5. 动态调整睡眠时间 (逻辑保持不变，使用常量)
                if (currentLoopDurationMillis > SLOW_EXECUTION_THRESHOLD_MS) {
                    currentSleepTime = Math.min(MAX_SLEEP_TIME_MS, (int) (currentSleepTime * (1 + SLEEP_ADJUSTMENT_FACTOR)));
                } else if (currentLoopDurationMillis < FAST_EXECUTION_THRESHOLD_MS) {
                    currentSleepTime = Math.max(MIN_SLEEP_TIME_MS, (int) (currentSleepTime * (1 - SLEEP_ADJUSTMENT_FACTOR)));
                } else {
                    // 使用 EWMA 平滑调整
                    currentSleepTime = (int) (EWMA_ALPHA * currentSleepTime + (1 - EWMA_ALPHA) * INITIAL_SLEEP_TIME_MS);
                }
                // 确保睡眠时间在边界内
                currentSleepTime = Math.max(MIN_SLEEP_TIME_MS, Math.min(MAX_SLEEP_TIME_MS, currentSleepTime));

                // 6. 休眠
                try {
                    Thread.sleep(currentSleepTime);
                } catch (InterruptedException ie) {
                    log.warn("任务：{}：休眠被中断，可能需要停止任务。", dataCleanTask.getTaskId(), ie);
                    Thread.currentThread().interrupt(); // 重置中断状态
                    dataCleanTask.setTaskStatus(TaskStatus.FAILED); // 或设置为特定中断状态
                    return dataCleanTask;
                }

                // 7. 检查总执行时间是否超过限制 (基于方法开始时间)
                // 原来的 total <= LIMIT_TIME 是判断活跃时间，如果需要保持原逻辑，则使用下面的判断：
                if (totalActiveDurationMillis > TimeUnit.SECONDS.toMillis(EXECUTION_TIME_LIMIT_SECONDS)) {
                    log.info("任务：{}：活跃执行时间超过 {} 秒限制，状态设置为 WAITING。", dataCleanTask.getTaskId(), EXECUTION_TIME_LIMIT_SECONDS);
                    dataCleanTask.setTaskStatus(TaskStatus.WAITING);
                    return dataCleanTask;
                }
            } while (true); // 循环由内部的 return 或 break 控制

        } catch (Exception e) {
            // 优化日志记录，包含堆栈信息
            log.info("任务：{}：执行过程中发生异常，状态设置为 FAILED。异常信息：{}", dataCleanTask.getTaskId(), e.getMessage(), e);
            dataCleanTask.setTaskStatus(TaskStatus.FAILED);
            return dataCleanTask;
        }
    }


    public CompletableFuture<String> submitTask(DataCleanTask param) {
        //设置预估时间

        return CompletableFuture.supplyAsync(() -> {
            // 执行任务删除一分钟，超过一分钟就停止
            DataCleanTask dataCleanTask = executeTask(param);
            //当一分钟未删除时候检测，是否规则任务已经停止
            if (dataCleanTask.getTaskStatus() == TaskStatus.WAITING) {
                DataCleanRuleCreate dataCleanRuleCreate = dataCleanRulerService.get(dataCleanTask.getRuleId());
                if (dataCleanRuleCreate.getRuleStatus() == RuleStatus.DISABLED) {
                    dataCleanTask.setTaskStatus(TaskStatus.SUCCESS);
                } else {
                    //延长下次的执行时间
                    // 获取当前时间
                    LocalDateTime currentTime = LocalDateTime.now();
                    // 将当前时间加上10秒
                    LocalDateTime nextExecTime = currentTime.plusSeconds(10);
                    dataCleanTask.setNextExecTime(nextExecTime);
                }
            }
            dataCleanTaskService.update(dataCleanTask);
            //todo 是否要增加一个专门记录日志的表
            return dataCleanTask.getTableName() + "_" + dataCleanTask.getRuleId();
        }, dataClearTaskExecutor);
    }


}

