package cn.newrank.niop.data.biz.export.constant;

import cn.newrank.niop.data.context.AppContext;
import java.util.List;

/**
 * <AUTHOR>
 */
public final class DataExportConstant {

    private DataExportConstant() {}

    /**
     * 提交最大参数量
     */
    public static final int MAX_PARAM_COUNT = 200000;

    /**
     * 导数应用 app-key 正式
     */
    private static final String PRODUCT_EXPORT_APP_KEY = "7e436b4fadb046988f774dc5b79abfa1";

    /**
     * 导数应用 app-key 测试
     */
    private static final String TEST_EXPORT_APP_KEY = "f9c66404d1b54804b2d288085ca58ae9";

    public static final String EXPORT_APP_KEY = AppContext.isProduct() ? PRODUCT_EXPORT_APP_KEY : TEST_EXPORT_APP_KEY;

    /**
     * 公网地址
     */
    private static final String PRODUCT_HOST = "https://open.newrank.cn";

    /**
     * 内网地址
     */
    private static final String PRODUCT_INTRANET_HOST = "https://open-intranet.newrank.cn";

    /**
     * 测试环境应用id
     */
    private static final String TEST_APP_ID = "5G2RJM2L";

    /**
     * 正式环境应用id
     */
    private static final String PRODUCT_APP_ID = "KDEKZYTI";

    public static final String EXPORT_APP_ID = AppContext.isProduct() ? PRODUCT_APP_ID : TEST_APP_ID;

    private static final String HOST = AppContext.isDev() ? PRODUCT_HOST : PRODUCT_INTRANET_HOST;

    public static final String SUBMIT_URL = HOST + "/api/open/atsp/task/submit";

    public static final String GET_RESULT_URL = HOST + "/api/open/atsp/task/result";

    /**
     * 导数场景id
     */
    public static final List<String> SCENE_IDS = List.of("61O7OIXO");

    /**
     * 导数任务提交中的缓存 key
     */
    public static final String EXPORT_TASK_SUBMITTING_KEY = "export-task-submitting:";

    /**
     * 游标查询条数
     */
    public static final int CURSOR_QUERY_SIZE = 100;

    /**
     * 单个文件最大结果数
     */
    public static final int FILE_MAX_RESULT_NUM = 100_0000;

}
