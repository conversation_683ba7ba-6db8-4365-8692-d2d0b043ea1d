package cn.newrank.niop.data.biz.biz.xhs.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2025/8/12 17:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XhsOpusClassifyAbility extends StorageEntity {
    private String opusId;
    private String noteCounterTypeV2;
    private String noteCounterTypeV1;

    @Override
    public String identifier() {
        return opusId;
    }
}
