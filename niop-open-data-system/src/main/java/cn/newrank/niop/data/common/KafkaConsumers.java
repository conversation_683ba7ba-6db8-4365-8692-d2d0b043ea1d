package cn.newrank.niop.data.common;

import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.config.property.KafkaProperties;
import lombok.experimental.UtilityClass;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/23 9:24
 */
@Log4j2
@UtilityClass
public class KafkaConsumers {

    /**
     * 创建一个kafka消费者
     *
     * @param config      配置
     * @param environment 环境
     * @return {@link KafkaConsumer}<{@link String}, {@link String}>
     */
    public static KafkaConsumer<String, String> createKafkaConsumer(KafkaProperties.Config config,
                                                                    Environment environment) {
        final Properties props = new Properties();
        //设置接入点
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());

        if (environment == Environment.DEVELOP) {
            final Path path = Paths.get("./kafka/api.kafka.client.truststore.jks");
            props.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, path.toAbsolutePath().toString());
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            props.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, "KafkaOnsClient");
            props.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");

            final String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";",
                    "org.apache.kafka.common.security.plain.PlainLoginModule",
                    config.getUsername(), config.getPassword());

            props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        } else {
            if (config.isPlaintext()) {
                props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "PLAINTEXT");
            } else {
                props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");

                final String jaasConfig = String.format("%s required username=\"%s\" password=\"%s\";",
                        "org.apache.kafka.common.security.plain.PlainLoginModule",
                        config.getUsername(), config.getPassword());

                props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
                props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            }
        }

        //可更加实际拉去数据和客户的版本等设置此值，默认30s
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        //设置单次拉取的量，走公网访问时，该参数会有较大影响
        //props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 32000);
        //props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 10000);
        //props.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, 32000);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 3000);
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, config.getMaxPoll());
        //消息的反序列化方式
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //属于同一个组的消费实例，会负载消费消息
        props.put(ConsumerConfig.GROUP_ID_CONFIG, config.getGroup());
        final KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);

        kafkaConsumer.subscribe(List.of(config.getTopic()));
        return kafkaConsumer;
    }
}
