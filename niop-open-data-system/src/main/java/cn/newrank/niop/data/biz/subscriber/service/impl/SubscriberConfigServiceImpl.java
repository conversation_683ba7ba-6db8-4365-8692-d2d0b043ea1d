package cn.newrank.niop.data.biz.subscriber.service.impl;

import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.manager.ConsoleManager;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.event.CbDeleteEvent;
import cn.newrank.niop.data.biz.service.CbConfigService;
import cn.newrank.niop.data.biz.subscriber.dao.SubscriberConfigDao;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.CbEnableParamSubCache;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigCreate;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigDelete;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigPageQuery;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigUpdate;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.common.BizErr;
import cn.newrank.niop.data.common.OpenConsoleRequest;
import cn.newrank.niop.util.U;
import cn.newrank.niop.web.model.PageView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.newrank.niop.data.util.Iterables.collectPart;
import static cn.newrank.niop.util.U.toList;
import static cn.newrank.niop.web.exception.BizExceptions.createBizException;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 11:09
 */
@Service
public class SubscriberConfigServiceImpl implements SubscriberConfigService {

    private final SubscriberConfigDao subscriberConfigDao;
    private final CbConfigService cbConfigService;
    private final ConsoleManager consoleManager;


    public SubscriberConfigServiceImpl(SubscriberConfigDao subscriberConfigDao,
                                       CbConfigService cbConfigService,
                                       ConsoleManager consoleManager) {
        this.subscriberConfigDao = subscriberConfigDao;
        this.cbConfigService = cbConfigService;
        this.consoleManager = consoleManager;
    }


    @Override
    public String create(SubscriberConfigCreate configCreate) {
        final SubscriberConfig config = configCreate.toDto();

        checkCallback(config);

        config.setSubscriberId(U.randUUID(8));

        subscriberConfigDao.save(config);

        return config.getSubscriberId();
    }

    private static void checkCallback(SubscriberConfig config) {
        SubSourceType sourceType = config.getSourceType();
        if (sourceType == SubSourceType.ABILITY_PARSED_RESULT ||
                sourceType == SubSourceType.ABILITY_RAW_RESULT ||
                sourceType == SubSourceType.ABILITY_PARSED_RESULT_OLD ||
                sourceType == SubSourceType.ABILITY_RAW_RESULT_OLD) {

            String sourceId = config.getSourceId();

            // 调用OpenConsoleRequest检查能力回调配置
            if (!OpenConsoleRequest.checkAbilityCallbackEnabled(sourceId)) {
                throw createBizException(BizErr.CALLBACK_ERROR, "能力回调配置未启用");
            }

        }
    }


    @Override
    public SubscriberConfig get(String subscriberId) {
        final SubscriberConfig subscriberConfig = subscriberConfigDao.get(subscriberId);
        if (subscriberConfig == null) {
            return null;
        }

        subscriberConfig.setApps(consoleManager.listApp(toList(subscriberConfig.getApps(), App::getAppId)));

        return subscriberConfig;
    }


    @Override
    public boolean update(SubscriberConfigUpdate configUpdate) {
        final SubscriberConfig config = configUpdate.toDto();

        checkCallback(config);

        if (subscriberConfigDao.get(config.getSubscriberId()) != null) {
            subscriberConfigDao.update(config);
            return true;
        }
        return false;
    }

    @Override
    public boolean delete(SubscriberConfigDelete subscriberConfigDelete) {
        SubscriberConfig config;
        if ((config = subscriberConfigDao.get(subscriberConfigDelete.getSubscriberId())) != null) {
            subscriberConfigDao.delete(config);
            return true;
        }

        return false;
    }

    @EventListener
    public void onCbDeleteEvent(CbDeleteEvent cbDeleteEvent) {
        final List<SubscriberConfig> configs = list(cbDeleteEvent.getCbId(), null, null);
        if (configs == null || configs.isEmpty()) {
            return;
        }

        throw createParamError("存在订阅关系(ids: {})， 无法删除", configs.stream()
                .map(SubscriberConfig::getSubscriberId)
                .toList());
    }

    @Override
    public PageView<SubscriberConfig> page(SubscriberConfigPageQuery pageQuery) {
        final PageView<SubscriberConfig> page = subscriberConfigDao.page(pageQuery);

        final Map<String, CbConfig> group = cbConfigService.group(toList(page.getRecords(), SubscriberConfig::getCbId));

        setApps(page.getRecords());
        return page.convert(config -> {

            final CbConfig cbConfig = group.get(config.getCbId());
            if (cbConfig != null) {
                config.setCbName(cbConfig.getName());
            }

            return config;
        });
    }

    private void setApps(List<SubscriberConfig> configs) {
        final List<String> appIds = configs.stream()
                .map(SubscriberConfig::getApps)
                .flatMap(List::stream)
                .map(App::getAppId)
                .distinct()
                .toList();

        final Map<String, App> appMap = consoleManager.mapApp(appIds);
        configs.forEach(config -> {
            final List<App> apps = toList(config.getApps(), App::getAppId).stream()
                    .map(appMap::get)
                    .toList();
            config.setApps(apps);
        });
    }

    @Override
    public List<SubscriberConfig> list(String cbId, String sourceName, String appName) {
        final List<SubscriberConfig> configs = subscriberConfigDao.listByCbId(cbId);
        if (configs == null) {
            return new ArrayList<>();
        }

        final Map<String, CbConfig> group = cbConfigService.group(toList(configs, SubscriberConfig::getCbId));

        setApps(configs);

        configs.forEach(config -> {
            final CbConfig cbConfig = group.get(config.getCbId());
            if (cbConfig != null) {
                config.setCbName(cbConfig.getName());
            }
        });


        return collectPart(configs, config -> {
            // 按照sourceName过滤(模糊查询效果)
            if (StringUtils.isNotBlank(sourceName)) {
                return config.getSourceName().contains(sourceName) || config.getSourceId().contains(sourceName);
            }

            // 按照appName过滤 apps列表必须有一项满足(模糊查询效果)
            if (StringUtils.isNotBlank(appName)) {
                final List<App> apps = config.getApps();
                if (apps == null) {
                    return false;
                }

                return apps.stream()
                        .anyMatch(app -> app.getAppName().contains(appName) || app.getAppId().contains(appName));
            }
            return true;
        });
    }

    //修改为根据三个sourceType, sourceId，appid
    @Override
    public List<String> searchCbIds(SubSourceType sourceType, String sourceId, String appId) {
        return subscriberConfigDao.searchCbIds(sourceType, sourceId, appId);
    }

    @Override
    public List<CbEnableParamSubCache> searchCdEnableParam(SubSourceType sourceType, String sourceId, String appId) {
        return subscriberConfigDao.searchCbEnableParam(sourceType, sourceId, appId);
    }

    @Override
    public List<SubscriberConfig> listBySourceIdAndTypes(String sourceId, List<SubSourceType> sourceTypes) {
        List<SubscriberConfig> configs = subscriberConfigDao.listBySourceIdAndTypes(sourceId, sourceTypes);

        // 设置cbName
        final Map<String, CbConfig> group = cbConfigService.group(toList(configs, SubscriberConfig::getCbId));
        configs.forEach(config -> {
            final CbConfig cbConfig = group.get(config.getCbId());
            if (cbConfig != null) {
                config.setCbName(cbConfig.getName());
            }
        });

        setApps(configs);
        return configs;
    }
}




