package cn.newrank.niop.data.biz.pojo.dto;

import cn.newrank.nrcore.utils.UuidUtils;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.MD5Utils;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.UUID;

/**
 * 往期参数
 *
 * <AUTHOR>
 * @since 2025/1/23 10:45:38
 */
@Data
@AllArgsConstructor
public class ApiPastParam {
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 动态接口id
     */
    private String interfaceId;
    /**
     * 参数
     */
    private JSONObject params;
    /**
     * 时间
     */
    private String requestTime;

    public static ApiPastParam create(String interfaceId,
                                      JSONObject param,
                                      String requestTime) {
        return new ApiPastParam(UuidUtils.getUuid(16),interfaceId, param, requestTime);
    }
}
