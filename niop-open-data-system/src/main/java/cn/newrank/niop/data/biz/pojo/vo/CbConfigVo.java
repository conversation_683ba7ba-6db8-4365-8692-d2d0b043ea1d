package cn.newrank.niop.data.biz.pojo.vo;

import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.web.model.EnumVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 回调信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 10:09
 */
@Data
public class CbConfigVo {
    /**
     * 负责人
     */
    List<String> maintainers;
    /**
     * 回调策略id
     */
    private String cbId;
    /**
     * 回调类型
     */
    private EnumVo type;
    /**
     * 回调名称
     */
    private String name;
    /**
     * 回调来源
     */
    private String source;
    /**
     * 回调配置信息
     */
    private Map<String, Object> config;
    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public static CbConfigVo fromDto(CbConfig config) {
        final CbConfigVo vo = new CbConfigVo();

        vo.setCbId(config.getCbId());
        vo.setType(EnumVo.of(config.getType()));
        vo.setName(config.getName());
        vo.setSource(config.getSource());
        vo.setConfig(config.getConfig().toMap());
        vo.setMaintainers(config.getMaintainers());
        vo.setGmtCreate(config.getGmtCreate());
        vo.setGmtModified(config.getGmtModified());

        return vo;
    }
}