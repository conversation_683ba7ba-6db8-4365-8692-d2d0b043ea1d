package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopCouponsDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_shop_coupons
 */
@Data
@Accessors(chain = true)
public class DyShopCoupons implements Serializable {

    private String useAreaDesc;
    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String couponTitle;

    /**
     *
     */
    private String whatCoupon;

    /**
     *
     */
    private String couponName;

    /**
     *
     */
    private String activityId;

    /**
     *
     */
    private Integer activityType;

    /**
     *
     */
    private String credit;

    /**
     *
     */
    private Long threshold;

    /**
     *
     */
    private Long discount;

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String expireTime;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     * 分区位点
     */
    private String partitionOffset;


    /**
     * 打折描述
     */
    private String couponsDesc;

    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;



    private static final long serialVersionUID = 1L;

    public static DyShopCoupons createItem(DyShopCouponsDTO dto) {
        return new DyShopCoupons()
                .setUseAreaDesc(dto.getUseAreaDesc())
                .setProductId(dto.getProductId())
                .setCouponTitle(dto.getCouponTitle())
                .setWhatCoupon(dto.getWhatCoupon())
                .setCouponName(dto.getCouponName())
                .setActivityId(dto.getActivityId())
                .setActivityType(dto.getActivityType())
                .setCredit(dto.getCredit())
                .setThreshold(dto.getThreshold())
                .setDiscount(dto.getDiscount())
                .setType(dto.getType())
                .setExpireTime(dto.getExpireTime())
                .setDeviceName(dto.getDeviceName())
                .setPartitionOffset(dto.getPartitionOffset())
                .setCouponsDesc(dto.getCouponsDesc());
    }
}