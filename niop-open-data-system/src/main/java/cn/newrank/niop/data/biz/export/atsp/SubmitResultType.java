package cn.newrank.niop.data.biz.export.atsp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public enum SubmitResultType {

    /**
     * 提交结果类型
     */
    SUCCEED(200, "成功", false),

    FAILED(-1, "", false),

    /**
     * 能力已达限额
     */
    EXCEED_LIMIT(2003, "能力已达限额", true),

    /**
     * 参数异常
     */
    INVALID_PARAM(10002, "参数异常", true),

    /**
     * 关系校验不通过
     */
    RELATIONSHIP_VALID_FAILED(403, "关系校验不通过", true),

    /**
     * 请求过于频繁
     */
    TOO_MANY_REQUESTS(429, "请求过于频繁", true),
    ;

    SubmitResultType(int code, String description, boolean paused) {
        this.code = code;
        this.description = description;
        this.paused = paused;
    }

    final int code;
    final String description;

    /**
     * 是否需要暂停任务
     */
    final boolean paused;

    public static SubmitResultType of(int code) {
        for (SubmitResultType resultType : values()) {
            if (resultType.getCode() == code) {
                return resultType;
            }
        }
        log.warn("Unknown submit result code: {}", code);
        return FAILED;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public boolean isPaused() {
        return paused;
    }

}
