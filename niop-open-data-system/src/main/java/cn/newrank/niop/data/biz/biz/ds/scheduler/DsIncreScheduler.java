package cn.newrank.niop.data.biz.biz.ds.scheduler;

import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonSyncInsertData;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;


/**
 * [增量数据同步]
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DsIncreScheduler {

    private final CommonSyncInsertData commonSyncInsertData;

    public DsIncreScheduler(CommonSyncInsertData commonSyncInsertData) {
        this.commonSyncInsertData = commonSyncInsertData;

    }

    @XxlJob("accountCollectCountSync")
    public ReturnT<String> accountCollectCountSync(String params) {
        commonSyncInsertData.accountCollectCountSync(params);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncDy")
    public ReturnT<String> syncInsertDataSyncDy(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.DY);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncXHS")
    public ReturnT<String> syncInsertDataSyncXHS(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.XHS);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncSPH")
    public ReturnT<String> syncInsertDataSyncSPH(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.SPH);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncWEIBO")
    public ReturnT<String> syncInsertDataSyncWEIBO(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.WEIBO);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncBILI")
    public ReturnT<String> syncInsertDataSyncBILI(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.BILI);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncKS")
    public ReturnT<String> syncInsertDataSyncKS(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.KS);
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncInsertDataSyncGZH")
    public ReturnT<String> syncInsertDataSyncGZH(String params) {
        commonSyncInsertData.syncInsertData("", PlatformType.GZH);
        return ReturnT.SUCCESS;
    }
}
