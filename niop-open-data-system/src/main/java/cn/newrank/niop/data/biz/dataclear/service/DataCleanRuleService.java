package cn.newrank.niop.data.biz.dataclear.service;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.param.CleanRuleUpadteQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanFuzzyQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanPageQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanRuleCreateQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.vo.DataCleanRuleSearchVo;
import cn.newrank.niop.web.model.PageView;

import java.util.List;

public interface DataCleanRuleService {

    /**
     * 创建数据清理
     *
     * @param callbackConfigCreate 数据清理信息
     * @return 数据清理id
     */
    String create(DataCleanRuleCreateQuery dataCleanRuleCreate);


    /**
     * 分页查询
     *
     * @param pageQuery 数据清理信息
     * @return 数据清理信息
     */
    PageView<DataCleanRuleSearchVo> page(DataCleanPageQuery pageQuery);

    /**
     * 更新数据清理
     *
     * @param dataCleanRuleCreate 数据清理信息
     * @return 是否更新成功
     */
    boolean update(CleanRuleUpadteQuery cleanRuleUpadteQuery);

    /**
     * 删除数据清理
     *
     * @param cbId 数据清理id
     * @return 是否删除成功
     */
    boolean delete(String cbId);


    /**
     * 获取数据清理配置
     *
     * @param cbId 数据清理id
     * @return 数据清理信息
     */
    DataCleanRuleCreate get(String ruleId);

    /**
     * 模糊查询
     *
     * @param fuzzyQuery 数据清理信息
     * @return 数据清理信息
     */
    List<DataCleanRuleCreate> fuzzyQuery(DataCleanFuzzyQuery fuzzyQuery);

    /**
     * 获取下次调度时间为当前这一小时的
     *
     * @return List<DataCleanRuleCreate>
     */
    List<DataCleanRuleCreate> listExecuteTask(Integer cursor);

}
