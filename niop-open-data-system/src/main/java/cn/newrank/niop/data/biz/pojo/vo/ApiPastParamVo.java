package cn.newrank.niop.data.biz.pojo.vo;

import cn.newrank.niop.data.biz.pojo.dto.ApiPastParam;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 往期参数
 *
 * <AUTHOR>
 * @since 2025/1/23 10:45:38
 */
@Data
@AllArgsConstructor
public class ApiPastParamVo {
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 动态接口id
     */
    private String interfaceId;
    /**
     * 参数
     */
    private JSONObject params;
    /**
     * 时间
     */
    private String requestTime;


    public static ApiPastParamVo build(ApiPastParam params) {
        return new ApiPastParamVo(
                params.getRequestId(),
                params.getInterfaceId(),
                params.getParams(),
                params.getRequestTime());
    }
}
