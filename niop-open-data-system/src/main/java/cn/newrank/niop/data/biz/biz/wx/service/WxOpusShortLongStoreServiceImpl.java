package cn.newrank.niop.data.biz.biz.wx.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.wx.enums.WxDataTypeEnum;
import cn.newrank.niop.data.biz.biz.wx.pojo.WxOpusShortLongResult;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.common.ds.LindormSQLFactory;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 小红书作品存储ldm服务
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
@Service
public class WxOpusShortLongStoreServiceImpl implements StorageBizService<WxOpusShortLongResult> {


    private final NamedParameterJdbcTemplate  template;
    private static final String INSERT_TEMPLATE = """
            UPSERT INTO dim_opus_url (uuid, short_url, long_url, comment_id, update_time)
            VALUES (:awemeId, :shortUrl, :longUrl, :commentId , :acqTime)
            """;
    public WxOpusShortLongStoreServiceImpl(DsConfigManager dsConfigManager) {
        this.template = LindormSQLFactory.DEFAULT.create(dsConfigManager.chooseWxLmConfig()).availableJdbcTemplate();
    }


    @Override
    public WxOpusShortLongResult castOf(JSONObject item) {
        if (Objects.isNull(item)) {
            return null;
        }
        String dataType = item.getString("data_type");
        WxDataTypeEnum dataTypeEnum = WxDataTypeEnum.getByJsonCode(dataType);
        if (Objects.isNull(dataTypeEnum)) {
            return null;
        }
        return WxOpusShortLongResult.covertByType(item, dataTypeEnum);
    }



    @Override
    public void storeBatch(List<WxOpusShortLongResult> results) {
        results = results.stream().filter(Objects::nonNull).toList();
        if (CollectionUtil.isEmpty(results)) {
            return;
        }
        final SqlParameterSource[] params = results.stream()
                .map(BeanPropertySqlParameterSource::new)
                .toArray(SqlParameterSource[]::new);
        try{
            template.batchUpdate(INSERT_TEMPLATE,params);
        }catch (Exception e){
            log.error("【wx-opus-short-long-url】 数据写入异常", e);
        }

    }

}
