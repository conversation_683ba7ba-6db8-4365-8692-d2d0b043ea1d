package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCoupons;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_shop_coupons】的数据库操作Mapper
 * @createDate 2024-10-17 15:08:05
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyShopCoupons
 */
@Mapper
public interface DyShopCouponsMapper {


    void batchSave(@Param("itemList") List<DyShopCoupons> itemList);

}




