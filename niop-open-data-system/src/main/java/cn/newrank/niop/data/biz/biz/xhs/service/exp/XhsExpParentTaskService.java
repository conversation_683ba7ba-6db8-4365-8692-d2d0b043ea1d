package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.*;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpConfig;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpSubmitSchedule;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopicOpus;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * CWWEVCGP
 *
 * <AUTHOR>
 * @since 2025/3/28 11:46:10
 */
@Slf4j
@Service
public class XhsExpParentTaskService {

    private final XhsExpScheduleMapper xhsExpScheduleMapper;
    private final XhsExpTopicMapper xhsExpTopicMapper;
    private final XhsExpParentTaskMapper xhsExpParentTaskMapper;
    private final XhsExpAtspTaskMapper xhsExpAtspTaskMapper;
    private final XhsExpCacheService xhsExpCacheService;
    private final XhsExpTaskStoreService xhsExpTaskStoreService;
    private final XhsExpHoloOpusMapper xhsExpHoloOpusMapper;
    private final XhsOpusLmMapper xhsOpusLmMapper;

    public XhsExpParentTaskService(XhsExpScheduleMapper xhsPocWeightMapper,
                                   XhsExpTopicMapper xhsExpTopicMapper,
                                   XhsExpParentTaskMapper xhsExpParentTaskMapper,
                                   XhsExpAtspTaskMapper xhsExpAtspTaskMapper,
                                   XhsExpCacheService xhsExpCacheService,
                                   XhsExpTaskStoreService xhsExpTaskStoreService, XhsExpHoloOpusMapper xhsExpHoloOpusMapper, XhsOpusLmMapper xhsOpusLmMapper) {
        this.xhsExpScheduleMapper = xhsPocWeightMapper;
        this.xhsExpTopicMapper = xhsExpTopicMapper;
        this.xhsExpParentTaskMapper = xhsExpParentTaskMapper;
        this.xhsExpAtspTaskMapper = xhsExpAtspTaskMapper;
        this.xhsExpCacheService = xhsExpCacheService;
        this.xhsExpTaskStoreService = xhsExpTaskStoreService;
        this.xhsExpHoloOpusMapper = xhsExpHoloOpusMapper;
        this.xhsOpusLmMapper = xhsOpusLmMapper;
    }

    /**
     * 运行调度器定时任务
     */
    public void runSchedule(String param) {
        List<XhsExpSubmitSchedule> schedules = xhsExpScheduleMapper.listSchedules()
                .stream()
                .filter(Objects::nonNull)
                .toList();
        if (CollectionUtil.isEmpty(schedules)) {
            XxlJobLogger.log("无调度执行");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        if (Strings.isNotBlank(param)) {
            XxlJobLogger.log("调度入参: {} 测试任务", param);
            now = LocalDateTime.parse(param, DatePattern.NORM_DATETIME_FORMATTER);
        }
        readyToTriggerSchedule(now, schedules);
    }

    /**
     * 准备触发调度
     *
     * @param now       时间
     * @param schedules 调度
     */
    private void readyToTriggerSchedule(LocalDateTime now, List<XhsExpSubmitSchedule> schedules) {
        createParentAndAtspTaskTablePartition(now);
        for (XhsExpSubmitSchedule item : schedules) {
            checkIsTrigger(item, now);
        }
    }

    /**
     * 创建父任务和子任务表分区
     *
     * @param now 时间
     */
    private void createParentAndAtspTaskTablePartition(LocalDateTime now) {
        now = now.plusDays(1);
        final LocalDate nowDate = now.toLocalDate();
        final String partition = DateTimeUtil.dayOfPartition(now);
        xhsExpParentTaskMapper.createTable(partition, nowDate);
        xhsExpAtspTaskMapper.createTable(partition, nowDate);
    }


    /**
     * 检查创建任务是否触发
     *
     * @param schedule 时间
     */
    public void checkIsTrigger(XhsExpSubmitSchedule schedule, LocalDateTime now) {
        final int nowHour = now.getHour();
        final Integer weight = schedule.getTopicWeight();
        final JSONObject execHourRange = JSON.parseObject(schedule.getExecHourRange());
        // 该调度是否在触发时间范围
        int startHour = execHourRange.getInteger("startHour");
        int endHour = execHourRange.getInteger("endHour");
        if (nowHour < startHour || nowHour >= endHour) {
            XxlJobLogger.log("权重: {} 未到触发时间范围", weight);
            return;
        }
        // 该调度轮询间隔
        final XhsExpConfig config = xhsExpCacheService.getXhsExpConfig(weight);
        final Integer publishTimePeriod = config.getPublishTimePeriod();
        final Integer interval = config.getInterval();
        final LocalDateTime lastExecDate = DateTimeUtil.toDateTime(schedule.getLastExecTime());

        // 判断是否为权重1或2
        if (weight.equals(1) || weight.equals(2)) {
            // 提前60分钟提交
            final LocalDateTime triggerTime = lastExecDate.plusMinutes(interval - 60);
            if (triggerTime.isAfter(now)) {
                XxlJobLogger.log("权重: {} 未到权重轮询触发的时间", weight);
                return;
            }
            final LocalDateTime minPublishTime = now.minusMinutes(publishTimePeriod);
            // 批量创建父任务和子任务
            createParentTaskAndCursorTask(schedule.getTopicWeight(), triggerTime, minPublishTime);
            // 更新调度时间
            xhsExpScheduleMapper.updateScheduleExecTime(schedule.getId(), triggerTime.plusMinutes(60));
            return;
        }


        if (lastExecDate.plusMinutes(interval).isAfter(now)) {
            XxlJobLogger.log("权重: {} 未到权重轮询触发的时间", weight);
            return;
        }
        // 判断触发时间范围内是否第一次执行
        int lastHour = lastExecDate.getHour();
        // 当前权重获取作品截止时间
        final LocalDateTime minPublishTime = now.minusMinutes(publishTimePeriod);
        final LocalDateTime triggerTime = getTriggerTime(now, lastExecDate, startHour, lastHour, endHour, interval);
        // 批量创建父任务和子任务
        createParentTaskAndCursorTask(schedule.getTopicWeight(), triggerTime, minPublishTime);
        // 更新调度时间
        xhsExpScheduleMapper.updateScheduleExecTime(schedule.getId(), triggerTime);
    }

    private static @NotNull LocalDateTime getTriggerTime(LocalDateTime now,
                                                         LocalDateTime lastExecDate,
                                                         int startHour,
                                                         int lastHour,
                                                         int endHour,
                                                         Integer interval) {
        LocalDateTime triggerTime;
        if (lastExecDate.getDayOfMonth() == now.getDayOfMonth() && startHour <= lastHour && lastHour < endHour) {
            // 更新调度时间为上次执行时间 + interval
            triggerTime = lastExecDate.plusMinutes(interval);
        } else {
            // 更新调度时间为触发时间
            triggerTime = now.toLocalDate().atStartOfDay().plusHours(startHour);
        }
        return triggerTime;
    }


    /**
     * 分页创建任务
     *
     * @param weight         权重
     * @param triggerTime    触发时间
     * @param minPublishTime 最小发布时间
     */
    public void createParentTaskAndCursorTask(Integer weight,
                                              LocalDateTime triggerTime,
                                              LocalDateTime minPublishTime) {
        Long cursor = -1L;
        while (true) {
            List<XhsExpTopic> list = xhsExpTopicMapper.listByWeight(weight, cursor, 1000);
            if (CollectionUtil.isEmpty(list)) {
                XxlJobLogger.log("权重: {} 无话题任务提交", weight);
                break;
            }
            xhsExpTaskStoreService.storeTaskBatch(triggerTime, minPublishTime, list);
            cursor = list.get(list.size() - 1).getId();
        }
    }
}
