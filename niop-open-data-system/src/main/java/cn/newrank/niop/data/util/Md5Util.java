package cn.newrank.niop.data.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.codec.binary.Hex;
import org.springframework.util.DigestUtils;

/**
 * md5工具类
 *
 * <AUTHOR>
 */
public final class Md5Util {

    private Md5Util() {}

    /**
     * 文件md5,支持大文件
     *
     * @param file 文件
     * @return md5
     * @throws NoSuchAlgorithmException 没有加密算法异常
     * <AUTHOR>
     **/
    public static String md5(File file) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("md5");
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192];
            int length;
            while ((length = fis.read(buffer)) != -1) {
                md5.update(buffer, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Hex.encodeHexString(md5.digest());
    }

    public static String md5(String text) {
        return DigestUtils.md5DigestAsHex(text.getBytes());
    }

}
