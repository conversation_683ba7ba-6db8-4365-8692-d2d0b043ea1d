package cn.newrank.niop.data.biz.biz.xhs.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusClassify;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.sdk.common.Environment;
import cn.newrank.niop.sdk.common.entity.Resp;
import cn.newrank.niop.sdk.common.entity.code.SubmitTaskRespCode;
import cn.newrank.niop.sdk.common.producer.ProducerClient;
import cn.newrank.niop.sdk.common.producer.TaskParam;
import cn.newrank.niop.sdk.http.base.DefaultProducerClient;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/8/12 15:09
 */
@Slf4j
@Service
public class XhsClassifyServiceImpl implements StorageBizService<XhsOpusClassify> {

    private final ProducerClient producerClient;

    private final String[] sceneIds;

    private final String abilityId;

    private final RedissonClient redissonClient;

    public XhsClassifyServiceImpl(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
        String gatewayContextPath = Environment.PRODUCT.getGatewayContextPath();
        String appKey = "c8f9bed432bc40edb2e73f28c1220ffa";
        this.producerClient = new DefaultProducerClient(gatewayContextPath, appKey);
        this.sceneIds = new String[]{"NA8VKPVW"};
        this.abilityId = "L3YI3KAC";
    }

    /**
     * bloom过滤器
     *
     * @return 过滤器
     */
    private RBloomFilter<String> bloomFilter() {
        String date = DateUtil.date().toString(DatePattern.PURE_DATE_PATTERN);
        RBloomFilter<String> bloomFilter = redissonClient.getBloomFilter("bloom_filter:xhs_classify:" + date);

        //期望插入2000万个Key，误报率0.001 (0.1%)
        int expectedInsertions = 20_000_000;
        double falseProbability = 0.001;
        boolean tryInit = bloomFilter.tryInit(expectedInsertions, falseProbability);
        if (tryInit) {
            bloomFilter.expire(Duration.ofHours(25));
        }
        return bloomFilter;
    }

    @Override
    public void storeBatch(List<XhsOpusClassify> items) {
        items.stream().filter(opus -> !bloomFilter().contains(opus.getOpusId())).forEach(this::submit);
    }

    /**
     * 提交任务
     *
     * @param opus 作品信息
     */
    private void submit(XhsOpusClassify opus) {
        if (CharSequenceUtil.isBlank(opus.getTitle()) && CharSequenceUtil.isBlank(opus.getDesc())) {
            return;
        }
        OpusClassifyParams params = OpusClassifyParams.build(opus);
        if (Objects.isNull(params.getLine())) {
            return;
        }
        int count = 0, max = 3;
        while (count < max) {
            try {
                TaskParam taskParam = new TaskParam();
                taskParam.setParams(params);
                taskParam.setSceneIds(sceneIds);
                taskParam.setReuseTimeout(7 * 24 * 60 * 60);
                Resp<String> resp = producerClient.submit(abilityId, taskParam);
                ThreadUtil.sleep(RandomUtil.randomInt(4, 10));
                if (Objects.nonNull(resp) && resp.is200Code()) {
                    bloomFilter().add(opus.getOpusId());
                    return;
                }
                SubmitTaskRespCode respCode = resp.toRespCode(SubmitTaskRespCode.class);
                if (respCode.isAbilityQuotaLimit()) {
                    return;
                }
                count++;
            } catch (Exception e) {
                log.error("submit_task_err", e);
            }
        }
    }

    @Data
    private static class OpusClassifyParams {
        private String opusId;
        private String line;

        private static OpusClassifyParams build(XhsOpusClassify opus) {
            OpusClassifyParams params = new OpusClassifyParams();
            params.opusId = opus.getOpusId();
            params.line = castStr(opus.getTitle()) + castStr(opus.getDesc());
            return params;
        }

        private static String castStr(String text) {
            return Objects.isNull(text) ? "" : text;
        }
    }

    @Override
    public XhsOpusClassify castOf(JSONObject item) {
        return item.to(XhsOpusClassify.class);
    }
}
