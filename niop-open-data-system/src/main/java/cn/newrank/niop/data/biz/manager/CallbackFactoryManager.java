package cn.newrank.niop.data.biz.manager;

import cn.newrank.niop.data.biz.component.callback.CallbackFactory;
import cn.newrank.niop.data.biz.pojo.enums.CbType;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.EnumMap;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 14:09
 */
@Log4j2
@Component
public class CallbackFactoryManager implements ApplicationContextAware, ApplicationRunner {

    private final EnumMap<CbType, CallbackFactory> callbackFactories;
    private ApplicationContext ioc;

    public CallbackFactoryManager() {
        this.callbackFactories = new EnumMap<>(CbType.class);
    }

    /**
     * 获取客户端工厂
     *
     * @param category 数据源类型
     * @return 客户端工厂
     */
    public CallbackFactory getFactory(CbType type) {
        final CallbackFactory callbackFactory = callbackFactories.get(type);
        if (callbackFactory != null) {
            return callbackFactory;
        }

        throw createParamError("未找到(type: {})回调工厂", type);
    }

    /**
     * 注册回调客户端工厂
     *
     * @param cbType          回调类型
     * @param callbackFactory 客户端工厂
     */
    public void register(CbType cbType, CallbackFactory callbackFactory) {
        if (callbackFactories.containsKey(cbType)) {
            throw createParamError("已存在(category: {})回调源工厂", cbType);
        }

        callbackFactories.put(cbType, callbackFactory);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.ioc = applicationContext;
    }

    @Override
    public void run(ApplicationArguments args) {
        for (CbType type : CbType.values()) {
            register(type, ioc.getBean(type.getFactoryClass()));
        }
    }
}
