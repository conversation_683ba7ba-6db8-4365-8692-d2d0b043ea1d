package cn.newrank.niop.data.biz.subscriber.pojo.po;

import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SendStrategy;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.Tag;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.util.U;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @TableName niop_data_subscriber
 */
@Data
public class SubscriberConfigPo implements Serializable {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private String subscriberId;

    /**
     *
     */
    private String appId;
    private String appIds;

    /**
     *
     */
    private String cbId;

    /**
     *
     */
    private String sourceId;
    private String sourceName;

    /**
     *
     */
    private SubSourceType sourceType;
    private Boolean enableParams;

    /**
     * 发送策略
     */
    private String sendStrategy;
    /**
     * 标签
     */
    private String tags;
    /**
     *
     */
    private String maintainers;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public SubscriberConfig toDto() {
        final SubscriberConfig config = new SubscriberConfig();

        config.setApps(U.toList(appIds(), idItem -> {
            final App app = new App();
            app.setAppId(idItem);
            return app;
        }));

        config.setCbId(cbId);
        config.setGmtCreate(gmtCreate);
        config.setGmtModified(gmtModified);
        config.setMaintainers(JSON.parseArray(maintainers, String.class));
        config.setSourceId(sourceId);
        config.setSourceName(sourceName);
        config.setSourceType(sourceType);
        config.setEnableParams(enableParams);
        if (StringUtils.isNotBlank(sendStrategy)) {
            config.setSendStrategy(JSON.parseObject(sendStrategy, SendStrategy.class));
        }
        if (StringUtils.isNotBlank(tags)) {
            config.setTags(JSON.parseArray(tags, Tag.class));
        }
        config.setSubscriberId(subscriberId);

        return config;
    }

    public List<String> appIds() {
        final List<String> ids;
        if (StringUtils.isBlank(appIds)) {
            ids = new ArrayList<>();
        } else {
            ids = JSON.parseArray(appIds, String.class);
        }

        if (StringUtils.isNotBlank(appId) && !ids.contains(appId)) {
            ids.add(appId);
        }

        return ids;
    }
}