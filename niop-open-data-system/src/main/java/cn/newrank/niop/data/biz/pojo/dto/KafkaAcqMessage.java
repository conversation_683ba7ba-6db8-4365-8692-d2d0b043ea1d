package cn.newrank.niop.data.biz.pojo.dto;

import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.nrcore.json.JsonField;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/7/11
 */
@Data
@Accessors(chain = true)
public class KafkaAcqMessage {

    @JsonField("ana_time")
    private String ana_time;
    
    @JsonField("data_type")
    private String data_type;

    @JsonField("event_time")
    private String event_time;

    @JsonField("gmt_time")
    private String gmt_time;

    @JsonField("json_details")
    private String json_details;

    @JsonField("logic_id")
    private String logic_id;

    @JsonField("normal_id")
    private String normal_id;

    /**
     * kafka partition
     */
    @Json<PERSON>ield("kafka_partition")
    private String kafka_partition;

    /**
     * kafka offset
     */
    @JsonField("kafka_offset")
    private String kafka_offset;


    /**
     * @param data ability result
     * @return kafka ability result
     */
    public static KafkaAcqMessage convertAcqMessage(JSONObject data) {
        KafkaAcqMessage result = JsonParser.parseObject(data, KafkaAcqMessage.class);
        if(Objects.isNull(result)){
            throw new RuntimeException("parse kafka acq message is null");
        }
        return result;
    }


    public static String getAcqMessageId(KafkaAcqMessage acqMessage) {
        if (Objects.isNull(acqMessage)) {
            return null;
        }
        final String partition = acqMessage.getKafka_partition();
        final String offset = acqMessage.getKafka_offset();
        return getMessageId(partition,offset);
    }

    public static String getMessageId(String partition,String offset) {
        if (Strings.isBlank(partition) || Strings.isBlank(offset)) {
            return null;
        }
        return partition + "_" + offset;
    }


    /**
     * 获取ds
     *
     * @param time str
     * @return ds  sendTime则默认当前时间
     */
    public static String getDs(String time) {
        if (Strings.isBlank(time)) {
            return DateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
        }
        LocalDateTime ds;
        try{
            ds = DateTimeUtil.toDateTime(time);
        }catch (Exception e){
            ds = DateTimeUtil.parseUtcToLocalDateTime(time);
        }
        return DateTimeUtil.format(ds, "yyyyMMdd");
    }
}



