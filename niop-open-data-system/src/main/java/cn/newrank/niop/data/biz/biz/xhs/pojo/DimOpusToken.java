package cn.newrank.niop.data.biz.biz.xhs.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 *
 *<AUTHOR>
 *@since  2025/1/8 13:48
 *@version 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="dim_opus_token")
@Data
public class DimOpusToken extends StorageEntity {
    /**
     * 
     */
    private String awemeId;

    /**
     * 
     */
    private String accountId;

    /**
     * 
     */
    private Timestamp publicTime;

    /**
     * 
     */
    private String xsecToken;

    /**
     * 
     */
    private String mbXsecToken;

    /**
     * 
     */
    private Long likeCount;

    /**
     * 
     */
    private Long commentCount;

    /**
     * 
     */
    private Long shareCount;

    /**
     * 
     */
    private Timestamp acqTime;

    /**
     * 
     */
    private Timestamp anaTime;

    public static DimOpusToken of(JSONObject item) {
        if (item == null) {
            return null;
        }
        final DimOpusToken token = new DimOpusToken();

        token.setAwemeId(item.getString("aweme_id"));
        token.setAccountId(item.getString("account_id"));
        token.setPublicTime(Timestamp.valueOf(item.getString("public_time")));
        token.setXsecToken(item.getString("xsec_token"));
        token.setMbXsecToken(item.getString("mb_xsec_token"));
        token.setLikeCount(item.getLong("like_count"));
        token.setCommentCount(item.getLong("comment_count"));
        token.setShareCount(item.getLong("share_count"));
        token.setAcqTime(Timestamp.valueOf(item.getString("acq_time")));
        token.setAnaTime(Timestamp.valueOf(item.getString("ana_time")));
        return token;
    }

    @Override
    public String identifier() {
        return awemeId;
    }

    public void copyIfExistNull(DimOpusToken other) {
        if (other == null) {
            return;
        }
        if (StringUtils.isBlank(awemeId)) {
            this.setAwemeId(other.getAwemeId());
        }

        if (StringUtils.isBlank(accountId)) {
            this.setAccountId(other.getAccountId());
        }
        if (publicTime == null) {
            this.setPublicTime(other.getPublicTime());
        }
        if (StringUtils.isBlank(xsecToken)) {
            this.setXsecToken(other.getXsecToken());
        }
        if (StringUtils.isBlank(mbXsecToken)) {
            this.setMbXsecToken(other.getMbXsecToken());
        }
        if (likeCount == null) {
            this.setLikeCount(other.getLikeCount());
        }
        if (commentCount == null) {
            this.setCommentCount(other.getCommentCount());
        }
        if (shareCount == null) {
            this.setShareCount(other.getShareCount());
        }
        if (acqTime == null) {
            this.setAcqTime(other.getAcqTime());
        }
        if (anaTime == null) {
            this.setAnaTime(other.getAnaTime());
        }
    }
}