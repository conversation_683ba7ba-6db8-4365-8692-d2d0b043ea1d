package cn.newrank.niop.data.biz.biz.dy.scheduler;

import cn.newrank.niop.data.biz.biz.dy.service.haihui.DyHaihuiSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:12:11
 */
@RestController
@RequestMapping("/dyHaihuiSchedule")
@AllArgsConstructor
public class DyHaiHuiSchedule {

    private final DyHaihuiSyncService dyHaihuiSyncService;

    /**
     * 同步ES数据到lindorm
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/syncHaiHuiDyOpusData")
    @XxlJob("syncHaiHuiDyOpusData")
    public ReturnT<String> syncHaiHuiDyOpusData(String param) {
        dyHaihuiSyncService.sync(param);
        return ReturnT.SUCCESS;
    }


}
