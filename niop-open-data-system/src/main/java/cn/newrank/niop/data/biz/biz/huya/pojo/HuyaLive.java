package cn.newrank.niop.data.biz.biz.huya.pojo;

import cn.newrank.niop.data.biz.component.biz.Calculation;
import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 虎牙直播数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 14:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HuyaLive extends SampleVersionEntity implements Serializable {
    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 人气峰值/峰值热度
     */
    private Long hot;

    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 直播状态
     */
    private String liveStatus;

    /**
     * 下播时间
     */
    private Timestamp endTime;

    /**
     * 开播时间
     */
    private Timestamp startTime;


    /**
     * 峰值热度
     */
    @Calculation("max(hot)")
    private Long maxHot;

    /**
     * 平均热度
     */
    @Calculation("avg(hot)")
    private BigDecimal avgHot;

    /**
     * 时长（s）
     */
    @Calculation("(endTime - startTime) / 1000 (s)")
    private Long duration;


    /**
     * 是否计算过
     */
    @Calculation("self")
    private Boolean calculated;
}