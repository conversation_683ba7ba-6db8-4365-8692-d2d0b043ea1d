package cn.newrank.niop.data.biz.pojo.po;

import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import cn.newrank.niop.data.common.model.Arg;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceSchemaPo {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private Timestamp gmtModified;

    /**
     *
     */
    private Timestamp gmtCreate;

    /**
     * 接口SchemaID
     */
    private String interfaceSchemaId;

    /**
     * 接口ID
     */
    private String interfaceId;

    /**
     * 数据解析Schema
     */
    private String dataSchema;

    /**
     * 数据样例
     */
    String dataExample;

    /**
     * 是否开始映射
     */
    Boolean enableMapping;

    public static DynamicInterfaceSchemaPo fromDto(DynamicInterfaceSchema dynamicInterfaceSchema) {
        final DynamicInterfaceSchemaPo interfaceSchemaPo = new DynamicInterfaceSchemaPo();

        interfaceSchemaPo.setInterfaceSchemaId(dynamicInterfaceSchema.getInterfaceSchemaId());
        interfaceSchemaPo.setInterfaceId(dynamicInterfaceSchema.getInterfaceId());

        interfaceSchemaPo.setDataSchema(dynamicInterfaceSchema.getDataSchema());
        interfaceSchemaPo.setDataExample(dynamicInterfaceSchema.getDataExample());
        interfaceSchemaPo.setEnableMapping(dynamicInterfaceSchema.getEnableMapping());

        return interfaceSchemaPo;
    }

    public DynamicInterfaceSchema toDto() {
        final DynamicInterfaceSchema dynamicInterfaceSchema = new DynamicInterfaceSchema();

        dynamicInterfaceSchema.setInterfaceSchemaId(this.interfaceSchemaId);
        dynamicInterfaceSchema.setInterfaceId(this.interfaceId);

        dynamicInterfaceSchema.setGmtCreate(this.gmtCreate.toLocalDateTime());
        dynamicInterfaceSchema.setGmtModified(this.gmtModified.toLocalDateTime());

        dynamicInterfaceSchema.setDataSchema(this.dataSchema);
        dynamicInterfaceSchema.setDataExample(this.dataExample);
        dynamicInterfaceSchema.setEnableMapping(this.enableMapping);

        return dynamicInterfaceSchema;
    }
}