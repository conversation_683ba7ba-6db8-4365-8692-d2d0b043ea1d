package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;

import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopicOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1 10:20:07
 */
@DS("holo")
@Mapper
public interface XhsExpHoloOpusLogMapper {


    /**
     * 存储作品列表
     * @param taskId 任务ID
     * @param parentId 父任务ID
     * @param topicId 话题ID
     * @param items 作品列表
     * @return 存储结果
     */
    boolean storeBatch(@Param("taskId") String taskId,
                       @Param("parentId") String parentId,
                       @Param("topicId") String topicId,
                       @Param("acqTime") LocalDateTime acqTime,
                       @Param("items") List<XhsExpTopicOpus> items,
                       @Param("ds") String ds,
                       @Param("dateTime")LocalDateTime dateTime);


    /**
     * 根据父任务ID获取作品数量
     * @param parentId 父任务ID
     * @return 作品数量
     */
    Integer getOpusNumByParentTaskId(@Param("parentId") String parentId,
                                     @Param("ds") String ds);
}
