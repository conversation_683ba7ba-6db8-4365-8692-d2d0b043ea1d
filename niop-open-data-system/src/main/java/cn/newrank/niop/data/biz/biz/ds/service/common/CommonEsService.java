package cn.newrank.niop.data.biz.biz.ds.service.common;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.EsCodec;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.TEST_YOU_DU_MAIN_ES_INDEX_20241015;
import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.YOU_DU_MAIN_ES_INDEX_20241015;
import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 10:35
 */
@Log4j2
@Service
public class CommonEsService {
    private final RestHighLevelClient storageEsClient;
    private final String index;

    public CommonEsService(RestHighLevelClient storageEsClient, SystemConfig systemConfig) {
        this.storageEsClient = storageEsClient;
        this.index = systemConfig.isProduct() ? YOU_DU_MAIN_ES_INDEX_20241015 : TEST_YOU_DU_MAIN_ES_INDEX_20241015;
    }

    public RestClient getRestClient() {
        return storageEsClient.getLowLevelClient();
    }

    /**
     * 批量更新
     *
     * @param entities 数据
     * @return 更新数量
     */
    public int update(List<? extends EsEntity> entities) {
        return update(entities, false);
    }

    /**
     * 批量更新，不存在就插入
     *
     * @param entities 数据
     * @return 更新数量
     */
    public int upsert(List<? extends EsEntity> entities) {
        return update(entities, true);
    }

    private int update(List<? extends EsEntity> entities, boolean upsert) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        final BulkRequest bulkRequest = new BulkRequest(index);
        for (EsEntity entity : entities) {
            final String id = entity.docId();
            if (StringUtils.isBlank(id)) {
                throw createParamError("id为空");
            }
            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest
                    .id(id)
                    .doc(EsCodec.serialize(entity), XContentType.JSON);

            if (upsert) {
                updateRequest.upsert(EsCodec.serialize(entity), XContentType.JSON);
            }

            bulkRequest.add(updateRequest);

        }
        if (bulkRequest.numberOfActions() == 0) {
            return 0;
        }

        try {
            BulkResponse bulkResponse = storageEsClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                XxlJobLogger.log("批量请求有失败: {}", bulkResponse.buildFailureMessage());
            }
            return bulkRequest.numberOfActions();
        } catch (IOException e) {
            XxlJobLogger.log("大搜Es-同步数据出现问题{}", e);
            throw createDbError(e, "大搜Es数据更新异常, err: {}", e.getMessage());
        }
    }
}
