package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyLittleYellowCarDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_little_yellow_car
 */
@Data
@Accessors(chain = true)
public class DyLittleYellowCar implements Serializable {
    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String shopId;

    /**
     *
     */
    private String shopName;

    /**
     *
     */
    private String shopSchema;

    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String promotionId;

    /**
     *
     */
    private String regularPrice;

    /**
     *
     */
    private Long stockNum;

    /**
     *
     */
    private String title;

    /**
     *
     */
    private String shopGuarantee;

    /**
     *
     */
    private String maxPrice;

    /**
     *
     */
    private String minPrice;

    /**
     *
     */
    private Boolean lottery;

    /**
     *
     */
    private Integer itemType;

    /**
     *
     */
    private Boolean isSoleSku;

    /**
     *
     */
    private Boolean inStock;

    /**
     *
     */
    private Integer flashType;

    /**
     *
     */
    private String activityInfo;

    /**
     *
     */
    private String priceInfo;

    /**
     *
     */
    private String showSkuId;

    /**
     *
     */
    private String elasticTitle;

    /**
     *
     */
    private String discountPrice;

    /**
     *
     */
    private String discountLabel;

    /**
     *
     */
    private String cover;

    /**
     *
     */
    private Long firstCid;

    /**
     *
     */
    private Long secondCid;

    /**
     *
     */
    private Long thirdCid;

    /**
     *
     */
    private Long fourthCid;

    /**
     *
     */
    private Boolean canAddCart;

    /**
     *
     */
    private Boolean canSold;

    /**
     *
     */
    private Integer applyCoupon;

    /**
     *
     */
    private Boolean campaign;

    /**
     *
     */
    private String campaignId;

    /**
     *
     */
    private Integer campaignType;

    /**
     *
     */
    private Long campaignStartTime;

    /**
     *
     */
    private Long campaignEndTime;

    /**
     *
     */
    private Boolean campaignIsPreheat;

    /**
     *
     */
    private Long campaignLeftStock;

    /**
     *
     */
    private String campaignMaxPrice;

    /**
     *
     */
    private String campaignDepositPrice;

    /**
     *
     */
    private String campaignOriginPrice;

    /**
     *
     */
    private String campaignPrice;

    /**
     *
     */
    private String campaignPromotionId;

    /**
     *
     */
    private String campaignRegularPrice;

    /**
     *
     */
    private Long campaignStock;

    private String deviceName;

    private String partitionOffset;

    /**
     * 直播id
     */
    private String roomId;

    private static final long serialVersionUID = 1L;

    public static DyLittleYellowCar createItem(DyLittleYellowCarDTO dto) {
        return new DyLittleYellowCar()
                .setShopId(dto.getShopId())
                .setShopName(dto.getShopName())
                .setShopSchema(dto.getShopSchema())
                .setProductId(dto.getProductId())
                .setPromotionId(dto.getPromotionId())
                .setRegularPrice(dto.getRegularPrice())
                .setStockNum(dto.getStockNum())
                .setTitle(dto.getTitle())
                .setShopGuarantee(dto.getShopGuarantee())
                .setMaxPrice(dto.getMaxPrice())
                .setMinPrice(dto.getMinPrice())
                .setLottery(dto.getLottery())
                .setItemType(dto.getItemType())
                .setIsSoleSku(dto.getIsSoleSku())
                .setInStock(dto.getInStock())
                .setFlashType(dto.getFlashType())
                .setActivityInfo(dto.getActivityInfo())
                .setPriceInfo(dto.getPriceInfo())
                .setShowSkuId(dto.getShowSkuId())
                .setElasticTitle(dto.getElasticTitle())
                .setDiscountPrice(dto.getDiscountPrice())
                .setDiscountLabel(dto.getDiscountLabel())
                .setCover(dto.getCover())
                .setFirstCid(dto.getFirstCid())
                .setSecondCid(dto.getSecondCid())
                .setThirdCid(dto.getThirdCid())
                .setFourthCid(dto.getFourthCid())
                .setCanAddCart(dto.getCanAddCart())
                .setCanSold(dto.getCanSold())
                .setApplyCoupon(dto.getApplyCoupon())
                .setCampaign(dto.getCampaign())
                .setCampaignId(dto.getCampaignId())
                .setCampaignType(dto.getCampaignType())
                .setCampaignStartTime(dto.getCampaignStartTime())
                .setCampaignEndTime(dto.getCampaignEndTime())
                .setCampaignIsPreheat(dto.getCampaignIsPreheat())
                .setCampaignLeftStock(dto.getCampaignLeftStock())
                .setCampaignMaxPrice(dto.getCampaignMaxPrice())
                .setCampaignDepositPrice(dto.getCampaignDepositPrice())
                .setCampaignOriginPrice(dto.getCampaignOriginPrice())
                .setCampaignPrice(dto.getCampaignPrice())
                .setCampaignPromotionId(dto.getCampaignPromotionId())
                .setCampaignRegularPrice(dto.getCampaignRegularPrice())
                .setCampaignStock(dto.getCampaignStock())
                .setDeviceName(dto.getDeviceName())
                .setPartitionOffset(dto.getPartitionOffset())
                .setRoomId(dto.getRoomId());
    }


}