package cn.newrank.niop.data.biz.subscriber.controller;

import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigCreate;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigDelete;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigPageQuery;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigUpdate;
import cn.newrank.niop.data.biz.subscriber.pojo.vo.SubscriberConfigVo;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 11:00
 */
@RestController
@Validated
@RequestMapping("subscriber")
public class SubscriberController {
    private final SubscriberConfigService subscriberConfigService;

    public SubscriberController(SubscriberConfigService subscriberConfigService) {
        this.subscriberConfigService = subscriberConfigService;
    }

    @PostMapping("config/create")
    public String createConfig(@Valid @RequestBody SubscriberConfigCreate configCreate) {
        return subscriberConfigService.create(configCreate);
    }


    @GetMapping("config/get")
    public SubscriberConfigVo get(@NotBlank(message = "订阅ID(subscriberId)不能为空") String subscriberId) {
        return SubscriberConfigVo.fromDto(subscriberConfigService.get(subscriberId));
    }


    @PostMapping("config/update")
    public boolean updateConfig(@Valid @RequestBody SubscriberConfigUpdate configUpdate) {
        return subscriberConfigService.update(configUpdate);
    }


    @GetMapping("config/page")
    public PageView<SubscriberConfigVo> page(@Valid SubscriberConfigPageQuery pageQuery) {
        return subscriberConfigService.page(pageQuery).convert(SubscriberConfigVo::fromDto);
    }

    //删除数据源头与回调源的订阅关系
    @PostMapping("config/delete")
    public boolean deleteConfig(@Valid @RequestBody SubscriberConfigDelete subscribeConfigDelete) {
        return subscriberConfigService.delete(subscribeConfigDelete);
    }


    /**
     * 获取数据源头与回调源的订阅关系
     *
     * @param cbId       回调源ID
     * @param sourceName 数据源
     * @param appName    应用名称
     * @return 订阅关系
     */
    @GetMapping("list")
    public List<SubscriberConfigVo> list(@NotBlank(message = "回调源ID不能为空") String cbId, String sourceName, String appName) {
        return subscriberConfigService.list(cbId, sourceName, appName).stream().map(SubscriberConfigVo::fromDto).toList();
    }

}
