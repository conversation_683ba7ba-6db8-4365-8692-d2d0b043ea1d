package cn.newrank.niop.data.biz.export.mapper;

import cn.newrank.niop.data.biz.export.pojo.param.DataExportPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DataExportMapper {

    /**
     * 新增导数
     *
     * @param dataExport 导数信息
     * @return 是否成功
     */
    boolean insert(DataExport dataExport);

    /**
     * 通过导数id获取导数信息
     *
     * @param exportId 导数id
     * @return 导数信息
     */
    DataExport getByExportId(@Param("exportId") String exportId);

    /**
     * 根据导数id判断是否存在
     *
     * @param exportId 导数id
     * @return 是否存在
     */
    boolean existDataExport(@Param("exportId") String exportId);

    /**
     * 分页搜索导数信息
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 分页数据
     */
    Page<DataExport> page(@Param("query") DataExportPageQuery query, Page<DataExport> page);

}
