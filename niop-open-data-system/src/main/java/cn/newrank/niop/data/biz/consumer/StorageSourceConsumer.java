package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.config.property.KafkaProperties;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:26
 */
@Log4j2
public class StorageSourceConsumer extends CommonConsumer {

    public StorageSourceConsumer(KafkaProperties.Config config
            , Environment environment
            , StorageBiz storageBiz
            , StorageService storageService
            , ThreadPoolExecutor executor) {
        super(config, environment, new StorageSourceConsumeService(storageService, storageBiz), executor);

    }

    private record StorageSourceConsumeService(StorageService storageService,
                                               StorageBiz storageBiz) implements ConsumeService {

        @Override
        public boolean consume(ConsumerRecords<String, String> consumerRecords) {
            return handle(consumerRecords);
        }

        private boolean handle(ConsumerRecords<String, String> consumerRecords) {
            final List<JSONObject> records = StreamSupport.stream(consumerRecords.spliterator(), false)
                    .map(consumerRecord -> {
                        JSONObject jsonObject = JSON.parseObject(consumerRecord.value());
                        jsonObject.put("kafka_partition", consumerRecord.partition());
                        jsonObject.put("kafka_offset", consumerRecord.offset());
                        return jsonObject;
                    })
                    .toList();
            try {
                storageService.storeBatch(storageBiz, records);
                return true;
            } catch (Exception e) {
                int partition = 0;
                long minOffset = Long.MAX_VALUE;
                for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
                    long offset = consumerRecord.offset();
                    if (offset < minOffset) {
                        minOffset = offset;
                        partition = consumerRecord.partition();
                    }
                }
                log.error("数据消费失败,Partition: {}, MinOffset: {}, e: {}", partition, minOffset, getStackTrace(e));
                return false;
            }
        }
    }


}
