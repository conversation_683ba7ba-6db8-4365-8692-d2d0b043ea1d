package cn.newrank.niop.data.biz.biz.tiktok.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * tiktok用户日榜
 */
@Data
public class TiktokDailyRank {
    /**
     *
     */
    private String uid;

    /**
     * 分区字段(yyyyMM)
     */
    private String ds;

    /**
     * 地域
     */
    private String region;

    /**
     * 关注数
     */
    private Long followingNum;

    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 点赞数
     */
    private Long likeNum;

    /**
     * 作品数
     */
    private Long opusNum;

    /**
     * 新榜指数
     */
    private BigDecimal nrIndex;

    /**
     * 榜单日期
     */
    private Date rankDate;

    /**
     * 入库时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}