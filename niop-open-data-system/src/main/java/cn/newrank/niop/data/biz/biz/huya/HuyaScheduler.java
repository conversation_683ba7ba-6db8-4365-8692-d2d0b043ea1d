package cn.newrank.niop.data.biz.biz.huya;

import cn.newrank.niop.data.biz.biz.huya.pojo.HuyaLive;
import cn.newrank.niop.data.biz.biz.huya.service.HuyaLiveService;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.common.lock.DistributeLock;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 18:56
 */
@Log4j2
@Component
public class HuyaScheduler {

    private final HuyaLiveService huyaLiveService;
    private final StorageService storageService;

    public HuyaScheduler(HuyaLiveService huyaLiveService, StorageService storageService) {
        this.huyaLiveService = huyaLiveService;
        this.storageService = storageService;
    }


    @DistributeLock(abortException = false)
    @Scheduled(cron = "0 0/5 * * * ?")
    public void run() {
        String failedSampleId = null;
        while (true) {
            final List<HuyaLive> lives = huyaLiveService.listCalculateLives();
            if (lives.isEmpty()) {
                return;
            }

            for (HuyaLive live : lives) {
                if (StringUtils.equals(failedSampleId, live.getSampleId())) {
                    return;
                }

                try {
                    log.info("计算数据: {}", live);
                    calculate(live);
                } catch (Exception e) {
                    log.error("计算失败，e: ", e);
                    failedSampleId = live.getSampleId();
                }
            }
        }
    }

    private void calculate(HuyaLive live) {
        final LinkedList<StorageHistory.History> histories =
                storageService.getHistory(StorageBiz.HUYA_LIVE, live.getSampleId()).getHistories();

        final int total = histories.size() + 1;


        final Timestamp endTime = live.getEndTime();
        final Timestamp startTime = live.getStartTime();
        if (endTime != null && startTime != null) {
            final Duration between = Duration.between(startTime.toLocalDateTime(), endTime.toLocalDateTime());

            // 直播
            live.setDuration(between.toSeconds());
        }


        long maxHot = live.getHot() == null ? 0 : live.getHot();
        long hotSum = maxHot;
        for (StorageHistory.History history : histories) {
            final HuyaLive huyaLive = history.getData().to(HuyaLive.class);

            final Long hot = huyaLive.getHot();
            if (hot == null) {
                continue;
            }
            if (hot > maxHot) {
                maxHot = hot;
            }

            hotSum += hot;
        }

        live.setMaxHot(maxHot);
        live.setAvgHot(new BigDecimal(hotSum).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP));

        huyaLiveService.updateCalculateProps(live);
    }
}
