package cn.newrank.niop.data.biz.component.biz;

import cn.newrank.niop.data.biz.component.sls.SlsClient;
import cn.newrank.niop.data.biz.component.sls.SlsProducer;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.pojo.dto.StorageResult;
import cn.newrank.niop.data.biz.pojo.param.StorageHistoryPageQuery;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.web.model.PageView;
import com.alibaba.fastjson2.JSON;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 11:04
 */
@Service
@Log4j2
public class HistoryStorageService {

    private static final String HISTORY_DETAIL_QUERY_TEMPLATE = """
            * and storageBiz: %s and version: %s and identifier: %s and environment: %s\s
            """;
    private static final String HISTORY_ALL_QUERY_TEMPLATE = """
            * and storageBiz: %s and identifier: %s and environment: %s\s
            """;

    private static final String HISTORY_PAGE_QUERY_TEMPLATE = """
            * and storageBiz: %s and identifier: %s and environment: %s and updateTime>= %s and updateTime < %s
            """;
    private final SlsProducer slsProducer;
    private final SlsClient slsClient;
    private final String environment;

    public HistoryStorageService(SlsProducer slsProducer,
                                 SlsClient slsClient,
                                 SystemConfig systemConfig) {
        this.slsProducer = slsProducer;
        this.slsClient = slsClient;
        this.environment = systemConfig.getEnvironment().name();
    }

    @NotNull
    private static StorageHistory createStorageHistory(String identifier, List<QueriedLog> logs) {
        final StorageHistory storageHistory = new StorageHistory();
        storageHistory.setIdentifier(identifier);

        if (logs.isEmpty()) {
            return storageHistory;
        }


        logs.stream()
                .map(StorageResult::of)
                .collect(Collectors.toMap(StorageResult::getVersion,
                        Function.identity(), (v1, v2) -> v1.getUpdateTime() >= v2.getUpdateTime() ? v1 : v2))
                .values()
                .stream()
                .sorted(Comparator.comparingInt(StorageResult::getVersion))
                .forEach(r -> {
                    final StorageHistory.History history = new StorageHistory.History();
                    history.setVersion(r.getVersion());
                    history.setUpdateTime(r.getUpdateTime());
                    history.setData(JSON.parseObject(r.getData()));

                    storageHistory.getHistories().add(history);
                });

        return storageHistory;
    }

    /**
     * 存储历史数据
     *
     * @param storageBiz    业务类型
     * @param historyEntity 历史数据
     */
    public void storeHistory(StorageBiz storageBiz, StorageEntity historyEntity) {
        if (storageBiz == null) {
            return;
        }

        if (historyEntity instanceof StorageVersionEntity storageVersionEntity) {
            final String identifier = storageVersionEntity.identifier();

            if (StringUtils.isBlank(identifier)) {
                throw createParamError("identifier is blank, entity: {}", storageVersionEntity.toJSONString());
            }

            final int version = storageVersionEntity.getVersion();
            if (version < 1) {
                throw createParamError("version is invalid, entity: {}", storageVersionEntity.toJSONString());
            }

            final LogItem item = new LogItem();
            item.PushBack("identifier", identifier);
            item.PushBack("updateTime", String.valueOf(storageVersionEntity.versionUpdateTime()));
            item.PushBack("storageBiz", storageBiz.getJson());
            item.PushBack("data", storageVersionEntity.toJSONString(true));
            item.PushBack("version", String.valueOf(version));
            item.PushBack("environment", environment);

            slsProducer.send(item);
        }
    }

    public StorageResult get(StorageBiz storageBiz, String identifier, int version) {
        final String query = HISTORY_DETAIL_QUERY_TEMPLATE.formatted(storageBiz.getJson(),
                version, identifier, environment);
        final List<QueriedLog> logs = slsClient.getLogs(LocalDateTime.now().minusYears(1), LocalDateTime.now(),
                query, 10000, 1);

        if (logs.isEmpty()) {
            return null;
        }

        return logs.stream()
                .map(StorageResult::of)
                .sorted(Comparator.comparingLong(StorageResult::getUpdateTime))
                .toList()
                .get(logs.size() - 1);
    }

    public StorageHistory getHistory(StorageBiz storageBiz, String identifier) {
        final String query = HISTORY_ALL_QUERY_TEMPLATE.formatted(storageBiz.getJson(), identifier, environment);

        final List<QueriedLog> logs = slsClient.getLogs(LocalDateTime.now().minusYears(1), LocalDateTime.now(),
                query, 10000, 1);

        return createStorageHistory(identifier, logs);
    }

    public StorageHistory getHistories(StorageHistoryPageQuery query) {
        final String pageQueryDsl = HISTORY_PAGE_QUERY_TEMPLATE.formatted(query.getStorageBiz().getJson(),
                query.getIdentifier(),
                environment,
                DateTimeUtil.toEpochMillis(query.getStartTime()), DateTimeUtil.toEpochMillis(query.getEndTime()));

        final PageView<QueriedLog> logPageView = slsClient.pageLogs(LocalDateTime.now().minusYears(1),
                LocalDateTime.now(),
                pageQueryDsl, query);

        final StorageHistory storageHistory = createStorageHistory(query.getIdentifier(), logPageView.getRecords());

        storageHistory.setPages(logPageView.getPages());
        storageHistory.setTotal(logPageView.getTotal());

        return storageHistory;
    }

}
