package cn.newrank.niop.data.biz.export.pojo.param;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import cn.newrank.nrcore.utils.UuidUtils;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

@Data
public class DataExportTaskSubmit {

    /**
     * 导数id
     */
    @NotBlank(message = "导数id不能为空")
    private String exportId;

    /**
     * 业务场景
     */
    @NotBlank(message = "业务场景不能为空")
    private String bizScene;

    /**
     * 参数文件地址
     */
    @URL(protocol = "https", message = "参数文件地址异常")
    private String paramFile;

    public DataExportTask initTask() {
        final DataExportTask task = new DataExportTask();
        task.setExportId(this.getExportId());
        task.setExportTaskId(UuidUtils.getUuid(16).toLowerCase());
        task.setBizScene(this.getBizScene());
        task.setTaskParamFile(this.getParamFile());
        task.setDeliveryType(DataExportDeliveryType.EXCEL);
        task.setRunningStatus(DataExportRunningStatus.PAUSED);
        task.setTaskStatus(DataExportTaskStatus.PARAM_FILE_UPLOADED);
        // 异步计算精确值
        task.setParamTotalNum(null);
        task.setSucceedTotalNum(0);
        task.setTaskFinishedTime(null);
        return task;
    }

}
