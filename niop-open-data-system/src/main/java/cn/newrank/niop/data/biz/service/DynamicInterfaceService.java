package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.dto.ApiPastParam;
import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import cn.newrank.niop.web.model.PageView;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 动态取数接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:24
 */
public interface DynamicInterfaceService {

    /**
     * 创建动态接口
     *
     * @param interfaceCreate 接口创建参数
     * @return 接口ID
     */
    String create(DynamicInterfaceCreate interfaceCreate);


    /**
     * 预览动态接口
     *
     * @param interfaceQueryPreview 预览参数
     * @return 预览结果
     */
    Resp queryPreview(@Valid DynamicInterfaceQueryPreview interfaceQueryPreview);

    /**
     * 更新动态接口
     *
     * @param interfaceUpdate 接口更新参数
     * @return 是否更新成功
     */
    boolean update(DynamicInterfaceUpdate interfaceUpdate);


    /**
     * 删除动态接口
     *
     * @param interfaceDelete 接口删除参数
     * @return true 删除成功
     */
    boolean delete(DynamicInterfaceDelete interfaceDelete);

    /**
     * 添加接口标签
     *
     * @param tagUpdate 标签更新参数
     * @return true 更新成功
     */
    boolean updateTags(DynamicInterfaceTagUpdate tagUpdate);

    /**
     * 分页查询接口
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    PageView<DynamicInterface> page(DynamicInterfacePageQuery pageQuery);

    /**
     * 获取接口详情
     *
     * @param interfaceId 接口ID
     * @return 接口详情
     */
    DynamicInterface get(String interfaceId);

    /**
     * 创建动态接口授权
     *
     * @param authCreate 授权创建参数
     * @return 授权ID
     */
    String createAuth(DynamicInterfaceAuthCreate authCreate);

    /**
     * 删除动态接口授权
     *
     * @param authId 授权ID
     * @return true 删除成功
     */
    boolean deleteAuth(String authId);

    /**
     * 更新动态接口授权
     *
     * @param authUpdate 授权更新参数
     * @return true 更新成功
     */
    boolean updateAuth(DynamicInterfaceAuthUpdate authUpdate);

    /**
     * 获取接口授权列表
     *
     * @param interfaceId 接口ID
     * @return 授权列表
     */
    List<DynamicInterfaceAuth> listAuth(String interfaceId);

    /**
     * 获取接口授权详情
     *
     * @param authId 授权ID
     * @return 授权详情
     */
    DynamicInterfaceAuth getAuth(String authId);

    /**
     * 获取接口授权详情
     *
     * @param interfaceId 接口ID
     * @param appId       应用ID
     */
    DynamicInterfaceAuth getAuth(String interfaceId, String appId);

    /**
     * 调试接口
     *
     * @param queryDebug 调试参数
     * @return 调试结果
     */
    Resp debug(HttpServletRequest request,DynamicInterfaceQueryDebug queryDebug);


    /**
     * 获取接口过去参数
     *
     * @param interfaceId 接口ID
     * @return 接口过去参数列表
     */
    List<ApiPastParam> listPastParams(String interfaceId);

    /**
     * 创建调试接口
     *
     * @param createDebug 调试参数
     * @return 调试结果
     */
    Resp createDebug(DynamicInterfaceCreateDebug createDebug);

    /**
     * 查询解析之后的动态接口预览
     *
     * @param interfaceQueryPreview 预览参数
     * @return 接口预览
     */
    Resp parsedQueryPreview(DynamicInterfaceParsedQueryPreview interfaceQueryPreview);

}
