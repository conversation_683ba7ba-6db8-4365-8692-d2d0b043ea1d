package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;


import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpNewTopic;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 监测话题表
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
public interface XhsExpNewTopicMapper {

    /**
     * 批量存储
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeBatch(@Param("items") List<XhsExpNewTopic> items);

    /**
     * 更新topic权重
     * @param item 话题
     * @return 更新结果
     */
    boolean updateTopicOpusNum(@Param("item") XhsExpNewTopic item,
                               @Param("newOpusNum") Integer newOpusNum);


    /**
     * 根据topicId
     * @param date
     * @param items
     * @return
     */
    List<XhsExpNewTopic> getExistByTopicIds(@Param("addDate") LocalDate addDate,
                                       @Param("items") List<String> items);


    /**
     * 根据topicId
     * @param date
     * @param opusNum
     * @return
     */
    List<XhsExpNewTopic> getTopicIdLimitOpusNum(@Param("addDate") LocalDate date,
                                                @Param("opusNum") Integer opusNum,
                                                @Param("cursor")Long cursor,
                                                @Param("size") Integer size);
}




