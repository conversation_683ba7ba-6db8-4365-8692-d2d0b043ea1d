package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.param.CollectionPageQuery;
import cn.newrank.niop.data.biz.pojo.param.ColumnQuery;
import cn.newrank.niop.data.biz.pojo.param.DatasourceConnectionTest;
import cn.newrank.niop.data.common.ds.Collection;
import cn.newrank.niop.data.common.ds.Column;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.web.model.PageView;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 14:00
 */
public interface DatasourceService {

    /**
     * 测试连接
     *
     * @param connectionTest 测试连接参数
     * @return 是否成功
     */
    boolean testConnection(DatasourceConnectionTest connectionTest);

    /**
     * 获取数据源
     *
     * @param dcId 数据源id
     * @return 数据源
     */
    Datasource getDatasource(String dcId);

    /**
     * 获取集合
     *
     * @param pageQuery 集合查询参数
     * @return 集合
     */
    PageView<Collection> pageCollection(CollectionPageQuery pageQuery);

    /**
     * 获取列
     *
     * @param columnQuery 列查询参数
     * @return 列
     */
    List<Column> getColumns(ColumnQuery columnQuery);

    /**
     * 数据源查询预览数据
     *
     * @param dcId 数据源配置id
     * @return 预览数据
     */
    Resp.PageView queryPreview(String dcId, String collection);

    /**
     * 数据源查询
     *
     * @param dcId  数据源配置ID
     * @param query 查询语句
     * @param args  查询参数
     * @return 结果
     */
    Resp query(String dcId, String query, Map<String, Object> args);

    /**
     * 获取子集合
     *
     * @param dcId       数据源配置ID
     * @param collection 集合名
     * @return 子集合
     */
    List<Collection> getChildren(String dcId, String collection);

    /**
     * 根据dcid获取下面所有的SchemaName
     *
     * @param dcId
     * @return List<String>
     * <AUTHOR>
     * @date 2025/2/26 17:59
     */
    List<String> querySchemaName(String dcId);

    List<String> querytableName(String dcId, String schemaName);
}
