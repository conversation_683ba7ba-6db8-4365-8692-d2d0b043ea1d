package cn.newrank.niop.data.biz.callback.pojo;

import lombok.Data;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/27 14:53
 */
@Data
public class CbHttpTask {
    /**
     * 回调任务ID
     */
    private String cid;

    /**
     * 数据源key
     */
    private String sourceKey;

    /**
     * 数据源ID
     */
    private String sourceId;

    /**
     * 数据源类型
     */
    private String sourceType;

    /**
     * 回调ID
     */
    private String cbId;

    /**
     * 状态
     */
    private CbStatus status;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 下次回调时间
     */
    private Timestamp nextCallbackTime;
    private Date partition;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 数据的cid
     */
    public String getDataCid() {
        return cid.substring(8);
    }

    public String getErrorMsg() {
        if (errorMsg == null) {
            return null;
        }

        return errorMsg.length() > 4000 ? errorMsg.substring(0, 4000) : errorMsg;
    }
}