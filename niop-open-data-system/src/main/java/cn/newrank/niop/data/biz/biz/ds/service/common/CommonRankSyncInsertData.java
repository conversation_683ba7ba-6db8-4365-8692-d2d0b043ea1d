package cn.newrank.niop.data.biz.biz.ds.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.mapper.DsMapper;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.po.DsEsSyncRecordPo;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBiz;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizServiceContext;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncEntity;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class CommonRankSyncInsertData {

    public static final String ACCOUNT_LIST_QUERY = """
            GET /%s/_search?scroll=5m
            {
                "size": 500,
                "_source": %s,
                "query": {
                    "bool": {
                        "must": [
                         {
                                  "range": {
                                   "%s": {
                                      "gte": "%s",
                                      "lte": "%s"
                                    }
                                  }
                                },
                         {
                                "range": {
                                    "%s": {
                                        "gte": 0
                                    }
                                }
                            }
                        ]
                    }
                }
                  , "sort": [
                    {
                      "%s": {
                        "order": "asc"
                      }
                    }
                  ]
            }
            """;

    public static final String ACCOUNT_LIST_SEARCH_AFTER_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "5m",
                "scroll_id" :  "%s"
            }
            """;

    private final EsFactory esFactory;
    private final DsMapper dsMapper;
    private final SyncBizServiceContext syncBizServiceContext;
    private final CommonEsService commonEsService;

    public CommonRankSyncInsertData(DsMapper dsMapper, SyncBizServiceContext syncBizServiceContext, CommonEsService commonEsService) {
        this.esFactory = EsFactory.DEFAULT;
        this.dsMapper = dsMapper;
        this.syncBizServiceContext = syncBizServiceContext;
        this.commonEsService = commonEsService;
    }

    public String[] getTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfWeek = now.minusDays(now.getDayOfWeek().getValue() + 8)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String startDate = startOfWeek.format(formatter);
        String endDate = now.format(formatter);

        return new String[]{startDate, endDate};
    }

    public void syncInsertData(String lastSyncTime, RankPlatformType platformType) {
        XxlJobLogger.log("大搜es开始同步平台{}", platformType);
        if (StrUtil.isBlank(lastSyncTime)) {
            final DsEsSyncRecordPo dsEsSyncRecordPo = dsMapper.get(platformType);

            if (null == dsEsSyncRecordPo) {
                XxlJobLogger.log("请检查同步时间，首次同步需要进行手动添加db记录");
                return;
            }

            lastSyncTime = dsEsSyncRecordPo.getLastSyncTime();
        }

        if (StrUtil.isBlank(lastSyncTime) || !DateTimeUtil.isValidDateFormat(lastSyncTime, DatePattern.NORM_DATETIME_PATTERN)) {
            XxlJobLogger.log("同步时间有误，请检查(params:{})", lastSyncTime);
            return;
        }

        final SyncBiz syncBiz = SyncBiz.ofJSONValue(platformType.getDbCode());
        final SyncBizService<? extends SyncEntity> syncBizService = syncBizServiceContext.getService(syncBiz);

        String[] time = getTime();
        String startTime = time[0];
        String endTime = time[1];
        try (final EsFactory.Es es = esFactory.create(syncBizService.getConfig())) {
            //每周执行一次
            DateTime nowSyncTime = DateUtil.beginOfDay(DateUtil.date()).offset(DateField.HOUR_OF_DAY, 16);
            Resp query = es.query(
                    es.newQueryBuilder()
                            .template(
                                    String.format(ACCOUNT_LIST_QUERY,
                                            syncBizService.getIndexName(),
                                            syncBizService.getSourceFields(),
                                            syncBizService.getRangIndex(),
                                            startTime,
                                            endTime,
                                            syncBizService.getRangField(),
                                            syncBizService.getRangIndex()
                                    )
                            )
            );
            Resp.DataView dataView = query.getDataView();

            int total = 0;
            while (true) {
                if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
                    JSONObject resultObj = esDataView.resp();

                    final List<DataCenterEsMetaData> dataList = syncBizService.parseData(resultObj, DataCenterEsMetaData.class);
                    if (CollUtil.isEmpty(dataList)) {
                        break;
                    }

                    // 同步到数据中心es
                    commonEsService.upsert(dataList);
                    total += dataList.size();
                    String scrollId = resultObj.getString(EsUtil.SCROLL_ID);

                    query = es.query(
                            es.newQueryBuilder().template(
                                    String.format(ACCOUNT_LIST_SEARCH_AFTER_QUERY,
                                            scrollId
                                    )
                            ));
                    dataView = query.getDataView();
                }
            }

            // 格式化日期时间
            String nowSyncTimeStr = DateUtil.formatDateTime(nowSyncTime);
            // 更新同步时间(每天下午4点)
            dsMapper.update(syncBiz.getJson(), nowSyncTimeStr);
            XxlJobLogger.log("大搜es数据-[({})平台数据]同步成功, total: {}", platformType, total);
        } catch (Exception e) {
            log.error("大搜es数据-[{}平台增量数据]同步失败, e: ", syncBiz.getJson(), e);
            XxlJobLogger.log("大搜es数据-[{}平台增量数据]同步失败, e: ", syncBiz.getJson(), e);
        }
    }
}
