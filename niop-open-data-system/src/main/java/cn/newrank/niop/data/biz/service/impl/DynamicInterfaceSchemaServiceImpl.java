package cn.newrank.niop.data.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.dao.DynamicInterfaceSchemaDao;
import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.service.DynamicInterfaceSchemaService;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import cn.newrank.niop.data.util.Ids;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:24
 */
@Service
@RequiredArgsConstructor
public class DynamicInterfaceSchemaServiceImpl implements DynamicInterfaceSchemaService {
    private final DynamicInterfaceSchemaDao dynamicInterfaceSchemaDao;

    @Override
    public String save(DynamicInterfaceSchemaSave interfaceSchemaSave) {
        String interfaceId = interfaceSchemaSave.getInterfaceId();
        final DynamicInterfaceSchema dynamicInterfaceSchema = interfaceSchemaSave.toDto();

        String interfaceSchemaId = dynamicInterfaceSchema.getInterfaceSchemaId();
        if (StrUtil.isBlank(interfaceSchemaId)) {
            DynamicInterfaceSchema interfaceSchema = dynamicInterfaceSchemaDao.getByInterfaceId(interfaceId);
            if (interfaceSchema != null) {
                throw createParamError("接口(ID: {})数据映射已存在", interfaceId);
            }
            dynamicInterfaceSchema.setInterfaceSchemaId(Ids.create(DynamicInterfaceSchema.ID_LENGTH));
            dynamicInterfaceSchemaDao.create(dynamicInterfaceSchema);
        }else {
            dynamicInterfaceSchemaDao.update(dynamicInterfaceSchema);
        }

        return dynamicInterfaceSchema.getInterfaceId();
    }

    @Override
    public DynamicInterfaceSchema get(String interfaceId) {
        return dynamicInterfaceSchemaDao.getByInterfaceId(interfaceId);
    }

}




