package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/9 15:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DsConfigUpdate extends DsConfigCreate {

    /**
     * 数据源id
     */
    @NotBlank(message = "数据源id(dcId)不能为空")
    String dcId;


    @Override
    public DsConfig toDto() {
        final DsConfig dsConfig = super.toDto();

        dsConfig.setDcId(dcId);

        return dsConfig;
    }
}
