package cn.newrank.niop.data.biz.export.controller;

import cn.newrank.niop.data.biz.export.pojo.param.DataExportCreate;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportVo;
import cn.newrank.niop.data.biz.export.service.DataExportService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.constraints.NotBlank;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/export")
public class DataExportController {

    private final DataExportService dataExportService;

    public DataExportController(DataExportService dataExportService) {
        this.dataExportService = dataExportService;
    }

    /**
     * 新建导数
     *
     * @param exportCreate 导数参数
     * @return 新建的导数信息
     */
    @PostMapping("/create")
    public DataExportVo create(@Validated @RequestBody DataExportCreate exportCreate) {
        final DataExport dataExport = dataExportService.create(exportCreate);
        return DataExportVo.buildBy(dataExport);
    }

    /**
     * 分页查询导数信息
     *
     * @param query 查询参数
     * @return 分页导数数据
     */
    @GetMapping("/page")
    public PageView<DataExportVo> page(@Validated DataExportPageQuery query) {
        return dataExportService.page(query).convert(DataExportVo::buildBy);
    }

    @GetMapping("/template/download")
    public ResponseEntity<FileSystemResource> downloadTemplateFile(@NotBlank(message = "导数id不能为空") String exportId) {
        return dataExportService.getTemplateFile(exportId);
    }

}
