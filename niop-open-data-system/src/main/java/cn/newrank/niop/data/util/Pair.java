package cn.newrank.niop.data.util;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

public class Pair<T1, T2> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final T1 object1;

    private final T2 object2;

    public Pair(T1 object1, T2 object2) {
        this.object1 = object1;
        this.object2 = object2;
    }

    public T1 getObject1() {
        return this.object1;
    }

    public T2 getObject2() {
        return this.object2;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(this.getObject1()) ^ Objects.hashCode(this.getObject2());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else if (!(obj instanceof Pair<?,?> other)) {
            return false;
        } else {
            return Objects.equals(this.getObject1(), other.getObject1()) && Objects.equals(this.getObject2(), other.getObject2());
        }
    }

}
