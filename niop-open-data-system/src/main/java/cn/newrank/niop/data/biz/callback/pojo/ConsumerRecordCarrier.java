package cn.newrank.niop.data.biz.callback.pojo;


import lombok.Data;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * ConsumerRecord载体
 */
@Data
public class ConsumerRecordCarrier {

    private String topic;
    private int partition;
    private long offset;
    private String key;
    private String value;

    public static ConsumerRecordCarrier buildBy(ConsumerRecord<String, String> record) {
        ConsumerRecordCarrier carrier = new ConsumerRecordCarrier();
        carrier.setTopic(record.topic());
        carrier.setPartition(record.partition());
        carrier.setOffset(record.offset());
        carrier.setKey(record.key());
        carrier.setValue(record.value());
        return carrier;
    }

    public ConsumerRecord<String, String> convert() {
        return new ConsumerRecord<>(this.topic, this.partition, this.offset, this.key, this.value);
    }


}
