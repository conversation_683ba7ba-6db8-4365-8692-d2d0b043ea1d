package cn.newrank.niop.data.biz.biz.xhs.pojo.exp;

import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/2 01:08:51
 */
@Data
public class XhsExpTopicLogOpus {

    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String desc;
    /**
     * uid
     */
    private String uid;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String images;
    /**
     * 图片列表
     */
    private String imagesList;
    /**
     * 话题列表
     */
    private String hashTags;
    /**
     * 作品id
     */
    private String opusId;
    /**
     * 作品类型
     */
    private String type;
    /**
     * token
     */
    private String token;
    /**
     * 收藏数
     */
    private Integer collectNum;
    /**
     * 评论数
     */
    private Integer commentNum;
    /**
     * 点赞数
     */
    private Integer likes;
    /**
     * 分享数
     */
    private Integer shareNum;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    /**
     * 最新编辑时间
     */
    private LocalDateTime editTime;
    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 视频宽
     */
    private Integer width;

    /**
     * 视频高
     */
    private Integer height;

    /**
     * 视频id
     */
    private String videoId;

    /**
     * 视频时长
     */
    private Integer duration;

    /**
     * 视频封面
     */
    private String cover;




    public static XhsExpTopicLogOpus castOf(JSONObject item) {
        if (Objects.isNull(item)) {
            return null;
        }
        XhsExpTopicLogOpus opus = new XhsExpTopicLogOpus();
        opus.setTitle(item.getString("title"));
        opus.setDesc(item.getString("desc"));
        opus.setUid(item.getString("uid"));
        opus.setNickname(item.getString("nickname"));
        opus.setImages(item.getString("images"));
        opus.setImagesList(item.getString("imagesList"));
        opus.setHashTags(item.getString("hashTags"));
        opus.setOpusId(item.getString("opusId"));
        opus.setType(item.getString("type"));
        opus.setToken(item.getString("token"));
        opus.setCollectNum(item.getInteger("collectNum"));
        opus.setCommentNum(item.getInteger("commentNum"));
        opus.setLikes(item.getInteger("likes"));
        opus.setShareNum(item.getInteger("shareNum"));
        opus.setPublishTime(DateTimeUtil.toDateTime(item.getString("publishTime")));
        opus.setEditTime(DateTimeUtil.toDateTime(item.getString("editTime")));
        opus.setVideoUrl(item.getString("videoUrl"));
        opus.setWidth(item.getInteger("width"));
        opus.setHeight(item.getInteger("height"));
        opus.setVideoId(item.getString("videoId"));
        opus.setDuration(item.getInteger("duration"));
        opus.setCover(item.getString("cover"));
        return opus;
    }

}
