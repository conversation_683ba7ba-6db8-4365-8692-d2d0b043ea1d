package cn.newrank.niop.data.biz.component.callback;

import cn.newrank.niop.data.biz.callback.service.CbDataService;
import cn.newrank.niop.data.biz.callback.service.CbHttpTaskService;
import cn.newrank.niop.data.common.ConfigProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/24 19:17
 */
@Component
public class WebhookCallbackFactory implements CallbackFactory {

    private final CbHttpTaskService cbHttpTaskService;
    private final CbDataService cbDataService;
    private final ApplicationEventPublisher eventPublisher;

    public WebhookCallbackFactory(CbHttpTaskService cbHttpTaskService,
                                  CbDataService cbDataService,
                                  ApplicationEventPublisher eventPublisher) {
        this.cbHttpTaskService = cbHttpTaskService;
        this.cbDataService = cbDataService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public Callback newCallback(ConfigProperties configProperties) {
        return new WebhhookCallback(configProperties, cbHttpTaskService, cbDataService, eventPublisher);
    }
}
