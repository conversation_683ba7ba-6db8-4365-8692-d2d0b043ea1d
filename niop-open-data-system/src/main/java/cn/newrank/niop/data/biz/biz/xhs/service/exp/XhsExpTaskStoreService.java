package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpAtspTaskMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpParentTaskMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspTask;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpParentTask;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/10 10:41:19
 */
@Service
public class XhsExpTaskStoreService {

    private final XhsExpParentTaskMapper xhsExpParentTaskMapper;
    private final XhsExpAtspTaskMapper xhsExpAtspTaskMapper;

    public XhsExpTaskStoreService(XhsExpParentTaskMapper xhsExpParentTaskMapper,
                                  XhsExpAtspTaskMapper xhsExpAtspTaskMapper) {
        this.xhsExpParentTaskMapper = xhsExpParentTaskMapper;
        this.xhsExpAtspTaskMapper = xhsExpAtspTaskMapper;
    }


    /**
     * 批量创建翻页任务并更新父任务
     *
     * @param newTasks      新翻页任务
     * @param parentTaskMap 父任务
     */
    @Transactional(rollbackFor = Exception.class)
    protected void storeAtspBatchAndUpdateParent(List<XhsExpAtspTask> newTasks,
                                                 String partition,
                                                 Map<String, XhsExpParentTask> parentTaskMap) {
        // 更新父任务状态
        for (XhsExpAtspTask task : newTasks) {
            String parentId = task.getParentId();
            xhsExpParentTaskMapper.updateParentTaskValue(parentTaskMap.get(parentId));
        }
        // 批量创建新翻页任务
        xhsExpAtspTaskMapper.storeBatch(newTasks, partition);
    }

    /**
     * 更新父任务状态和翻页任务状态
     *
     * @param parentTask
     * @param task
     */
    @Transactional(rollbackFor = Exception.class)
    protected void updateParentTaskAndAtspTask(XhsExpParentTask parentTask, XhsExpAtspTask task) {
        xhsExpAtspTaskMapper.updateTaskStatus(task, task.getTaskId());
        xhsExpParentTaskMapper.updateParentTaskStatus(parentTask);
    }

    /**
     * 批量创建父、翻页任务
     *
     * @param triggerTime    触发时间
     * @param minPublishTime 最小发布时间
     * @param list           待提交任务
     */
    @Transactional(rollbackFor = Exception.class)
    protected void storeTaskBatch(LocalDateTime triggerTime,
                                  LocalDateTime minPublishTime,
                                  List<XhsExpTopic> list) {
        final String partition = DateTimeUtil.dayOfPartition(triggerTime);

        // 父任务： 话题 + 触发时间 唯一
        List<XhsExpParentTask> parentTasks = getXhsExpParentTasks(triggerTime, minPublishTime, list);
        xhsExpParentTaskMapper.storeBatch(parentTasks, partition);

        // 翻页任务：父任务id + 页数 唯一
        List<XhsExpAtspTask> atspTasks = getXhsExpAtspTasks(triggerTime, parentTasks);
        xhsExpAtspTaskMapper.storeBatch(atspTasks, partition);
    }

    private static final Integer SAVE_DAYS = 7;

    public void dropTaskTable(String param) {
        XxlJobLogger.log("start drop task table param:{}", param);
        final LocalDateTime now = LocalDateTime.now();
        LocalDateTime triggerTime;
        // 删除指定日期表
        if (Strings.isNotBlank(param)) {
            triggerTime = LocalDateTime.parse(param, DatePattern.NORM_DATETIME_FORMATTER);
            String partition = DateTimeUtil.dayOfPartition(triggerTime);
            if (triggerTime.isBefore(now.minusDays(SAVE_DAYS))) {
                xhsExpAtspTaskMapper.dropTable(partition);
                XxlJobLogger.log("drop atsp table: {}", partition);
                xhsExpParentTaskMapper.dropTable(partition);
                XxlJobLogger.log("drop parent table: {}", partition);
            } else {
                XxlJobLogger.log("{}天之内 不予删除 partition: {}", SAVE_DAYS, partition);
            }
            return;
        }
        // 删除7天之后的所有表
        triggerTime = now.minusDays(SAVE_DAYS);
        String partition = DateTimeUtil.dayOfPartition(triggerTime);
        xhsExpAtspTaskMapper.dropTable(partition);
        XxlJobLogger.log("drop atsp table: {}  ok", partition);
        xhsExpParentTaskMapper.dropTable(partition);
        XxlJobLogger.log("drop parent table: {}  ok", partition);
        XxlJobLogger.log("删除完毕");
    }


    /**
     * 获取父任务
     *
     * @param triggerTime    触发时间
     * @param minPublishTime 最小发布时间
     * @param list           待提交任务
     * @return 父任务
     */
    private static @NotNull List<XhsExpParentTask> getXhsExpParentTasks(LocalDateTime triggerTime,
                                                                        LocalDateTime minPublishTime,
                                                                        List<XhsExpTopic> list) {
        return list.stream()
                .map(item -> XhsExpParentTask.initTask(item, triggerTime, minPublishTime))
                .toList();
    }

    /**
     * 获取翻页任务
     *
     * @param triggerTime 触发时间
     * @param parentTasks 父任务
     * @return 翻页任务
     */
    private static @NotNull List<XhsExpAtspTask> getXhsExpAtspTasks(LocalDateTime triggerTime,
                                                                    List<XhsExpParentTask> parentTasks) {
        return parentTasks
                .stream().map(item ->
                        XhsExpAtspTask.initTask(item.getParentId(),
                                item.getTopicId(),
                                triggerTime.toLocalDate(),
                                "1",
                                1,
                                item.getTopicWeight()))
                .toList();
    }
}
