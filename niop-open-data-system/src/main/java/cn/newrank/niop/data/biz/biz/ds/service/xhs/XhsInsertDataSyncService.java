package cn.newrank.niop.data.biz.biz.ds.service.xhs;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.XhsEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_XHS_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class XhsInsertDataSyncService implements SyncBizService<XhsEsMetaData> {


    private final DsConfigManager dsConfigManager;

    public XhsInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseXhsEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_xhs_user";
    }

    @Override
    public String getSortField() {
        return "userid";
    }

    @Override
    public String getRangField() {
        return "ana_time";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_XHS_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<XhsEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), XhsEsMetaData.class);
        });
    }

    @Override
    public List<XhsEsMetaData> convertData(List<XhsEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (XhsEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();
            for (Map.Entry<String, String> entry : MAIN_XHS_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }

            if (data.containsKey("gender")) {
                if (data.get("gender").equals(0)) {
                    mainMap.put("gender", "男");
                } else if (data.get("gender").equals(1)) {
                    mainMap.put("gender", "女");
                } else if (data.get("gender").equals(2)) {
                    mainMap.put("gender", "未知");
                }
            }

            mainMap.put("platform_type", PlatformType.XHS.getDbCode());

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.XHS.getDbCode() + "_" + mainMap.get("account_id"));
            }
            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), XhsEsMetaData.class);
    }


}
