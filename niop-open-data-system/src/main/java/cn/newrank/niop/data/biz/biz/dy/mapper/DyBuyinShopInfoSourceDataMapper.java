package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShopInfoSourceData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_buyin_shop_info_source_data】的数据库操作Mapper
 * @createDate 2024-10-29 17:35:51
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShopInfoSourceData
 */
@Mapper
public interface DyBuyinShopInfoSourceDataMapper {

    /**
     * 插入一条数据
     *
     * @param item
     */
    void insertOne(@Param("item") DyBuyinShopInfoSourceData item);

}




