package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyShopSkuMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopSku;
import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopSkuDTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2024/10/17
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DyShopSingleSkuService {


    private final DyShopSkuMapper shopSkuMapper;


    public void shopSkuDataV1(JSONObject item, String deviceName) {
        String partitionOffset = item.getString("kafka_partition") + "_" + item.getString("kafka_offset");

        JSONObject jsonData = item.getJSONObject("json_details").getJSONObject("yata_data");
        JSONObject entranceInfoJson = jsonData.getJSONObject("global").getJSONObject("entrance_info");
        String productId = entranceInfoJson.getString("product_id");
/*        //获取已售的数量
        Long saleNum = null;
        String saleNumStr = entranceInfoJson.getString("product_sell_info_content");
        if (StrUtil.isNotBlank(saleNumStr) && saleNumStr.startsWith("已售")) {
            saleNum = Long.valueOf(saleNumStr.substring(2));
        }*/
        JSONObject priceJson = entranceInfoJson.getJSONObject("price_info");
        String skuId = "";
        String showPrice = "";
        if (priceJson == null) {
            JSONArray afterRenderArray = jsonData.getJSONObject("data").getJSONObject("after_render_1").getJSONObject("events").getJSONArray("AfterRender");
            if (CollUtil.isNotEmpty(afterRenderArray)) {
                JSONObject afterRenderJson = (JSONObject)afterRenderArray.get(0);
                JSONObject skuItemJson = afterRenderJson.getJSONObject("fields").getJSONObject("sku_item");
                if (skuItemJson == null) {
                    log.warn("订单数据-多个SKU，位点信息:{}",partitionOffset);
                    return;
                }
                skuId = skuItemJson.getString("sku_id");
                showPrice = skuItemJson.getString("price");
            }
        } else {
            skuId = priceJson.getString("show_sku_id");
            showPrice = priceJson.getString("show_price");
        }
        if (StrUtil.isBlank(skuId)) {
            log.error("订单数据-单个SKU，没有拿到skuId，位点信息:{}",partitionOffset);
            return;
        }
        //获取规格信息
        JSONArray specsArray = jsonData.getJSONObject("data").getJSONObject("specs_1").getJSONObject("fields").getJSONArray("specs");
        if (CollUtil.isNotEmpty(specsArray)) {
            JSONObject specsJson = specsArray.getJSONObject(0);
            JSONArray specList = specsJson.getJSONArray("spec_item_list");
            if (CollUtil.isEmpty(specList) || specList.size() > 1) {
                //batchSku(specList,skuId,partitionOffset,deviceName,productId);
                return;
            }
            String specsName = specsJson.getString("name");
            JSONObject speJson = (JSONObject) specList.get(0);
            String specItemId = speJson.getString("spec_item_id");
            String specItemName = speJson.getString("name");
            String pic = speJson.getString("big_pic");
            Map<String, Long> skuExtraMap = Maps.newHashMap();
            skuExtraMap.put("sku_id", Long.valueOf(skuId));
            List<String> specsNameList = Lists.newArrayList();
            specsNameList.add(specsName);
            List<JSONObject> specsItemsList = Lists.newArrayList();
            JSONObject itemJson = new JSONObject();
            itemJson.put("id", specItemId);
            itemJson.put("name", specItemName);
            specsItemsList.add(itemJson);
            DyShopSkuDTO dto = new DyShopSkuDTO()
                    .setPartitionOffset(partitionOffset)
                    .setDeviceName(deviceName)
                    .setProductId(productId)
                    .setSkuLongId(skuId)
                    .setSkuPrice(showPrice)
                    .setSkuStockNum(0L)
                    .setSkuId(skuId)
                    .setSkuExtra(JSON.toJSONString(skuExtraMap))
                    .setSpecsName(specsNameList)
                    .setSpecsItems(specsItemsList)
                    .setPic(StrUtil.isBlank(pic) ? "" : JSONObject.toJSONString(pic));
            shopSkuMapper.oneSave(DyShopSku.createItem(dto));
        }

    }

    private void batchSku(JSONArray specList, String skuId,String partitionOffset,String deviceName,String productId) {
        List<String> specsName = new ArrayList<>();
        List<JSONObject> specsItems = new ArrayList<>();
        Map<String, Long> skuExtraMap = Maps.newHashMap();
        skuExtraMap.put("sku_id", Long.valueOf(skuId));
        List<DyShopSkuDTO> skuList = specList.stream().map(json -> {
                    JSONObject speJson = (JSONObject) json;
                    specsName.add(speJson.getString("name"));
                    String specItemId = speJson.getString("spec_item_id");
                    String specItemName = speJson.getString("name");
                    String pic = speJson.getString("big_pic");
                    String showPrice = speJson.getString("price").replace("¥","") + "00";
                    JSONObject itemJson = new JSONObject();
                    itemJson.put("id", specItemId);
                    itemJson.put("name", specItemName);
                    specsItems.add(itemJson);
                    return new DyShopSkuDTO()
                            .setPartitionOffset(partitionOffset)
                            .setDeviceName(deviceName)
                            .setProductId(productId)
                            .setSkuLongId(skuId)
                            .setSkuPrice(showPrice)
                            .setSkuStockNum(0L)
                            .setSkuId(skuId)
                            .setSkuExtra(JSON.toJSONString(skuExtraMap))
                            .setSpecsName(specsName)
                            .setSpecsItems(specsItems)
                            .setPic(StrUtil.isBlank(pic) ? "" : JSONObject.toJSONString(pic));
                }
        ).toList();
        List<DyShopSku> resultList = skuList.stream().map(DyShopSku::createItem).toList();
        if (CollUtil.isNotEmpty(resultList)) {
            shopSkuMapper.batchSave(resultList);
        }

    }


    public void shopSkuDataV2(JSONObject item, String deviceName) {
        String partitionOffset = item.getString("kafka_partition") + "_" + item.getString("kafka_offset");
        String productId = item.getString("product_id");
        JSONObject dataJson = item.getJSONObject("json_details").getJSONObject("yata_data");
        //获取skuId
        JSONObject trackerJson = dataJson.getJSONObject("global").getJSONObject("tracker_common_params");
        String skuId = trackerJson.getString("sku_id");
        String showPrice = trackerJson.getString("product_card_price");

        Map<String, Long> skuExtraMap = Maps.newHashMap();
        skuExtraMap.put("sku_id", Long.valueOf(skuId));

        JSONObject fieldsJson = dataJson.getJSONObject("data").getJSONObject("product_card_orderLineId_1").getJSONObject("fields");
        String pic = fieldsJson.getString("main_pic");


        List<String> specsNameList = Lists.newArrayList();
        specsNameList.add(fieldsJson.getString("spec_info_desc"));

        List<JSONObject> specsItemsList = Lists.newArrayList();
        JSONObject itemJson = new JSONObject();
        itemJson.put("id", fieldsJson.getString("sku_id"));
        itemJson.put("name", fieldsJson.getString("product_name"));
        specsItemsList.add(itemJson);

        DyShopSkuDTO dto = new DyShopSkuDTO()
                .setPartitionOffset(partitionOffset)
                .setDeviceName(deviceName)
                .setProductId(productId)
                .setSkuLongId(skuId)
                .setSkuPrice(showPrice)
                .setSkuStockNum(0L)
                .setSkuId(skuId)
                .setSkuExtra(JSON.toJSONString(skuExtraMap))
                .setSpecsName(specsNameList)
                .setSpecsItems(specsItemsList)
                .setPic(StrUtil.isBlank(pic) ? "" : JSONObject.toJSONString(pic));
        shopSkuMapper.oneSave(DyShopSku.createItem(dto));
    }
}
