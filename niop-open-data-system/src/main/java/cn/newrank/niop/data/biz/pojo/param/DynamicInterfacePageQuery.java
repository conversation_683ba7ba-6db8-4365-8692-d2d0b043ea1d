package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.web.model.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 15:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DynamicInterfacePageQuery extends PageQuery {

    String keyword;

    /**
     * 负责人名称
     */
    private String maintainerName;

    /**
     * 数据源ID
     */
    private String dcId;


}
