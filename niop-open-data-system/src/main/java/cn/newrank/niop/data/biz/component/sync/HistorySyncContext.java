package cn.newrank.niop.data.biz.component.sync;

import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 13:38
 */
@Component
@Log4j2
public class HistorySyncContext implements ApplicationListener<ContextClosedEvent> {


    final Map<String, HistorySynchronizer> historySynchronizerCache;

    public HistorySyncContext(List<HistorySynchronizer> historySynchronizers) {
        this.historySynchronizerCache = historySynchronizers.stream()
                .collect(java.util.stream.Collectors.toMap(HistorySynchronizer::getName, item -> item));
    }

    public HistorySynchronizer getSynchronizer(String name) {
        final HistorySynchronizer historySynchronizer = historySynchronizerCache.get(name);
        if (historySynchronizer == null) {
            throw createParamError("未找到对应同步任务:{}", name);
        }

        return historySynchronizer;
    }

    public List<String> getNames() {
        return historySynchronizerCache.keySet().stream().sorted().toList();
    }

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        for (HistorySynchronizer synchronizer : historySynchronizerCache.values()) {
            try {
                synchronizer.close();
            } catch (Exception e) {
                log.error("HistorySynchronizer close error", e);
            }
        }
    }
}
