package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.pojo.param.StorageHistoryPageQuery;
import cn.newrank.niop.data.biz.pojo.param.StorageQuery;
import cn.newrank.niop.data.biz.pojo.vo.StorageEntityVo;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.common.limiter.Limitable;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/15 9:46
 */
@Validated
@RestController
@RequestMapping("storage")
public class StorageController {

    private final StorageService storageService;

    public StorageController(StorageService storageService) {
        this.storageService = storageService;
    }

    /**
     * 直播结果数据
     *
     * @param storageQuery 数据存储查询参数
     * @return 数据
     */
    @GetMapping("live-result/list")
    @Limitable(refreshPermits = 20)
    public List<StorageEntityVo> storageEntities(@Valid StorageQuery storageQuery) {
        return StorageEntityVo.of(storageQuery.getIdentifiers(), storageService.listLastedEntities(storageQuery));
    }


    /**
     * 历史结果数据
     *
     * @param pageQuery 数据存储查询参数
     * @return 数据
     */
    @GetMapping("histories")
    @Limitable(refreshPermits = 20)
    public StorageHistory getHistories(@Valid StorageHistoryPageQuery pageQuery) {
        return storageService.getHistories(pageQuery);
    }
}
