package cn.newrank.niop.data.biz.biz.tiktok.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 15:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TiktokAccount extends SampleVersionEntity {
    /**
     * account
     */
    private String account;

    /**
     * uid
     */
    private String uid;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * secUid
     */
    private String secUid;

    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 关注数
     */
    private Long followNum;

    /**
     * 作品数
     */
    private Long opusNum;

    /**
     * 点赞数
     */
    private Long likeNum;
    /**
     * 地区
     */
    private String region;

    /**
     * 转存头像
     */
    private String avatarUrl;

}