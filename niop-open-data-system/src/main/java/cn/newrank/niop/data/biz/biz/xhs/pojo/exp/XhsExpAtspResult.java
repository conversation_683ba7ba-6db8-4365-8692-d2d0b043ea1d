package cn.newrank.niop.data.biz.biz.xhs.pojo.exp;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.service.exp.XhsExpTaskResultServiceImpl;
import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.sdk.common.producer.TaskResult;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.bytebuddy.asm.Advice;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 能力 CWWEVCGP
 *
 * <AUTHOR>
 * @since 2025/3/31 14:24:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XhsExpAtspResult extends StorageEntity {

    /**
     * 能力ID
     */
    String abilityId;

    /**
     * 应用ID
     */
    String appId;

    /**
     * 任务ID
     */
    String taskId;

    /**
     * 业务码
     */
    Integer bizCode;

    /**
     * 业务信息
     */
    String bizMsg;

    /**
     * 数据类型
     */
    Integer dataType;

    /**
     * 完成时间
     */
    LocalDateTime finishTime;

    /**
     * 列表
     */
    List<XhsExpTopicOpus> list;

    /**
     * 是否还有更多
     */
    Boolean hasMore;

    /**
     * 游标
     */
    String cursor;

    @Override
    public String identifier() {
        return taskId;
    }


    public static XhsExpAtspResult buildByTaskResult(TaskResult result){
        XhsExpAtspResult atspResult = new XhsExpAtspResult();
        atspResult.setTaskId(result.getTaskId());
        atspResult.setBizCode(result.getBizCode());
        atspResult.setBizMsg(result.getBizMsg());
        atspResult.setFinishTime(LocalDateTime.parse(result.getFinishTime(), DatePattern.NORM_DATETIME_FORMATTER));

        JSONObject object = JSONObject.from(result.getData());
        if(Objects.nonNull(object)){
            atspResult.setHasMore(object.getBoolean("hasMore"));
            atspResult.setCursor(object.getString("cursor"));
            atspResult.setList(XhsExpTaskResultServiceImpl.getXhsExpTopicOpusList(object));
        }
        return atspResult;
    }
}
