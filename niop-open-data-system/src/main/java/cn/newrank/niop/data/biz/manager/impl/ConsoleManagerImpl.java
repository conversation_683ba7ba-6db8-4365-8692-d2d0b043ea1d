package cn.newrank.niop.data.biz.manager.impl;

import cn.newrank.niop.common.ServiceNames;
import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.console.biz.project.service.IAppDubboService;
import cn.newrank.niop.data.biz.manager.ConsoleManager;
import cn.newrank.niop.util.U;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/26 10:46
 */
@Service
public class ConsoleManagerImpl implements ConsoleManager {
    /**
     * 应用服务
     */
    @DubboReference(providedBy = ServiceNames.DUBBO_OPEN_CONSOLE)
    private IAppDubboService appDubboService;


    @Override
    public App getApp(String appId) {
        final List<App> apps = appDubboService.listApp(List.of(appId));
        if (apps == null || apps.isEmpty()) {
            return null;
        }

        return apps.get(0);
    }


    @Override
    public boolean hasApp(String appId) {
        return getApp(appId) != null;
    }

    @Override
    public Map<String, App> mapApp(List<String> appIds) {
        return U.toMap(appDubboService.listApp(appIds), App::getAppId, Function.identity());
    }

    @Override
    public List<App> listApp(List<String> appIds) {
        if (appIds == null || appIds.isEmpty()) {
            return new ArrayList<>();
        }
        return appDubboService.listApp(appIds);
    }
}
