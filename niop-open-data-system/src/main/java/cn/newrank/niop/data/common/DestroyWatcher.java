package cn.newrank.niop.data.common;

import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 优雅停机的监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/23 13:55
 */
@Log4j2
@Component
public class DestroyWatcher implements ApplicationContextAware, ApplicationListener<ContextClosedEvent> {

    ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        log.info("destroy watcher start...");
        final Collection<Destructible> callables = applicationContext.getBeansOfType(Destructible.class)
                .values();

        List<Callable> rs = new ArrayList<>();
        for (Destructible destructible : callables) {
            try {
                rs.add(destructible.destroy());
            } catch (Exception e) {
                log.warn("destroy error", e);
            }
        }

        for (int i = 0; i < 15; i++) {
            rs = rs.stream()
                    .dropWhile(Callable::isDestroyed)
                    .toList();

            if (rs.isEmpty()) {
                break;
            }

            ThreadUtil.sleep(1000);
        }

        log.info("destroy watcher end");
    }

    public interface Destructible {

        /**
         * 销毁方法
         *
         * @return 销毁结果回调
         */
        Callable destroy();
    }

    public interface Callable {

        /**
         * 是否销毁
         *
         * @return true: 已销毁
         */
        boolean isDestroyed();
    }

}
