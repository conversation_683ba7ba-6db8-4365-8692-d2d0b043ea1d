package cn.newrank.niop.data.biz.component.sls;

import cn.newrank.niop.data.config.property.AliyunProperties;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.web.model.PageQuery;
import cn.newrank.niop.web.model.PageView;
import cn.newrank.nrcore.exception.BizException;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.response.GetHistogramsResponse;
import com.aliyun.openservices.log.response.GetLogsResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/19 10:39
 */
@Log4j2
@Component
public class SlsClient {

    private final Client client;
    private final AliyunProperties aliyunProperties;

    public SlsClient(AliyunProperties aliyunProperties) {
        this.aliyunProperties = aliyunProperties;
        this.client = new Client(AliyunProperties.getSlsHost(), aliyunProperties.getAk(), aliyunProperties.getSk());
    }


    public List<QueriedLog> getLogs(LocalDateTime start, LocalDateTime end, String query, int size, int page) {
        try {
            final GetLogsResponse response = client.GetLogs(AliyunProperties.SLS_PROJECT,
                    aliyunProperties.getLogstore()
                    , DateTimeUtil.toSeconds(start), DateTimeUtil.toSeconds(end)
                    , "", query
                    , size, (Math.max(page, 1) - 1) * size, true);

            final List<QueriedLog> logs = response.getLogs();
            if (logs == null) {
                return Collections.emptyList();
            }

            return logs;
        } catch (Exception e) {
            log.warn("SLS查询异常 {}", e.getMessage());
            throw createParamError("SLS查询异常");
        }
    }

    public PageView<QueriedLog> pageLogs(LocalDateTime start, LocalDateTime end, String query, PageQuery pageQuery) {
        try {
            final int pageSize = pageQuery.getPageSize();
            final int pageNum = pageQuery.getPageNum();
            if (pageSize > 100) {
                throw createParamError("单次查询最多100条");
            }

            final GetHistogramsResponse histogramsResponse = client.GetHistograms(AliyunProperties.SLS_PROJECT, aliyunProperties.getLogstore(),
                    DateTimeUtil.toSeconds(start),
                    DateTimeUtil.toSeconds(end),
                    "", query);
            final long total = histogramsResponse.GetTotalCount();
            if (total == 0) {
                return PageView.emptyPage();
            }

            final GetLogsResponse response = client.GetLogs(AliyunProperties.SLS_PROJECT,
                    aliyunProperties.getLogstore()
                    , DateTimeUtil.toSeconds(start), DateTimeUtil.toSeconds(end)
                    , "", query
                    , pageSize,  (Math.max(pageNum, 1) - 1) * pageSize, false);


            return new PageView<>(response.getLogs(), total / pageSize + 1, total);
        } catch (Exception e) {
            if (e instanceof BizException bizException) {
                throw bizException;
            }
            log.warn("SLS查询异常 {}", e.getMessage());
            throw createParamError("SLS查询异常");
        }
    }
}
