package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import java.io.File;
import java.io.IOException;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public interface DataExportResultFileHandler {

    /**
     * 获取文件交付类型
     *
     * @return 交付类型
     */
    DataExportDeliveryType getFileDeliveryType();

    /**
     * 根据导数任务id获取结果处理接口
     *
     * @param exportTaskId 导数任务id
     * @return 结果处理接口
     */
    DataExportResultHandler getResultHandler(String exportTaskId);

    /**
     * 生成和上传结果文件列表
     *
     * @param exportTaskId 导数任务id
     * @param uploadResultHandler 上传结果处理
     * @throws IOException 文件写入异常
     * @return  是否成功
     */
    Boolean generateAndUploadResultFiles(String exportTaskId, Consumer<FileUploadResult> uploadResultHandler) throws IOException;

    /**
     * 处理文件头
     *
     * @param header 文件头
     * @return 更新后的文件头集合
     */
    default Set<String> handleFileHeader(Set<String> header) {
        return header;
    }

    /**
     * 处理结果数据
     *
     * @param results 结果数据
     */
    default void handleResults(ExportResult results) {

    }

    /**
     * 创建一个结果文件
     *
     * @param exportTaskId 导数任务id
     * @param fileNumber 文件编号
     * @return 新的结果文件
     * @throws IOException IO异常
     */
    File createResultFile(String exportTaskId, Integer fileNumber) throws IOException;

    /**
     * 获取文件名称
     *
     * @param exportTaskId 导数任务id
     * @param fileNumber 文件编号
     * @return 文件名称
     */
    String getFileName(String exportTaskId, Integer fileNumber);

    /**
     * 上传结果文件
     *
     * @param exportTaskId 导数任务id
     * @param file 文件
     * @return 上传结果
     */
    FileUploadResult uploadResultFile(String exportTaskId, File file);

}
