package cn.newrank.niop.data.biz.biz.dy.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @createTime 2024/10/23
 * @description
 */
@Accessors(chain = true)
@Data
public class DyShopVerifyDTO {

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品价格
     */
    private String priceInfo;

    /**
     * 是否在售
     */
    private Boolean onSale;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     *位点
     */
    private String partitionOffset;

    /**
     * 售出描述
     */
    private String saleInfo;
}
