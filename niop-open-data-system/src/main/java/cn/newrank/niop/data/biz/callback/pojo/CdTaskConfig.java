package cn.newrank.niop.data.biz.callback.pojo;

import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/16 16:38
 */
@Data
public class CdTaskConfig {

    private Map<String /*cbId*/, List<String>/*taskId*/> cbWithParsed;
    private Map<String /*cbId*/, List<String>/*taskId*/> cbWithRaw;
    private Map<String /*cbId*/, List<String>/*taskId*/> cbOldWithRaw;
    private Map<String /*cbId*/, List<String>/*taskId*/> cbOldWithParsed;
    private Map<String /*cbId*/, Boolean/*enableParams*/> cdOldEnableParamWithRaw;
    private Map<String /*cbId*/, Boolean/*enableParams*/> cdOldEnableParamWithParsed;

    public CdTaskConfig() {
        this.cbWithParsed = new HashMap<>();
        this.cbWithRaw = new HashMap<>();
        this.cbOldWithRaw = new HashMap<>();
        this.cbOldWithParsed = new HashMap<>();
        this.cdOldEnableParamWithRaw = new HashMap<>();
        this.cdOldEnableParamWithParsed = new HashMap<>();
    }

    public static CdTaskConfig of() {
        return new CdTaskConfig();
    }

    public void addCbWithRaw(String cbId, String taskId) {
        cbWithRaw.computeIfAbsent(cbId, key -> new ArrayList<>()).add(taskId);
    }

    public void addCbWithParsed(String cbId, String taskId) {
        cbWithParsed.computeIfAbsent(cbId, key -> new ArrayList<>()).add(taskId);
    }

    public void addCbOldWithRaw(String cbId, String taskId) {
        cbOldWithRaw.computeIfAbsent(cbId, key -> new ArrayList<>()).add(taskId);
    }

    public void addCbOldWithParsed(String cbId, String taskId) {
        cbOldWithParsed.computeIfAbsent(cbId, key -> new ArrayList<>()).add(taskId);
    }

    public void setCdOldEnableParamWithRaw(String cbId, Boolean enableParams) {
        cdOldEnableParamWithRaw.put(cbId, enableParams);
    }

    public void setCdOldEnableParamWithParsed(String cbId, Boolean enableParams) {
        cdOldEnableParamWithParsed.put(cbId, enableParams);
    }


    public List<String> getAllTaskIds() {
        Set<String> allTaskIds = new HashSet<>();

        for (List<String> taskIds : cbWithRaw.values()) {
            allTaskIds.addAll(taskIds);
        }

        for (List<String> taskIds : cbWithParsed.values()) {
            allTaskIds.addAll(taskIds);
        }

        for (List<String> taskIds : cbOldWithRaw.values()) {
            allTaskIds.addAll(taskIds);
        }

        for (List<String> taskIds : cbOldWithParsed.values()) {
            allTaskIds.addAll(taskIds);
        }

        return new ArrayList<>(allTaskIds);
    }

    public List<String> getEnabledTaskIds() {
        Set<String> enabledTaskIds = new HashSet<>();

        for (Map.Entry<String, List<String>> entry : cbOldWithRaw.entrySet()) {
            if (cdOldEnableParamWithRaw.getOrDefault(entry.getKey(), false)) {
                enabledTaskIds.addAll(entry.getValue());
            }
        }

        for (Map.Entry<String, List<String>> entry : cbOldWithParsed.entrySet()) {
            if (cdOldEnableParamWithParsed.getOrDefault(entry.getKey(), false)) {
                enabledTaskIds.addAll(entry.getValue());
            }
        }

        return new ArrayList<>(enabledTaskIds);
    }
}
