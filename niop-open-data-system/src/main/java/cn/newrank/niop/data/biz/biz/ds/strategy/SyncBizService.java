package cn.newrank.niop.data.biz.biz.ds.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.util.Collections;
import java.util.List;

/**
 * 业务的同步服务， {@link SyncBiz} 管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SyncBizService<T extends SyncEntity> {
    /**
     * 是否可以开始同步
     * 规则：当天下午4点且同步时间小于当天下午4点，则执行同步
     *
     * @param lastSyncTime 上一次同步时间
     * @return boolean true 可以开始同步，false 不可以
     */
    default boolean isStart(String lastSyncTime) {
        DateTime now = DateUtil.date();
        DateTime lastSyncDateTime = DateUtil.parse(lastSyncTime, DatePattern.NORM_DATETIME_PATTERN);
        DateTime fourPmToday = DateUtil.beginOfDay(now).offset(DateField.HOUR_OF_DAY, 16);
        return now.isAfter(fourPmToday) && lastSyncDateTime.isBefore(fourPmToday);
    }

    /**
     * 获取索引名称
     */
    default String getIndexName() {
        throw new IllegalArgumentException("未配置es索引名称");
    }

    /**
     * 获取es配置信息
     *
     * @return ConfigProperties
     */
    default ConfigProperties getConfig() {
        throw new IllegalArgumentException("未配置es连接信息");
    }

    /**
     * 获取范围字段
     *
     * @return {@link String }
     */
    default String getRangField() {
        throw new IllegalArgumentException("未指定范围字段");
    }

    /**
     * 获取排序字段
     *
     * @return {@link String }
     */
    default String getSortField() {
        throw new IllegalArgumentException("未指定排序字段");
    }

    /**
     * 获取源字段
     *
     * @return {@link List }<{@link String }>
     */
    default List<String> getSourceFields() {
        throw new IllegalArgumentException("未指定数据源字段");
    }

    /**
     * 获取时间排序字段
     */
    default String getRangIndex() {
        throw new IllegalArgumentException("未指定排序字段");
    }

    /**
     * 唯一性建
     */
    default String getUniqueIndex() {
        throw new IllegalArgumentException("未指定唯一字段");
    }

    /**
     * 类型转换
     *
     * @param item 原始数据
     * @return 目标数据
     */
    List<T> castOf(JSONObject item);

    /**
     * 数据转换
     *
     * @param dataList 数据列表
     * @return {@link List }<{@link DataCenterEsMetaData }>
     */
    default List<T> convertData(List<T> dataList) {
        return dataList;
    }


    default <Target> List<Target> parseData(JSONObject item, Class<Target> objectClass) {
        if (null == item) {
            return Collections.emptyList();
        }

        final List<T> dataList = convertData(castOf(item));
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return EsCodec.deserializeToList(JSON.toJSONString(dataList), objectClass);
    }


}
