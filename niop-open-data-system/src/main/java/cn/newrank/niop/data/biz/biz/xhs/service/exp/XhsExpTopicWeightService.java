package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpConfigMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpHoloOpusMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpParentTaskMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTopicMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpConfig;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CWWEVCGP
 *
 * <AUTHOR>
 * @since 2025/3/28 11:46:10
 */
@Slf4j
@Service
public class XhsExpTopicWeightService {
    private final XhsExpTopicMapper xhsExpTopicMapper;
    private final XhsExpConfigMapper xhsExpConfigMapper;
    private final XhsExpParentTaskMapper xhsExpParentTaskMapper;
    private final RedissonClient redissonClient;
    private final XhsExpHoloOpusMapper xhsExpHoloOpusMapper;

    public XhsExpTopicWeightService(XhsExpTopicMapper xhsExpTopicMapper,
                                    XhsExpConfigMapper xhsExpConfigMapper,
                                    XhsExpParentTaskMapper xhsExpParentTaskMapper,
                                    RedissonClient redissonClient, XhsExpHoloOpusMapper xhsExpHoloOpusMapper) {
        this.xhsExpTopicMapper = xhsExpTopicMapper;
        this.xhsExpConfigMapper = xhsExpConfigMapper;
        this.xhsExpParentTaskMapper = xhsExpParentTaskMapper;
        this.redissonClient = redissonClient;
        this.xhsExpHoloOpusMapper = xhsExpHoloOpusMapper;
    }

    /**
     * 运行调度器定时任务
     */
    public void runSchedule(String param) {
        XxlJobLogger.log("手动设置下次更新权重时间 参数格式：2025-04-30 09:00:00");
        LocalDateTime now = LocalDateTime.now();
        RBucket<String> bucket = redissonClient.getBucket(getUpdateXhsExpTopicWeightKey());
        // 缓存为空初始化为次日9点
        if (Strings.isBlank(bucket.get())) {
            bucket.set(getNextUpdateTime(now));
        }
        // 手动设置下次更新时间
        if (Strings.isNotBlank(param)) {
            setUpdateWeightTime(param, bucket);
            return;
        }
        LocalDateTime execTime = DateTimeUtil.toDateTime(bucket.get());
        // 判断是否到达执行时间
        if (now.isBefore(execTime)) {
            XxlJobLogger.log("未到达执行时间: {} 小于下次更新权重时间: {}", now.toString(), execTime.toString());
            return;
        }
        try {
            XxlJobLogger.log("开始更新话题权重, 当前时间: {}", now.toString());
            updateTopicWeight(execTime.toLocalDate());
            bucket.set(getNextUpdateTime(now));
        } catch (Exception e) {
            XxlJobLogger.log("更新话题权重失败, error: {}", e.getMessage());
            log.error("更新话题权重异常, error: {}", e.getMessage());
        }
    }

    /**
     * 设置更新权重时间
     *
     * @param param  时间字符串
     * @param bucket redisson bucket
     */
    private static void setUpdateWeightTime(String param, RBucket<String> bucket) {
        try {
            //有参 手动设置下次更新权重时间 yyyy-MM-dd HH:mm:ss
            bucket.set(DateTimeUtil.toDateTime(param).format(DateTimeUtil.NORMAL_DT_FORMATTER));
            XxlJobLogger.log("设置更新时间: {} 成功", param);
        } catch (Exception e) {
            XxlJobLogger.log("设置更新时间: {} 失败, 请检查时间格式 yyyy-MM-dd HH:mm:ss", param);
        }
    }

    /**
     * 获取下次更新权重时间
     *
     * @param now 当前时间
     * @return 下次更新权重时间
     */
    private static @NotNull String getNextUpdateTime(LocalDateTime now) {
        return now.toLocalDate()
                .plusDays(1).atStartOfDay().plusHours(9)
                .format(DateTimeUtil.NORMAL_DT_FORMATTER);
    }

    /**
     * 更新话题权重,根据前一天任务结果
     */
    private void updateTopicWeight(LocalDate date) {
        final LocalDate yesterday = date.minusDays(1);
        Map<Integer, XhsExpConfig> configMap = getConfigMap();
        if (CollectionUtil.isEmpty(configMap)) {
            return;
        }
        configMap.keySet().forEach(weight -> {
            JSONObject object = JSON.parseObject(configMap.get(weight).getOpusNumRange());
            final Integer maxOpusNum = object.getInteger("max");
            final Integer minOpusNum = object.getInteger("min");
            // 查询当前权重的话题，并更新权重
            searchCurrentWeightAndUpdate(weight, yesterday, maxOpusNum, minOpusNum, configMap);
        });
        XxlJobLogger.log("更新完毕");
    }

    /**
     * 根据当前权重查询话题，并更新话题权重
     * @param weight 当前权重
     * @param date 日期
     * @param maxOpusNum 当前最大opusNum
     * @param minOpusNum 当前最小opusNum
     * @param configMap 配置映射
     */
    private void searchCurrentWeightAndUpdate(Integer weight,
                                              LocalDate date,
                                              Integer maxOpusNum,
                                              Integer minOpusNum,
                                              Map<Integer, XhsExpConfig> configMap) {
        Long cursor = 0L;
        while (true) {
            List<XhsExpTopic> topics = xhsExpTopicMapper.listByWeight(weight, cursor, 1000);
            if (CollectionUtil.isEmpty(topics)) {
                return;
            }
            topics.forEach(topic -> opusNumChooseWeight(topic, date, maxOpusNum, minOpusNum, configMap));
            cursor = topics.get(topics.size() - 1).getId();
        }
    }


    /**
     * 根据opusNum选择权重
     * @param topic 话题
     * @param date 日期
     * @param maxOpusNum 当前最大opusNum
     * @param minOpusNum 当前最小opusNum
     * @param configMap 配置映射
     */
    private void opusNumChooseWeight(XhsExpTopic topic,
                                     LocalDate date,
                                     Integer maxOpusNum,
                                     Integer minOpusNum,
                                     Map<Integer, XhsExpConfig> configMap) {
        try{
            String parentId = xhsExpParentTaskMapper.getParentIdByTopicId(date,topic.getTopicId());
            Integer opusNum = xhsExpHoloOpusMapper.getOpusNumByParentTaskId(parentId);
            // 判断作品数与当前权重是否匹配
            if (opusNum >= maxOpusNum || opusNum < minOpusNum) {
                //不匹配重选权重并更新
                reChooseNewWeight(topic, configMap, opusNum);
            }
        }catch (Exception e){
            log.error("更新话题权重异常, error: {}", e.getMessage());
        }
    }

    /**
     * 重选权重并更新
     * @param topic topic
     * @param configMap configMap
     * @param opusNum opusNum
     */
    private void reChooseNewWeight(XhsExpTopic topic,
                                   Map<Integer, XhsExpConfig> configMap,
                                   Integer opusNum) {
        configMap.values().forEach(config -> {
            JSONObject range = JSON.parseObject(config.getOpusNumRange());
            Integer max = range.getInteger("max");
            Integer min = range.getInteger("min");
            max = Objects.isNull(max) ? Integer.MAX_VALUE : max;
            min = Objects.isNull(min) ? Integer.MIN_VALUE : min;
            if (opusNum >= min && opusNum < max) {
                xhsExpTopicMapper.updateTopicWeight(topic.getTopicId(), config.getTopicWeight());
            }
        });
    }


    /**
     * 获取更新权重时间key
     *
     * @return key
     */
    public static String getUpdateXhsExpTopicWeightKey() {
        return "xhs:exp:topic:updateWeightTime";
    }


    /**
     * 获取配置映射
     * @return 配置映射
     */
    private @NotNull Map<Integer, XhsExpConfig> getConfigMap() {
        return xhsExpConfigMapper.listConfigs().stream().collect(Collectors.toMap(
                XhsExpConfig::getTopicWeight,
                Function.identity()
        ));
    }
}
