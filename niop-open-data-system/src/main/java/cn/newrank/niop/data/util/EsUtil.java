package cn.newrank.niop.data.util;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.lang.NonNull;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * es 工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class EsUtil {

    public static final String MUST = "must";
    public static final String SHOULD = "should";
    public static final String MUST_NOT = "must_not";
    public static final String HITS = "hits";
    public static final String QUERY = "query";
    public static final String BOOL = "bool";
    public static final String FILTER = "filter";
    public static final String TERM = "term";
    public static final String TERMS = "terms";
    public static final String FROM = "from";
    public static final String SIZE = "size";
    public static final String SOURCE = "_source";
    public static final String SCROLL_ID = "_scroll_id";
    public static final String SORT = "sort";


    private EsUtil() {
    }

    /**
     * 获取查询结果命中数据
     *
     * @param queryResultStr 查询结果字符串
     * @return JSONArray
     */
    public static JSONArray listResultHits(String queryResultStr) {
        JSONObject resultObj = JSON.parseObject(queryResultStr);
        return resultObj.getJSONObject(HITS).getJSONArray(HITS);
    }

    /**
     * 将hits数据装换实体对象
     *
     * @param <T>    类型
     * @param result 查询结果字符串
     * @param mapper 解析映射
     * @return 集合列表
     */
    public static <T> List<T> listHitsToEntity(String result, @NonNull Function<JSONObject, T> mapper) {
        JSONObject resultObj = JSON.parseObject(result);
        final JSONArray hits = resultObj.getJSONObject(HITS).getJSONArray(HITS);
        if (CollUtil.isEmpty(hits)) {
            return Collections.emptyList();
        }
        return hits.stream().map(hit -> mapper.apply((JSONObject) hit)).collect(Collectors.toList());
    }

    /**
     * 将hits数据装换实体对象
     *
     * @param resultObj 结果obj
     * @param mapper    映射器
     * @return {@link List }<{@link T }>
     */
    public static <T> List<T> listHitsToEntity(JSONObject resultObj, @NonNull Function<JSONObject, T> mapper) {
        final JSONArray hits = resultObj.getJSONObject(HITS).getJSONArray(HITS);
        if (CollUtil.isEmpty(hits)) {
            return Collections.emptyList();
        }
        return hits.stream().map(hit -> mapper.apply((JSONObject) hit)).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
