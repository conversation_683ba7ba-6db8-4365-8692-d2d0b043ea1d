package cn.newrank.niop.data.biz.export.factory;

import cn.newrank.niop.data.biz.export.handler.DataExportResultFileHandler;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DataExportResultFileHandlerFactory implements ApplicationContextAware {

    private final Map<DataExportDeliveryType, DataExportResultFileHandler> resultFileHandlerMap = new EnumMap<>(DataExportDeliveryType.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        final Map<String, DataExportResultFileHandler> handlerMap = applicationContext.getBeansOfType(DataExportResultFileHandler.class);

        for (Map.Entry<String, DataExportResultFileHandler> handler : handlerMap.entrySet()) {
            final DataExportResultFileHandler value = handler.getValue();
            this.resultFileHandlerMap.put(value.getFileDeliveryType(), value);
        }
    }

    public DataExportResultFileHandler getResultFileHandler(DataExportDeliveryType deliveryType) {
        return Optional.ofNullable(this.resultFileHandlerMap.get(deliveryType))
            .orElseThrow(() -> new IllegalArgumentException("未实现的导数文件类型: " + deliveryType.getDescription()));
    }

}
