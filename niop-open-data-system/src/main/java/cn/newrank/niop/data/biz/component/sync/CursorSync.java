package cn.newrank.niop.data.biz.component.sync;

import cn.newrank.niop.data.util.DateTimeUtil;
import lombok.extern.log4j.Log4j2;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 游标运行器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 15:27
 */
@Log4j2
public abstract class CursorSync<T, C extends Cursor<T>> implements Runnable {

    protected final AtomicBoolean running = new AtomicBoolean(false);
    protected final CursorStorage<T, C> cursorStorage;
    protected final C cursor;
    protected final String name;

    protected CursorSync(String name,C cursor, CursorStorage<T, C> cursorStorage) {
        this.cursor = cursor;
        this.cursorStorage = cursorStorage;
        this.name = name;
    }


    @Override
    public final void run() {
        if (running.compareAndSet(false, true)) {
            while (isRunning()) {
                try {
                    final int rs = sync(cursor);
                    if (rs == 0) {
                        cursor.setState(Cursor.STATE_FINISHED);
                        log.info("({}->{})同步完成", name, cursor.getKey());
                        break;
                    } else if (rs > 0) {
                        cursor.addCompleted(rs);
                    }
                } catch (Exception e) {
                    if (isRunning()) {
                        log.error("({}->{})同步异常 error", name, cursor.getKey(), e);
                        cursor.setError(e.getMessage());
                        cursor.setState(Cursor.STATE_FINISHED);
                    }
                } finally {
                    if (isRunning()) {
                        cursor.setUpdateTime(DateTimeUtil.format(LocalDateTime.now()));
                        cursorStorage.update(cursor);
                    }
                }
            }
        }

        afterSync();
    }

    private boolean isRunning() {
        if (!running.get()) {
            return false;
        }

        final C c = cursorStorage.get(cursor.getKey());
        return c != null && c.isStarted();
    }

    /**
     * 停止同步
     */
    public void stop() {
        running.set(false);
        cursor.setAddress(null);
        cursorStorage.update(cursor);
    }

    /**
     * 数据同步
     *
     * @param cursor 游标
     * @return 同步结果
     */
    protected abstract int sync(C cursor);

    /**
     * 同步完成hook
     */
    protected abstract void afterSync();
}
