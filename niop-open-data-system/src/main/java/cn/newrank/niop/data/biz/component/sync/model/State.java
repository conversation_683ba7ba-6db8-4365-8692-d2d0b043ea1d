package cn.newrank.niop.data.biz.component.sync.model;

import cn.newrank.niop.web.model.BizEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 16:12
 */
public enum State implements BizEnum {
    STOP("stop", "停止"),
    RESET("reset", "重置"),
    DELETE("delete", "删除"),
    ;

    private final String code;
    private final String description;

    State(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
