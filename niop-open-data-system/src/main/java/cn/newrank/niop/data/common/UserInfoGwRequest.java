package cn.newrank.niop.data.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.context.AppContext;
import cn.newrank.nrcore.pojo.user.SysUser;
import cn.newrank.nrcore.web.client.JsonBody;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * Newrank Gateway 请求工具类
 *
 * <AUTHOR>
 */
public final class UserInfoGwRequest {

    private UserInfoGwRequest() {}

    private static final Logger log = LoggerFactory.getLogger(UserInfoGwRequest.class);

    private static final String PRODUCT_INTRANET_HOST = "https://inner-gw.newrank.cn";

    private static final String TEST_HOST = "http://test-gw.newrank.cn:18080";

    private static final String HOST = AppContext.isProduct() ? PRODUCT_INTRANET_HOST : TEST_HOST;

    /**
     * <a href="https://gateway.newrank.cn/document/business/detail/VLXXST4Z/UZMNNJZE/Z7N9QT18/0">获取用户信息集合</a>
     */
    private static final String LIST_USERS_URL = "/api/internalUser/nr/user/inner/userinfo/getUserMessageByUserIdList";

    private static final String TOKEN_KEY = "n-token";

    private static final String TOKEN = "ee3c23f8f29348f2b764c29b31f7b2a1";

    private static final int RETRIES = 3;

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
        .connectTimeout(Duration.ofSeconds(10))
        .readTimeout(Duration.ofSeconds(20))
        .writeTimeout(Duration.ofSeconds(20))
        .connectionPool(new ConnectionPool(4, 1, TimeUnit.MINUTES))
        .build();


    public static List<SysUser> listUsers(Set<String> userIds) {

        JSONObject params = JSONObject.of("userIdList", userIds);
        final JsonBody jsonBody = new JsonBody.Builder(JSON.toJSONString(params)).build();

        final Request request = new Request.Builder()
            .url(HOST + LIST_USERS_URL)
            .header(TOKEN_KEY, TOKEN)
            .post(jsonBody)
            .build();

        for (int i = 1; i <= RETRIES; i++) {
            try (Response response = CLIENT.newCall(request).execute()) {
                final ResponseBody body = response.body();

                if (Objects.nonNull(body)) {
                    final String bodyStr = body.string();
                    ListUsersResult result = JSONObject.parseObject(bodyStr, ListUsersResult.class);
                    return result.toUsers();
                }
                throw createBizException(BizErr.REQUEST_ERROR, "Status: {}, body is null", response.code());
            } catch (Exception e) {
                log.warn("批量用户信息查询异常, param: {}, e", JSON.toJSONString(params), e);
                ThreadUtil.sleep(100);
            }
        }

        throw createBizException(BizErr.REQUEST_ERROR, "批量用户信息查询多次失败, 请稍后重试");
    }

    @Data
    static class ListUsersResult {

        /**
         * 用户数据
         */
        private List<User> value;

        @Data
        static class User {

            /**
             * nrId
             */
            private String nrId;

            /**
             * 昵称
             */
            private String nickName;

            /**
             * uid
             */
            private String userId;

            public SysUser toSysUser() {
                final SysUser result = new SysUser();
                result.setNrId(this.getNrId());
                result.setNickname(this.getNickName());
                result.setUserId(this.getUserId());
                return result;
            }

        }

        public List<SysUser> toUsers() {
            if (CollUtil.isEmpty(this.getValue())) {
                return Collections.emptyList();
            }
            return this.getValue().stream().map(User::toSysUser).toList();
        }

    }

}
