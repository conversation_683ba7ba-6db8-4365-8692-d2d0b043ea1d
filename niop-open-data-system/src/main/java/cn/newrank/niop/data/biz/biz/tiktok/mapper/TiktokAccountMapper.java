package cn.newrank.niop.data.biz.biz.tiktok.mapper;

import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 15:48
 */
@Mapper
public interface TiktokAccountMapper {

    int save(@Param("items") List<TiktokAccount> items);

    TiktokAccount get(String account);

    List<TiktokAccount> list(long cursor);
}




