package cn.newrank.niop.data.biz.biz.txhx.service;

import cn.newrank.niop.data.biz.biz.txhx.mapper.TxhxSphSampleMapper;
import cn.newrank.niop.data.biz.biz.txhx.pojo.TxhxSphSample;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TxhxSphSampleService implements StorageBizService<TxhxSphSample> {

    private final TxhxSphSampleMapper txhxSphSampleMapper;

    public TxhxSphSampleService(TxhxSphSampleMapper txhxSphSampleMapper) {
        this.txhxSphSampleMapper = txhxSphSampleMapper;
    }

    @Override
    public TxhxSphSample get(String identifier) {
        return txhxSphSampleMapper.get(identifier);
    }

    @Override
    public void storeBatch(List<TxhxSphSample> items) {
        txhxSphSampleMapper.insertBatch(items);
    }

    @Override
    public TxhxSphSample castOf(JSONObject item) {
        return item.to(TxhxSphSample.class);
    }
}
