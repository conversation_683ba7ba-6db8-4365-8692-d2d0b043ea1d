package cn.newrank.niop.data.biz.pojo.dto;


import cn.newrank.niop.data.biz.pojo.enums.CbType;
import cn.newrank.niop.data.biz.pojo.po.CbConfigPo;
import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.util.List;

/**
 * 回调信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 10:09
 */
@Data
public class CbConfig {
    /**
     * 负责人
     */
    List<String> maintainers;
    /**
     * 回调策略id
     */
    private String cbId;
    /**
     * 回调唯一键
     */
    String uuid;
    /**
     * 回调类型
     */
    private CbType type;
    /**
     * 回调名称
     */
    private String name;
    /**
     * 回调来源
     */
    private String source;
    /**
     * 回调配置信息
     */
    private ConfigProperties config;
    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public CbConfigPo toPo() {
        final CbConfigPo cbConfigPo = new CbConfigPo();

        cbConfigPo.setCbId(this.cbId);
        cbConfigPo.setUuid(this.uuid);
        cbConfigPo.setType(this.type);
        cbConfigPo.setName(this.name);
        cbConfigPo.setSource(this.source);
        cbConfigPo.setConfig(this.config.toJSONString());
        cbConfigPo.setMaintainers(JSON.toJSONString(this.maintainers));
        cbConfigPo.setGmtCreate(this.gmtCreate);
        cbConfigPo.setGmtModified(this.gmtModified);

        return cbConfigPo;
    }
}