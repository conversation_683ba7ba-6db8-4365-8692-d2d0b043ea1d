package cn.newrank.niop.data.biz.export.mapper;

import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DataExportTaskMapper {

    /**
     * 新增导数任务
     *
     * @param exportTask 导数任务
     * @return 是否成功
     */
    boolean insert(DataExportTask exportTask);

    /**
     * 分页搜索导数任务
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 分页数据
     */
    Page<DataExportTask> page(@Param("query") DataExportTaskPageQuery query, Page<DataExportTask> page);

    /**
     * 根据导数任务id查询导数任务
     *
     * @param exportTaskId 导数任务id
     * @return 导数任务信息
     */
    DataExportTask getExportTask(@Param("exportTaskId") String exportTaskId);

    /**
     * 更新导数任务运行状态
     *
     * @param id 主键id
     * @param runningStatus 运行状态
     * @return 是否成功
     */
    boolean updateRunningStatus(@Param("id") Integer id,
                                @Param("runningStatus") DataExportRunningStatus runningStatus);

    /**
     * 根据任务状态查询运行中的导数任务列表
     *
     * @param taskStatus 任务状态
     * @return 运行中的导数任务列表
     */
    List<DataExportTask> listRunningExportTasksByStatus(@Param("taskStatus") DataExportTaskStatus taskStatus);

    /**
     * 根据任务状态查询运行中的导数任务执行信息列表
     *
     * @param taskStatus 任务状态
     * @return 运行中的导数任务执行信息
     */
    List<DataExportTaskExecutionDTO> listRunningExportTaskDTOByStatus(@Param("taskStatus") DataExportTaskStatus taskStatus);

    /**
     * 根据导数任务id获取执行信息
     *
     * @param exportTaskId 导数任务id
     * @return 执行信息
     */
    DataExportTaskExecutionDTO getExportTaskExecutionDTO(@Param("exportTaskId") String exportTaskId);

    /**
     * 更新导数任务信息
     *
     * @param dataExportTask 待更新的导数信息
     * @return 是否成功
     */
    boolean update(DataExportTask dataExportTask);

    /**
     * 更新导数任务为完成
     *
     * @param finishedTask 导数任务完成信息
     */
    void taskFinished(DataExportTask finishedTask);

}
