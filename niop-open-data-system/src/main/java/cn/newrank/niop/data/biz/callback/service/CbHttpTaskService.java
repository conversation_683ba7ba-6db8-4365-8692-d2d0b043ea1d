package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/27 14:54
 */
public interface CbHttpTaskService {

    void saveBatch(List<CbHttpTask> tasks);

    List<CbHttpTask> listRunning(String cbId, Timestamp startTime, int taskCount, String tablePartition);

    void update(List<CbHttpTask> tasks, String tablePartition);

    void createTable(LocalDate partition);

    LocalDate queryCleanEndPartition();

    void dropTable(LocalDate partition);

    List<String> queryDistributedTables();

    LocalDate queryStartPartition(String cbId);

}
