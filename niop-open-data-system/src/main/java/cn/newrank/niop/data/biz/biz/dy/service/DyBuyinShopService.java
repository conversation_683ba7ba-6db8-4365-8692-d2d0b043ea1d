package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinRoomMapper;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinRoomSourceDataMapper;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinShopMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoom;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinRoomSourceData;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShop;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024/10/29
 * @description
 */
@Service
@RequiredArgsConstructor
public class DyBuyinShopService {

    private final DyBuyinShopMapper dyBuyinShopMapper;

    private final DyBuyinRoomMapper dyBuyinRoomMapper;

    private final DyBuyinRoomSourceDataMapper roomSourceDataMapper;

    public void shopSkuData(JSONObject json, String deviceName) {
        String partitionOffset = json.getString("kafka_partition") + "_" + json.getString("kafka_offset");
        JSONObject dataJson = json.getJSONObject("json_details").getJSONObject("data");
        if (dataJson == null || dataJson.isEmpty()) {
            roomSourceDataMapper.insertOne(new DyBuyinRoomSourceData()
                    .setRoomId(json.getString("room_id"))
                    .setPartitionOffset(partitionOffset)
                    .setSourceData(json.toJSONString()));
            return;
        }
        //获取直播信息
        JSONObject roomJson = dataJson.getJSONObject("live_room");
        String roomId = roomJson.getString("room_id");
        DyBuyinRoom dyBuyinRoom = buildLiveRoom(roomJson);
        dyBuyinRoom.setDeviceName(deviceName)
                .setPartitionOffset(partitionOffset);
        dyBuyinRoomMapper.insertOne(dyBuyinRoom);
        //获取直播商品信息
        List<DyBuyinShop> dtoList = dataJson.getJSONArray("products").stream().map(item -> {
            JSONObject productJson = (JSONObject) item;
            return new DyBuyinShop()
                    .setRoomId(roomId)
                    .setProductId(productJson.getString("product_id"))
                    .setPromotionId(productJson.getString("promotion_id"))
                    .setName(productJson.getString("name"))
                    .setCover(productJson.getString("cover"))
                    .setPrice(productJson.getString("price"))
                    .setGmv(productJson.getString("gmv"))
                    .setSaleNum(productJson.getString("sale_num"))
                    .setReplay(CollUtil.isEmpty(productJson.getJSONArray("replay")) ? "" : productJson.getJSONArray("replay").toJSONString())
                    .setDeviceName(deviceName)
                    .setPartitionOffset(partitionOffset);
        }).toList();
        if (CollUtil.isNotEmpty(dtoList)) {
            dyBuyinShopMapper.batchInsert(dtoList);
        }
        //保存原始数据
        roomSourceDataMapper.insertOne(new DyBuyinRoomSourceData()
                .setRoomId(roomId)
                .setPartitionOffset(partitionOffset)
                .setSourceData(json.toJSONString()));
    }

    private static DyBuyinRoom buildLiveRoom(JSONObject roomJson) {
        return new DyBuyinRoom()
                .setRoomId(roomJson.getString("room_id"))
                .setName(roomJson.getString("name"))
                .setStartTime(roomJson.getString("start_time"))
                .setEndTime(roomJson.getString("end_time"))
                .setCover(roomJson.getString("cover"))
                .setStatus(roomJson.getLong("status"))
                .setStreamUrl(roomJson.getString("stream_url"))
                .setViewerNums(roomJson.getString("viewer_nums"))
                .setMaxViewerNum(roomJson.getString("max_viewer_num"))
                .setTotalSaleNum(roomJson.getString("total_sale_num"))
                .setTotalGmv(roomJson.getString("total_gmv"))
                .setProductNum(roomJson.getString("product_num"));
    }
}
