package cn.newrank.niop.data.biz.biz.athm.service;

import cn.newrank.niop.data.biz.biz.athm.mapper.AthmOpusListMapper;
import cn.newrank.niop.data.biz.biz.athm.pojo.AthmOpusList;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class AthmOpusListService implements StorageBizService<AthmOpusList> {

    private final AthmOpusListMapper athmOpusListMapper;

    public AthmOpusListService(AthmOpusListMapper athmOpusListMapper) {
        this.athmOpusListMapper = athmOpusListMapper;
    }

    @Override
    public void storeBatch(List<AthmOpusList> items) {
        athmOpusListMapper.insertBatch(items);
    }


    @Override
    public AthmOpusList get(String identifier) {
        return athmOpusListMapper.get(identifier);
    }

    @Override
    public AthmOpusList castOf(JSONObject item) {
        return item.to(AthmOpusList.class);
    }
}




