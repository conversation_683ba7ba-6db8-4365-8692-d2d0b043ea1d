package cn.newrank.niop.data.biz.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.config.property.KafkaProperties;
import com.alibaba.fastjson2.JSON;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.StreamSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecords;

/**
 * 导数能力回调消费
 *
 * <AUTHOR>
 */
@Slf4j
public class ExportAbilityCallbackConsumer extends CommonConsumer {

    public ExportAbilityCallbackConsumer(KafkaProperties.Config config,
                                         Environment environment,
                                         DataExportSubtaskService exportSubtaskService,
                                         ThreadPoolExecutor executor) {
        super(config, environment, new ExportAbilityConsumeService(exportSubtaskService), executor);
    }

    public static class ExportAbilityConsumeService implements ConsumeService {

        private final DataExportSubtaskService exportSubtaskService;

        protected ExportAbilityConsumeService(DataExportSubtaskService exportSubtaskService) {
            this.exportSubtaskService = exportSubtaskService;
        }

        @Override
        public boolean consume(ConsumerRecords<String, String> consumerRecords) {
            final List<DataExportSubtask> subtasks = StreamSupport.stream(consumerRecords.spliterator(), false)
                .map(consumerRecord -> JSON.parseObject(consumerRecord.value(), AbilityFinishTask.class))
                .filter(AbilityFinishTask::fromExportApp)
                .map(DataExportSubtask::buildBy)
                .toList();

            // 更新子任务状态信息
            if (CollUtil.isNotEmpty(subtasks)) {
                exportSubtaskService.batchUpdateSubtasks(subtasks);
            }
            return true;
        }

    }

}
