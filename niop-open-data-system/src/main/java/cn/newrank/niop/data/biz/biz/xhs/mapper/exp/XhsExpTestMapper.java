package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;

import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTestTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/1 10:20:07
 */
@Mapper
public interface XhsExpTestMapper {


    boolean saveTask(@Param("testCode") String testCode,
                     @Param("taskTriggerTime") LocalDateTime taskTriggerTime,
                     @Param("taskId") String taskId,
                     @Param("taskParam") String param,
                     @Param("taskResult") String taskResult);

    XhsExpTestTask submitTask(@Param("testCode") String testCode,
                              @Param("taskTriggerTime") LocalDateTime taskTriggerTime,
                              @Param("taskParam") String param);
}
