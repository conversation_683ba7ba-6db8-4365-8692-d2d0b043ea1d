package cn.newrank.niop.data.biz.export.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import cn.newrank.niop.data.biz.export.excel.ExportExcelReader;
import cn.newrank.niop.data.biz.export.mapper.DataExportMapper;
import cn.newrank.niop.data.biz.export.mapper.DataExportTaskMapper;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportParamPreviewResult;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportParamFilePreview;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskRunningControl;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskSubmit;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import cn.newrank.niop.data.biz.export.service.DataExportResultFileService;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import cn.newrank.niop.data.common.UserInfoGwRequest;
import cn.newrank.niop.web.model.PageView;
import cn.newrank.nrcore.exception.BizException;
import cn.newrank.nrcore.pojo.user.SysUser;
import cn.newrank.nrcore.pojo.user.SysUserThreadLocal;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

@Log4j2
@Service
public class DataExportTaskServiceImpl implements DataExportTaskService {

    private final DataExportMapper dataExportMapper;

    private final DataExportTaskMapper exportTaskMapper;

    private final OssService ossService;

    private final DataExportResultFileService exportResultFileService;

    public DataExportTaskServiceImpl(DataExportMapper dataExportMapper,
                                     DataExportTaskMapper exportTaskMapper,
                                     OssService ossService,
                                     DataExportResultFileService exportResultFileService) {
        this.dataExportMapper = dataExportMapper;
        this.exportTaskMapper = exportTaskMapper;
        this.ossService = ossService;
        this.exportResultFileService = exportResultFileService;
    }

    @Override
    public DataExportParamPreviewResult taskFilePreview(DataExportParamFilePreview filePreview) {
        try (OSSObject ossObject = ossService.getObjectFromUrl(filePreview.getParamFile());
             ExportExcelReader reader = new ExportExcelReader(ossObject.getObjectContent())) {

            if (!dataExportMapper.existDataExport(filePreview.getExportId())) {
                throw createParamError("导数信息不存在");
            }

            int rowTotal = reader.getRowTotal();
            if (rowTotal < 1) {
                throw createParamError("参数文件没有数据, 无法提交");
            }

            if (rowTotal > DataExportConstant.MAX_PARAM_COUNT) {
                throw createParamError("参数文件任务数超出最大限制: " + DataExportConstant.MAX_PARAM_COUNT);
            }

            return DataExportParamPreviewResult.of(rowTotal, reader.viewData(10));
        } catch (Exception e) {
            // 删除异常 OSS 文件
            ossService.deleteObjectByUrl(filePreview.getParamFile());
            if (e instanceof BizException bizException) {
                throw bizException;
            }
            throw createParamError("导数参数文件读取异常");
        }

    }

    @Override
    public String submitExportTask(DataExportTaskSubmit taskSubmit) {
        final String exportId = taskSubmit.getExportId();
        if (!dataExportMapper.existDataExport(exportId)) {
            throw createParamError("导数信息不存在");
        }

        // 校验一下上传的参数文件是否存在, 不存在很可能是因为异常删除, 终止此次提交
        if (!ossService.existObject(taskSubmit.getParamFile())) {
            throw createParamError("参数文件不存在, 请重新上传");
        }

        final DataExportTask exportTask = taskSubmit.initTask();
        // 获取创建人
        SysUser user = SysUserThreadLocal.get();
        exportTask.setCreator(user.getUserId());

        boolean inserted = exportTaskMapper.insert(exportTask);
        if (!inserted) {
            throw createDbError("导数任务提交失败, 请重试");
        }

        return exportTask.getExportTaskId();
    }

    @Override
    public PageView<DataExportTask> page(DataExportTaskPageQuery pageQuery) {
        Page<DataExportTask> taskPage = exportTaskMapper.page(pageQuery, pageQuery.toMybatisPlusPage());

        final Set<String> userIds = taskPage.getRecords().stream().map(DataExportTask::getCreator).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(userIds)) {
            final List<SysUser> creators = UserInfoGwRequest.listUsers(userIds);
            batchSetCreators(taskPage.getRecords(), creators);
        }

        return PageView.of(taskPage);
    }

    @Override
    public DataExportRunningStatus updateRunningStatus(DataExportTaskRunningControl runningControl) {
        final String exportTaskId = runningControl.getExportTaskId();
        final DataExportTask exportTask = Optional.ofNullable(exportTaskMapper.getExportTask(exportTaskId))
            .orElseThrow(() -> createDbError("导数任务不存在"));

        DataExportRunningStatus oldStatus = exportTask.getRunningStatus();
        if (DataExportRunningStatus.FINISHED == oldStatus) {
            throw createParamError("当前状态不支持操作");
        }

        DataExportRunningStatus newStatus = DataExportRunningStatus.parseByCode(runningControl.getRunningStatus());
        // 运行状态相同, 直接返回
        if (oldStatus == newStatus) {
            return newStatus;
        }

        return exportTaskMapper.updateRunningStatus(exportTask.getId(), newStatus) ? newStatus : oldStatus;
    }

    @Override
    public List<DataExportTask> listRunningExportTasksByStatus(DataExportTaskStatus taskStatus) {
        return Objects.nonNull(taskStatus) ? exportTaskMapper.listRunningExportTasksByStatus(taskStatus) : Collections.emptyList();
    }

    @Override
    public List<DataExportTaskExecutionDTO> listRunningExportTaskDTOByStatus(DataExportTaskStatus taskStatus) {
        return Objects.nonNull(taskStatus) ? exportTaskMapper.listRunningExportTaskDTOByStatus(taskStatus) : Collections.emptyList();
    }

    @Override
    public DataExportTaskExecutionDTO getExportTaskExecutionInfo(String exportTaskId) {
        return exportTaskMapper.getExportTaskExecutionDTO(exportTaskId);
    }

    @Override
    public boolean taskInitialized(Integer id, Integer paramTotalNum) {
        return exportTaskMapper.update(DataExportTask.initialized(id, paramTotalNum));
    }

    @Override
    public void taskPaused(Integer id) {
        Optional.ofNullable(id).ifPresent(i -> exportTaskMapper.updateRunningStatus(i, DataExportRunningStatus.PAUSED));
    }

    @Override
    public void taskFailed(Integer id) {
        exportTaskMapper.update(DataExportTask.failed(id));
    }

    @Override
    public void taskRunning(Integer id) {
        exportTaskMapper.update(DataExportTask.running(id));
    }

    @Override
    public void taskResultFileReady(Integer id) {
        exportTaskMapper.update(DataExportTask.resultFileReady(id));
    }

    @Override
    public void taskFinished(DataExportSubtaskExecutionInfo subtaskExecutionInfo) {
        final DataExportTask taskFinished = new DataExportTask();
        taskFinished.setExportTaskId(subtaskExecutionInfo.getExportTaskId());
        taskFinished.setTaskStatus(subtaskExecutionInfo.isSucceed() ? DataExportTaskStatus.SUCCEED : DataExportTaskStatus.FAILED);
        taskFinished.setSucceedTotalNum(subtaskExecutionInfo.getSucceedCount());
        taskFinished.setTaskFinishedTime(subtaskExecutionInfo.getLastFinishedTime());
        exportTaskMapper.taskFinished(taskFinished);
    }

    @Override
    public List<DataExportResultFile> listResultFiles(String exportTaskId) {
        final DataExportTask exportTask = Optional.ofNullable(exportTaskMapper.getExportTask(exportTaskId))
            .orElseThrow(() -> createParamError("导数任务不存在"));

        if (DataExportTaskStatus.RESULT_FILE_IS_READY != exportTask.getTaskStatus()) {
            throw createParamError("导数任务结果文件未准备好");
        }

        return exportResultFileService.listResultFiles(exportTaskId, true);
    }

    private void batchSetCreators(List<DataExportTask> tasks, List<SysUser> creators) {
        if (CollUtil.isEmpty(tasks) || CollUtil.isEmpty(creators)) {
            return;
        }

        final Map<String, String> creatorMap = creators.stream()
            .collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickname));
        tasks.forEach(t -> t.setCreator(creatorMap.get(t.getCreator())));
    }

}
