package cn.newrank.niop.data.biz.biz.ds.pojo.po;

import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DsEsSyncRecordPo implements Serializable {
    /**
     * 平台类型
     */
    private PlatformType platformType;
    /**
     * 最后同步时间
     */
    private String lastSyncTime;

    private Integer id;
    private String gmtCreate;
    private String gmtModified;

    private Long openSynInsertCount;

    private Long synInsertCount;
}