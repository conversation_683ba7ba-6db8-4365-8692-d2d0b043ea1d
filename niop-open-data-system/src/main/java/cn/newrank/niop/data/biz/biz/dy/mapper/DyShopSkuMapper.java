package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_shop_sku】的数据库操作Mapper
 * @createDate 2024-10-16 17:00:25
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyShopSku
 */
@Mapper
public interface DyShopSkuMapper {


    void batchSave(@Param("itemList") List<DyShopSku> itemList);

    void oneSave(@Param("item") DyShopSku item);

}




