package cn.newrank.niop.data.biz.biz.xhs.service;

import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsHotWord;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/16 11:24
 */
@Log4j2
@Service
public class XhsHotWordService implements StorageBizService<XhsHotWord> {

    private final RestHighLevelClient storageEsClient;
    private final String index;

    public XhsHotWordService(RestHighLevelClient storageEsClient) {
        this.storageEsClient = storageEsClient;
        this.index = "search_xhs_hot_word_20240827";
    }

    @Override
    public void storeBatch(List<XhsHotWord> items) {
        final BulkRequest bulkRequest = new BulkRequest(index);
        for (XhsHotWord item : items) {
            final String hotWordId = item.getHotWordId();

            final UpdateRequest updateRequest = new UpdateRequest();
            final String jsonString = item.getBody().toJSONString();
            updateRequest
                    .id(hotWordId)
                    .doc(jsonString, XContentType.JSON)
                    .upsert(jsonString, XContentType.JSON);
            bulkRequest.add(updateRequest);
        }

        try {
            storageEsClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error(e);
        }
    }


    @Override
    public XhsHotWord castOf(JSONObject item) {
        item.remove("kafka_partition");
        item.remove("kafka_offset");
        final XhsHotWord xhsHotWord = new XhsHotWord();
        xhsHotWord.setHotWordId(String.valueOf( item.getString("id")));
        xhsHotWord.setBody(item);

        return xhsHotWord;
    }

    @Override
    public XhsHotWord castWithPrintException(JSONObject item) {
        try {
            return castOf(item);
        } catch (Exception e) {
            log.warn(item.toJSONString(JSONWriter.Feature.PrettyFormat));
            e.printStackTrace();
            throw e;
        }
    }

    public static void main(String[] args) {
        final JSONObject jsonObject = JSONObject.parseObject("""
                {
                        "ana_time":"2024-10-15 14:22:08",
                        "hot_note_count_ninety":0,
                        "hot_note_count_seven":0,
                        "hot_note_count_thirty":0,
                        "hot_score_ninety":1022,
                        "hot_score_seven":11,
                        "hot_score_thirty":206,
                        "hot_word":"娜彩婚纱",
                        "note_count_ninety":55,
                        "note_count_seven":2,
                        "note_count_thirty":17,
                        "note_label_seven":[
                                {
                                        "label":"婚嫁",
                                        "ratio":1.0,
                                        "note_count":2
                                }
                        ],
                        "note_label_thirty":[
                                {
                                        "label":"婚嫁",
                                        "ratio":1.0,
                                        "note_count":17
                                }
                        ],
                        "note_label_v2_seven":[
                                {
                                        "label":"婚礼造型",
                                        "ratio":1.0,
                                        "note_count":2
                                }
                        ],
                        "note_label_v2_thirty":[
                                {
                                        "label":"婚礼造型",
                                        "ratio":0.9411764705882353,
                                        "note_count":16
                                },
                                {
                                        "label":"婚礼经验",
                                        "ratio":0.058823529411764705,
                                        "note_count":1
                                }
                        ],
                        "rank_date":"2024-10-15",
                        "top_label_v1_seven":"婚嫁",
                        "top_label_v1_thirty":"婚嫁",
                        "top_label_v2_seven":"婚礼造型",
                        "top_label_v2_thirty":"婚礼造型",
                        "total_collected_count_ninety":180,
                        "total_collected_count_seven":3,
                        "total_collected_count_thirty":53,
                        "total_comments_count_ninety":97,
                        "total_comments_count_seven":0,
                        "total_comments_count_thirty":6,
                        "total_liked_count_ninety":745,
                        "total_liked_count_seven":8,
                        "total_liked_count_thirty":147,
                        "total_shared_count_ninety":119,
                        "total_shared_count_seven":2,
                        "total_shared_count_thirty":36,
                        "is_seo_word":1,
                        "word_len":4,
                        "word_flag":"n"
                }
                """);
        final XhsHotWord xhsHotWord = new XhsHotWordService(null).castOf(jsonObject);

        System.out.println(xhsHotWord.getBody().toJSONString());
    }
}
