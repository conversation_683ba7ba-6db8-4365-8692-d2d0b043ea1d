package cn.newrank.niop.data.biz.biz.xhs.mapper;


import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsUserLdm;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 多回调源存-小红书作品数据-覆盖写lindorm
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
@DS("xhs")
public interface XhsUserLmMapper {

    /**
     * 批量存储-无上次更新时间-新表
     *
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeBatch(@Param("items") List<XhsUserLdm> items);

    /**
     * 批量获取作品是否存在
     *
     * @param userIds 作品ID
     * @return 存在返回对象，不存在返回null
     */
    List<XhsUserLdm> getExistUserIds(@Param("items") Set<String> userIds);
}




