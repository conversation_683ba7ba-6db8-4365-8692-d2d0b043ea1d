package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.dto.DataExportParamPreviewResult;
import com.alibaba.fastjson.JSON;
import java.util.Objects;
import lombok.Data;

@Data
public class DataExportParamPreviewResultVo {

    /**
     * 参数总数
     */
    private Integer paramTotalNum;

    /**
     * 部分参数 Json 数据
     */
    private String someParamJson;

    public static DataExportParamPreviewResultVo buildBy(DataExportParamPreviewResult previewResult) {
        if (Objects.isNull(previewResult)) {
            return null;
        }
        DataExportParamPreviewResultVo vo = new DataExportParamPreviewResultVo();
        vo.setParamTotalNum(previewResult.getParamTotalNum());
        vo.setSomeParamJson(JSON.toJSONString(previewResult.getSomeParams()));
        return vo;
    }

}
