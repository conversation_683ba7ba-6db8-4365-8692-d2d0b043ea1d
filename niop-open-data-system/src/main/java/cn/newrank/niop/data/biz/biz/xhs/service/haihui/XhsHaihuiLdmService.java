package cn.newrank.niop.data.biz.biz.xhs.service.haihui;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsHaiHuiLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.haihui.XhsHaiHuiOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:12:11
 */
@Slf4j
@Service
public class XhsHaihuiLdmService {

    private final XhsHaiHuiLmMapper xhsHaiHuiLmMapper;
    private final RedissonClient redissonClient;
    private final EsFactory.Es kolEs;
    private final EsFactory.Es xhsEs;
    private final HoloFactory.Holo holo;
    public static final String XHS_ES_OPUS_SYNC = """
               GET search_xhs_note/_search
                 {
                   "size": #{size},
                   "query": {
                     "bool": {
                         "filter": [
                           {
                             "range": {
                               "create_time": {
                                 "from": #{startTime},
                                 "to": #{endTime}
                               }
                             }
                           }
                         ]
                       }
                   },
                   "sort": [ { "id": "asc" } ],
                   "search_after": [#{searchAfter}],
                   "_source": ["id", "user.userid", "title", "desc","official_keyword",
                   "cooperate_binds_name","topics","create_time","type"]
                 }
            """;
    public static final String XHS_OPUS_HAIHUI_KOL = """
                     GET /ad_haihui_work_mmrecog/_search
                     {
                       "query": {
                         "bool": {
                           "should": [
                             {
                               "terms": {
                                 "workId": #{opusIds},
                                 "boost": 1.0
                               }
                             }
                           ],
                           "minimum_should_match": 1
                         }
                       },
                       "_source": ["workId", "cover.summary", "cover.otherSummary"]
                     }
            """;

    private static final String XHS_YOUZHUAN_HOLO_USER_QUERY = """
            SELECT
                id,
                gender,
                location,
                ip_location,
                mcn_name,
                fans_num,
                personal_tags,
                feature_tags,
                content_tags,
                picture_price,
                video_price,
                picture_read_cost,
                video_read_cost,
                fans30_growth_rate,
                normal_pv_all_imp_median,
                normal_pv_all_read_median,
                normal_pv_all_interaction_median,
                normal_pv_all_like_median,
                normal_pv_all_collect_median,
                normal_pv_all_comment_median,
                normal_pv_all_share_median,
                normal_pv_all_interaction_rate,
                high_percent_fans_age,
                high_percent_fans_device,
                high_percent_fans_area,
                normal_pv_all_note_number,
                normal_pv_all_note_type,
                normal_pv_all_thousand_like_percent,
                normal_pv_all_hundred_like_percent,
                cooperate_pv_all_note_number,
                cooperate_pv_all_note_type,
                cooperate_pv_all_imp_median,
                cooperate_pv_all_read_median,
                cooperate_pv_all_interaction_median,
                cooperate_pv_all_like_median,
                cooperate_pv_all_collect_median,
                cooperate_pv_all_comment_median,
                cooperate_pv_all_share_median,
                cooperate_pv_all_interaction_rate,
                cooperate_pv_all_thousand_like_percent,
                cooperate_pv_all_hundred_like_percent
            FROM
                public.dws_xhs_user
            WHERE
                fans_num > 1000 and current_level != 0 and
                id in (%s)
            """;


    public XhsHaihuiLdmService(XhsHaiHuiLmMapper xhsHaiHuiLmMapper,
                               DsConfigManager dsConfigManager,
                               RedissonClient redissonClient) {
        this.xhsHaiHuiLmMapper = xhsHaiHuiLmMapper;
        this.redissonClient = redissonClient;
        this.kolEs = EsFactory.DEFAULT.create(dsConfigManager.chooseAdHoloKolConfig());
        this.xhsEs = EsFactory.DEFAULT.create(dsConfigManager.chooseXhsEsConfig());
        this.holo = HoloFactory.DEFAULT.create(dsConfigManager.chooseYsHoloConfig());
    }

    public void runScheduleUser(String param) {
        try {
            XxlJobLogger.log("开始同步数据到lindorm");
            syncUser(param);
            XxlJobLogger.log("同步完毕");
        } catch (Exception e) {
            XxlJobLogger.log("【海汇同步数据】到lindorm失败：{}", e.getMessage());
            log.error("【海汇同步数据】到lindorm失败：{}", e.getMessage());
        }
    }

    /**
     * 运行定时任务
     *
     * @param param param
     */
    public void syncUser(String param) {
        RBucket<String> bucket = redissonClient.getBucket("haihui:sync:schedule:user");
        String opusCursor = Strings.isBlank(bucket.get()) ? "" : bucket.get();
        XxlJobLogger.log("cursor:{}",opusCursor);
        // 翻页拿es作品
        while (true) {
            List<XhsHaiHuiOpus> pageRes = xhsHaiHuiLmMapper.pageXhsOpus(opusCursor, 1000);
            Set<String> userIds = pageRes.stream().map(XhsHaiHuiOpus::getUserId).collect(Collectors.toSet());
            if(CollUtil.isEmpty(userIds)){
                break;
            }
            Map<String, JSONObject> holoUserMap = getHoloUserMap(userIds);
            if (CollUtil.isEmpty(pageRes)) {
                break;
            }
            opusCursor = pageRes.get(pageRes.size() - 1).getOpusId();
            bucket.set(opusCursor);
            pageRes = pageRes.stream().map(opus -> {
                JSONObject holoUser = holoUserMap.get(opus.getUserId());
                if(Objects.isNull(holoUser)){
                    return null;
                }
                Integer gender = holoUser.getInteger("gender");
                if(Objects.isNull(gender)){
                    return null;
                }
                if(gender.equals(opus.getGender())){
                    return null;
                }
                opus.setGender(gender);
                return opus;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollUtil.isEmpty(pageRes)){
                continue;
            }
            xhsHaiHuiLmMapper.saveBatchUser(pageRes);
            XxlJobLogger.log("更新作品：{}", pageRes.size());
        }
    }

    /**
     * 运行定时任务
     *
     * @param param param
     */
    public void runSchedule(String param) {
        RBucket<String> bucket = redissonClient.getBucket(getHaiHuiSyncEsScheduleKey());
        if (Strings.isNotBlank(param)) {
            setTimeByParam(param, bucket);
            return;
        }
        String nextSyncTime = bucket.get();
        if (Strings.isBlank(nextSyncTime)) {
            setTimeAtNextDay(bucket);
            return;
        }
        LocalDateTime nextSyncDateTime = LocalDateTime.parse(nextSyncTime, DatePattern.NORM_DATETIME_FORMATTER);
        if (LocalDateTime.now().isBefore(nextSyncDateTime.plusDays(1))) {
            XxlJobLogger.log("未到执行时间：{}", nextSyncDateTime.format(DatePattern.NORM_DATETIME_FORMATTER));
            return;
        }
        try {
            XxlJobLogger.log("开始同步数据到lindorm");
            // 同步一天数据
            LocalDateTime startTime = nextSyncDateTime.toLocalDate().atStartOfDay();
            LocalDateTime endTime = nextSyncDateTime.toLocalDate().plusDays(1).atStartOfDay();
            syncDataFormEsAndHolo(startTime, endTime);

            final String dateTime = getScheduleTime(endTime);
            bucket.set(dateTime);
            XxlJobLogger.log("设置下次执行时间：{}", dateTime);
            XxlJobLogger.log("同步完毕");
        } catch (Exception e) {
            XxlJobLogger.log("【海汇同步数据】到lindorm失败：{}", e.getMessage());
            log.error("【海汇同步数据】到lindorm失败：{}", e.getMessage());
        }
    }

    private static @NotNull String getScheduleTime(LocalDateTime endTime) {
        // 更新下一次同步时间为，上次同步的结束时间
        String dateTime = endTime.plusHours(8).format(DatePattern.NORM_DATETIME_FORMATTER);
        // 超过当前时间，则更新为明日
        if (endTime.isAfter(LocalDateTime.now())) {
            dateTime = LocalDate.now().plusDays(1)
                    .atStartOfDay()
                    .plusHours(8)
                    .format(DatePattern.NORM_DATETIME_FORMATTER);
        }
        return dateTime;
    }


    private void syncDataFormEsAndHolo(LocalDateTime startTime, LocalDateTime endTime) {
        long countOpusSize = 0L;
        RBucket<String> cachedCursor = redissonClient.getBucket(getHaiHuiSyncEsScrollIdKey());
        String opusCursor = Strings.isBlank(cachedCursor.get()) ? "" : cachedCursor.get();
        final String beginTime = startTime.format(DatePattern.NORM_DATETIME_FORMATTER);
        final String stopTime = endTime.format(DatePattern.NORM_DATETIME_FORMATTER);
        XxlJobLogger.log("同步时间范围：{} - {} ,缓存cursor: {}", beginTime, stopTime, opusCursor);

        // 翻页拿es作品
        while (true) {
            Resp pageRes = getEsRespScroll(opusCursor, beginTime, stopTime);
            List<XhsHaiHuiOpus> esDataList = getXhsHaiHuiOpuses(pageRes);
            if (CollUtil.isEmpty(esDataList)) {
                break;
            }
            opusCursor = esDataList.get(esDataList.size() - 1).getOpusId();
            countOpusSize += esDataList.size();
            cachedCursor.set(opusCursor, 30, TimeUnit.MINUTES);
            try {
                saveOpusToLindorm(esDataList);
            } catch (Exception e) {
                for (XhsHaiHuiOpus opus : esDataList) {
                    try {
                        saveOpusToLindorm(Collections.singletonList(opus));
                    } catch (Exception exception) {
                        XxlJobLogger.log("写入错误作品 {} json:{}", opus.getOpusId(), JSON.toJSONString(opus), exception);
                        log.info("写入错误作品 {} json:{}", opus.getOpusId(), JSON.toJSONString(opus), exception);
                        break;
                    }
                }
                throw new RuntimeException(e);
            }
            XxlJobLogger.log("数据同步到lindorm共: {} 作品, 新增 {},cursor: {}", countOpusSize, esDataList.size(), opusCursor);
        }
        cachedCursor.set("");
        XxlJobLogger.log("总数据同步到lindorm共: {} 作品", countOpusSize);
    }

    /**
     * 判断是否为过滤条件的达人，并补全数据
     *
     * @param userMap
     * @return
     */
    private List<XhsHaiHuiOpus> filterOpusAndParseUserData(List<XhsHaiHuiOpus> opusList,
                                                           Map<String, JSONObject> userMap,
                                                           Map<String, String> coverSummaryMap) {
        return opusList.stream().map(data -> {
            JSONObject userDataHolo = userMap.get(data.getUserId());
            if (Objects.isNull(userDataHolo)) {
                return null;
            }
            XhsHaiHuiOpus.fillUserData(data, getXhsHaiHuiOpusUserInfo(userDataHolo));
            // 避免lindorm空Array写入报错
            data.setOfficialKeyword(checkArrayIsBlank(data.getOfficialKeyword()));
            data.setTopicsName(checkArrayIsBlank(data.getTopicsName()));
            data.setPersonalTags(checkArrayIsBlank(data.getPersonalTags()));
            data.setFeatureTags(checkArrayIsBlank(data.getFeatureTags()));
            data.setHighPercentFansAge(checkArrayIsBlank(data.getHighPercentFansAge()));
            data.setHighPercentFansArea(checkArrayIsBlank(data.getHighPercentFansArea()));
            data.setHighPercentFansDevice(checkArrayIsBlank(data.getHighPercentFansDevice()));
            // 查图像信息
            String summary = coverSummaryMap.get(data.getOpusId());
            if (Strings.isNotBlank(summary)) {
                data.setCoverSummary(summary);
            }
            data.setTextField(getTextField(data));
            return data;
        }).filter(Objects::nonNull).toList();
    }

    private static String checkArrayIsBlank(String array) {
        if (Strings.isBlank(array) || "[]".equals(array)) {
            return null;
        }
        JSONArray jsonArray = JSON.parseArray(array);
        jsonArray = jsonArray.stream().map(item -> {
            String str = (String)item;
            return str.replace("\"", "");
        }).collect(Collectors.toCollection(JSONArray::new));
        return jsonArray.toJSONString();
    }

    private static XhsHaiHuiOpus getXhsHaiHuiOpusUserInfo(JSONObject userDataHolo) {
        XhsHaiHuiOpus user = JsonParser.parseObject(userDataHolo, XhsHaiHuiOpus.class);
        Optional.ofNullable(userDataHolo.get("personal_tags")).map(item -> {
            user.setPersonalTags(JSON.toJSONString(item));
            return item;
        });
        Optional.ofNullable(userDataHolo.get("feature_tags")).map(item -> {
            user.setFeatureTags(JSON.toJSONString(item));
            return item;
        });
        Optional.ofNullable(userDataHolo.get("high_percent_fans_age")).map(item -> {
            user.setHighPercentFansAge(JSON.toJSONString(item));
            return item;
        });
        Optional.ofNullable(userDataHolo.get("high_percent_fans_device")).map(item -> {
            user.setHighPercentFansDevice(JSON.toJSONString(item));
            return item;
        });
        Optional.ofNullable(userDataHolo.get("high_percent_fans_area")).map(item -> {
            user.setHighPercentFansArea(JSON.toJSONString(item));
            return item;
        });
        return user;
    }

    private Map<String, JSONObject> getHoloUserMap(Set<String> userIds) {
        String listUserStr = userIds.stream()
                .filter(Objects::nonNull)
                .filter(s -> !s.isEmpty())
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
        final QueryBuilder queryBuilder = holo.newQueryBuilder()
                .template(String.format(XHS_YOUZHUAN_HOLO_USER_QUERY, listUserStr));
        Resp holoUser = holo.query(queryBuilder);
        Object dataView = holoUser.data();
        if (dataView instanceof JSONArray) {
            return ((JSONArray) dataView).stream().map(item -> (JSONObject) item)
                    .collect(Collectors.toMap(obj -> obj.getString("id"), Function.identity()));
        }
        return Map.of();
    }


    public List<XhsHaiHuiOpus> getXhsHaiHuiOpuses(Resp pageRes) {
        List<XhsHaiHuiOpus> esDataList = List.of();
        Resp.DataView dataView = pageRes.getDataView();
        if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
            esDataList = getXhsHaiHuiOpusesWithUser(esDataView.resp());
            if (CollectionUtils.isEmpty(esDataList)) {
                return esDataList;
            }
            Set<String> userIds = esDataList.stream().map(XhsHaiHuiOpus::getUserId).collect(Collectors.toSet());
            List<String> opusIds = esDataList.stream().map(XhsHaiHuiOpus::getOpusId).toList();
            // 补充达人数据
            return filterOpusAndParseUserData(esDataList, getHoloUserMap(userIds), getCoverSummaryMap(opusIds));
        }
        return esDataList;
    }

    private Resp getEsRespScroll(String cursor, String beginTime, String stopTime) {
        if (Strings.isBlank(cursor)) {
            cursor = "";
        }
        return xhsEs.query(xhsEs.newQueryBuilder().template(XHS_ES_OPUS_SYNC)
                .addParam("size", 2000)
                .addParam("searchAfter", cursor)
                .addParam("startTime", beginTime)
                .addParam("endTime", stopTime));
    }

    private List<XhsHaiHuiOpus> getXhsHaiHuiOpusesWithUser(JSONObject resultObj) {
        return EsUtil.listHitsToEntity(resultObj, json -> {
            JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
            if (Objects.isNull(sourceObj)) {
                return null;
            }
            XhsHaiHuiOpus opus = JsonParser.parseObject(sourceObj, XhsHaiHuiOpus.class);
            if (Strings.isBlank(opus.getOpusId())) {
                return null;
            }
            Optional.ofNullable(sourceObj.getJSONArray("topics")).ifPresent(topicJsons -> {
                if (CollUtil.isNotEmpty(topicJsons)) {
                    final JSONArray topics = new JSONArray();
                    topicJsons.forEach(topicJson -> {
                        String name = ((JSONObject) topicJson).getString("name");
                        if (Strings.isNotBlank(name)) {
                            topics.add(name);
                        }
                    });
                    opus.setTopicsName(JSON.toJSONString(topics));
                }
            });
            return opus;
        });
    }

    public Map<String, String> getCoverSummaryMap(List<String> opusIds) {
        Resp resp = kolEs.query(kolEs.newQueryBuilder().template(XHS_OPUS_HAIHUI_KOL)
                .addParam("opusIds", opusIds));
        Resp.DataView dataView = resp.getDataView();
        if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
            JSONObject resultObj = esDataView.resp();
            if (Objects.isNull(resultObj)) {
                return Map.of();
            }
            List<XhsHaiHuiOpus> coverSummaries = EsUtil.listHitsToEntity(resultObj, json -> {
                JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
                String opusId = sourceObj.getString("workId");
                JSONObject summary = sourceObj.getJSONObject("cover");
                String coverSummary = Optional.ofNullable(summary.getString("summary")).orElse("");
                String coverOtherSummary = Optional.ofNullable(summary.getString("otherSummary")).orElse("");

                XhsHaiHuiOpus res = new XhsHaiHuiOpus();
                res.setOpusId(opusId);
                res.setCoverSummary(getCoverSummary(coverSummary, coverOtherSummary));
                return res;
            });
            if (coverSummaries.isEmpty()) {
                return Map.of();
            }
            return coverSummaries.stream().collect(Collectors.toMap(XhsHaiHuiOpus::getOpusId, XhsHaiHuiOpus::getCoverSummary));
        }
        return Map.of();
    }

    private static String getCoverSummary(String coverSummary, String coverOtherSummary) {
        if (Strings.isNotBlank(coverSummary) && Strings.isNotBlank(coverOtherSummary)) {
            return coverSummary + "," + coverOtherSummary;
        } else if (Strings.isBlank(coverSummary) && Strings.isBlank(coverOtherSummary)) {
            return "";
        } else if (Strings.isBlank(coverSummary)) {
            return coverOtherSummary;
        } else {
            return coverSummary;
        }
    }

    private static String getTextField(XhsHaiHuiOpus opus) {
        String interval = "。\n";
        List<String> fields = Arrays.asList(
                opus.getTitle(),
                opus.getDesc(),
                opus.getOfficialKeyword(),
                opus.getTopicsName(),
                opus.getCoverSummary()
        );
        return fields.stream().filter(Strings::isNotBlank).collect(Collectors.joining(interval));
    }


    /**
     * 保存数据到lindorm
     *
     * @param opusList
     */
    private void saveOpusToLindorm(List<XhsHaiHuiOpus> opusList) {
        xhsHaiHuiLmMapper.saveBatch(opusList);
    }


    private static void setTimeByParam(String param, RBucket<String> bucket) {
        XxlJobLogger.log("调度入参: {}", param);
        try {
            LocalDateTime paramDate = LocalDateTime.parse(param, DatePattern.NORM_DATETIME_FORMATTER);
            bucket.set(paramDate.format(DatePattern.NORM_DATETIME_FORMATTER));
            XxlJobLogger.log("原时间：{} 下次执行时间设置成功：{}", bucket.get(), param);
        } catch (Exception e) {
            return;
        }
    }

    private static void setTimeAtNextDay(RBucket<String> bucket) {
        bucket.set(LocalDate.now().plusDays(1)
                .atStartOfDay()
                .plusHours(9).format(DatePattern.NORM_DATETIME_FORMATTER));
        XxlJobLogger.log("默认设置为次日9点");
    }

    public static String getHaiHuiSyncEsScheduleKey() {
        return "haihui:sync:schedule";
    }

    public static String getHaiHuiSyncEsScrollIdKey() {
        return "haihui:sync:schedule:scrollId";
    }
}
