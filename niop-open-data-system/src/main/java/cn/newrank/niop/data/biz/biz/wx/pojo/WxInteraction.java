package cn.newrank.niop.data.biz.biz.wx.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/24 16:27
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class WxInteraction extends SampleVersionEntity {
    /**
     * 作品id
     */
    private String opusId;

    /**
     * 文章地址
     */
    private String url;

    /**
     * 文章点赞数
     */
    private Long likeNum;

    /**
     * 在看数
     */
    private Long viewingNum;

    /**
     * 分享数
     */
    private Long shareNum;

    /**
     * 文章阅读数
     */
    private Long viewNum;

    /**
     * 评论数
     */
    private Long commentNum;

}