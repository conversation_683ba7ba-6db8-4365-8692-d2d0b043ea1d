package cn.newrank.niop.data.biz.biz.ds.service.common.temp;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import java.util.List;
import java.util.Objects;


/**
 * [认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class VerifyInfoHistorySync extends DefaultHistorySynchronizer<String> {

    static final String SCROLL_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "15m",
                "scroll_id" : #{scrollId}
            }
            """;

    protected final CommonEsService commonEsService;
    protected final Datasource platformEs;

    protected VerifyInfoHistorySync(String platform, ConfigProperties configProperties, RedissonClient redissonClient, CommonEsService commonEsService) {
        super("verify_info_sync_" + platform, redissonClient, 1);
        this.platformEs = EsFactory.DEFAULT.create(configProperties);
        this.commonEsService = commonEsService;
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final String scrollId = cursor.getNext();
        final JSONObject resp;
        if (StringUtils.isBlank(scrollId)) {
            final QueryBuilder queryBuilder = platformEs.newQueryBuilder()
                    .template(startQuery());

            resp = platformEs.query(queryBuilder).data();
            final Integer total = resp.getJSONObject("hits")
                    .getInteger("total");
            cursor.setTotal(total);
            cursor.setNext(resp.getString("_scroll_id"));
        } else {
            final QueryBuilder queryBuilder = platformEs.newQueryBuilder()
                    .template(SCROLL_QUERY)
                    .addParam("scrollId", scrollId);

            resp = platformEs.query(queryBuilder).data();
        }

        final List<JSONObject> hits = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class);

        final List<EsEntity> updates = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream().map(item -> castOf(item.getJSONObject("_source")))
                .filter(Objects::nonNull)
                .toList();

        commonEsService.update(updates);

        return hits.size();
    }

    @Data
    public static class VerifyInfoUpdate implements EsEntity {

        private String indexId;

        private String verifyInfo;

        private String verifyTypeV1;

        private String verifyTypeV2;

        @Override
        public String docId() {
            return indexId;
        }
    }

    protected abstract String startQuery();

    protected abstract EsEntity castOf(JSONObject sourceObj);
}
