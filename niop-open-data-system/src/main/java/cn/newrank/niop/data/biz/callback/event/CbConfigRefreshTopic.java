package cn.newrank.niop.data.biz.callback.event;

import cn.newrank.niop.data.biz.pojo.event.CbUpdateEvent;
import cn.newrank.niop.data.common.event.GlobalTopic;
import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/11 11:44
 */
@Component
@Log4j2
public class CbConfigRefreshTopic  implements GlobalTopic<CbUpdateEvent> {

    private final RTopic topic;

    public CbConfigRefreshTopic(RedissonClient redissonClient) {
        this.topic = redissonClient.getTopic("topic:cb:refresh");
    }

    @Override
    public void emitEvent(CbUpdateEvent event) {
        topic.publish(JSON.toJSONString(event));
    }

    @Override
    public void addListener(MessageListener<CbUpdateEvent> messageListener) {
        topic.addListener(String.class, (channel, msg) -> {
                    try {
                        messageListener.onMessage(channel, JSON.parseObject(msg, CbUpdateEvent.class));
                    } catch (Exception e) {
                        log.error("回调源刷新事件监听异常", e);
                    }
                }
        );
    }


}
