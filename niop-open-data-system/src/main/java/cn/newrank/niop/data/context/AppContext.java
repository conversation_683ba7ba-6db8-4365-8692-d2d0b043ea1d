package cn.newrank.niop.data.context;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.pojo.enums.AppEnvEnum;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.StandardEnvironment;

/**
 * 应用环境类
 *
 * <AUTHOR>
 **/
public final class AppContext {

    private static final Logger log = LoggerFactory.getLogger(AppContext.class);

    private static final AppEnvEnum APP_ENV;

    static {
        List<String> activeProfiles = Arrays.asList(new StandardEnvironment().getActiveProfiles());
        APP_ENV = CollUtil.isEmpty(activeProfiles) ? null : AppEnvEnum.parse(activeProfiles.get(0));
        log.info("AppContext initialized. Current environment: {}", APP_ENV);
    }

    private AppContext() {}

    /**
     * @return 当前环境名称
     */
    public static String getAppEnvName() {
        return Optional.ofNullable(APP_ENV)
            .map(AppEnvEnum::getEnvName)
            .orElse(null);
    }

    /**
     * @return 当前环境枚举
     */
    public static AppEnvEnum getAppEnv() {
        return APP_ENV;
    }

    /**
     * @return 是否为正式环境
     */
    public static boolean isProduct() {
        return Objects.nonNull(APP_ENV) && AppEnvEnum.PRODUCT == APP_ENV;
    }

    /**
     * @return 是否为测试环境
     */
    public static boolean isTest() {
        return Objects.nonNull(APP_ENV) && AppEnvEnum.TEST == APP_ENV;
    }

    /**
     * @return 是否为开发环境
     */
    public static boolean isDev() {
        return Objects.nonNull(APP_ENV) && AppEnvEnum.DEV == APP_ENV;
    }

}
