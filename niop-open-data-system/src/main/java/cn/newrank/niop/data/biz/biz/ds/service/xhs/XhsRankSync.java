package cn.newrank.niop.data.biz.biz.ds.service.xhs;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.XhsRank;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 9:59
 */
@Service
public class XhsRankSync extends DefaultHistorySynchronizer<String> {
    static final String QUERY = """
                  SELECT
                        userid as account_id,
                        newrank_index::double precision as nr_index_week,
                        rank_date as nr_index_week_date,
                        concat('xhs_',userid) as index_id
                    FROM
                        dwd_user_rank_week
                    WHERE
                    rank_date >= #{startTime}::TIMESTAMP
                     and rank_date &lt; #{endTime}::TIMESTAMP
                        and newrank_index > 0
                    and userid > #{cursor}
                    ORDER BY
                        userid ASC
                    LIMIT 1000
            """;
    private final Datasource datasource;
    private final CommonEsService commonEsService;

    protected XhsRankSync(RedissonClient redissonClient,
                          DsConfigManager dsConfigManager,
                          CommonEsService commonEsService) {
        super("xhs_rank_sync", redissonClient, 1);
        this.datasource = HoloFactory.DEFAULT.create(dsConfigManager.chooseXhsHoloConfig());
        datasource.checkHealth();
        this.commonEsService = commonEsService;
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(QUERY)
                .addParam("startTime", cursor.getStartTime())
                .addParam("endTime", cursor.getEndTime());
        if (StringUtils.isBlank(cursor.getNext())) {
            queryBuilder.addParam("cursor", "");
        } else {
            queryBuilder.addParam("cursor", cursor.getNext());
        }

        final JSONArray items = datasource.query(queryBuilder).data();
        final List<XhsRank> xhsRanks = items.toList(JSONObject.class)
                .stream()
                .map(XhsRank::of)
                .toList();

        if (xhsRanks.isEmpty()) {
            return 0;
        }
        cursor.setNext(xhsRanks.get(xhsRanks.size() - 1).getAccountId());

        commonEsService.update(xhsRanks);

        return xhsRanks.size();
    }
}
