package cn.newrank.niop.data.common.model;

import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.atsp.AtspSubmitResult;
import cn.newrank.niop.data.biz.export.atsp.SubmitResultType;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/2 13:51:16
 */
@Data
public class AtspSubmitResp {
    /**
     * atsp 任务id
     */
    private String taskId;

    /**
     * 是否成功
     */
    private boolean succeed;

    /**
     * 消息
     */
    private String msg;

    /**
     * 响应code
     */
    private Integer code;

    private AtspSubmitResp(String taskId, Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.taskId = taskId;
        this.succeed = CharSequenceUtil.isNotBlank(taskId);
    }

    public static AtspSubmitResp failed() {
        return new AtspSubmitResp(null, null, null);
    }

    public static AtspSubmitResp of(JSONObject json) {
        if(Objects.isNull(json)){
            return failed();
        }
        return new AtspSubmitResp(json.getString("data"), json.getInteger("code"), json.getString("msg"));
    }

    public SubmitResultType getResultType() {
        return SubmitResultType.of(this.code);
    }
}
