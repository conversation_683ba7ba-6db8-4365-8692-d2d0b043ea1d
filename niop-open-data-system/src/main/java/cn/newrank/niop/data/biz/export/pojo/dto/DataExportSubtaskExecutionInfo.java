package cn.newrank.niop.data.biz.export.pojo.dto;

import java.sql.Timestamp;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataExportSubtaskExecutionInfo {

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 成功任务数
     */
    private Integer succeedCount;

    /**
     * 未完成任务数
     */
    private Integer unfinishedCount;

    /**
     * 最近一个子任务的完成时间
     */
    private Timestamp lastFinishedTime;

    /**
     * 任务是否已完成
     *
     * @return 是否完成
     */
    public boolean isFinished() {
        return this.getUnfinishedCount() <= 0;
    }

    /**
     * 任务是否已成功
     *
     * 成功子任务数大于 0
     *
     * @return 是否成功
     */
    public boolean isSucceed() {
        return this.getSucceedCount() > 0;
    }

}
