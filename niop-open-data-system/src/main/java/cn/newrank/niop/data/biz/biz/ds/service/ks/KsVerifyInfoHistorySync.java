package cn.newrank.niop.data.biz.biz.ds.service.ks;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.CharSequenceUtil.EMPTY;


/**
 * [ks认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class KsVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET search_kuaishou_user/_search?scroll=15m
            {
                "_source": [
                  "userId",
                  "profile",
                  "accountVerified"
                ],
              "size": 1000,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "profile" } },
                    { "exists": { "field": "accountVerified" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected KsVerifyInfoHistorySync(RedissonClient redissonClient,
                                      CommonEsService commonEsService,
                                      DsConfigManager dsConfigManager) {
        super(PlatformType.KS.getDbCode(), dsConfigManager.chooseKsEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final VerifyInfoUpdate update = new VerifyInfoUpdate();
        update.setIndexId(PlatformType.KS.getDbCode() + "_" + sourceObj.get("userId"));

        JSONObject profile = Optional.ofNullable(sourceObj.getJSONObject("profile")).orElse(new JSONObject());
        JSONObject verifiedDetail = Optional.ofNullable(profile.getJSONObject("verifiedDetail")).orElse(new JSONObject());

        update.setVerifyInfo(Optional.ofNullable(verifiedDetail.getString("description")).orElse(EMPTY));
        update.setVerifyTypeV1(Optional.ofNullable(verifiedDetail.getString("type")).orElse(EMPTY));
        update.setVerifyTypeV2(Optional.ofNullable(sourceObj.getString("accountVerified")).orElse(EMPTY));

        return update;
    }
}
