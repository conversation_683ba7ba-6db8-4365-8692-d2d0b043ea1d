package cn.newrank.niop.data.biz.callback.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.biz.callback.util.CallbackRedirectUtil;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.dao.CallbackRedirectRetryDao;
import cn.newrank.niop.data.biz.pojo.enums.RetryStatus;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.util.ExceptionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class CallbackRedirectRetryService extends BaseRedirectService<CallbackRetry> {

    private final CallbackRedirectRetryDao redirectRetryDao;

    public CallbackRedirectRetryService(CallbackService callbackService,
                                        CommonRedirectService commonRedirectService,
                                        SubscriberConfigService subscriberConfigService,
                                        CallbackRedirectRetryDao redirectRetryDao) {
        super(callbackService, commonRedirectService, subscriberConfigService);
        this.redirectRetryDao = redirectRetryDao;
    }

    public void retryCallbackRedirect() {
        List<CallbackRetry> retries = redirectRetryDao.listByRetryStatus(RetryStatus.PROCESS, 1000);
        if (CollUtil.isEmpty(retries)) {
            XxlJobLogger.log("重试队列 没有回调重定向");
            return;
        }

        for (CallbackRetry retry : retries) {
            // 反序列化载体
            ConsumerRecordCarrier carrier = retry.getConsumerRecordCarrier();
            ConsumerRecord<String, String> consumerRecord = carrier.convert();
            retry.setConsumerRecord(consumerRecord);
        }

        // 重试回调重定向
        List<CallbackRetry> retryList = this.redirectCallback(retries);

        // 完成重试列表
        List<CallbackRetry> finishRetryList = new ArrayList<>();

        // 所有重试都完成
        if (CollUtil.isEmpty(retryList)) {
            for (CallbackRetry retry : retries) {
                retry.setRetryStatus(RetryStatus.SUCCESS);
            }
            finishRetryList.addAll(retries);
        } else {
            // 有未完成的重试
            Set<String> retryTaskIdSet = retryList.stream()
                    .map(CallbackRetry::getSourceKey)
                    .collect(Collectors.toSet());

            for (CallbackRetry retry : retries) {
                String taskId = retry.getSourceKey();
                if (!retryTaskIdSet.contains(taskId)) {
                    // 不在重试列表回调重试完成
                    retry.setRetryStatus(RetryStatus.SUCCESS);
                    finishRetryList.add(retry);
                }
            }
        }

        if (CollUtil.isNotEmpty(finishRetryList)) {
            redirectRetryDao.saveBatch(finishRetryList);
        }

        XxlJobLogger.log("重试队列 回调重定向 完成 {} 个 重入队 {} 个",
                retries.size() - retryList.size(), retryList.size());
    }

    @Override
    protected CallbackRetry newCallbackRedirect(CallbackRetry snapshot, JSONObject param, JSONObject result, SubSourceType sourceType, List<CallbackRetry> retryList) {
        try {
            // 结果为空异常重试
            if (result == null) {
                if (hasResult(snapshot)) {
                    log.error("ability result not found: {}", snapshot);

                    snapshot.setSourceType(sourceType.getJsonValue());
                    snapshot.setErrorInfo("查询任务结果为空");
                    retryList.add(snapshot);

                    return null;
                } else {
                    result = new JSONObject();
                }
            }

            final CallbackRetry retry = new CallbackRetry();

            // 构建结果
            CallbackRedirectUtil.buildRedirectResult(retry, snapshot, param, result, sourceType);

            return retry;
        } catch (Exception e) {
            // 结果解析异常重试
            log.error("回调重定向-原始结果解析异常异常: {}", ExceptionUtil.getStackTrace(e));

            snapshot.setSourceType(sourceType.getJsonValue());
            snapshot.setErrorInfo("原始结果解析异常: " + e.getMessage());
            retryList.add(snapshot);

            return null;
        }
    }

}
