package cn.newrank.niop.data.biz.export.handler;

import com.alibaba.fastjson2.JSONObject;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class BaseExportResult implements ExportResult {

    /**
     * 结果任务id
     */
    protected String resultTaskId;

    /**
     * 数据列表
     */
    protected List<JSONObject> dataList;

    protected BaseExportResult(String resultTaskId, List<JSONObject> dataList) {
        this.resultTaskId = resultTaskId;
        this.dataList = dataList;
    }

    @Override
    public String getResultTaskId() {
        return this.resultTaskId;
    }

    @Override
    public List<JSONObject> getDataList() {
        return this.dataList;
    }

}
