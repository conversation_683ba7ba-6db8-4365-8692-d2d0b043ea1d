package cn.newrank.niop.data.biz.export.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

@Data
public class DataExportParamFilePreview {

    /**
     * 导数id
     */
    @NotBlank(message = "导数id不能为空")
    private String exportId;

    /**
     * 参数文件地址
     */
    @URL(protocol = "https", message = "参数文件地址异常")
    private String paramFile;

}
