package cn.newrank.niop.data.biz.biz.xhs.schedule;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.service.haihui.XhsHaihuiLdmService;
import cn.newrank.niop.data.biz.biz.xhs.service.haihui.XhsHaihuiLdmSummaryService;
import cn.newrank.niop.data.biz.component.sync.HistorySyncContext;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/21 17:12:11
 */
@RestController
@RequestMapping("/xhsHaihuiSchedule")
@AllArgsConstructor
public class XhsHaiHuiSchedule {

    private final XhsHaihuiLdmService xhsHaihuiLdmService;
    /**
     * 同步ES数据到lindorm
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/syncHaihuiEsOpusData")
    @XxlJob("syncHaiHuiEsOpusData")
    public ReturnT<String> syncHaiHuiEsOpusData(String param) {
        xhsHaihuiLdmService.runSchedule(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步ES数据到lindorm
     *
     * @param param 参数
     * @return 执行结果
     */
    @GetMapping("/syncUser")
    @XxlJob("syncUser")
    public ReturnT<String> syncUser(String param) {
        xhsHaihuiLdmService.runScheduleUser(param);
        return ReturnT.SUCCESS;
    }
}
