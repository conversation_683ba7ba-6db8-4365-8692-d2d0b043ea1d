package cn.newrank.niop.data.biz.biz.ks.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * 快手作品维度表
 *
 * @TableName dc_dim_ks_opus
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DimKsOpus extends StorageEntity {
    /**
     * 原始作品id
     */
    private String originalPhotoId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 发布时间分区(yyyyMM)
     */
    private String ds;

    /**
     * 作品id
     */
    private String photoId;

    /**
     * 用户基本信息
     */
    private String userInfo;

    /**
     * 标题/描述
     */
    private String caption;

    /**
     * 发布时间
     */
    private Timestamp publishTime;

    /**
     * 作品类型
     */
    private Integer mType;

    /**
     * 视频时长
     */
    private Integer duration;

    /**
     * 流信息(视频高宽等信息)
     */
    private String streamManifest;

    /**
     * 封面图片类型
     */
    private String coverType;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 参与话题列表
     */
    private Object anaTag;

    /**
     * 播放数
     */
    private Long viewNum;

    /**
     * 获赞数
     */
    private Long likeNum;

    /**
     * 评论数
     */
    private Long commentNum;

    /**
     * 分享数
     */
    private Long shareNum;

    /**
     * 是否带货
     */
    private Boolean isPromotion;

    /**
     * 背景音乐信息
     */
    private String soundTrack;

    /**
     * 额外参数信息
     */
    private String extParams;

    /**
     *
     */
    private Timestamp createTime;

    /**
     *
     */
    private Timestamp updateTime;
    private String music;
    private String shareInfo;
    private String mainMvUrls;
    private String subMtype;

    @Override
    public String identifier() {
        return originalPhotoId;
    }
}