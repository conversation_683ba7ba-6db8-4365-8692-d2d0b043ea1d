package cn.newrank.niop.data.biz.biz.dy.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_buyin_shop
 */
@Data
@Accessors(chain = true)
public class DyBuyinShop implements Serializable {
    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String roomId;

    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String promotionId;

    /**
     *
     */
    private String name;

    /**
     *
     */
    private String cover;

    /**
     *
     */
    private String price;

    /**
     *
     */
    private String gmv;

    /**
     *
     */
    private String saleNum;

    /**
     *
     */
    private String replay;


    private String deviceName;

    private String partitionOffset;

    private static final long serialVersionUID = 1L;


}