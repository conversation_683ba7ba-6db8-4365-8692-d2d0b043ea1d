package cn.newrank.niop.data.biz.biz.xhs.pojo.exp;

import cn.newrank.niop.data.biz.biz.xhs.enums.XhsExpTaskEnum;
import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/3/31 14:24:03
 */
@Data
public class XhsExpAtspTask {

    private static final String FIRST_PAGE_TASK_ID = "first_page";
    private static final String FIRST_PAGE = "1";

    /**
     * 话题任务ID
     */
    String taskId;
    /**
     * 话题ID
     */
    String parentId;
    /**
     * 执行日期
     */
    LocalDate execDate;
    /**
     * 任务参数
     */
    String param;
    /**
     * 任务状态
     */
    Integer taskStatus;
    /**
     * 下次执行时间
     */
    LocalDateTime nextExecTime;
    /**
     * 当前页码
     */
    Integer page;
    /**
     * 业务返回码
     */
    Integer bizCode;
    /**
     * 业务返回信息
     */
    String bizMsg;

    /**
     * 失败任务重新创建
     */
    Boolean isFailReCreateTask;

    /**
     * 权重
     */
    Integer topicWeight;

    public static XhsExpAtspTask initTask(String parentId,
                                          String topicId,
                                          LocalDate execTime,
                                          String cursor,
                                          Integer page,
                                          Integer topicWeight) {
        XhsExpAtspTask task = new XhsExpAtspTask();
        task.setTaskId(getTaskId(parentId,cursor));
        task.setParentId(parentId);
        task.setParam(JSON.toJSONString(XhsExpAtspParam.initParam(topicId,cursor)));
        task.setPage(page);
        task.setTopicWeight(topicWeight);
        // 用于分表
        task.setExecDate(execTime);
        task.setTaskStatus(Integer.valueOf(XhsExpTaskEnum.CREATE.getDbCode()));
        task.setNextExecTime(LocalDateTime.now());
        return task;
    }

    public static String getTaskId(String parentId, String cursor) {
        return parentId + "-" + cursor;
    }
}
