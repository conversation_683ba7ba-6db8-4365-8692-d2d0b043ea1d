package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceMapper;
import cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceTagMapper;
import cn.newrank.niop.data.biz.pojo.param.DynamicInterfacePageQuery;
import cn.newrank.niop.data.biz.pojo.po.DynamicInterfacePo;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.util.U;
import cn.newrank.niop.web.model.PageView;
import com.alibaba.nacos.api.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:51
 */
@Slf4j
@Component
public class DynamicInterfaceDao {

    private final DynamicInterfaceMapper dynamicInterfaceMapper;
    private final DynamicInterfaceTagMapper dynamicInterfaceTagMapper;
    private final RedissonClient redissonClient;

    public DynamicInterfaceDao(DynamicInterfaceMapper dynamicInterfaceMapper,
                               DynamicInterfaceTagMapper dynamicInterfaceTagMapper,
                               RedissonClient redissonClient) {
        this.dynamicInterfaceMapper = dynamicInterfaceMapper;
        this.dynamicInterfaceTagMapper = dynamicInterfaceTagMapper;
        this.redissonClient = redissonClient;
    }


    public void save(DynamicInterface dynamicInterface) {
        final DynamicInterfacePo interfacePo = DynamicInterfacePo.fromDto(dynamicInterface);

        checkNameExisted(interfacePo);

        try {
            dynamicInterfaceMapper.save(interfacePo);

            getCachedInterface(interfacePo.getInterfaceId()).delete();
        } catch (Exception e) {
            throw createDbError(e, "保存动态接口失败");
        }
    }

    private DynamicInterface getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        final DynamicInterfacePo interfacePo = dynamicInterfaceMapper.getByName(name);
        return interfacePo == null ? null : interfacePo.toDto();
    }

    public DynamicInterface get(String interfaceId) {
        if (StringUtils.isBlank(interfaceId)) {
            return null;
        }

        final RBucket<String> cachedInterface = getCachedInterface(interfaceId);

        if (cachedInterface.isExists()) {
            return DynamicInterface.fromJson(cachedInterface.get());
        }

        final DynamicInterfacePo interfacePo = dynamicInterfaceMapper.get(interfaceId);
        if (interfacePo == null) {
            cachedInterface.set("");
            cachedInterface.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterface dynamicInterface = interfacePo.toDto();

        dynamicInterface.setTags(dynamicInterfaceTagMapper.getAll(interfaceId));

        cachedInterface.set(dynamicInterface.toJSONString());
        cachedInterface.expire(Duration.ofDays(15));

        return dynamicInterface;
    }

    private RBucket<String> getCachedInterface(String interfaceId) {
        return redissonClient.getBucket("dynamic:interface:" + interfaceId);
    }

    public void delete(String interfaceId) {
        try {
            dynamicInterfaceMapper.delete(interfaceId);
            getCachedInterface(interfaceId).delete();
        } catch (Exception e) {
            throw createDbError(e, "删除动态接口失败");
        }
    }

    public boolean update(DynamicInterface dynamicInterface) {
        final DynamicInterfacePo interfacePo = DynamicInterfacePo.fromDto(dynamicInterface);

        log.info("update interface: {}", interfacePo);
        final DynamicInterfacePo dbInterface = dynamicInterfaceMapper.get(interfacePo.getInterfaceId());
        if (dbInterface == null) {
            throw createParamError("接口不存在");
        }

        if (!dbInterface.getName().equals(interfacePo.getName())) {
            final DynamicInterface byName = getByName(interfacePo.getName());
            if (byName != null && !byName.getInterfaceId().equals(interfacePo.getInterfaceId())) {
                throw createParamError("接口名称(name: {})已存在", byName.getName());
            }
        }

        try {
            boolean deleted = dynamicInterfaceMapper.update(interfacePo) > 0;

            getCachedInterface(interfacePo.getInterfaceId()).delete();

            return deleted;
        } catch (Exception e) {
            throw createDbError(e, "更新动态接口失败");
        }
    }

    private void checkNameExisted(DynamicInterfacePo interfacePo) {
        final DynamicInterface dbInterfaceByName = getByName(interfacePo.getName());
        if (dbInterfaceByName != null) {
            throw createParamError("接口名称(name: {})已存在", dbInterfaceByName.getName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateTags(String interfaceId, List<String> tags) {
        dynamicInterfaceTagMapper.delete(interfaceId);

        if (U.isEmpty(tags)) {
            return true;
        }

        dynamicInterfaceTagMapper.save(interfaceId, tags);

        getCachedInterface(interfaceId).delete();
        return true;
    }

    public PageView<DynamicInterface> page(DynamicInterfacePageQuery pageQuery) {

        return PageView.of(dynamicInterfaceMapper.page(pageQuery.toMybatisPlusPage(), pageQuery))
                .convert(DynamicInterfacePo::toDto);
    }
}
