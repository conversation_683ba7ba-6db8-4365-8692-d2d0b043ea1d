package cn.newrank.niop.data.biz.biz.ds.mapper;

import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.po.DsEsSyncRecordPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DsMapper {

    void insert(DsEsSyncRecordPo dsEsSyncRecord);

    void update(@Param("platformType") String platformType, @Param("lastSyncTime") String lastSyncTime);

    DsEsSyncRecordPo get(@Param("platformType") PlatformType platformType);

    void updateCheckCount(DsEsSyncRecordPo dsEsSyncRecord);

    DsEsSyncRecordPo get(@Param("platformType") RankPlatformType platformType);

}




