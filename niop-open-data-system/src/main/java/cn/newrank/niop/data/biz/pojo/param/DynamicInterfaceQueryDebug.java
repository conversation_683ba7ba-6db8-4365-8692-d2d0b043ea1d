package cn.newrank.niop.data.biz.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceQueryDebug {
    /**
     * 接口ID
     */
    @NotBlank(message = "接口ID(interfaceId)不能为空")
    String interfaceId;

    /**
     * debug参数
     */
    private Map<String, Object> debugParams;

}