package cn.newrank.niop.data.biz.subscriber.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/26 11:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CbSubCache {
    public static final CbSubCache NOOP = new CbSubCache("0", "0", Set.of());
    /**
     * 回调源ID
     */
    String cbId;
    /**
     * 应用ID
     */
    @Deprecated(forRemoval = true)
    String appId;
    /**
     * 应用ID列表
     */
    Set<String> appIds = new HashSet<>();

}
