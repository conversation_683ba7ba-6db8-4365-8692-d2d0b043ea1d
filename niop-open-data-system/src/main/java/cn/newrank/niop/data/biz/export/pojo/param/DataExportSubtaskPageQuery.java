package cn.newrank.niop.data.biz.export.pojo.param;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportSubtaskStatus;
import cn.newrank.niop.web.model.PageQuery;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DataExportSubtaskPageQuery extends PageQuery {

    /**
     * 导数任务id
     */
    @NotBlank(message = "导数任务id不能为空")
    private String exportTaskId;

    /**
     * 子任务状态
     *
     * @see DataExportSubtaskStatus
     */
    private String subtaskStatus;

}
