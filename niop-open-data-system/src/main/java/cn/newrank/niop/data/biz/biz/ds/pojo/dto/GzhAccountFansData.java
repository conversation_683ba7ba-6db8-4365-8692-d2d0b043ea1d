package cn.newrank.niop.data.biz.biz.ds.pojo.dto;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账号-公众号粉丝数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GzhAccountFansData extends StorageEntity {
    private String accountId;
    private String fansRange;
    private String fansType;
    private String fansTime;
    private Integer followerCount;
    private String indexId;


    @Override
    public String identifier() {
        return accountId;
    }
}
