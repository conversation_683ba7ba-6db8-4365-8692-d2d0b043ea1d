package cn.newrank.niop.data.biz.pojo.vo;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.common.enums.DsType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/1 15:10
 */
@Data
public class DsConfigVo {
    /**
     * 数据源配置id
     */
    private String dcId;

    /**
     * 数据源类型
     */
    private DsType type;

    /**
     * 数据源配置名称
     */
    private String name;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;


    public static DsConfigVo fromDto(DsConfig config) {
        DsConfigVo vo = new DsConfigVo();
        vo.setDcId(config.getDcId());
        vo.setType(config.getType());
        vo.setName(config.getName());
        vo.setGmtCreate(config.getGmtCreate());
        vo.setGmtModified(config.getGmtModified());

        return vo;
    }
}
