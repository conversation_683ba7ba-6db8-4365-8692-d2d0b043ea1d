package cn.newrank.niop.data.biz.dao.mapper;

import cn.newrank.niop.data.biz.pojo.param.DsFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.DsPageQuery;
import cn.newrank.niop.data.biz.pojo.po.DatasourceConfigPo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:41
 */
@Mapper
public interface DatasourceConfigMapper {

    void insert(DatasourceConfigPo configPo);

    DatasourceConfigPo getByName(String name);

    DatasourceConfigPo get(String dcId);

    int delete(String dcId);

    int update(DatasourceConfigPo configPo);

    List<DatasourceConfigPo> fuzzyQuery(@Param("fuzzyQuery") DsFuzzyQuery fuzzyQuery);

    List<DatasourceConfigPo> listAll();

    Page<DatasourceConfigPo> page(@Param("mpPage") Page<DatasourceConfigPo> mpPage,
                                  @Param("pageQuery") DsPageQuery pageQuery);

    List<DatasourceConfigPo> list(@Param("dcIds") Set<String> dcIds);

    List<DatasourceConfigPo> findByTypes(@Param("types") Set<String> types);
}




