package cn.newrank.niop.data.util;

import cn.hutool.core.util.IdUtil;
import lombok.experimental.UtilityClass;

/**
 * 项目内部id生成工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/12/26 14:30
 */
@UtilityClass
public class Ids {

    /**
     * 生成随机id
     *
     * @param length 长度
     * @return 长度为 {@code length}的随机id
     */
    public static String create(int length) {
        return create(length, true);
    }


    /**
     * 生成随机id
     *
     * @param length    长度
     * @param upperCase 设置字符大小写
     * @return 长度为 {@code length}的随机id
     */
    public static String create(int length, boolean upperCase) {
        final String id = IdUtil.simpleUUID().substring(0, length);
        return upperCase ? id.toUpperCase() : id.toLowerCase();
    }
}
