package cn.newrank.niop.data.biz.biz.xhs.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2025/8/27 16:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XhsOpusExpand extends StorageEntity {
    @JSONField(alternateNames = {"aweme_id"})
    private String opusId;
    private String dataType;
    @JSONField(alternateNames = {"account_id"})
    private String userId;
    @JSONField(alternateNames = {"acq_time"})
    private String acqTime;
    @JSONField(alternateNames = {"public_time"})
    private String publicTime;
    @JSONField(alternateNames = {"ana_time"})
    private String anaTime;
    @JSONField(alternateNames = {"nickname"})
    private String userName;
    @JSONField(alternateNames = {"avatar"})
    private String userAvatar;
    @JSONField(alternateNames = {"cover_url"})
    private String cover;
    @JSONField(alternateNames = {"like_count"})
    private Integer likeCount;
    @JSONField(alternateNames = {"comment_count"})
    private Integer commentCount;
    @JSONField(alternateNames = {"collect_count"})
    private Integer collectCount;
    @JSONField(alternateNames = {"aweme_type"})
    private String opusType;
    @JSONField(alternateNames = {"aweme_title"})
    private String title;

    @Override
    public String identifier() {
        return opusId;
    }

    @Data
    public static class Message {
        /**
         * 作品ID
         */
        @JSONField(alternateNames = {"logic_id"})
        private String opusId;
        /**
         * 数据来源
         */
        @JSONField(alternateNames = {"data_type"})
        private String dataType;
        /**
         * 用户ID
         */
        @JSONField(alternateNames = {"normal_id"})
        private String userId;
        /**
         * 采集时间
         */
        @JSONField(alternateNames = {"gmt_create"})
        private String gmtCreate;
        /**
         * 详情信息
         */
        @JSONField(alternateNames = {"json_details"})
        private XhsOpusExpand detail;
    }

    public static void main(String[] args) {
        String json = """
                {
                	"logic_id": "5f0316b0000000000101c4f6",
                	"data_type":"web_topic_search" ,
                	"normal_id": "5c3a5705000000000703bc58",
                	"gmt_create": "2025-08-26 14:12:11",
                	"json_details": {
                		"aweme_id": "5d511297000000002602332b",
                		"data_type": "web_topic_search",
                		"account_id": "59b3829850c4b4197d115edf",
                		"public_time": "2019-08-12 15:17:00",
                		"acq_time": "2025-08-26 14:33:40",
                		"ana_time": "2025-08-26 14:33:40",
                		"nickname": "运动薯",
                		"avatar": "https://sns-avatar-qc.xhscdn.com/avatar/613037363707e365c22b6b83.jpg?imageView2/2/w/80/format/jpg",
                		"cover_url": "https://sns-webpic.xhscdn.com/9641dd56-60bc-5f64-bf10-96d1a10b4c74?imageView2/2/w/540/format/jpg", 
                		"like_count": "3373",
                		"comment_count": "0",
                		"collect_count": "0", 
                		"aweme_type": "normal", 
                		"aweme_title": "＃我的运动日常|在这个夏天动起来吧！" 
                	}
                }
                """;
        XhsOpusExpand.Message message = JSON.parseObject(json).to(XhsOpusExpand.Message.class);
        XhsOpusExpand opus = message.getDetail();
        opus.setDataType(message.getDataType());
    }
}
