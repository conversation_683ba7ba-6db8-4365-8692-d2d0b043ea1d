package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

public enum DataExportSubtaskStatus implements BizEnum {

    /**
     * 导数子任务状态
     */
    FAILED("-1", "失败"),
    NOT_SUBMITTED("0", "未提交"),
    RUNNING("1", "执行中"),
    SUCCEED("2", "成功"),
    ;

    private final String code;
    private final String description;

    DataExportSubtaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

}
