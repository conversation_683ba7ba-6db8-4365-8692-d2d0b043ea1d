package cn.newrank.niop.data.biz.export.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import cn.newrank.niop.data.biz.export.mapper.DataExportTableStorageMapper;
import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import cn.newrank.niop.data.biz.export.service.DataExportTableStorageService;
import java.util.Collections;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class DataExportTableStorageServiceImpl implements DataExportTableStorageService {

    private final DataExportTableStorageMapper exportTableStorageMapper;

    public DataExportTableStorageServiceImpl(DataExportTableStorageMapper exportTableStorageMapper) {
        this.exportTableStorageMapper = exportTableStorageMapper;
    }

    @Override
    public List<DaTaExportTableStorage> queryTableStorageContents(String taskId, String uniqueId) {
        if (CharSequenceUtil.isBlank(taskId)) {
            return Collections.emptyList();
        }
        return exportTableStorageMapper.queryTableStorageContents(taskId, uniqueId, DataExportConstant.CURSOR_QUERY_SIZE);
    }

    @Override
    public Boolean existsData(List<String> taskIds) {
        return CollUtil.isNotEmpty(taskIds) && exportTableStorageMapper.existsData(taskIds);
    }

}
