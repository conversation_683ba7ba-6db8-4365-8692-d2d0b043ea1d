package cn.newrank.niop.data.biz.biz.ks.service;

import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusMapper;
import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusSltMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.util.U;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

/**
 * 基础数据存储
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:50
 */
@Service
@Log4j2
public class LmKsOpusBasicService extends DefaultHistorySynchronizer<String> implements StorageBizService<LmKsOpus> {

    public static final String START_SCROLL_QUERY = """
            GET search_kuaishou_photo/_search?scroll=10m
            {
              "_source": [
                "anaDel",
                "anaTags",
                "awemePortrait",
                "awemePortraitUpdateTime",
                "baseInsertTime",
                "caption",
                "collectCount",
                "commentCount",
                "cover",
                "coverType",
                "duration",
                "extParams",
                "forwardCount",
                "headurls",
                "headurl",
                "imageUrls",
                "insertTime",
                "isHotAweme",
                "isLowHot",
                "isPromotion",
                "kwaiId",
                "likeCount",
                "mainMvUrls",
                "merchant",
                "mtype",
                "music",
                "ownerCount",
                "photoId",
                "poi",
                "screenType",
                "shareCount",
                "shareInfo",
                "soundTrack",
                "standardSerial",
                "streamManifest",
                "subMtype",
                "time",
                "type",
                "unlikeCount",
                "updateTime",
                "userFan",
                "userId",
                "userName",
                "userType",
                "verifiedDetail",
                "viewCount",
                "vpf",
                "awemeAcqDataType",
                "anaTime",
                "awemePortraitAcqTime",
                "flinkAwemeSyncTime",
                "mcAwemeSyncTime",
                "inDaily",
                "originalPhotoId",
                "searchTags"
              ],
              "size": 1000,
              "query": {
                "bool": {
                  "filter": [
                    {
                      "range": {
                        "time": {
                          "gte": #{startTime},
                          "lt":  #{endTime}
                        }
                      }
                    }
                  ]
                }
              }
            }
            """;
    public static final String SCROLL_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "10m",
                "scroll_id" : #{scrollId}
            }
            """;
    static final Set<String> MUSIC_KEYS = Set.of("id", "audioUrls", "artist", "name");
    static final Set<String> SOUND_TRACK_KEYS = Set.of("id", "audioUrls", "name");
    static final Set<String> STREAM_MANIFEST_TRACK_KEYS = Set.of("videoId", "adaptationSet");
    final Datasource es;
    private final LmKsOpusMapper lmKsOpusMapper;
    private final LmKsOpusSltMapper lmKsOpusSltMapper;

    public LmKsOpusBasicService(LmKsOpusMapper lmKsOpusMapper,
                                DsConfigManager dsConfigManager,
                                RedissonClient redissonClient,
                                LmKsOpusSltMapper lmKsOpusSltMapper) {
        super("ks_es_sync", redissonClient, 2);
        this.lmKsOpusMapper = lmKsOpusMapper;
        this.es = EsFactory.DEFAULT.create(dsConfigManager.chooseKsEsConfig());
        this.lmKsOpusSltMapper = lmKsOpusSltMapper;
    }


    @Override
    public void storeBatch(List<LmKsOpus> items) {
        if (items.isEmpty()) {
            return;
        }
        final String now = LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER);
        final List<LmKsOpus> ksOpuses = lmKsOpusMapper.list(U.toList(items, LmKsOpus::getPhotoId));
        final Map<String, LmKsOpus> map = U.toMap(ksOpuses,
                LmKsOpus::getPhotoId,
                Function.identity());
        items.forEach(item -> {
            item.setGmtTime(now);
            item.copyIfExistNull(map.get(item.getPhotoId()));
            newFiledInfo(item);
        });


        lmKsOpusMapper.storeBasicBatch(items);
        saveKsSltOpus(items);
    }

    private void saveKsSltOpus(List<LmKsOpus> items) {
        List<LmKsOpus> filterSltItems = items.stream()
                .filter(opus -> {
                    final String caption = opus.getCaption();
                    if (Strings.isNotBlank(caption)) {
                        return caption.contains("信用卡");
                    }
                    return false;
                }).toList();
        if (CollectionUtils.isNotEmpty(filterSltItems)) {
            lmKsOpusSltMapper.storeBasicBatch(filterSltItems);
        }
    }

    private static void newFiledInfo(LmKsOpus item) {
        item.setHasMerchant(StringUtils.isNotBlank(item.getMerchant()));
        final String merchant = item.getMerchant();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(merchant)) {
            final JSONObject merchantJson = JSON.parseObject(merchant);
            item.setMerchantAdType(merchantJson.getString("adType"));
        }
        final String music = item.getMusic();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(music)) {
            item.setMusicId(JSON.parseObject(music).getString("id"));
        }

        final String soundTrack = item.getSoundTrack();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(soundTrack)) {
            item.setSoundTrackId(JSON.parseObject(soundTrack).getString("id"));
        }

        final String anaTags = item.getAnaTags();
        if (anaTags != null) {
            item.setSearchTags(anaTags);
        }

        final String userId = item.getUserId();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(userId)) {
            item.setUserIdL(Long.parseLong(userId));
        }
    }

    @Override
    public LmKsOpus castOf(JSONObject item) {
        final JSONObject music = item.getJSONObject("music");
        if (music != null) {
            music.keySet().removeIf(key -> !MUSIC_KEYS.contains(key));
        }

        final JSONObject soundTrack = item.getJSONObject("soundTrack");
        if (soundTrack != null) {
            soundTrack.keySet().removeIf(key -> !SOUND_TRACK_KEYS.contains(key));
        }

        final JSONObject streamManifest = item.getJSONObject("streamManifest");
        if (streamManifest != null) {
            streamManifest.keySet().removeIf(key -> !STREAM_MANIFEST_TRACK_KEYS.contains(key));
        }


        return item.to(LmKsOpus.class);
    }

    @Override
    protected int sync(Cursor<String> cursor) {
        final String next = cursor.getNext();
        final JSONObject resp;
        if (StringUtils.isBlank(next)) {
            final String startTime = cursor.getStartTime();
            final String endTime = cursor.getEndTime();
            final QueryBuilder startQueryBuilder = es.newQueryBuilder()
                    .template(START_SCROLL_QUERY)
                    .addParam("startTime", startTime)
                    .addParam("endTime", endTime);

            resp = es.query(startQueryBuilder).data();

            final String scrollId = resp.getString("_scroll_id");
            final Integer hitTotal = resp.getJSONObject("hits").getInteger("total");
            cursor.setNext(scrollId);
            cursor.setTotal(hitTotal);
        } else {
            final QueryBuilder queryBuilder = es.newQueryBuilder()
                    .template(SCROLL_QUERY)
                    .addParam("scrollId", next);

            resp = es.query(queryBuilder).data();
        }

        final List<LmKsOpus> dimKsOpuses = getDimKsOpuses(resp);
        storeBatch(dimKsOpuses);

        return dimKsOpuses.size();
    }

    private @NotNull List<LmKsOpus> getDimKsOpuses(JSONObject resp) {
        return resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toList(JSONObject.class)
                .stream()
                .map(json -> json.getJSONObject("_source"))
                .map(this::castOf)
                .filter(Objects::nonNull)
                .toList();
    }
}
