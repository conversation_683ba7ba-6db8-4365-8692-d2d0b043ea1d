package cn.newrank.niop.data.biz.biz.xhs.pojo.exp;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/4/10 13:58:36
 */
@Data
public class XhsExpNewTopic {
    Long id;

    /**
     * 添加时间
     */
    LocalDate addDate;

    /**
     * 主题id
     */
    String topicId;

    /**
     * 当日作品数量
     */
    Integer opusNum;



    public static XhsExpNewTopic build(String topicId,LocalDate addDate) {
        XhsExpNewTopic topic = new XhsExpNewTopic();
        topic.setAddDate(addDate);
        topic.setTopicId(topicId);
        topic.setOpusNum(1);
        return topic;
    }
}
