package cn.newrank.niop.data.biz.biz.ks.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusSltMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
@Service
public class LmKsOpusSltFilterService {
    private final DsConfigManager dsConfigManager;

    private final LmKsOpusSltMapper lmKsOpusSltMapper;
    private final EsFactory esFactory;
    public static final String XHS_ES_OPUS_SYNC = """
            GET  search_kuaishou_photo/_search
            {
              "size": #{size},
              "query": {
                "bool": {
                  "must": [
                    {
                      "range": {
                        "time": {
                          "gte": "2025-05-01 00:00:00",
                          "lte": "2025-06-18 00:00:00"
                        }
                      }
                    },
                    {
                      "bool": {
                        "should": [
                          {
                            "match_phrase_prefix": {
                              "caption": "信用卡"
                            }
                          }
                        ]
                      }
                    }
                  ]
                }
              }
            }
            """;

    public LmKsOpusSltFilterService(DsConfigManager dsConfigManager, LmKsOpusSltMapper lmKsOpusSltMapper) {
        this.dsConfigManager = dsConfigManager;
        this.lmKsOpusSltMapper = lmKsOpusSltMapper;
        this.esFactory = EsFactory.DEFAULT;
    }


    /**
     * 运行定时任务
     *
     * @param param param
     */
    public void runSchedule(String param) {
        execSyncEs();
    }


    /**
     * 执行同步数据到lindorm
     */
    public void execSyncEs() {
        try (final EsFactory.Es es = esFactory.create(dsConfigManager.chooseKsEsConfig())) {
            Resp pageRes = getEsRespScroll(es);
            List<LmKsOpus> esDataList = getXhsHaiHuiOpuses(pageRes);
            if (CollectionUtil.isEmpty(esDataList)) {
                XxlJobLogger.log("获取数据为空");
                return;
            }
            saveOpusToLindorm(esDataList);
        }
        XxlJobLogger.log("同步完毕");
    }


    public List<LmKsOpus> getXhsHaiHuiOpuses(Resp pageRes) {
        List<LmKsOpus> esDataList = List.of();
        Resp.DataView dataView = pageRes.getDataView();
        if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
            JSONObject resultObj = esDataView.resp();
            esDataList = getXhsHaiHuiOpuses(resultObj);
        }
        return esDataList;
    }

    private Resp getEsRespScroll(EsFactory.Es es) {
        return es.query(es.newQueryBuilder().template(XHS_ES_OPUS_SYNC)
                .addParam("size", 2000));
    }

    private static List<LmKsOpus> getXhsHaiHuiOpuses(JSONObject resultObj) {
        return EsUtil.listHitsToEntity(resultObj, json -> {
            JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
            if (Objects.isNull(sourceObj)) {
                return null;
            }
            return JSON.parseObject(sourceObj.toJSONString(), LmKsOpus.class);
        });
    }


    /**
     * 保存数据到lindorm
     *
     * @param opusList
     */
    private void saveOpusToLindorm(List<LmKsOpus> opusList) {
        lmKsOpusSltMapper.storeBasicBatch(opusList);
    }
}
