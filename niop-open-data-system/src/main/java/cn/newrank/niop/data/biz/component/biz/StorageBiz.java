package cn.newrank.niop.data.biz.component.biz;

import cn.newrank.niop.data.biz.biz.athm.service.AthmAccountService;
import cn.newrank.niop.data.biz.biz.athm.service.AthmOpusListService;
import cn.newrank.niop.data.biz.biz.bz.service.BzLiveService;
import cn.newrank.niop.data.biz.biz.douyu.service.DouyuLiveService;
import cn.newrank.niop.data.biz.biz.ds.service.gzh.GzhEnterpriseCtInfoService;
import cn.newrank.niop.data.biz.biz.ds.service.gzh.GzhFansDataService;
import cn.newrank.niop.data.biz.biz.dy.service.DyLittleYellowCarService;
import cn.newrank.niop.data.biz.biz.huya.service.HuyaLiveService;
import cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusBasicService;
import cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusCategoryService;
import cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusExtService;
import cn.newrank.niop.data.biz.biz.ks.service.LmKsOpusHistoryService;
import cn.newrank.niop.data.biz.biz.tiktok.service.TiktokAccountService;
import cn.newrank.niop.data.biz.biz.tiktok.service.TiktokOpusService;
import cn.newrank.niop.data.biz.biz.txhx.service.TxhxSphIconService;
import cn.newrank.niop.data.biz.biz.txhx.service.TxhxSphSampleService;
import cn.newrank.niop.data.biz.biz.wx.service.WxInteractionService;
import cn.newrank.niop.data.biz.biz.wx.service.WxOpusService;
import cn.newrank.niop.data.biz.biz.wx.service.WxOpusShortLongStoreServiceImpl;
import cn.newrank.niop.data.biz.biz.wx.service.WxOpusSlAbilityStoreServiceImpl;
import cn.newrank.niop.data.biz.biz.xhs.service.DimOpusTokenService;
import cn.newrank.niop.data.biz.biz.xhs.service.XhsHotWordService;
import cn.newrank.niop.data.biz.biz.xhs.service.exp.XhsExpTaskResultServiceImpl;
import cn.newrank.niop.data.biz.biz.xhs.service.impl.*;
import cn.newrank.niop.data.biz.data.ark.service.ObjDataService;
import cn.newrank.niop.web.model.BizEnum;
import lombok.Getter;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 存储业务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 10:03
 */
@Getter
public enum StorageBiz implements BizEnum {
    /**
     * 汽车之家作品列表
     */
    ATHM_OPUS_LIST(AthmOpusListService.class, "athm_opus_list", "汽车之家作品列表"),
    /**
     * 汽车之家账号
     */
    ATHM_ACCOUNT(AthmAccountService.class, "athm_account", "汽车之家账号"),
    /**
     * 腾讯互选-视频号-样本
     */
    TXHX_SPH_AMPLE(TxhxSphSampleService.class, "txhx_sph_sample", "腾讯互选-视频号-样本"),
    /**
     * 腾讯互选-视频号-详情
     */
    TXHX_SPH_ICON(TxhxSphIconService.class, "txhx_sph_icon", "腾讯互选-视频号-详情"),
    /**
     * 微信作品
     */
    WX_OPUS(WxOpusService.class, "wx_opus", "微信作品"),
    /**
     * 抖音作品
     */
    TIKTOK_OPUS(TiktokOpusService.class, "tiktok_opus", "抖音作品"),
    /**
     * 抖音账号
     */
    TIKTOK_ACCOUNT(TiktokAccountService.class, "tiktok_account", "抖音账号"),
    /**
     * 微信互动数
     */
    WX_INTERACTION(WxInteractionService.class, "wx_interaction", "微信互动数"),

    /**
     * 斗鱼直播
     */
    DOUYU_LIVE(DouyuLiveService.class, "douyu_live", "斗鱼直播"),
    /**
     * 虎牙直播
     */
    HUYA_LIVE(HuyaLiveService.class, "huya_live", "虎牙直播"),
    /**
     * 哔哩哔哩直播
     */
    BZ_LIVE(BzLiveService.class, "bz_live", "B站直播"),

    /**
     * 快手作品
     */
    LM_KS_OPUS_BASIC(LmKsOpusBasicService.class, "lm_ks_opus_basic", "快手作品"),
    LM_KS_OPUS_EXT(LmKsOpusExtService.class, "lm_ks_opus_ext", "快手作品"),
    LM_KS_OPUS_CATEGORY(LmKsOpusCategoryService.class, "lm_ks_opus_category", "快手作品分类"),
    LM_KS_OPUS_HISTORY(LmKsOpusHistoryService.class, "lm_ks_opus_history", "快手历史同步"),

    /**
     * 小红书热词
     */
    XHS_HOT_WORD(XhsHotWordService.class, "xhs_hot_word", "小红书热词"),
    /**
     * 抖音直播小黄车
     */
    DY_LITTLE_YELLOW_CAR(DyLittleYellowCarService.class, "dy_little_yellow_car", "小红书热词"),

    /**
     * 公众号企业认证信息
     */
    GZH_ENTERPRISE_CT_INFO(GzhEnterpriseCtInfoService.class, "gzh_enterprise_info", "公众号企业认证信息"),
    /**
     * 公众号粉丝数据
     */
    GZH_FANS_DATA(GzhFansDataService.class, "gzh_fans_data", "公众号粉丝数据"),
    /**
     * 公众号作品短长链存储
     */
    WX_OPUS_SHORT_LONG_OPUS(WxOpusShortLongStoreServiceImpl.class, "wx_opus_short_long_opus", "公众号短长链"),
    /**
     * 公众号作品短长链存储
     */
    WX_OPUS_SHORT_LONG_ABILITY_OPUS(WxOpusSlAbilityStoreServiceImpl.class, "wx_opus_short_long_ability_opus", "公众号短长链"),


    /**
     * 小红书作品Token
     */
    XHS_OPUS_TOKEN(DimOpusTokenService.class, "xhs_opus_token", "小红书作品Token"),
    /**
     * 小红书大盘作品数据
     */
    XHS_DAPAN_OPUS_STORE(XhsOpusSaveServiceImpl.class, "xhs_dp_opus_store_ldm", "小红书ldm同步"),
    /**
     * 小红书话题扩量
     */
    XHS_EXP_TASK_CB(XhsExpTaskResultServiceImpl.class, "xhs_exp_ability_cb", "小红书扩量业务"),

    /**
     * 小红书话题唯一索引
     */
    XHS_TOPIC_UNIQUE_CB(XhsTopicUniIndexStoreServiceImpl.class, "xhs_topic_index_cb", "小红书话题唯一索引"),

    /**
     * 小红书二级分类补充采集
     */
    XHS_DAPAN_OPUS_CLASSIFY(XhsClassifyServiceImpl.class, "xhs_dapan_opus_classify", "小红书大盘作品二级分类采集"),
    XHS_DAPAN_OPUS_CLASSIFY_ABILITY(XhsClassifyAbilityServiceImpl.class, "xhs_dapan_opus_classify_ability", "小红书大盘作品二级分类采集能力结果"),
    /**
     * 小红书扩样本作品
     */
    XHS_OPUS_EXPAND(XhsOpusExpandServiceImpl.class, "xhs_opus_expand", "小红书扩样本作品"),

    ODS(ObjDataService.class, "ods", "数据回收数据"),
    ;

    final Class<? extends StorageBizService<? extends StorageEntity>> storageBizServiceClass;
    final String json;
    final String description;

    StorageBiz(Class<? extends StorageBizService<? extends StorageEntity>> storageBizServiceClass,
               String json,
               String description) {
        this.storageBizServiceClass = storageBizServiceClass;
        this.json = json;
        this.description = description;
    }

    public static StorageBiz ofJSONValue(String storageBiz) {
        for (StorageBiz biz : values()) {
            if (biz.json.equals(storageBiz)) {
                return biz;
            }
        }
        throw createParamError("未知StorageBiz类型-{}", storageBiz);
    }


    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return json;
    }

    @Override
    public String getDbCode() {
        return json;
    }
}
