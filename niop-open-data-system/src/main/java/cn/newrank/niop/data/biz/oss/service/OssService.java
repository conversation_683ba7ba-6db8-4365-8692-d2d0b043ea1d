package cn.newrank.niop.data.biz.oss.service;

import cn.newrank.niop.data.biz.oss.pojo.dto.OssSignature;
import com.aliyun.oss.model.OSSObject;
import java.io.File;
import java.io.IOException;
import java.time.Duration;

public interface OssService {

    /**
     * 获取 OSS 签名
     *
     * @param dir 目录, 自动拼接服务前缀
     * @param duration 时效
     * @return 签名信息
     */
    OssSignature getSignature(String dir, Duration duration);

    /**
     *
     * @param appendBaseDir 是否拼接服务前缀
     * @param dir 存放目录
     * @param duration 时效
     * @return 签名信息
     */
    OssSignature getSignature(boolean appendBaseDir, String dir, Duration duration);

    /**
     * 从文件链接中获取 OSS 数据
     *
     * @param fileUrl 文件链接
     * @return OSS 数据
     */
    OSSObject getObjectFromUrl(String fileUrl);

    /**
     * 通过文件链接删除 OSS 文件
     *
     * @param fileUrl 文件链接
     */
    void deleteObjectByUrl(String fileUrl);

    /**
     * 判断 OSS 文件是否存在
     *
     * @param fileUrl 文件链接
     * @return 是否存在
     */
    boolean existObject(String fileUrl);

    /**
     * 分片上传文件
     *
     * @param dir 存放目录, 自动拼接服务前缀
     * @param file 文件
     * @return 文件地址
     */
    String multipartUploadFile(String dir, File file) throws IOException;

    /**
     * 分片上传文件
     *
     * @param appendBaseDir 是否拼接服务前缀
     * @param dir 存放目录
     * @param file 文件
     * @return 文件地址
     */
    String multipartUploadFile(boolean appendBaseDir, String dir, File file) throws IOException;

    /**
     * 文件地址访问授权
     *
     * @param fileUrl 文件地址
     * @return 授权后的地址
     */
    String authorize(String fileUrl);

}
