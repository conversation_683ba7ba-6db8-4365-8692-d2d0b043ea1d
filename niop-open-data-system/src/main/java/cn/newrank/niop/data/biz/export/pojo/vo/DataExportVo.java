package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import cn.newrank.niop.data.util.DateTimeUtil;
import java.util.Objects;
import lombok.Data;

@Data
public class DataExportVo {

    /**
     * 导数id
     */
    private String exportId;

    /**
     * 导数类型
     *
     * @see DataExportType
     */
    private String exportType;

    /**
     * 导数源id
     */
    private String targetId;

    /**
     * 导数源名称
     */
    private String targetName;

    /**
     * 导数名称
     */
    private String exportName;

    /**
     * 导数描述
     */
    private String description;

    /**
     * 创建时间
     */
    private String gmtCreate;

    public static DataExportVo buildBy(DataExport export) {
        if (Objects.isNull(export)) {
            return null;
        }
        final DataExportVo vo = new DataExportVo();
        vo.setExportId(export.getExportId());
        vo.setExportType(export.getExportType().getDbCode());
        vo.setExportName(export.getExportName());
        vo.setTargetId(export.getTargetId());
        vo.setTargetName(export.getTargetName());
        vo.setDescription(export.getDescription());
        vo.setGmtCreate(DateTimeUtil.simplifyDateTime(export.getGmtCreate()));
        return vo;
    }

}
