package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.callback.pojo.CbPageSearchType;
import cn.newrank.niop.data.biz.dao.mapper.CbConfigMapper;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.param.CbConfigFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.CbConfigPageQuery;
import cn.newrank.niop.data.biz.pojo.po.CbConfigPo;
import cn.newrank.niop.web.model.PageView;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.newrank.niop.data.util.Iterables.*;
import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 13:56
 */
@Component
public class CbConfigDao {
    private final CbConfigMapper cbConfigMapper;

    public CbConfigDao(CbConfigMapper cbConfigMapper) {
        this.cbConfigMapper = cbConfigMapper;
    }


    public boolean update(CbConfig cbConfig) {
        final CbConfigPo cbConfigPo = cbConfig.toPo();

        final String cbId = cbConfigPo.getCbId();
        final CbConfigPo dbCallback = cbConfigMapper.get(cbId);
        if (dbCallback == null) {
            throw createDbError("回调(id: {})不存在", cbId);
        }

        validateConfig(cbConfigPo);

        try {
            return cbConfigMapper.update(cbConfigPo) > 0;
        } catch (Exception e) {
            throw createDbError(e, "更新回调失败");
        }

    }

    private void validateConfig(CbConfigPo cbConfigPo) {
        final CbConfigPo uuidPo = cbConfigMapper.getByUUID(cbConfigPo.getUuid());
        if (uuidPo == null) {
            return;
        }

        if (uuidPo.getCbId().equals(cbConfigPo.getCbId())) {
            return;
        }

        throw createDbError("存在相同配置回调(ID: {})", uuidPo.getCbId());
    }

    public CbConfig get(String cbId) {
        final CbConfigPo cbConfigPo = cbConfigMapper.get(cbId);

        return cbConfigPo == null ? null : cbConfigPo.toDto();
    }

    public boolean delete(String cbId) {
        return cbConfigMapper.delete(cbId) > 0;
    }

    public void save(CbConfig cbConfig) {
        final String name = cbConfig.getName();
        if (StringUtils.isBlank(name)) {
            throw createParamError("回调名称(name)不能为空");
        }

        final CbConfigPo cbConfigPo = cbConfig.toPo();

        validateConfig(cbConfigPo);
        try {
            cbConfigMapper.insert(cbConfigPo);
        } catch (Exception e) {
            throw createDbError(e, "保存回调失败");
        }
    }

    public List<CbConfig> fuzzyQuery(CbConfigFuzzyQuery fuzzyQuery) {
        return toList(cbConfigMapper.fuzzyQuery(fuzzyQuery), CbConfigPo::toDto);
    }

    public PageView<CbConfig> page(CbConfigPageQuery pageQuery) {
        final Page<CbConfigPo> mybatisPlusPage = pageQuery.toMybatisPlusPage();

        final CbPageSearchType searchType = CbPageSearchType.parseByCode(pageQuery.getSearchType());
        final Page<CbConfigPo> page = CbPageSearchType.CALLBACK_CONFIG == searchType
            ? cbConfigMapper.page(pageQuery, mybatisPlusPage)
            : cbConfigMapper.pageWithSubscriber(pageQuery, mybatisPlusPage);
        return PageView.of(page).convert(CbConfigPo::toDto);
    }

    public List<CbConfig> list(List<String> cbIds) {
        if (isEmpty(cbIds)) {
            return emptyList();
        }
        return toList(cbConfigMapper.list(cbIds), CbConfigPo::toDto);
    }

    public List<String> listCbIds() {
        return cbConfigMapper.listCbIds();
    }
}
