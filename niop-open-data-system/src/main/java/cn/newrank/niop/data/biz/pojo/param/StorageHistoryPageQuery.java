package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.web.model.PageQuery;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/15 9:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StorageHistoryPageQuery extends PageQuery {
    /**
     * 存储标识符
     */
    @NotBlank(message = "存储标识符(identifier)不能为空")
    String identifier;

    /**
     * 存储业务
     */
    @NotNull(message = "存储业务(storageBiz)不能为空")
    StorageBiz storageBiz;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间(startTime)不能为空")
    LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间(endTime)不能为空")
    LocalDateTime endTime;
}
