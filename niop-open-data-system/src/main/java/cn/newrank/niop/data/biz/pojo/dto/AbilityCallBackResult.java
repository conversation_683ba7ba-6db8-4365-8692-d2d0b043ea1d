package cn.newrank.niop.data.biz.pojo.dto;

import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/11
 */
@Data
public class AbilityCallBackResult {
    @JsonField("abilityId")
    private String abilityId;
    @JsonField("appId")
    private String appId;
    @JsonField("taskId")
    private String taskId;
    @JsonField("bizCode")
    private Integer bizCode;
    @JsonField("bizMsg")
    private String bizMsg;
    @JsonField("data")
    private Object data;
    @JsonField("dataType")
    private Integer dataType;
    @JsonField("finishTime")
    private String finishTime;
    @JsonField("sceneIds")
    private List<String> sceneIds;
    @JsonField("status")
    private Integer status;
    @JsonField("extra")
    private String extra;


    /**
     * 获取Data兼容 JSONArray 和 JSONObject
     * @return  数据
     */
    public List<JSONObject> getData() {
        if (data instanceof JSONArray) {
            return ((JSONArray) data).toJavaList(JSONObject.class);
        } else if (data instanceof JSONObject) {
            List<JSONObject> list = new ArrayList<>();
            list.add((JSONObject) data);
            return list;
        }
        return new ArrayList<>();
    }
}
