package cn.newrank.niop.data.biz.callback.service.impl;

import cn.newrank.niop.data.biz.callback.mapper.CbHttpTaskMapper;
import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;
import cn.newrank.niop.data.biz.callback.service.CbHttpTaskService;
import cn.newrank.niop.data.util.DateTimeUtil;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Function;

import static cn.newrank.niop.util.U.group;

/**
 *
 */
@Service
public class CbHttpTaskServiceImpl implements CbHttpTaskService {
    private final CbHttpTaskMapper cbHttpTaskMapper;

    public CbHttpTaskServiceImpl(CbHttpTaskMapper cbHttpTaskMapper) {
        this.cbHttpTaskMapper = cbHttpTaskMapper;
    }

    @Override
    public void saveBatch(List<CbHttpTask> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return;
        }

        group(tasks, task -> DateTimeUtil.format(task.getGmtCreate().toLocalDateTime(), "yyyyMMdd"), Function.identity())
                .forEach((partition, data) -> cbHttpTaskMapper.insertBatch(data, partition));
    }

    @Override
    public List<CbHttpTask> listRunning(String cbId, Timestamp startTime, int taskCount, String tablePartition) {
        return cbHttpTaskMapper.listRunning(cbId, startTime, taskCount, tablePartition);
    }

    @Override
    public void update(List<CbHttpTask> tasks, String tablePartition) {
        if (tasks == null || tasks.isEmpty()) {
            return;
        }
        cbHttpTaskMapper.updateBatch(tasks, tablePartition);
    }

    @Override
    public void createTable(LocalDate partition) {
        cbHttpTaskMapper.createTable(DateTimeUtil.format(partition, "yyyyMMdd"),
                Date.valueOf(partition),
                Date.valueOf(partition.plusDays(1)));
    }

    @Override
    public LocalDate queryCleanEndPartition() {
        final Date partition = cbHttpTaskMapper.queryCleanEndPartition();
        final LocalDate minCleanPartition = LocalDate.now().minusDays(3);
        if (partition == null) {
            return minCleanPartition;
        }

        final LocalDate queryPartition = partition.toLocalDate();
        if (queryPartition.isBefore(minCleanPartition)) {
            return queryPartition;
        }

        return minCleanPartition;
    }

    @Override
    public void dropTable(LocalDate partition) {
        cbHttpTaskMapper.dropTable(DateTimeUtil.dayOfPartition(partition));
    }

    @Override
    public List<String> queryDistributedTables() {
        return cbHttpTaskMapper.queryTables();
    }

    @Override
    public LocalDate queryStartPartition(String cbId) {
        final Date partition = cbHttpTaskMapper.queryStartPartition(cbId);
        if (partition == null) {
            return LocalDate.now();
        }

        return partition.toLocalDate();
    }

}





