package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsSourceDataTypeEnum;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsUserLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsUserLdm;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspResult;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpCallBack;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopicOpus;
import cn.newrank.niop.data.biz.component.biz.CbConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.pojo.dto.AcqMessage;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @since 2025/4/1 22:01:22
 */
@Slf4j
@Service
public class XhsExpTaskResultServiceImpl implements StorageBizService<XhsExpAtspResult> {

    private final Cache<String, String> cache;
    private final RedissonClient redissonClient;
    private final XhsExpAtspTaskService xhsExpAtspTaskService;
    private final KafkaCallback acqKafkaCallback;
    private final KafkaCallback xhsLdmKafkaCallback;
    private final XhsUserLmMapper xhsUserLmMapper;

    public XhsExpTaskResultServiceImpl(RedissonClient redissonClient,
                                       XhsExpAtspTaskService xhsExpAtspTaskService,
                                       XhsUserLmMapper xhsUserLmMapper,
                                       CbConfigManager cbConfigManager) {
        this.redissonClient = redissonClient;
        this.xhsUserLmMapper = xhsUserLmMapper;
        this.xhsExpAtspTaskService = xhsExpAtspTaskService;
        this.acqKafkaCallback = new KafkaCallback(cbConfigManager.acqCallBackConfig("xhs_account_open_reflux_source"));
        this.xhsLdmKafkaCallback = new KafkaCallback(cbConfigManager.apiCallBackConfig("niop_dc_xhs_opus_dapan_prod"));
        this.cache = buildUidCache();
    }

    private static @NotNull Cache<String, String> buildUidCache() {
        return Caffeine.newBuilder().maximumSize(5000)
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public void storeBatch(List<XhsExpAtspResult> items) {
        if (CollectionUtil.isEmpty(items)) {
            return;
        }
        xhsExpAtspTaskService.taskResultBatch(items);
        callBack(items);
    }

    /**
     * 回调结果
     * @param items 结果
     */
    private void callBack(List<XhsExpAtspResult> items) {
        final List<XhsExpTopicOpus> opuses = items.stream()
                .filter(item -> CollectionUtil.isNotEmpty(item.getList()))
                .flatMap(item -> {
                    // 需要任务信息的回调
                    callBackOpusToLdm(item);
                    return item.getList().stream();
                })
                .collect(Collectors.toList());
        // 不需要任务信息的回调
        callBackUserToAcq(opuses);
    }

    /**
     * 回调作品信息到ldm
     *
     * @param result  任务获取结果
     */
    private void callBackOpusToLdm(XhsExpAtspResult result) {
        final List<XhsExpTopicOpus> opuses = result.getList();
        for (XhsExpTopicOpus opus : opuses) {
            final String opusId = opus.getOpusId();
            if (Strings.isBlank(opusId)) {
                log.error("opusId is null");
                return;
            }
            // 回调作品
            xhsLdmKafkaCallback.callback(opusId, JSON.toJSONString(getXhsLdmCallBack(result, opus)));
        }
    }

    /**
     * 获取ldm回调信息
     * @param result 任务结果
     * @param opus  作品
     * @return 回调信息
     */
    private static @NotNull AcqMessage getXhsLdmCallBack(XhsExpAtspResult result, XhsExpTopicOpus opus) {
        final String now = LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER);
        final String finishTime = result.getFinishTime().format(DatePattern.NORM_DATETIME_FORMATTER);
        AcqMessage callBack = new AcqMessage();
        callBack.setData_type(XhsSourceDataTypeEnum.ABILITY_TOPIC.getJsonValue());
        // 消费时间
        callBack.setGmt_time(now);
        callBack.setEvent_time(now);
        // 任务完成时间
        callBack.setAna_time(finishTime);
        callBack.setLogic_id(opus.getOpusId());
        callBack.setNormal_id(opus.getOpusId());
        callBack.setJson_details(JSON.toJSONString(opus));
        return callBack;
    }


    /**
     * 回调用户信息
     *
     * @param opuses 作品
     */
    private void callBackUserToAcq(List<XhsExpTopicOpus> opuses) {
        final Map<String, XhsExpCallBack> callBackMap = new HashMap<>(16);
        buildAndFilterCallBack(opuses, callBackMap);
        if (callBackMap.isEmpty()) {
            return;
        }
        final Map<String, String> existUser = getExistUserMap(callBackMap);
        final String time = LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER);
        final List<XhsUserLdm> users = getXhsUserLdmsAndCallBack(callBackMap, existUser, time);
        if (CollectionUtil.isEmpty(users)) {
            return;
        }
        xhsUserLmMapper.storeBatch(users);
    }

    private void buildAndFilterCallBack(List<XhsExpTopicOpus> opuses, Map<String, XhsExpCallBack> callBackMap) {
        opuses.forEach(opus -> {
            final String uid = opus.getUid();
            if (Strings.isBlank(uid)) {
                return;
            }
            if (Objects.nonNull(callBackMap.get(uid))) {
                return;
            }
            if (Objects.nonNull(cache.getIfPresent(uid))) {
                return;
            }
            XhsExpCallBack callBack = new XhsExpCallBack();
            callBack.setData_type("account_base_open_reflux");
            callBack.setGmt_create(LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER));
            callBack.setLogic_id(uid);
            callBack.setJson_details(getUserJsonDetail(opus));
            callBackMap.put(uid, callBack);
            cache.put(uid, uid);
        });
    }

    private @NotNull Map<String, String> getExistUserMap(Map<String, XhsExpCallBack> callBackMap) {
        return Optional.ofNullable(xhsUserLmMapper.getExistUserIds(callBackMap.keySet()))
                .map(list -> list.stream().collect(Collectors.toMap(XhsUserLdm::getUserId, XhsUserLdm::getUpdateTime)))
                .orElse(Map.of());
    }


    /**
     * 回调并返回列表
     *
     * @param callBackMap
     * @param existUser
     * @param time
     * @return
     */
    private @NotNull List<XhsUserLdm> getXhsUserLdmsAndCallBack(Map<String, XhsExpCallBack> callBackMap, Map<String, String> existUser, String time) {
        return callBackMap.values().stream().map(call -> {
            final String updateTime = existUser.get(call.getLogic_id());
            //不存在则回调
            if (Objects.nonNull(updateTime)) {
                LocalDateTime dateTime = LocalDateTime.parse(updateTime, DatePattern.NORM_DATETIME_FORMATTER);
                // 30之内不回调
                if (dateTime.isAfter(LocalDateTime.now().minusDays(30))) {
                    return null;
                }
            }
            acqKafkaCallback.callback(JSON.toJSONString(call));
            XhsUserLdm user = new XhsUserLdm();
            user.setUserId(call.getLogic_id());
            user.setUpdateTime(time);
            return user;
        }).filter(Objects::nonNull).toList();
    }

    @Override
    public XhsExpAtspResult castOf(JSONObject item) {
        if (Objects.isNull(item)) {
            return null;
        }
        JSONObject data = item.getJSONObject("data");
        if (Objects.isNull(data)) {
            return null;
        }
        String finishTime = data.getString("finishTime");
        LocalDateTime finishTimeDate = Optional.ofNullable(finishTime)
                .map(timeStr -> LocalDateTime.parse(timeStr.substring(0, NORM_DATETIME_PATTERN.length()), DatePattern.NORM_DATETIME_FORMATTER))
                .orElse(LocalDateTime.now());

        XhsExpAtspResult result = new XhsExpAtspResult();
        result.setTaskId(data.getString("taskId"));
        result.setBizCode(data.getInteger("bizCode"));
        result.setBizMsg(data.getString("bizMsg"));
        result.setDataType(data.getInteger("dataType"));
        result.setFinishTime(finishTimeDate);
        JSONObject abilityData = data.getJSONObject("data");
        if (Objects.nonNull(abilityData)) {
            result.setHasMore(Optional.ofNullable(abilityData.getBoolean("hasMore")).orElse(false));
            result.setCursor(Optional.ofNullable(abilityData.getString("cursor")).orElse(""));
            result.setList(getXhsExpTopicOpusList(abilityData));
        }
        return result;
    }

    private static @NotNull JSONObject getUserJsonDetail(XhsExpTopicOpus opus) {
        JSONObject object = new JSONObject();
        object.put("account_id", opus.getUid());
        object.put("nickname", opus.getNickname());
        object.put("avatar", opus.getImages());
        return object;
    }

    /**
     * 获取作品列表
     *
     * @param item abilityData
     * @return XhsExpTopicOpus
     */
    public static List<XhsExpTopicOpus> getXhsExpTopicOpusList(JSONObject item) {
        return Optional.ofNullable(item)
                .map(innerData -> innerData.getJSONArray("list"))
                .filter(CollectionUtil::isNotEmpty)
                .map(jsonArray -> jsonArray.stream()
                        .filter(Objects::nonNull)
                        .map(ob -> (JSONObject) ob)
                        .map(XhsExpTopicOpus::castOf)
                        .filter(Objects::nonNull)
                        .filter(opus -> Strings.isNotBlank(opus.getOpusId()))
                        .toList()
                )
                .orElse(Collections.emptyList());
    }
}
