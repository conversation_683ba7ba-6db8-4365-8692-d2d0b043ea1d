package cn.newrank.niop.data.biz.export.service.impl;

import cn.newrank.niop.data.biz.export.handler.FileUploadResult;
import cn.newrank.niop.data.biz.export.mapper.DataExportResultFileMapper;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import cn.newrank.niop.data.biz.export.service.DataExportResultFileService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class DataExportResultFileServiceImpl implements DataExportResultFileService {

    private final DataExportResultFileMapper dataExportResultFileMapper;

    private final OssService ossService;

    public DataExportResultFileServiceImpl(DataExportResultFileMapper dataExportResultFileMapper,
                                           OssService ossService) {
        this.dataExportResultFileMapper = dataExportResultFileMapper;
        this.ossService = ossService;
    }

    @Override
    public void save(FileUploadResult fileUploadResult) {
        dataExportResultFileMapper.insert(DataExportResultFile.of(fileUploadResult));
    }

    @Override
    public List<DataExportResultFile> listResultFiles(String exportTaskId, boolean authorize) {
        final List<DataExportResultFile> resultFiles = dataExportResultFileMapper.listResultFiles(exportTaskId);
        if (authorize) {
            resultFiles.forEach(file -> file.setDownloadUrl(ossService.authorize(file.getDownloadUrl())));
        }
        return resultFiles;
    }

}
