package cn.newrank.niop.data.biz.callback.controller;

import cn.newrank.niop.data.biz.callback.event.CbScheduleEvent;
import jakarta.validation.Valid;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/10 15:16
 */
@Validated
@RestController
@RequestMapping("callback")
public class CallbackController implements ApplicationEventPublisherAware {
    ApplicationEventPublisher eventPublisher;

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    /**
     * 发送调度事件
     */
    @PostMapping("schedule-event")
    public boolean sendSchedule(@Valid @RequestBody CbScheduleEvent cbScheduleEvent) {
        eventPublisher.publishEvent(cbScheduleEvent);
        return true;
    }
}
