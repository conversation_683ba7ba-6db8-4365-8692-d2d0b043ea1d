package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 10:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CallbackConfigUpdate extends CallbackConfigCreate {

    String cbId;


    @Override
    public CbConfig toDto() {
        final CbConfig cbConfig = super.toDto();

        cbConfig.setCbId(this.cbId);

        return cbConfig;
    }
}
