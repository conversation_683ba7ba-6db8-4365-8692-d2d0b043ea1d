package cn.newrank.niop.data.biz.export.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.newrank.niop.data.biz.export.mapper.DataExportSubtaskMapper;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportSubtaskStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskReset;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.web.model.PageView;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

@Log4j2
@Service
public class DataExportSubtaskServiceImpl implements DataExportSubtaskService {

    private final DataExportSubtaskMapper subtaskMapper;

    private final DataExportTaskService taskService;

    public DataExportSubtaskServiceImpl(DataExportSubtaskMapper subtaskMapper, DataExportTaskService taskService) {
        this.subtaskMapper = subtaskMapper;
        this.taskService = taskService;
    }

    @Override
    public int batchInsert(List<DataExportSubtask> subtasks) {
        return CollUtil.isNotEmpty(subtasks) ? subtaskMapper.batchInsert(subtasks) : 0;
    }

    @Override
    public int countSubtasks(String exportTaskId) {
        return CharSequenceUtil.isNotBlank(exportTaskId) ? subtaskMapper.countSubtasks(exportTaskId) : 0;
    }

    @Override
    public List<DataExportSubtask> listNotSubmittedSubtasks(String exportTaskId, int batchSize) {
        return subtaskMapper.listNotSubmittedSubtasks(exportTaskId, Math.max(100, batchSize));
    }

    @Override
    public Integer countNotSubmittedSubtasks(String exportTaskId) {
        return subtaskMapper.countNotSubmittedSubtasks(exportTaskId);
    }

    @Override
    public boolean isFinishedSubmitSubtasks(String exportTaskId) {
        return countNotSubmittedSubtasks(exportTaskId) <= 0;
    }

    @Override
    public void subtaskSubmitted(Integer id, String resultTaskId) {
        if (Objects.isNull(id) || CharSequenceUtil.isBlank(resultTaskId)) {
            return;
        }
        subtaskMapper.subtaskSubmitted(id, resultTaskId);
    }

    @Override
    public void subtaskFailed(DataExportSubtask exportSubtask) {
        Optional.ofNullable(exportSubtask).ifPresent(subtaskMapper::subtaskFailed);
    }

    @Override
    public void batchUpdateSubtasks(List<DataExportSubtask> tasks) {
        if (CollUtil.isNotEmpty(tasks)) {
            subtaskMapper.batchUpdateSubtasks(tasks);
        }
    }

    @Override
    public DataExportSubtaskExecutionInfo getSubtaskExecutionInfo(String exportTaskId) {
        return subtaskMapper.getSubtaskExecutionInfo(exportTaskId);
    }

    @Override
    public List<DataExportSubtask> listSubtasksByCursor(String exportTaskId, int cursor, int limit) {
        return subtaskMapper.listSubtasksByCursor(exportTaskId, cursor, Math.max(1, limit));
    }

    @Override
    public List<String> listResultTaskIds(String exportTaskId) {
        return CharSequenceUtil.isNotBlank(exportTaskId) ? subtaskMapper.listResultTaskIds(exportTaskId) : Collections.emptyList();
    }

    @Override
    public PageView<DataExportSubtask> page(DataExportSubtaskPageQuery pageQuery) {
        return PageView.of(subtaskMapper.page(pageQuery, pageQuery.toMybatisPlusPage()));
    }

    @Override
    public void updateSubtaskDataIsNull(String resultTaskId) {
        subtaskMapper.emptyData(resultTaskId);
    }

    @Override
    public Boolean subtaskReset(DataExportSubtaskReset subtaskReset) {
        final String exportTaskId = subtaskReset.getExportTaskId();
        final DataExportTaskExecutionDTO exportTask = Optional.ofNullable(taskService.getExportTaskExecutionInfo(exportTaskId))
            .orElseThrow(() -> createParamError("导数任务不存在"));

        // 只有执行中的导数任务才能进行重置
        if (DataExportTaskStatus.TASK_RUNNING != exportTask.getTaskStatus()) {
            throw createParamError("当前任务状态不支持该操作");
        }

        final DataExportSubtask subtask = Optional.ofNullable(subtaskMapper.getSubtask(exportTaskId, subtaskReset.getResultTaskId()))
            .orElseThrow(() -> createParamError("导数子任务不存在或已重置"));

        if (DataExportSubtaskStatus.FAILED != subtask.getSubtaskStatus()) {
            throw createParamError("只能重置失败的子任务");
        }

        return subtaskMapper.resetSubtask(exportTaskId, subtaskReset.getResultTaskId());
    }

}
