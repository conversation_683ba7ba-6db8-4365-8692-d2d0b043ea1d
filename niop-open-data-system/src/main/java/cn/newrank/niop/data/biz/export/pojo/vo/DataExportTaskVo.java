package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import cn.newrank.niop.data.util.DateTimeUtil;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;

@Data
public class DataExportTaskVo {

    /**
     * 导数id
     */
    private String exportId;

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 运行状态
     *
     * @see DataExportRunningStatus
     */
    private String runningStatus;

    /**
     * 任务状态
     *
     * @see DataExportTaskStatus
     */
    private String taskStatus;

    /**
     * 参数总数
     */
    private Integer paramTotalNum;

    /**
     * 成功数
     */
    private Integer succeedTotalNum;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 任务完成时间
     */
    private String taskFinishedTime;

    /**
     * 创建时间
     */
    private String gmtCreate;

    public static DataExportTaskVo buildBy(DataExportTask task) {
        if (Objects.isNull(task)) {
            return null;
        }

        final DataExportTaskVo vo = new DataExportTaskVo();
        vo.setExportId(task.getExportId());
        vo.setExportTaskId(task.getExportTaskId());
        vo.setBizScene(task.getBizScene());
        vo.setRunningStatus(task.getRunningStatus().getDbCode());
        vo.setTaskStatus(parseTaskStatus(task.getTaskStatus()).getDbCode());
        vo.setParamTotalNum(task.getParamTotalNum());
        vo.setSucceedTotalNum(task.getSucceedTotalNum());
        vo.setCreator(task.getCreator());
        final String taskFinishedTime = Optional.ofNullable(task.getTaskFinishedTime())
            .map(DateTimeUtil::format)
            .orElse("-");
        vo.setTaskFinishedTime(taskFinishedTime);
        vo.setGmtCreate(DateTimeUtil.simplifyDateTime(task.getGmtCreate()));
        return vo;
    }

    private static DataExportTaskStatus parseTaskStatus(DataExportTaskStatus taskStatus) {
        return switch (taskStatus) {
            case PARAM_FILE_UPLOADED, TASK_INITIALIZED -> DataExportTaskStatus.TASK_INITIALIZED;
            case TASK_RUNNING, SUCCEED -> DataExportTaskStatus.TASK_RUNNING;
            case RESULT_FILE_IS_READY -> DataExportTaskStatus.SUCCEED;
            default -> taskStatus;
        };
    }

}
