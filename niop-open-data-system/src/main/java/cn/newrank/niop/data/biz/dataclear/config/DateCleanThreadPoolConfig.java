package cn.newrank.niop.data.biz.dataclear.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
public class DateCleanThreadPoolConfig {

    @Bean(name = "dataClearTaskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        int keepAliveSeconds = 300; // 保持活动时间，单位为秒

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        // todo
        //executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("DataClearTaskExecutor-");
        executor.initialize();
        return executor;
    }


}
