package cn.newrank.niop.data.biz.dataclear.pojo.param;

import cn.newrank.niop.web.model.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DataCleanPageQuery extends PageQuery {

    /**
     * 规则名称
     */
    private String ruleName;

    private String dcId;

    /**
     * 表名
     */
    private String tableName;
    /**
     * 负责人，逗号分割
     */
    private String principal;

}
