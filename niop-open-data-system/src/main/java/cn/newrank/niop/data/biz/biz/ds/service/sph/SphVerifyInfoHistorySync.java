package cn.newrank.niop.data.biz.biz.ds.service.sph;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * [sph认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class SphVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET search_account/_search?scroll=15m
            {
              "_source": ["uid", "auth_profession", "auth_icon_type"],
              "size": 1500,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "auth_profession" } },
                    { "exists": { "field": "auth_icon_type" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected SphVerifyInfoHistorySync(RedissonClient redissonClient,
                                       CommonEsService commonEsService,
                                       DsConfigManager dsConfigManager) {
        super(PlatformType.SPH.getDbCode(), dsConfigManager.chooseSphEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final VerifyInfoUpdate update = new VerifyInfoUpdate();
        update.setIndexId(PlatformType.SPH.getDbCode() + "_" + sourceObj.get("uid"));
        update.setVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("auth_profession"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV1(StrUtil.nullToDefault(sourceObj.getString("auth_icon_type"), CharSequenceUtil.EMPTY));

        return update;
    }
}
