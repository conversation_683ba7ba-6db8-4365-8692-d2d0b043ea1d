package cn.newrank.niop.data.biz.biz.ds.pojo.constants;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class DsConstant {

    public static final Map<String, String> MAIN_DY_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);
    public static final Map<String, String> MAIN_GZH_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);

    public static final Map<String, String> MAIN_SPH_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);
    public static final Map<String, String> MAIN_KS_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);

    public static final Map<String, String> MAIN_WB_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);

    public static final Map<String, String> MAIN_BZ_RANK_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);

    /**
     * key: 分析主站es字段名
     * value: 公众号es字段名
     */
    public static final Map<String, String> MAIN_GZH_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(11);

    /**
     * key: 分析主站es字段名
     * value: 公众号es字段名
     */
    public static final Map<String, String> MAIN_DY_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(21);


    public static final Map<String, String> MAIN_BZ_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(19);

    public static final Map<String, String> MAIN_KS_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(16);

    public static final Map<String, String> MAIN_WB_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(15);


    public static final Map<String, String> MAIN_SPH_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(16);

    public static final Map<String, String> MAIN_XHS_FIELD_MAPPING = Maps.newHashMapWithExpectedSize(24);

    /**
     * 最新的有度主站es索引名称-开发/测试环境
     */
    public static final String TEST_YOU_DU_MAIN_ES_INDEX_20241015 = "test_search_nr_account_20241015";
    /**
     * 最新的有度主站es索引名称-生产环境
     */
    public static final String YOU_DU_MAIN_ES_INDEX_20241015 = "search_nr_account_20241015";

    static {
        MAIN_BZ_RANK_FIELD_MAPPING.put("search_nr_account", "bili_user_detail_rank_week");
        MAIN_BZ_RANK_FIELD_MAPPING.put("account_id", "mid");
        MAIN_BZ_RANK_FIELD_MAPPING.put("nr_index_week", "newrank_index");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_aweme_count", "opus_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_barrage_count", "video_review_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_coin_count", "coin_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_comment_count", "comment_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_like_count", "like_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("last_week_share_count", "share_count");
        MAIN_BZ_RANK_FIELD_MAPPING.put("nr_index_week_date", "rank_date");


        MAIN_KS_RANK_FIELD_MAPPING.put("search_nr_account", "search_kuaishou_user_rank_week");
        MAIN_KS_RANK_FIELD_MAPPING.put("account_id", "userId");
        MAIN_KS_RANK_FIELD_MAPPING.put("last_week_aweme_count", "photoCount");
        MAIN_KS_RANK_FIELD_MAPPING.put("last_week_comment_count", "commentCount");
        MAIN_KS_RANK_FIELD_MAPPING.put("last_week_like_count", "likeCount");
        MAIN_KS_RANK_FIELD_MAPPING.put("last_week_share_count", "shareCount");
        MAIN_KS_RANK_FIELD_MAPPING.put("nr_index_week_date", "rankDate");
        MAIN_KS_RANK_FIELD_MAPPING.put("nr_index_week", "newrankIndex");

        MAIN_WB_RANK_FIELD_MAPPING.put("search_nr_account", "search_weibo_week_rank");
        MAIN_WB_RANK_FIELD_MAPPING.put("account_id", "uid");
        MAIN_WB_RANK_FIELD_MAPPING.put("last_week_aweme_count", "article_count");
        MAIN_WB_RANK_FIELD_MAPPING.put("last_week_comment_count", "total_conments_count");
        MAIN_WB_RANK_FIELD_MAPPING.put("last_week_forward_count", "followers_count");
        MAIN_WB_RANK_FIELD_MAPPING.put("last_week_like_count", "total_like_count");
        MAIN_WB_RANK_FIELD_MAPPING.put("nr_index_week_date", "rank_date");
        MAIN_WB_RANK_FIELD_MAPPING.put("nr_index_week", "week_index");


        MAIN_SPH_RANK_FIELD_MAPPING.put("search_nr_account", "search_nr_account");
        MAIN_SPH_RANK_FIELD_MAPPING.put("account_id", "uid");
        MAIN_SPH_RANK_FIELD_MAPPING.put("nr_index_week", "week_index");
        MAIN_SPH_RANK_FIELD_MAPPING.put("last_week_aweme_count", "video_count");
        MAIN_SPH_RANK_FIELD_MAPPING.put("last_week_like_count", "total_like_count");
        MAIN_SPH_RANK_FIELD_MAPPING.put("nr_index_week_date", "rank_date");

        MAIN_DY_RANK_FIELD_MAPPING.put("account_id", "uid");
        MAIN_DY_RANK_FIELD_MAPPING.put("last_week_aweme_count", "works_count");
        MAIN_DY_RANK_FIELD_MAPPING.put("last_week_comment_count", "comment_count");
        MAIN_DY_RANK_FIELD_MAPPING.put("last_week_like_count", "digg_count");
        MAIN_DY_RANK_FIELD_MAPPING.put("last_week_share_count", "share_count");
        MAIN_DY_RANK_FIELD_MAPPING.put("nr_index_week_date", "rank_date");
        MAIN_DY_RANK_FIELD_MAPPING.put("nr_index_week", "newrank_index");

        //GZH
        MAIN_GZH_FIELD_MAPPING.put("account_id", "wxId");
        MAIN_GZH_FIELD_MAPPING.put("account_name", "name");
        MAIN_GZH_FIELD_MAPPING.put("province", "province");
        MAIN_GZH_FIELD_MAPPING.put("city", "city");
        MAIN_GZH_FIELD_MAPPING.put("verify_info", "certifiedText");
        MAIN_GZH_FIELD_MAPPING.put("verify_type_v1", "verifyStatus");
        MAIN_GZH_FIELD_MAPPING.put("verify_type_v2", "verifyType");
        MAIN_GZH_FIELD_MAPPING.put("signature", "description");
        MAIN_GZH_FIELD_MAPPING.put("avatar_url", "headImageUrl");
        MAIN_GZH_FIELD_MAPPING.put("acq_time", "updateTime");
        MAIN_GZH_FIELD_MAPPING.put("ana_time", "updateTime");
        MAIN_GZH_FIELD_MAPPING.put("account_type", "type");
        MAIN_GZH_FIELD_MAPPING.put("account_tag", "tags");
        MAIN_GZH_FIELD_MAPPING.put("account_status", "status");

        //DY
        MAIN_DY_FIELD_MAPPING.put("account_id", "uid");
        MAIN_DY_FIELD_MAPPING.put("account_name", "nickname");
        MAIN_DY_FIELD_MAPPING.put("gender", "gender");
        MAIN_DY_FIELD_MAPPING.put("province", "province_new");
        MAIN_DY_FIELD_MAPPING.put("city", "city_new");
        MAIN_DY_FIELD_MAPPING.put("follower_count", "mplatform_followers_count");
        MAIN_DY_FIELD_MAPPING.put("verify_info", "custom_verify");
        MAIN_DY_FIELD_MAPPING.put("verify_type_v1", "verify_label");
        MAIN_DY_FIELD_MAPPING.put("signature", "signature");
        MAIN_DY_FIELD_MAPPING.put("avatar_url", "avatar");
        MAIN_DY_FIELD_MAPPING.put("acq_time", "crawl_time");
        MAIN_DY_FIELD_MAPPING.put("ana_time", "ana_time");
        MAIN_DY_FIELD_MAPPING.put("mcn_name", "mcn_name");
        MAIN_DY_FIELD_MAPPING.put("aweme_count_rt_thirty_days", "aweme_count_30");
        MAIN_DY_FIELD_MAPPING.put("account_type", "account_classify_first");
        MAIN_DY_FIELD_MAPPING.put("account_tag", "ana_xd_tags");
        MAIN_DY_FIELD_MAPPING.put("total_aweme_count", "aweme_count");
        MAIN_DY_FIELD_MAPPING.put("total_like_count", "total_favorited");
        MAIN_DY_FIELD_MAPPING.put("official_redirect_id", "sec_uid");
        MAIN_DY_FIELD_MAPPING.put("huoshan_follower_count", "live_stream_follower_count");
        MAIN_DY_FIELD_MAPPING.put("douyin_follower_count", "follower_count");
        MAIN_DY_FIELD_MAPPING.put("toutiao_follower_count", "new_article_follower_count");
        MAIN_DY_FIELD_MAPPING.put("account_sec_type", "account_classify_second");

        //BZ
        MAIN_BZ_FIELD_MAPPING.put("account_id", "mid");
        MAIN_BZ_FIELD_MAPPING.put("officail_display_id", "mid");
        MAIN_BZ_FIELD_MAPPING.put("account_name", "name");
        MAIN_BZ_FIELD_MAPPING.put("gender", "sex");
        MAIN_BZ_FIELD_MAPPING.put("province", "region_desc");
        MAIN_BZ_FIELD_MAPPING.put("city", "second_region_desc");
        MAIN_BZ_FIELD_MAPPING.put("follower_count", "follower");
        MAIN_BZ_FIELD_MAPPING.put("verify_info", "official_title");
        // 特殊处理
        MAIN_BZ_FIELD_MAPPING.put("enterprise_verify_info", "official_title");
        MAIN_BZ_FIELD_MAPPING.put("verify_type_v1", "official_type");
        MAIN_BZ_FIELD_MAPPING.put("verify_type_v2", "official_role");
        MAIN_BZ_FIELD_MAPPING.put("signature", "sign");
        MAIN_BZ_FIELD_MAPPING.put("avatar_url", "face");
        MAIN_BZ_FIELD_MAPPING.put("acq_time", "gmt_create");
        MAIN_BZ_FIELD_MAPPING.put("ana_time", "gmt_modify");
        MAIN_BZ_FIELD_MAPPING.put("nr_index_day", "newrank_index");
        MAIN_BZ_FIELD_MAPPING.put("nr_index_month", "month_newrank_index");
        MAIN_BZ_FIELD_MAPPING.put("total_collected_count", "favourite_count");
        MAIN_BZ_FIELD_MAPPING.put("account_type", "type");
        MAIN_BZ_FIELD_MAPPING.put("account_tag", "tags");
        MAIN_BZ_FIELD_MAPPING.put("total_aweme_count", "video_count");
        MAIN_BZ_FIELD_MAPPING.put("total_like_count", "like_count");
        MAIN_BZ_FIELD_MAPPING.put("account_sec_type", "type_v2");

        //KS
        MAIN_KS_FIELD_MAPPING.put("account_id", "userId");
        MAIN_KS_FIELD_MAPPING.put("officail_display_id", "profile.kwaiId");
        MAIN_KS_FIELD_MAPPING.put("account_name", "profile.userName");
        MAIN_KS_FIELD_MAPPING.put("gender", "profile.userSex");
        MAIN_KS_FIELD_MAPPING.put("age", "profile.age");
        MAIN_KS_FIELD_MAPPING.put("province", "profile.province");
        MAIN_KS_FIELD_MAPPING.put("city", "profile.city");
        MAIN_KS_FIELD_MAPPING.put("follower_count", "ownerCount.fan");
        MAIN_KS_FIELD_MAPPING.put("verify_info", "profile.verifiedDetail.description");
//        MAIN_KS_FIELD_MAPPING.put("verify_type_v1", "profile.verifiedDetail.type");
//        MAIN_KS_FIELD_MAPPING.put("verify_type_v2", "accountVerified");
        MAIN_KS_FIELD_MAPPING.put("signature", "profile.userText");
        MAIN_KS_FIELD_MAPPING.put("avatar_url", "profile.headurl");
        MAIN_KS_FIELD_MAPPING.put("acq_time", "updateTime");
        MAIN_KS_FIELD_MAPPING.put("ana_time", "anaTime");
        MAIN_KS_FIELD_MAPPING.put("aweme_count_rt_thirty_days", "photoCacl.photoCount30");
        MAIN_KS_FIELD_MAPPING.put("account_type", "type");
        MAIN_KS_FIELD_MAPPING.put("total_aweme_count", "ownerCount.photo");
        MAIN_KS_FIELD_MAPPING.put("official_redirect_id", "eid");

        //WB
        MAIN_WB_FIELD_MAPPING.put("account_id", "uid");
        MAIN_WB_FIELD_MAPPING.put("officail_display_id", "uid");
        MAIN_WB_FIELD_MAPPING.put("account_name", "name");
        MAIN_WB_FIELD_MAPPING.put("gender", "gender");
        MAIN_WB_FIELD_MAPPING.put("province", "location");
        MAIN_WB_FIELD_MAPPING.put("follower_count", "followers_count");
        MAIN_WB_FIELD_MAPPING.put("signature", "description");
        MAIN_WB_FIELD_MAPPING.put("avatar_url", "avatar_hd");
        MAIN_WB_FIELD_MAPPING.put("acq_time", "last_acq_time");
        MAIN_WB_FIELD_MAPPING.put("ana_time", "ana_time");
        MAIN_WB_FIELD_MAPPING.put("account_tag", "label_desc");
        MAIN_WB_FIELD_MAPPING.put("total_aweme_count", "statuses_count");
        MAIN_WB_FIELD_MAPPING.put("total_like_count", "comment_like_cnt");
        MAIN_WB_FIELD_MAPPING.put("verify_info", "verified_reason");
        MAIN_WB_FIELD_MAPPING.put("enterprise_verify_info", "verified_reason");
        MAIN_WB_FIELD_MAPPING.put("verify_type_v1", "verified");
        MAIN_WB_FIELD_MAPPING.put("verify_type_v2", "verified_type");
        //SPH
        MAIN_SPH_FIELD_MAPPING.put("account_id", "uid");
        MAIN_SPH_FIELD_MAPPING.put("officail_display_id", "username");
        MAIN_SPH_FIELD_MAPPING.put("account_name", "nickname");
        MAIN_SPH_FIELD_MAPPING.put("gender", "gender");
        MAIN_SPH_FIELD_MAPPING.put("province", "province_cn");
        MAIN_SPH_FIELD_MAPPING.put("city", "city_cn");
        MAIN_SPH_FIELD_MAPPING.put("follower_count", "fans_count");
        MAIN_SPH_FIELD_MAPPING.put("verify_info", "auth_profession");
        MAIN_SPH_FIELD_MAPPING.put("verify_type_v1", "auth_icon_type");
        MAIN_SPH_FIELD_MAPPING.put("signature", "signature");
        MAIN_SPH_FIELD_MAPPING.put("avatar_url", "head_url");
        MAIN_SPH_FIELD_MAPPING.put("acq_time", "last_acq_time");
        MAIN_SPH_FIELD_MAPPING.put("ana_time", "gmt_modify");
        MAIN_SPH_FIELD_MAPPING.put("nr_index_day", "day_index");
        MAIN_SPH_FIELD_MAPPING.put("nr_index_month", "month_index");
        MAIN_SPH_FIELD_MAPPING.put("account_type", "type");
        MAIN_SPH_FIELD_MAPPING.put("account_tag", "tags");
        MAIN_SPH_FIELD_MAPPING.put("total_aweme_count", "feeds_count");


        //xhs
        MAIN_XHS_FIELD_MAPPING.put("account_id", "userid");
        MAIN_XHS_FIELD_MAPPING.put("officail_display_id", "red_id");
        MAIN_XHS_FIELD_MAPPING.put("account_name", "nickname");
        MAIN_XHS_FIELD_MAPPING.put("gender", "gender");
        MAIN_XHS_FIELD_MAPPING.put("province", "province");
        MAIN_XHS_FIELD_MAPPING.put("city", "city");
        MAIN_XHS_FIELD_MAPPING.put("follower_count", "fans");
        MAIN_XHS_FIELD_MAPPING.put("verify_info", "official_verify");
        MAIN_XHS_FIELD_MAPPING.put("verify_type_v1", "identify_sign");
        MAIN_XHS_FIELD_MAPPING.put("signature", "desc");
        MAIN_XHS_FIELD_MAPPING.put("avatar_url", "imageb");
        MAIN_XHS_FIELD_MAPPING.put("acq_time", "gmt_create");
        MAIN_XHS_FIELD_MAPPING.put("ana_time", "ana_time");
        MAIN_XHS_FIELD_MAPPING.put("mcn_name", "mcn_name");
        MAIN_XHS_FIELD_MAPPING.put("aweme_count_rt_thirty_days", "note_count_thirty");
        MAIN_XHS_FIELD_MAPPING.put("total_collected_count", "collected");
        MAIN_XHS_FIELD_MAPPING.put("account_type", "account_type_v1");
        MAIN_XHS_FIELD_MAPPING.put("account_tag", "account_tags");
        MAIN_XHS_FIELD_MAPPING.put("total_aweme_count", "ndiscovery");
        MAIN_XHS_FIELD_MAPPING.put("total_like_count", "liked");
        MAIN_XHS_FIELD_MAPPING.put("avg_read_count_seven", "avg_read_count_seven");
        MAIN_XHS_FIELD_MAPPING.put("note_count_seven", "note_count_seven");
        MAIN_XHS_FIELD_MAPPING.put("avg_comments_count_seven", "avg_comments_count_seven");
        MAIN_XHS_FIELD_MAPPING.put("avg_liked_count_seven", "avg_liked_count_seven");
        MAIN_XHS_FIELD_MAPPING.put("avg_collected_count_seven", "avg_collected_count_seven");
        MAIN_XHS_FIELD_MAPPING.put("account_sec_type", "account_sec_type");

        // gzh
        MAIN_GZH_RANK_FIELD_MAPPING.put("last_week_aweme_count", "articleCount");
        MAIN_GZH_RANK_FIELD_MAPPING.put("last_week_like_count", "articlePreLike");
        MAIN_GZH_RANK_FIELD_MAPPING.put("last_week_read_count", "articleClicksCount");
        MAIN_GZH_RANK_FIELD_MAPPING.put("last_week_watch_count", "articleLikesCount");
        MAIN_GZH_RANK_FIELD_MAPPING.put("nr_index_week", "log1pMark");
        MAIN_GZH_RANK_FIELD_MAPPING.put("nr_index_week_date", "rankDate");
        MAIN_GZH_RANK_FIELD_MAPPING.put("uid", "uid");

    }

}
