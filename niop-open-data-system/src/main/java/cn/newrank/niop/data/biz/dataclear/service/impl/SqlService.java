package cn.newrank.niop.data.biz.dataclear.service.impl;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.SqlCount;
import cn.newrank.niop.data.biz.dataclear.pojo.enums.DataSourceType;
import cn.newrank.niop.data.biz.dataclear.pojo.param.CleanRuleQuery;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.service.DatasourceService;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.JDBC;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

@Service
public class SqlService {
    public static final String CHECK_SQL = """
            select count(*) as count from %s where %s  LIMIT 1
            """;

    public static final String COUNT_SQL = """
            select count(*) as count from %s where %s
            """;

    public static final String DELETE_SQL = """
            delete  from %s where %s;
               """;
    private final DatasourceService datasourceService;
    private final DsConfigService dsConfigService;

    public SqlService(DatasourceService datasourceService, DsConfigService dsConfigService) {
        this.datasourceService = datasourceService;
        this.dsConfigService = dsConfigService;
    }


    //构建对外展示的sql
    public String buildViewDataSourceSql(CleanRuleQuery event) {
        return String.format(DELETE_SQL,
                event.getTableName(),
                event.getFilterCondition());

    }

    //构建真实执行应用的语句sql语法
    public String buildRealSQL(CleanRuleQuery cleanRuleUpadteQuery) {
        //需要根据dcId去做区分
        DsConfig config = dsConfigService.getConfig(cleanRuleUpadteQuery.getDcId());

        DataSourceType dataSourceType = DataSourceType.fromString(config.getType().getDescription());
        return switch (dataSourceType) {
            case POSTGRES -> String.format(dataSourceType.getDsl(), cleanRuleUpadteQuery.getTableName(),
                    cleanRuleUpadteQuery.getTableName(), cleanRuleUpadteQuery.getFilterCondition());
            case MYSQL -> String.format(dataSourceType.getDsl(), cleanRuleUpadteQuery.getTableName(),
                    cleanRuleUpadteQuery.getFilterCondition());
        };
    }

    public Datasource getDatasource(String dcId) {
        return datasourceService.getDatasource(dcId);
    }

    public void mysqlCheck(CleanRuleQuery dataCleanRuleCreate) {
        final Datasource datasource = getDatasource(dataCleanRuleCreate.getDcId());
        String sql = String.format(CHECK_SQL, dataCleanRuleCreate.getTableName(), dataCleanRuleCreate.getFilterCondition());
        try {
            NamedParameterJdbcTemplate jdbcTemplate = getJdbcTemplate(datasource);
            jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SqlCount.class));
        } catch (Exception e) {
            throw createParamError("检查sql是否有错误");
        }
    }

    public Integer querySql(DataCleanTask dataCleanTask) {
        String executeSql = Optional.ofNullable(dataCleanTask.getRealSql()).orElse(dataCleanTask.getSql());
        return executeWithTransaction(dataCleanTask.getDcId(),
                dataCleanTask.getSql(), jdbcTemplate -> jdbcTemplate.update(executeSql, Collections.emptyMap()));
    }

    public Integer sqlCount(CleanRuleQuery dataCleanRuleCreate) {
        String sql = String.format(COUNT_SQL, dataCleanRuleCreate.getTableName(), dataCleanRuleCreate.getFilterCondition());
        return executeWithTransaction(dataCleanRuleCreate.getDcId(), sql, jdbcTemplate -> {
            List<SqlCount> query = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SqlCount.class));
            return query.isEmpty() ? 0 : query.get(0).getCount();
        });
    }

    private NamedParameterJdbcTemplate getJdbcTemplate(Datasource datasource) {
        if (datasource instanceof JDBC jdbc) {
            return jdbc.availableJdbcTemplate();
        }
        throw new IllegalStateException("Unsupported datasource type");
    }


    private <T> T executeWithTransaction(String dcId, String sql, Function<NamedParameterJdbcTemplate, T> operation) {
        Datasource datasource = getDatasource(dcId);
        if (datasource instanceof JDBC jdbc) {
            PlatformTransactionManager transactionManager = jdbc.getTransactionManager();
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setName("MyTransaction-" + sql.hashCode());
            def.setTimeout(20);

            TransactionStatus status = transactionManager.getTransaction(def);
            try {
                NamedParameterJdbcTemplate jdbcTemplate = getJdbcTemplate(datasource);
                T result = operation.apply(jdbcTemplate);
                transactionManager.commit(status);
                return result;
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }
        return null;
    }
}
