package cn.newrank.niop.data.biz.component.callback;

import cn.newrank.niop.data.common.BizErr;
import cn.newrank.nrcore.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import okhttp3.*;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * 回调请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/29 13:54
 */
public class CallbackRequest {
    static final MediaType DEFAULT_MEDIA_TYPE = MediaType.get("application/json");
    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(5))
            .readTimeout(Duration.ofSeconds(10))
            .writeTimeout(Duration.ofSeconds(10))
            .connectionPool(new ConnectionPool(400, 1, TimeUnit.MINUTES))
            .build();
    @Getter
    private final String url;
    private final Headers headers;

    public CallbackRequest(String url, Headers headers) {
        this.url = url;
        this.headers = headers;
    }

    public void request(String data) throws BizException {
        if (data == null || data.isBlank()) {
            throw createBizException(BizErr.CALLBACK_ERROR, "回调结果为空");
        }
        final Request request = new Request.Builder()
                .headers(headers)
                .url(url)
                .post(RequestBody.create(data, DEFAULT_MEDIA_TYPE))
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            final ResponseBody body = response.body();
            if (body != null) {
                final String content = body.string();
                if (response.isSuccessful()) {
                    final JSONObject resp = JSONObject.parseObject(content);

                    final Integer code = resp.getInteger("code");
                    if (code != null && code == 0) {
                        return;
                    }
                }

                throw createBizException(BizErr.CALLBACK_ERROR, "status: {}, resp: {}", response.code(), content);
            }

            throw createBizException(BizErr.CALLBACK_ERROR, "status: {}, body is null", response.code());
        } catch (Exception e) {
            throw createBizException(BizErr.CALLBACK_ERROR, e.getMessage());
        }
    }

    public boolean ping() {
        final Request request = new Request.Builder()
                .headers(headers)
                .url(url)
                .post(RequestBody.create("{}", DEFAULT_MEDIA_TYPE))
                .build();

        try (Response response = CLIENT.newCall(request).execute()) {
            return response.code() != 404;
        } catch (Exception e) {
            return false;
        }
    }
}
