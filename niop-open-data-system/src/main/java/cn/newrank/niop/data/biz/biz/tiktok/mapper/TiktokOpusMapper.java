package cn.newrank.niop.data.biz.biz.tiktok.mapper;

import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokOpus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;


@Mapper
public interface TiktokOpusMapper {

    Tiktok<PERSON><PERSON> get(String opusId);

    int save(@Param("items") List<TiktokOpus> items);

    List<TiktokOpus> list(@Param("cursor") long cursor,
                          @Param("startTime") Timestamp startTime,
                          @Param("endTime") Timestamp endTime);
}




