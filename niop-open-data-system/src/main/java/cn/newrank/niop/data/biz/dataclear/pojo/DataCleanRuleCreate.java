package cn.newrank.niop.data.biz.dataclear.pojo;

import lombok.Data;


@Data
public class DataCleanRuleCreate {

    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 规则id
     */
    private String ruleId;

    /**
     * 数据源id
     */
    private String dcId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 执行表达式（小时级）
     */
    private String cron;

    /**
     * 过滤条件
     */
    private String filterCondition;

    /**
     * 负责人，逗号分割
     */
    private String principal;

    /**
     * 规则状态，1：开启，0：关闭
     */
    private int ruleStatus;

    private String gmtCreate;

}