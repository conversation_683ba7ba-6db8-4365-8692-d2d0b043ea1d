package cn.newrank.niop.data.biz.scheduler;

import cn.newrank.niop.data.biz.component.sync.AbstractHistorySynchronizer;
import cn.newrank.niop.data.biz.component.sync.DefaultHistorySynchronizer;
import cn.newrank.niop.data.biz.service.DictionaryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/12 9:32
 */
@Component
@Log4j2
public class CommonScheduler {
    private final DictionaryService dictionaryService;
    private final List<? extends DefaultHistorySynchronizer<?>> historySynchronizers;

    public CommonScheduler(DictionaryService dictionaryService,
                           List<? extends DefaultHistorySynchronizer<?>> historySynchronizes) {
        this.dictionaryService = dictionaryService;
        this.historySynchronizers = historySynchronizes;
    }

    @XxlJob("StartSyncDictionary")
    public ReturnT<String> startSyncDictionary(String ignore) {
        dictionaryService.startSync();
        return ReturnT.SUCCESS;
    }


    /**
     * 启动历史同步任务
     */
    @XxlJob("HistorySyncScheduler")
    public ReturnT<String> startSync(String ignore) {
        for (DefaultHistorySynchronizer<?> historySynchronizer : historySynchronizers) {
            try {
                historySynchronizer.schedule();
            } catch (Exception e) {
                log.error("同步({})任务执行失败", historySynchronizer.getName(), e);
            }
        }

        return ReturnT.SUCCESS;
    }
}
