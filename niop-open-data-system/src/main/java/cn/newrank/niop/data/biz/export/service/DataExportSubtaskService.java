package cn.newrank.niop.data.biz.export.service;


import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskReset;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.web.model.PageView;
import java.util.List;

public interface DataExportSubtaskService {

    /**
     * 批量插入导数子任务
     *
     * @param subtasks 导数子任务
     * @return 新增数量
     */
    int batchInsert(List<DataExportSubtask> subtasks);

    /**
     * 根据导数任务id统计子任务数量
     *
     * @param exportTaskId 导数任务id
     * @return 子任务数量
     */
    int countSubtasks(String exportTaskId);

    /**
     * 查询未提交的子任务列表
     *
     * @param exportTaskId 导数任务id
     * @param batchSize 查询数量
     * @return 导数子任务列表
     */
    List<DataExportSubtask> listNotSubmittedSubtasks(String exportTaskId, int batchSize);

    /**
     * 统计未提交的子任务数量
     *
     * @param exportTaskId 导数任务id
     * @return 未提交子任务数量
     */
    Integer countNotSubmittedSubtasks(String exportTaskId);

    /**
     * 提交子任务是否完成
     *
     * @param exportTaskId 导数任务id
     * @return 是否完成
     */
    boolean isFinishedSubmitSubtasks(String exportTaskId);

    /**
     * 子任务已提交
     *
     * @param id 主键id
     * @param resultTaskId 结果任务id
     */
    void subtaskSubmitted(Integer id, String resultTaskId);

    /**
     * 子任务失败
     *
     * @param exportSubtask 子任务信息
     */
    void subtaskFailed(DataExportSubtask exportSubtask);

    /**
     * 批量更新子任务信息
     *
     * @param tasks 待更新任务列表
     */
    void batchUpdateSubtasks(List<DataExportSubtask> tasks);

    /**
     * 获取子任务执行信息
     *
     * @param exportTaskId 导数任务id
     * @return 子任务执行信息
     */
    DataExportSubtaskExecutionInfo getSubtaskExecutionInfo(String exportTaskId);

    /**
     * 通过游标查询子任务列表
     *
     * @param exportTaskId 导数任务id
     * @param cursor 游标id
     * @param limit 获取条数
     * @return 子任务列表
     */
    List<DataExportSubtask> listSubtasksByCursor(String exportTaskId, int cursor, int limit);

    /**
     * 获取结果任务id列表
     *
     * @param exportTaskId 导数任务id
     * @return 结果任务id列表
     */
    List<String> listResultTaskIds(String exportTaskId);

    /**
     * 分页搜索导数子任务信息
     *
     * @param pageQuery 搜索参数
     * @return 分页数据
     */
    PageView<DataExportSubtask> page(DataExportSubtaskPageQuery pageQuery);

    /**
     * 更新子任务结果为 null
     *
     * @param resultTaskId 结果任务id
     */
    void updateSubtaskDataIsNull(String resultTaskId);

    /**
     * 子任务重置
     *
     * @param subtaskReset 重置参数
     * @return 是否成功
     */
    Boolean subtaskReset(DataExportSubtaskReset subtaskReset);

}
