package cn.newrank.niop.data.biz.biz.txhx.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 腾讯互选-视频号-详情
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/06/05 10:09:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TxhxSphIcon extends StorageEntity {
    /**
     * 达人id
     */
    private String appId;
    /**
     * 长视频报价
     */
    private String aderLongVideoPrice;
    /**
     * 短视频报价
     */
    private String aderShortVideoPrice;
    /**
     * 达人公司
     */
    private String authCompany;
    /**
     * 达人认证信息
     */
    private String authProfession;
    /**
     * 达人头像
     */
    private String avatar;
    /**
     * 平均互动量
     */
    private String avgInteractionCount;
    /**
     * 平均点赞量
     */
    private String avgLikeCount;
    /**
     * 平均播放量
     */
    private String avgReadCount;
    /**
     * 达人类别一
     */
    private Integer categoryLevel1;
    /**
     * 达人类别二
     */
    private Integer categoryLevel2;
    /**
     * 达人城市
     */
    private String cityLabel;
    /**
     * 联系方式
     */
    private String contactInfo;
    /**
     * 合作指数
     */
    private Integer cooperationIndex;
    /**
     * 暂无
     */
    private Integer costEffectivenessIndex;
    /**
     * 预期cpm
     */
    private Integer expectedCpm;
    /**
     * 近30天粉丝增长率
     */
    private String fansNumGrowthRange;
    /**
     * 近30天粉丝增长量
     */
    private Integer fansNumGrowthRate;
    /**
     * 近30天粉丝增长层级
     */
    private String fansNumIncrement;
    /**
     * 粉丝量层级
     */
    private String fansNumLevel;
    /**
     * 互动率
     */
    private Integer interactionRate;
    /**
     * 60s以上视频报价
     */
    private String longVideoPrice;
    /**
     * 60s以上视频报价增长
     */
    private String longVideoPriceDeprecated;
    /**
     * 播放中位数
     */
    private String medianReadCount;
    /**
     * 达人昵称
     */
    private String nickname;
    /**
     * 完播率
     */
    private String playFinishRate;
    /**
     * 暂无
     */
    private String plusV;
    /**
     * 暂无
     */
    private String proceedingOrderCount;
    /**
     * 合作指数
     */
    private String propagationIndex;
    /**
     * 二维码
     */
    private String qrCode;
    /**
     * 暂无
     */
    private String recallType;
    /**
     * 近期报价变价
     */
    private String recentChangedPrice;
    /**
     * 暂无
     */
    private String releaseDate;
    /**
     * 得分
     */
    private String score;
    /**
     * 1-60s视频报价
     */
    private String shortVideoPrice;
    /**
     * 1-60s视频报价增长
     */
    private String shortVideoPriceDeprecated;
    /**
     * 沟通指数
     */
    private String socialInteractionIndex;
    /**
     * 达人简介
     */
    private String synopsis;
    /**
     * 达人标签
     */
    private String tags;
    /**
     * 最近 15 个视频最高播放量
     */
    private String topReadCount;
    /**
     * 最近 15 个视频平均播放量
     */
    private String avgReadCount15;
    /**
     * 粉丝年龄
     */
    private String fansAge;
    /**
     * 粉丝城市
     */
    private String fansArea;
    /**
     * 粉丝设备
     */
    private String fansDevice;
    /**
     * 粉丝性别
     */
    private String fansGender;
    /**
     * 粉丝省份
     */
    private String fansProvince;
    /**
     * 观众年龄
     */
    private String viewerAge;
    /**
     * 观众城市
     */
    private String viewerArea;
    /**
     * 观众设备
     */
    private String viewerDevice;
    /**
     * 观众性别
     */
    private String viewerGender;
    /**
     * 观众省份
     */
    private String viewerProvince;
    /**
     * 更新时间
     **/
    private String updateTime;

    @Override
    public String identifier() {
        return appId;
    }
}