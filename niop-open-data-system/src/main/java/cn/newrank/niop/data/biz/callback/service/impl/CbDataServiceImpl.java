package cn.newrank.niop.data.biz.callback.service.impl;

import cn.newrank.niop.data.biz.callback.mapper.DcCbDataMapper;
import cn.newrank.niop.data.biz.callback.pojo.CbData;
import cn.newrank.niop.data.biz.callback.service.CbDataService;
import cn.newrank.niop.data.util.DateTimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.newrank.niop.data.util.DateTimeUtil.dayOfPartition;
import static cn.newrank.niop.util.U.group;

/**
 *
 */
@Service
public class CbDataServiceImpl implements CbDataService {

    private final DcCbDataMapper dcCbDataMapper;

    public CbDataServiceImpl(DcCbDataMapper dcCbDataMapper) {
        this.dcCbDataMapper = dcCbDataMapper;
    }

    @Override
    public void saveBatch(List<CbData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        group(dataList, d -> DateTimeUtil.format(d.getGmtCreate().toLocalDateTime(), "yyyyMMdd"), Function.identity())
                .forEach((partition, data) -> dcCbDataMapper.insertBatch(data, partition));
    }

    @Override
    public List<CbData> list(List<String> cIds) {
        if (CollectionUtils.isEmpty(cIds)) {
            return Collections.emptyList();
        }

        List<CbData> ts = dcCbDataMapper.list(cIds);
        if (CollectionUtils.isEmpty(ts)) {
            return Collections.emptyList();
        }

        return ts.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                CbData::getCid,
                                Function.identity(),
                                CbDataServiceImpl::compareByModifiedTime
                        ),
                        map -> new ArrayList<>(map.values()))
                );
    }


    private static CbData compareByModifiedTime(CbData o1, CbData o2) {
        Timestamp m1 = o1.getGmtModified();
        Timestamp m2 = o2.getGmtModified();

        if (m1 == null) {
            return m2 == null ? o1 : o2;
        }

        if (m2 == null) {
            return o1;
        }

        return m1.after(m2) ? o1 : o2;
    }
    @Override
    public void createTable(LocalDate partition) {
        dcCbDataMapper.createTable(dayOfPartition(partition)
                , Date.valueOf(partition)
                , Date.valueOf(partition.plusDays(1)));
    }

    @Override
    public void dropTable(LocalDate partition) {
        dcCbDataMapper.dropTable(dayOfPartition(partition));
    }
}




