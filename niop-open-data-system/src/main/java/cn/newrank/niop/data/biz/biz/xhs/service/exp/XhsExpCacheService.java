package cn.newrank.niop.data.biz.biz.xhs.service.exp;

import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpConfigMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpTopicMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpConfig;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CWWEVCGP
 *
 * <AUTHOR>
 * @since 2025/3/28 11:46:10
 */
@Slf4j
@Service
public class XhsExpCacheService {

    private final Cache<String, Integer> xhsExpTopicCache;
    private final Cache<Integer, XhsExpConfig> xhsExpConfigCache;
    private final XhsExpConfigMapper xhsExpConfigMapper;
    private final XhsExpTopicMapper xhsExpTopicMapper;

    public XhsExpCacheService(XhsExpConfigMapper xhsExpConfigMapper,
                              XhsExpTopicMapper xhsExpTopicMapper) {
        this.xhsExpTopicMapper = xhsExpTopicMapper;
        this.xhsExpTopicCache = topicCacheInit();
        this.xhsExpConfigMapper = xhsExpConfigMapper;
        this.xhsExpConfigCache = configCacheInit();
    }

    private static @NotNull Cache<String, Integer> topicCacheInit() {
        return Caffeine.newBuilder().maximumSize(1000)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .build();
    }

    private static @NotNull Cache<Integer, XhsExpConfig> configCacheInit() {
        return Caffeine.newBuilder().maximumSize(100)
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build();
    }

    /**
     * 获取配置缓存
     *
     * @param topicWeight 配置权重
     * @return 配置
     */
    public XhsExpConfig getXhsExpConfig(Integer topicWeight) {
        if (topicWeight == null) {
            return null;
        }
        XhsExpConfig config = xhsExpConfigCache.getIfPresent(topicWeight);
        if (Objects.isNull(config)) {
            xhsExpConfigCache.putAll(xhsExpConfigMapper.listConfigs().stream().collect(Collectors.toMap(
                    XhsExpConfig::getTopicWeight,
                    Function.identity()
            )));
            config = xhsExpConfigCache.getIfPresent(topicWeight);
        }
        return config;
    }

    /**
     * 获取话题权重
     *
     * @param topicId 话题id
     * @return 权重
     */
    public Integer getTopicWeight(String topicId) {
        Integer weight = xhsExpTopicCache.getIfPresent(topicId);
        if (Objects.isNull(weight)) {
            weight = xhsExpTopicMapper.getTopicWeight(topicId);
            if(Objects.isNull(weight)){
                return null;
            }
            xhsExpTopicCache.put(topicId, weight);
        }
        return weight;
    }


    /**
     * 删除话题缓存
     * @param topicId 话题id
     */
    public void invalidateTopic(String topicId) {
        xhsExpTopicCache.invalidate(topicId);
    }


}
