package cn.newrank.niop.data.biz.biz.tiktok.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/31 10:37
 */
@Mapper
@DS("holo")
public interface TiktokDailyRankMapper {

    void startCalculate(@Param("ds") String ds, @Param("rankDate") Date rankDate,
                        @Param("beforeDay") Date beforeDay,
                        @Param("afterDay") Date afterDay);

    void clear(@Param("ds") String ds, @Param("rankDate") Date rankDate);
}




