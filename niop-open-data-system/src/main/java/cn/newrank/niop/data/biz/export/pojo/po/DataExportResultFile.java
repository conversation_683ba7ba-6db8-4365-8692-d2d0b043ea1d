package cn.newrank.niop.data.biz.export.pojo.po;

import cn.newrank.niop.data.biz.export.handler.FileUploadResult;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataExportResultFile {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 更新时间
     */
    private String gmtModified;

    public static DataExportResultFile of(FileUploadResult uploadResult) {
        if (Objects.isNull(uploadResult)) {
            return null;
        }

        final DataExportResultFile resultFile = new DataExportResultFile();
        resultFile.setExportTaskId(uploadResult.getExportTaskId());
        resultFile.setFileName(uploadResult.getFileName());
        resultFile.setMd5(uploadResult.getMd5());
        resultFile.setDownloadUrl(uploadResult.getFileUrl());
        return resultFile;
    }

}
