package cn.newrank.niop.data.biz.controller;


import cn.newrank.iam.annotation.NkCheckPermission;
import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.pojo.vo.DsConfigDetailVo;
import cn.newrank.niop.data.biz.pojo.vo.DsConfigVo;
import cn.newrank.niop.data.biz.service.DatasourceService;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.common.ds.Collection;
import cn.newrank.niop.data.common.ds.Column;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.util.U;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/21 9:38
 */
@Validated
@RestController
@RequestMapping("datasource")
public class DatasourceController {

    private final DsConfigService dsConfigService;
    private final DatasourceService datasourceService;

    public DatasourceController(DsConfigService dsConfigService, DatasourceService datasourceService) {
        this.dsConfigService = dsConfigService;
        this.datasourceService = datasourceService;
    }

    /**
     * 新建配置
     *
     * @param configCreate 配置
     * @return id
     */
    @NkCheckPermission("datasource:edit")
    @PostMapping("config/create")
    public String create(@Valid @RequestBody DsConfigCreate configCreate) {
        return dsConfigService.create(configCreate);
    }

    /**
     * 更新配置
     *
     * @param configUpdate 配置
     * @return true/false
     */
    @PostMapping("config/update")
    public boolean update(@Valid @RequestBody DsConfigUpdate configUpdate) {
        return dsConfigService.update(configUpdate);
    }

    /**
     * 删除配置
     *
     * @param configDelete 参数
     * @return true/false
     */
    @PostMapping("config/delete")
    public boolean delete(@Valid @RequestBody DsConfigDelete configDelete) {
        return dsConfigService.delete(configDelete.getDcId());
    }

    /**
     * 链接测试
     *
     * @param connectionTest 链接测试
     * @return true/false
     */
    @PostMapping("connection/test")
    public boolean testConnection(@Valid @RequestBody DatasourceConnectionTest connectionTest) {
        return datasourceService.testConnection(connectionTest);
    }

    /**
     * 分页查询
     */
    @GetMapping("config/page")
    public PageView<DsConfigVo> page(@Valid DsPageQuery pageQuery) {
        return dsConfigService.page(pageQuery).convert(DsConfigVo::fromDto);
    }

    /**
     * 查询配置详情
     *
     * @param dcId 配置id
     * @return 配置
     */
    @GetMapping("config/detail")
    public DsConfigDetailVo get(@NotBlank(message = "数据源配置(dcId)不能为空") String dcId) {
        return DsConfigDetailVo.fromDto(dsConfigService.getConfig(dcId));
    }

    /**
     * 分页查询库下的集合
     *
     * @param pageQuery 参数
     * @return 集合
     */
    @GetMapping("collection/page")
    public PageView<Collection> pageConfigCollection(@Valid CollectionPageQuery pageQuery) {
        return datasourceService.pageCollection(pageQuery);
    }

    /**
     * 查询集合下的子集合
     *
     * @param dcId       数据源配置id
     * @param collection 集合
     * @return 子集合
     */
    @GetMapping("collection/children")
    public List<Collection> pageConfigCollection(@NotBlank(message = "数据源配置(dcId)不能为空") String dcId,
                                                 @NotBlank(message = "集合(collection)不能为空") String collection) {
        return datasourceService.getChildren(dcId, collection);
    }

    /**
     * 查询集合下的字段
     *
     * @param columnQuery 参数
     * @return 字段
     */
    @GetMapping("columns")
    public List<Column> getColumns(@Valid ColumnQuery columnQuery) {
        return datasourceService.getColumns(columnQuery);
    }

    /**
     * 查询数据源配置
     *
     * @param fuzzyQuery 模糊查询
     * @return 数据源
     */
    @GetMapping("fuzzy-search")
    public List<DsConfigVo> fuzzySearch(@Valid DsFuzzyQuery fuzzyQuery) {
        return U.toList(dsConfigService.fuzzyQuery(fuzzyQuery), DsConfigVo::fromDto);
    }

    /**
     * 根据types查询数据源配置
     *
     * @param types 根据types查询
     * @return 数据源
     */

    @PostMapping("find-ByTypes")
    public List<DsConfigVo> findByTypes(@RequestBody Map<String, List<String>> types) {
        return U.toList(dsConfigService.findByTypes(types.get("types")), DsConfigVo::fromDto);
    }

    /**
     * 数据源查询预览数据
     *
     * @param dcId 数据源配置id
     * @return 预览数据
     */
    @GetMapping("query-preview")
    public Resp.PageView queryPreview(@NotBlank(message = "数据源配置(dcId)不能为空") String dcId,
                                      @NotBlank(message = "集合(collection)不能为空") String collection) {
        return datasourceService.queryPreview(dcId, collection);
    }

    @GetMapping("query-schemaName")
    public List<String> querySchemaName(@NotBlank(message = "数据源配置(dcId)不能为空") String dcId) {
        return datasourceService.querySchemaName(dcId);
    }

    @GetMapping("query-tableName")
    public List<String> querytableName(@NotBlank(message = "数据源配置(dcId)不能为空") String dcId,
                                       String schemaName) {
        return datasourceService.querytableName(dcId, schemaName);
    }
}
