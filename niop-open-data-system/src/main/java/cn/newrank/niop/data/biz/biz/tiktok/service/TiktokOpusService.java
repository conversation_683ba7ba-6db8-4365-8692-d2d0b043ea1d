package cn.newrank.niop.data.biz.biz.tiktok.service;

import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokOpusMapper;
import cn.newrank.niop.data.biz.biz.tiktok.pojo.TiktokOpus;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
@Service
public class TiktokOpusService implements StorageBizService<TiktokOpus> {

    private final TiktokOpusMapper tiktokOpusMapper;

    public TiktokOpusService(TiktokOpusMapper tiktokOpusMapper) {
        this.tiktokOpusMapper = tiktokOpusMapper;
    }

    @Override
    public void storeBatch(List<TiktokOpus> items) {
        tiktokOpusMapper.save(items);
    }

    @Override
    public TiktokOpus get(String identifier) {
        return tiktokOpusMapper.get(identifier);
    }

    @Override
    public TiktokOpus castOf(JSONObject item) {
        return StorageBizService.format(TiktokOpus.class, item);
    }
}
