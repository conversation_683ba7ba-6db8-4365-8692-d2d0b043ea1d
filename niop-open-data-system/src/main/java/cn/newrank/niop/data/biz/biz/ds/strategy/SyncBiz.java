package cn.newrank.niop.data.biz.biz.ds.strategy;

import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.bz.BzInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.bz.BzRankInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.dy.DyInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.dy.DyRankInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.gzh.GzhInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.gzh.GzhRankSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.ks.KsInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.ks.KsRankInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.sph.SphInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.sph.SphRankInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.wb.WbInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.wb.WbRankInsertDataSyncService;
import cn.newrank.niop.data.biz.biz.ds.service.xhs.XhsInsertDataSyncService;
import cn.newrank.niop.web.model.BizEnum;
import lombok.Getter;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 同步业务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum SyncBiz implements BizEnum {
    /**
     * 公众号es增量数据
     */
    GZH_ES_INSERT_DATA(GzhInsertDataSyncService.class, PlatformType.GZH.getDbCode(), "公众号es增量数据"),


    BILI_ES_INSERT_DATA(BzInsertDataSyncService.class, PlatformType.BILI.getDbCode(), "b站es增量数据"),


    DY_ES_INSERT_DATA(DyInsertDataSyncService.class, PlatformType.DY.getDbCode(), "抖音es增量数据"),

    KS_ES_INSERT_DATA(KsInsertDataSyncService.class, PlatformType.KS.getDbCode(), "快手es增量数据"),

    WB_ES_INSERT_DATA(WbInsertDataSyncService.class, PlatformType.WEIBO.getDbCode(), "微博es增量数据"),

    SPH_ES_INSERT_DATA(SphInsertDataSyncService.class, PlatformType.SPH.getDbCode(), "视频号es增量数据"),

    XHS_ES_INSERT_DATA(XhsInsertDataSyncService.class, PlatformType.XHS.getDbCode(), "小红书es增量数据"),


    DY_RANK_ES_INSERT_DATA(DyRankInsertDataSyncService.class, RankPlatformType.DYR.getDbCode(), "抖音es榜单增量数据"),

    GZH_RANK_ES_INSERT_DATA(GzhRankSyncService.class, RankPlatformType.GZHR.getDbCode(), "公众号es榜单增量数据"),

    SPH_RANK_ES_INSERT_DATA(SphRankInsertDataSyncService.class, RankPlatformType.SPHR.getDbCode(), "视频号es榜单增量数据"),

    WB_RANK_ES_INSERT_DATA(WbRankInsertDataSyncService.class, RankPlatformType.WBR.getDbCode(), "微博es榜单增量数据"),

    BZ_RANK_ES_INSERT_DATA(BzRankInsertDataSyncService.class, RankPlatformType.BZR.getDbCode(), "B站es榜单增量数据"),


    KS_RANK_ES_INSERT_DATA(KsRankInsertDataSyncService.class, RankPlatformType.KSR.getDbCode(), "快手es榜单增量数据"),
    ;

    final Class<? extends SyncBizService<? extends SyncEntity>> syncBizServiceClass;
    final String json;
    final String description;

    SyncBiz(Class<? extends SyncBizService<? extends SyncEntity>> syncBizServiceClass,
            String json,
            String description) {
        this.syncBizServiceClass = syncBizServiceClass;
        this.json = json;
        this.description = description;
    }

    public static SyncBiz ofJSONValue(String syncBiz) {
        for (SyncBiz biz : values()) {
            if (biz.json.equals(syncBiz)) {
                return biz;
            }
        }
        throw createParamError("未知SyncBiz类型-{}", syncBiz);
    }


    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return json;
    }

    @Override
    public String getDbCode() {
        return json;
    }
}
