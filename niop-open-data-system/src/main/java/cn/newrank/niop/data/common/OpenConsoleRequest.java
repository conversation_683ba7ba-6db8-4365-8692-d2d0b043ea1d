package cn.newrank.niop.data.common;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.biz.pojo.enums.AppEnvEnum;
import cn.newrank.niop.data.context.AppContext;
import cn.newrank.nrcore.web.client.JsonBody;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * Open 控制台请求工具类
 *
 * <AUTHOR>
 */
public final class OpenConsoleRequest {

    private OpenConsoleRequest() {}

    private static final Logger log = LoggerFactory.getLogger(OpenConsoleRequest.class);

    /**
     * 公网地址
     */
    private static final String PRODUCT_HOST = "https://open.newrank.cn";

    /**
     * 内网地址
     */
    private static final String PRODUCT_INTRANET_HOST = "https://open-intranet.newrank.cn";

    private static final String HOST = AppContext.isProduct() ? PRODUCT_INTRANET_HOST : PRODUCT_HOST;

    private static final String LIST_ABILITY_URL = "/api/open/console/ability/v2/list/listByIds";
    
    private static final String ENABLE_CALLBACK_LIST_URL = "/api/open/console/ability/enable-callback/list";

    private static final int RETRIES = 3;

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
        .connectTimeout(Duration.ofSeconds(10))
        .readTimeout(Duration.ofSeconds(20))
        .writeTimeout(Duration.ofSeconds(20))
        .connectionPool(new ConnectionPool(4, 1, TimeUnit.MINUTES))
        .build();


    public static List<AbilityInfo> listAbilities(Set<String> abilityIdSet) {

        final JsonBody jsonBody = new JsonBody.Builder(JSON.toJSONString(abilityIdSet)).build();

        final Request request = new Request.Builder()
            .url(HOST + LIST_ABILITY_URL)
            .post(jsonBody)
            .build();

        for (int i = 1; i <= RETRIES; i++) {
            try (Response response = CLIENT.newCall(request).execute()) {
                final ResponseBody body = response.body();

                if (Objects.nonNull(body)) {
                    final String bodyStr = body.string();
                    JSONObject respJson = JSONObject.parseObject(bodyStr);
                    return JSON.parseArray(respJson.getString("data"), AbilityInfo.class);
                }
                throw createBizException(BizErr.REQUEST_ERROR, "Status: {}, body is null", response.code());
            } catch (Exception e) {
                log.warn("批量能力信息查询异常, param: {}, e", JSON.toJSONString(abilityIdSet), e);
                ThreadUtil.sleep(100);
            }
        }

        throw createBizException(BizErr.REQUEST_ERROR, "批量能力信息查询多次失败, 请稍后重试");
    }

    /**
     * 查询能力回调启用配置
     *
     * @param sourceId 源ID
     * @return 是否启用回调
     */
    public static boolean checkAbilityCallbackEnabled(String sourceId) {
        final String[] sourceIdArray = {sourceId};
        final JsonBody jsonBody = new JsonBody.Builder(JSON.toJSONString(sourceIdArray)).build();

        final Request request = new Request.Builder()
            .url(getEnvUrl() + ENABLE_CALLBACK_LIST_URL)
            .post(jsonBody)
            .build();

        for (int i = 1; i <= RETRIES; i++) {
            try (Response response = CLIENT.newCall(request).execute()) {
                final ResponseBody body = response.body();

                if (Objects.nonNull(body)) {
                    final String bodyStr = body.string();
                    JSONObject respJson = JSONObject.parseObject(bodyStr);
                    
                    // 使用Optional安全获取data数组并检查大小
                    return Optional.ofNullable(respJson.getJSONArray("data"))
                            .map(JSONArray::size)
                            .map(size -> size > 0)
                            .orElse(false);
                }
                log.warn("查询能力回调配置响应体为空, sourceId: {}, status: {}", sourceId, response.code());
            } catch (Exception e) {
                log.warn("请求查询能力回调配置接口异常, sourceId: {}, e", sourceId, e);
                ThreadUtil.sleep(100);
            }
        }
        return false;
    }

    public static String getEnvUrl(){
        String appEnvName = AppContext.getAppEnvName();
        switch (appEnvName) {
            case "dev":
                return "http://localhost:8020";
            case "test":
                return "http://test.open.newrank.cn";
            case "product":
                return "https://open.newrank.cn";
            default:
                return "https://open-intranet.newrank.cn";
        }

    }

    @Data
    public static class AbilityInfo {

        /**
         * 能力id
         */
        private String abilityId;

        /**
         * 能力名称
         */
        private String abilityName;

    }

}
