package cn.newrank.niop.data.config.property;

import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:17
 */
@Data
@ConfigurationProperties(prefix = "newrank.kafka")
public class KafkaProperties implements InitializingBean {
    /**
     * 默认服务器
     */
    String defaultBootstrapServers;

    /**
     * 用户名
     */
    String username;

    /**
     * 密码
     */
    String password;

    /**
     * 回调重定向
     */
    Config callbackRedirect;

    /**
     * 样本数据重定向
     */
    Config sampleRedirect;
    /**
     * 工作流重定向
     */
    Config schedulerRedirect;

    /**
     * 综合服务重定向
     */
    Config serviceHubRedirect;

    /**
     * 数据方舟重定向
     */
    Config dataArkRedirect;

    /**
     * 分析快手数据
     */
    Config analysisKs;

    /**
     * 分析快手数据
     */
    Config analysisKsExt;

    /**
     * 分析热词
     */
    Config analysisHotWord;

    /**
     * 分析热词
     */
    Config analysisDyLittleYellowCar;

    /**
     * 能力任务
     */
    Config abilityTask;


    /**
     * 工作流任务
     */
    Config schedulerTask;

    /**
     * 公众号企业认证信息
     */
    Config gzhEnterpriseCertificationInfo;

    /**
     * 公众号粉丝数据
     */
    Config gzhFansData;

    /**
     * 能力回调
     */
    Config abilityCallback;

    /**
     * 导数能力回调
     */
    Config exportAbilityCallback;

    /**
     * 导数综合服务回调
     */
    Config exportHubServiceCallback;
    /**
     * xhs 作品token
     */
    Config xhsToken;
    /**
     * 快手分类
     */
    Config analysisKsCategory;

    /**
     * xhs 大盘数据
     */
    Config xhsDapanOpusStore;

    /**
     * xhs扩量 能力回调
     */
    Config xhsExpAbilityCallback;

    /**
     * xhs 话题唯一索引
     */
    Config xhsTopicUniqueStore;

    /**
     * 公众号短长链-大盘
     */
    Config wxOpusShortLongUrl;

    /**
     * 公众号短长链-数据订阅 BF8D3D6A
     */
    Config wxOpusShortLongAbilityUrl;

    /**
     * 快手历史数据同步
     */
    Config ksHistorySyncLindorm;

    /**
     * 小红书二级分类
     */
    Config xhsOpusClassify;
    /**
     * 小红书二级分类能力结果
     */
    Config xhsOpusClassifyAbility;
    /**
     * 小红书扩样本作品
     */
    Config xhsOpusExpand;
    /**
     * ods_data_ark_obj_data
     */
    Config ods;
    /**
     * 百川 小红书
     * niop_dc_bc_xhs_dp_opus_prod
     */
    Config bcXhsDpOpus;

    @Data
    public static class Config {
        /**
         * 默认休眠时间 100ms
         */
        int sleepTime = 100;
        /**
         * 注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿
         */
        int maxPoll = 30;
        /**
         * 组
         */
        String group;
        /**
         * 服务器
         */
        String bootstrapServers;
        /**
         * 主题
         */
        String topic;
        /**
         * 用户名
         */
        String username;
        /**
         * 密码
         */
        String password;
        /**
         * plaintext 协议
         */
        boolean plaintext = false;
        /**
         * 失败重置偏移量
         */
        boolean resetOffsetAfterFailed = false;
        /**
         * 启用压缩
         */
        boolean enabledCompression = false;
    }

    @Override
    public void afterPropertiesSet() {
        ReflectionUtils.doWithFields(this.getClass(), field -> {
            ReflectionUtils.makeAccessible(field);
            final Config config = (Config) ReflectionUtils.getField(field, this);
            if (config == null) {
                return;
            }
            final String bootstrapServers = config.getBootstrapServers();
            if (bootstrapServers == null || bootstrapServers.isBlank()) {
                config.setBootstrapServers(this.defaultBootstrapServers);
                config.setUsername(this.username);
                config.setPassword(this.password);
            }
        }, field -> field.getType() == Config.class);
    }
}
