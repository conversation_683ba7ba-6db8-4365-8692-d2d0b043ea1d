package cn.newrank.niop.data.util;

import com.google.common.hash.Hashing;
import lombok.experimental.UtilityClass;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/14 15:45
 */
@UtilityClass
public class Hash {

    @SuppressWarnings("all")
    public static String sha256(String input) {
        return Hashing.sha256().hashString(input, StandardCharsets.UTF_8).toString();
    }


    public static String sha256(String input, int length) {
        final String sha256 = sha256(input);
        return length < sha256.length()
                ? sha256.substring(0, length)
                : sha256;
    }
}
