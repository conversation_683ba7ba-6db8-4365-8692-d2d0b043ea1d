package cn.newrank.niop.data.config.property;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/12/21 10:26
 */
@Component
@Data
public class SystemProperties {
    /**
     * Spring active profile
     */
    @Value("${spring.config.activate.on-profile}")
    private String activeProfile;

}
