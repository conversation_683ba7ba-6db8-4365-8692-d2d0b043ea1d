package cn.newrank.niop.data.biz.data.ark.mapper;

import cn.newrank.niop.data.biz.data.ark.pojo.ObjData;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/8/27 10:36
 */
@DS("niop_dc_ldm")
@Mapper
public interface ObjDataMapper {
    /**
     * 批量写入数据
     *
     * @param list 列表
     * @return 受影响行数
     */
    int batchInsert(@Param("list") List<ObjData> list);
}
