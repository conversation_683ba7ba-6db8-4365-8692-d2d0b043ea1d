package cn.newrank.niop.data.biz.biz.ds.service.wb;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * [微博认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WbVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET search_wb_user/_search?scroll=15m
            {
                "_source": [
                  "uid",
                  "verified_reason",
                  "verified",
                  "verified_type"
                ],
              "size": 1000,
              "query": {
                "match_all": {}
              }
            }
            """;

    protected WbVerifyInfoHistorySync(RedissonClient redissonClient,
                                      CommonEsService commonEsService,
                                      DsConfigManager dsConfigManager) {
        super(PlatformType.WEIBO.getDbCode(), dsConfigManager.chooseWbEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final WbVerifyInfoUpdate update = new WbVerifyInfoUpdate();
        update.setIndexId(PlatformType.WEIBO.getDbCode() + "_" + sourceObj.get("uid"));
        update.setVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("verified_reason"), CharSequenceUtil.EMPTY));
        update.setEnterpriseVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("verified_reason"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV1(StrUtil.nullToDefault(sourceObj.getString("verified"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV2(StrUtil.nullToDefault(sourceObj.getString("verified_type"), CharSequenceUtil.EMPTY));

        return update;
    }

    @Data
    public static class WbVerifyInfoUpdate extends VerifyInfoUpdate {

        private String enterpriseVerifyInfo;

    }
}
