package cn.newrank.niop.data.biz.biz.ds.service.xhs;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * [xhs认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class XhsVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET search_xhs_user/_search?scroll=15m
            {
                "_source": [
                  "userid",
                  "official_verify",
                  "identify_sign"
                ],
              "size": 1000,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "official_verify" } },
                    { "exists": { "field": "identify_sign" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected XhsVerifyInfoHistorySync(RedissonClient redissonClient,
                                       CommonEsService commonEsService,
                                       DsConfigManager dsConfigManager) {
        super(PlatformType.XHS.getDbCode(), dsConfigManager.chooseXhsEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final VerifyInfoUpdate update = new VerifyInfoUpdate();
        update.setIndexId(PlatformType.XHS.getDbCode() + "_" + sourceObj.get("userid"));
        update.setVerifyInfo(StrUtil.nullToDefault(sourceObj.getString("official_verify"), CharSequenceUtil.EMPTY));
        update.setVerifyTypeV1(StrUtil.nullToDefault(sourceObj.getString("identify_sign"), CharSequenceUtil.EMPTY));

        return update;
    }
}
