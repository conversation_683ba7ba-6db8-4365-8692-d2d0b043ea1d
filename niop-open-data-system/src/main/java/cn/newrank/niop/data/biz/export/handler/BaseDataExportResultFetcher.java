package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;

/**
 * <AUTHOR>
 */
public abstract class BaseDataExportResultFetcher implements DataExportResultFetcher {

    protected final DataExportSubtaskService subtaskService;

    protected BaseDataExportResultFetcher(DataExportSubtaskService subtaskService) {
        this.subtaskService = subtaskService;
    }

    @Override
    public void handleNullData(String resultTaskId) {
        subtaskService.updateSubtaskDataIsNull(resultTaskId);
    }

}
