package cn.newrank.niop.data.biz.analysis.pojo;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyOpus;
import cn.newrank.niop.data.util.DateTimeUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/3 10:47
 */
@Data
public class DyBusinessAweme {
    /**
     * 达人ID
     */
    private String bloggerUid;
    /**
     * 作品合作品牌ID
     */
    private String brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 1：指派
     * 4：投稿
     * 133：招募
     * 134：星广联投
     * 其他
     */
    private Integer e_type;
    /**
     * 发布时间
     */
    private String publishTime;

    public static DyBusinessAweme of(DyOpus dyOpus) {
        final DyBusinessAweme dyBusinessAweme = new DyBusinessAweme();

        dyBusinessAweme.setBloggerUid(dyOpus.getBloggerUid());
        dyBusinessAweme.setBrandId(dyOpus.getBrandId());
        dyBusinessAweme.setBrandName(dyOpus.getBrandName());
        dyBusinessAweme.setPublishTime(DateTimeUtil.format(dyOpus.getPublishTime()));
        dyBusinessAweme.setE_type(dyOpus.getEType());

        return dyBusinessAweme;
    }
}
