package cn.newrank.niop.data.biz.pojo.param;


import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.enums.DsType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/9 16:40
 */
@Data
public class DatasourceConnectionTest {
    @NotNull(message = "数据源类型(type)不能为空")
    DsType type;
    /**
     * 配置信息
     */
    Map<String, Object> config;

    public DsConfig toDto() {
        final DsConfig datasource = new DsConfig();
        datasource.setType(type);
        datasource.setConfig(ConfigProperties.of(config));

        return datasource;
    }
}
