package cn.newrank.niop.data.biz.pojo.param;


import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.enums.CbType;
import cn.newrank.niop.data.common.ConfigProperties;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 10:26
 */
@Data
public class CallbackConfigCreate {
    String name;

    CbType type;

    String source;

    Map<String, Object> config;
    /**
     * 负责人
     */
    @NotEmpty(message = "负责人(maintainers)不能为空")
    List<String> maintainers;


    public CbConfig toDto() {
        final CbConfig cbConfig = new CbConfig();

        cbConfig.setName(name);
        cbConfig.setType(type);
        cbConfig.setConfig(ConfigProperties.of(config));
        cbConfig.setSource(StringUtils.isBlank(source) ? "default" : source);
        cbConfig.setMaintainers(maintainers);

        return cbConfig;
    }
}
