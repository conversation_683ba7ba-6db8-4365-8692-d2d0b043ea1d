package cn.newrank.niop.data.biz.callback.service;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.listener.AbstractEventListener;
import com.alibaba.nacos.api.naming.listener.Event;
import com.alibaba.nacos.api.naming.listener.NamingEvent;
import com.alibaba.nacos.api.naming.pojo.Instance;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/25 17:25
 */
@Log4j2
public abstract class AbstractNacosServiceRefresher implements ApplicationContextAware, ApplicationRunner {

    protected AtomicBoolean initialized = new AtomicBoolean(false);
    protected NamingService namingService;
    protected NacosDiscoveryProperties nacosDiscoveryProperties;
    protected String serviceName;
    protected String group;
    ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    @Override
    public void run(ApplicationArguments args) throws Exception {
        this.nacosDiscoveryProperties = applicationContext.getBean(NacosDiscoveryProperties.class);
        this.namingService = applicationContext.getBean(NacosServiceManager.class).getNamingService();
        this.serviceName = nacosDiscoveryProperties.getService();
        this.group = nacosDiscoveryProperties.getGroup();
        initNacosEventListener();
        initialized.set(true);
    }

    private void initNacosEventListener() throws NacosException {
        namingService.subscribe(serviceName, group, new AbstractEventListener() {
            @Override
            public void onEvent(Event event) {
                if (isInitialized() && event instanceof NamingEvent namingEvent) {
                    refresh(namingEvent);
                }
            }
        });
    }

    protected abstract void refresh(NamingEvent event);


    protected boolean isInitialized() {
        return initialized.get();
    }

    /**
     * 获取健康实例
     *
     * @return 实例
     */
    public List<String> getServerAddresses() {
        try {
            final List<Instance> healthyInstances = namingService.selectInstances(serviceName, group, true);
            if (healthyInstances == null) {
                return List.of();
            }

            return healthyInstances.stream()
                    .map(instance -> instance.getIp() + ":" + instance.getPort())
                    .distinct()
                    .toList();
        } catch (NacosException e) {
            log.error("获取健康实例失败", e);
        }

        return List.of();
    }
}
