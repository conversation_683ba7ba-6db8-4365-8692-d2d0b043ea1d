package cn.newrank.niop.data.biz.biz.ds.service.bz;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.BzEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_BZ_RANK_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;

/**
 * <AUTHOR>
 * @date 2024/9/6 上午9:48
 */
@Log4j2
@Service
public class BzRankInsertDataSyncService implements SyncBizService<BzEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public BzRankInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseBzEsConfig();
    }

    @Override
    public String getIndexName() {
        return "bili_user_detail_rank_week";
    }


    @Override
    public String getRangField() {
        return "newrank_index";
    }


    @Override
    public String getRangIndex() {
        return "rank_date";
    }

    @Override
    public String getUniqueIndex() {
        return MAIN_BZ_RANK_FIELD_MAPPING.get("account_id");
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_BZ_RANK_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }

        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<BzEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), BzEsMetaData.class);
        });
    }

    @Override
    public List<BzEsMetaData> convertData(List<BzEsMetaData> dataList) {
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (BzEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_BZ_RANK_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", PlatformType.BILI.getDbCode());


            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.BILI.getDbCode() + "_" + mainMap.get("account_id"));
            }

            // 移除空值字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }

        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), BzEsMetaData.class);
    }
}
