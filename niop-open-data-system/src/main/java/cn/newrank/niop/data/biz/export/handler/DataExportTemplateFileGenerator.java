package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.InitializingBean;

/**
 * <AUTHOR>
 */
public interface DataExportTemplateFileGenerator extends InitializingBean {

    /**
     * 获取导数类型
     *
     * @return 导数类型
     */
    DataExportType getExportType();

    /**
     * 创建模板文件
     *
     * @param exportId 导数id
     * @return 模板文件
     * @throws IOException IO异常
     */
    File createFile(String exportId) throws IOException;

    /**
     * 写入模板文件
     *
     * @param file 模板文件
     * @param export 导数信息
     * @return 是否成功
     */
    boolean writeTemplateFile(File file, DataExport export);

    /**
     * 获取目录
     *
     * @return 目录
     */
    default String getDir() {
        return System.getProperty("user.home") + File.separator + "template" + File.separator;
    }

    /**
     * 创建目录文件
     */
    default void createDirIfNotExist() {
        File f = new File(getDir());
        if (!f.exists()) {
            f.mkdirs();
        }
    }

    /**
     * 初始化方法
     */
    @Override
    default void afterPropertiesSet() {
        // 创建模板文件目录
        createDirIfNotExist();
    }

}
