package cn.newrank.niop.data.biz.biz.dy.scheduler;

import cn.newrank.niop.data.biz.biz.dy.service.DyOpusService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Component
public class DyScheduler {

    private final DyOpusService dyOpusService;


    public DyScheduler(DyOpusService dyOpusService) {
        this.dyOpusService = dyOpusService;
    }


    @XxlJob("DyBizOpusSync")
    public ReturnT<String> syncBizOpus(String startTime) {
        return dyOpusService.syncBizOpus(startTime);
    }
}
