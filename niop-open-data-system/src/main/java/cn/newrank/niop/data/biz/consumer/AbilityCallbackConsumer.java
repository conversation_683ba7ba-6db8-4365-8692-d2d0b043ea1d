package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.biz.callback.service.CallbackRedirectService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.config.property.KafkaProperties;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 能力回调消费者
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 17:51
 */
public class AbilityCallbackConsumer extends CommonConsumer {

    public AbilityCallbackConsumer(KafkaProperties.Config config,
                                   Environment environment,
                                   CallbackRedirectService redirectService,
                                   CallbackService callbackService,
                                   ThreadPoolExecutor executor) {
        super(config, environment,
                new AbilityCallbackConsumeService(redirectService, callbackService),
                executor);
    }
}
