package cn.newrank.niop.data.biz.biz.ds.service.ks;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.KsEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.EMPTY;
import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_KS_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class KsInsertDataSyncService implements SyncBizService<KsEsMetaData> {


    private final DsConfigManager dsConfigManager;

    public KsInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseKsEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_kuaishou_user";
    }

    @Override
    public String getSortField() {
        return "userId";
    }

    @Override
    public String getRangField() {
        return "updateTime";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_KS_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<KsEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), KsEsMetaData.class);
        });
    }


    @Override
    public List<KsEsMetaData> convertData(List<KsEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (KsEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();
            for (Map.Entry<String, String> entry : MAIN_KS_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            if (data.get("profile") instanceof LinkedHashMap<?, ?> hashMap) {
                mainMap.put("officail_display_id", hashMap.get("kwaiId"));
                mainMap.put("account_name", hashMap.get("userName"));
                mainMap.put("gender", hashMap.get("userSex"));
                mainMap.put("age", hashMap.get("age"));
                mainMap.put("province", hashMap.get("province"));
                mainMap.put("city", hashMap.get("city"));
                if (hashMap.get("verifiedDetail") instanceof LinkedHashMap<?, ?> hashMap1) {
                    mainMap.put("verify_info", hashMap1.get("description"));
                }
                mainMap.put("signature", hashMap.get("userText"));
                mainMap.put("avatar_url", hashMap.get("headurl"));
            }

            JSONObject profile = Optional.ofNullable(data.getJSONObject("profile")).orElse(new JSONObject());
            JSONObject verifiedDetail = Optional.ofNullable(profile.getJSONObject("verifiedDetail")).orElse(new JSONObject());

            mainMap.put("verify_info", Optional.ofNullable(verifiedDetail.getString("description")).orElse(EMPTY));
            mainMap.put("verify_type_v1", Optional.ofNullable(verifiedDetail.getString("type")).orElse(EMPTY));
            mainMap.put("verify_type_v2", Optional.ofNullable(data.getString("accountVerified")).orElse(EMPTY));

            if (data.get("ownerCount") instanceof LinkedHashMap<?, ?> hashMap) {
                mainMap.put("follower_count", hashMap.get("fan"));
                mainMap.put("total_aweme_count", hashMap.get("photo"));
            }

            if (data.get("photoCacl") instanceof LinkedHashMap<?, ?> hashMap) {
                mainMap.put("aweme_count_rt_thirty_days", hashMap.get("photoCount30"));
            }

            mainMap.put("platform_type", PlatformType.KS.getDbCode());

            mainMap.put("gender", mapGenderValue((String) mainMap.get("gender")));

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.KS.getDbCode() + "_" + mainMap.get("account_id"));
            }

            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), KsEsMetaData.class);
    }

    private String mapGenderValue(String gender) {
        if (gender == null) {
            return "未知";
        } else if (gender.equals("M")) {
            return "男";
        } else if (gender.equals("F")) {
            return "女";
        }
        return "未知";
    }

}
