package cn.newrank.niop.data.biz.biz.dy.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;
import org.mockito.internal.verification.Times;

/**
 * 
 * @TableName dwd_live_comment_v2
 */
@TableName(value ="dwd_live_comment_v2")
@Data
public class DyLiveCommentV2 implements Serializable {
    /**
     * 
     */
    private String roomId;

    /**
     * 
     */
    private String msgId;

    /**
     * 
     */
    private Timestamp gmtCreate;

    /**
     * 
     */
    private String msgType;

    /**
     * 
     */
    private String msgMethod;

    /**
     * 
     */
    private Integer gender;

    /**
     * 
     */
    private String secUid;

    /**
     * 
     */
    private String avatar;

    /**
     * 
     */
    private String shortId;

    /**
     * 
     */
    private String uid;

    /**
     * 
     */
    private String displayId;

    /**
     * 
     */
    private String nickname;

    /**
     * 
     */
    private String content;

    /**
     * 
     */
    private Timestamp anaTime;

    /**
     * 
     */
    private Integer followerCount;

    /**
     * 
     */
    private String dataType;
    private Timestamp liveStartTime;
}