package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.hutool.core.util.ObjUtil;
import cn.newrank.niop.data.util.StrPool;
import lombok.Data;

import java.sql.Timestamp;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class YsDyOpus {
    String aweme_id;
    String title;
    Timestamp pub_time;
    Integer task_category;
    String brand_id;
    String brand_name;
    String blogger_uid;
    Integer update_status;
    Timestamp update_time;
    Integer play_cnt;
    Integer like_cnt;
    Integer comment_cnt;
    Integer share_cnt;
    Integer collect_cnt;
    Integer aweme_type;

    public DyOpus toDyOpus() {
        final DyOpus dyOpus = new DyOpus();

        dyOpus.setAwemeId(aweme_id);
        dyOpus.setTitle(ObjUtil.defaultIfNull(title, StrPool.EMPTY));
        dyOpus.setPublishTime(pub_time);
        dyOpus.setEType(ObjUtil.defaultIfNull(task_category, 0));
        dyOpus.setBrandId(brand_id);
        dyOpus.setBrandName(brand_name);
        dyOpus.setBloggerUid(blogger_uid);
        // -1 表示数据为空的场景，后面类似
        dyOpus.setSUpdateStatus(ObjUtil.defaultIfNull(update_status, -1));
        dyOpus.setSUpdateTime(update_time);
        dyOpus.setPlayNum(ObjUtil.defaultIfNull(play_cnt, -1));
        dyOpus.setLikeNum(ObjUtil.defaultIfNull(like_cnt, -1));
        dyOpus.setCommentNum(ObjUtil.defaultIfNull(comment_cnt, -1));
        dyOpus.setShareNum(ObjUtil.defaultIfNull(share_cnt, -1));
        dyOpus.setCollectNum(ObjUtil.defaultIfNull(collect_cnt, -1));

        return dyOpus;
    }

}
