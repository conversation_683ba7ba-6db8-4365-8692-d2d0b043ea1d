package cn.newrank.niop.data.biz.export.pojo.vo;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportSubtaskStatus;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.data.util.DateTimeUtil;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;

@Data
public class DataExportSubtaskVo {

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 结果任务id
     */
    private String resultTaskId;

    /**
     * 参数 Json
     */
    private String paramJson;

    /**
     * 业务code
     */
    private Integer bizCode;

    /**
     * 业务消息
     */
    private String bizMsg;

    /**
     * 子任务状态
     */
    private DataExportSubtaskStatus subtaskStatus;

    /**
     * 数据量
     */
    private Integer dataNum;

    /**
     * 任务完成时间
     */
    private String taskFinishedTime;

    /**
     * 创建时间
     */
    private String gmtCreate;

    public static DataExportSubtaskVo buildBy(DataExportSubtask subtask) {
        if (Objects.isNull(subtask)) {
            return null;
        }

        final DataExportSubtaskVo vo = new DataExportSubtaskVo();
        vo.setExportTaskId(subtask.getExportTaskId());
        vo.setResultTaskId(subtask.getResultTaskId());
        vo.setParamJson(subtask.getParamJson());
        vo.setBizCode(subtask.getBizCode());
        vo.setBizMsg(subtask.getBizMsg());
        vo.setDataNum(subtask.getDataNum());
        vo.setSubtaskStatus(subtask.getSubtaskStatus());
        final String taskFinishedTime = Optional.ofNullable(subtask.getTaskFinishedTime())
            .map(DateTimeUtil::format)
            .orElse("-");
        vo.setTaskFinishedTime(taskFinishedTime);
        vo.setGmtCreate(DateTimeUtil.simplifyDateTime(subtask.getGmtCreate()));
        return vo;
    }

}
