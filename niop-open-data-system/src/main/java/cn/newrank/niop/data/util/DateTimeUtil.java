package cn.newrank.niop.data.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import lombok.experimental.UtilityClass;
import org.apache.logging.log4j.util.Strings;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/7 14:35
 */
@UtilityClass
public class DateTimeUtil {
    public static final String MAX_DATE_TIME = "2060-12-31 23:59:59";
    public static final DateTimeFormatter NORMAL_DT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter NORMAL_D_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter UTC_SIMPLE_FORMATTER = DateTimeFormatter.ofPattern(DatePattern.UTC_SIMPLE_PATTERN);
    public static final DateTimeFormatter UTC_SIMPLE_MS_FORMATTER = DateTimeFormatter.ofPattern(DatePattern.UTC_SIMPLE_MS_PATTERN);
    public static final Integer MAX_SECOND_TIMESTAMP = 10;
    /**
     * 转换为时间戳
     *
     * @param dateTime 字符串
     * @return 时间戳
     */
    public static long toMillis(String dateTime) {
        return toDateTime(dateTime).toInstant(java.time.ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * 时间戳转换为时间
     *
     * @param timestamp 时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime timeStampToDateTime(long timestamp) {
        if (timestamp < 0) {
            throw new IllegalArgumentException("时间戳不能为负数");
        }
        Instant instant ;
        if(String.valueOf(timestamp).length() > MAX_SECOND_TIMESTAMP){
            instant = Instant.ofEpochMilli(timestamp);
        }else{
            instant = Instant.ofEpochSecond(timestamp);
        }
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }


    /**
     * 转换为日期时间
     *
     * @param dateTime 字符串
     * @return LocalDateTime
     */
    public static LocalDateTime toDateTime(String dateTime) {
        try {
            return LocalDateTime.parse(dateTime, NORMAL_DT_FORMATTER);
        } catch (Exception e) {
            throw createParamError("{} 不符合日期格式(yyyy-MM-dd HH:mm:ss)", dateTime);
        }
    }

    /**
     * 转换为日期
     *
     * @param date 字符串
     * @return LocalDate
     */
    public static LocalDate toDate(String date) {
        try {
            return LocalDate.parse(date, NORMAL_D_FORMATTER);
        } catch (Exception e) {
            throw createParamError("{} 不符合日期格式(yyyy-MM-dd)", date);
        }
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 时间
     * @return 字符串
     */
    public static String format(TemporalAccessor dateTime) {
        if (dateTime instanceof LocalDateTime) {
            return NORMAL_DT_FORMATTER.format(dateTime);
        }

        if (dateTime instanceof LocalDate) {
            return NORMAL_D_FORMATTER.format(dateTime);
        }

        throw createParamError("{} 不是日期类型", dateTime);
    }

    public static String format(Timestamp timestamp) {
        return format(timestamp.toLocalDateTime());
    }

    public static int toSeconds(LocalDateTime dateTime) {
        return Math.toIntExact(toEpochMillis(dateTime) / 1000);
    }

    public static long toEpochMillis(LocalDateTime dateTime) {
        return dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static String format(TemporalAccessor dateTime, String fmt) {
        return DateTimeFormatter.ofPattern(fmt).format(dateTime);
    }


    /**
     * 获取 上一周的周天
     *
     * @param localDate 日期
     * @return {@link LocalDate }  上一周周日
     */
    public static LocalDate getSundayOfLastWeek(LocalDate localDate) {
        final List<LocalDate> dataList = getEachDayOfWeek(localDate, "last");
        return dataList.get(dataList.size() - 1);
    }

    /**
     * 获取 当前周 | 上一周 | 下一周 的每一天日期
     *
     * @param timePoint 时间点
     * @param type      获取类型 可选值为："current"（当前周）、"last"（上一周）、"next"（下一周）
     * @return List<LocalDate> 包含每一天的日期信息列表，从周一到周天按照顺序返回<br>
     * <p>
     * getEachDayOfWeek(LocalDate.of(2024, 8, 23), "last"); <br>
     * <p>
     * 返回列表：["2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-17","2024-08-18"]
     */
    public static List<LocalDate> getEachDayOfWeek(LocalDate timePoint, String type) {
        LocalDate localToday = timePoint;

        switch (type) {
            case "current":
                break;
            case "last":
                localToday = localToday.minusWeeks(1);
                break;
            case "next":
                localToday = localToday.plusWeeks(1);
                break;
            default:
                throw new IllegalArgumentException("Invalid type: " + type);
        }

        // 获取周一开始的日期
        final LocalDate weekStart = localToday.with(DayOfWeek.MONDAY);

        final List<LocalDate> days = new ArrayList<>(7);
        for (int i = 0; i < 7; i++) {
            LocalDate date = weekStart.plusDays(i);
            days.add(date);
        }

        return days;
    }

    public static String monthOfPartition(Timestamp timestamp) {
        return format(timestamp.toLocalDateTime(), "yyyyMM");
    }

    public static String monthOfPartition(TemporalAccessor accessor) {
        return format(accessor, "yyyyMM");
    }

    public static String dayOfPartition(TemporalAccessor accessor) {
        return format(accessor, "yyyyMMdd");
    }

    /**
     * 简化日期时间字符串
     *
     * e.g.
     * 2024-11-11 19:29:43.926982 => 2024-11-11 19:29:43
     *
     * @param dateTime 日期时间字符串
     * @return 简化后的日期时间
     */
    public static String simplifyDateTime(String dateTime) {
        return dateTime.contains(StrPool.DOT) ? dateTime.split("\\.")[0] : dateTime;
    }

    /**
     * 判断字符串是否为指定格式的日期时间
     *
     * @param dateStr    需要检查的日期字符串
     * @param dateFormat 指定的日期格式，例如："yyyyMMdd", "yyyy-MM-dd", "yyyy/MM/dd" 等
     * @return 如果字符串是指定格式的日期时间，返回 true; 否则返回 false
     */
    public static boolean isValidDateFormat(String dateStr, String dateFormat) {
        if (ObjUtil.isEmpty(dateStr)) {
            return false;
        }
        try {
            DateUtil.parse(dateStr, dateFormat);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 时间格式转换
     *
     * @param time
     * @return
     */
    public static String formatUtcDateTime(String time) {
        LocalDateTime localDateTime = parseUtcToLocalDateTime(time);
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.format(DatePattern.NORM_DATETIME_FORMATTER);
    }

    public static LocalDateTime parseUtcToLocalDateTime(String time) {
        if (Strings.isBlank(time)) {
            return null;
        }
        LocalDateTime localDateTime;
        try {
            localDateTime = LocalDateTime.parse(time, UTC_SIMPLE_FORMATTER);
        } catch (Exception e) {
            localDateTime = LocalDateTime.parse(time, UTC_SIMPLE_MS_FORMATTER);
        }
        return localDateTime;
    }
}
