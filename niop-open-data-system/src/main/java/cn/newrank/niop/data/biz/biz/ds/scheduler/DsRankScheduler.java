package cn.newrank.niop.data.biz.biz.ds.scheduler;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonRankSyncInsertData;
import cn.newrank.niop.data.biz.biz.ds.service.xhs.XhsRankSync;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.EnumSet;


/**
 * [榜单数据同步]
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RestController
public class DsRankScheduler {

    private final CommonRankSyncInsertData commonRankSyncInsertData;
    private final XhsRankSync xhsRankSync;

    public DsRankScheduler(CommonRankSyncInsertData commonRankSyncInsertData, XhsRankSync xhsRankSync) {
        this.commonRankSyncInsertData = commonRankSyncInsertData;
        this.xhsRankSync = xhsRankSync;
    }

    @XxlJob("syncRankInsertDataSync")
    public ReturnT<String> commonRankSyncInsertData(String params) {
        boolean isStart = false;
        //平台，时间
        EnumSet<RankPlatformType> allTypes = EnumSet.allOf(RankPlatformType.class);
        for (RankPlatformType platformType : allTypes) {
            String code = platformType.getDbCode();
            if (StrUtil.isNotBlank(params) && !isStart) {
                if (code.equals(params)) {
                    isStart = true;
                } else {
                    continue;
                }
            }
            // if (code.equals("dyr") || code.equals("gzhr")) continue;

            commonRankSyncInsertData.syncInsertData("", platformType);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataDYR")
    public ReturnT<String> rankSyncInsertDataDYR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.DYR);

        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataGZHR")
    public ReturnT<String> rankSyncInsertDataGZHR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.GZHR);

        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataSPHR")
    public ReturnT<String> rankSyncInsertDataSPHR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.SPHR);

        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataWBR")
    @GetMapping("rankSyncInsertDataWBR")
    public ReturnT<String> rankSyncInsertDataWBR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.WBR);

        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataKSR")
    public ReturnT<String> rankSyncInsertDataKSR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.KSR);

        return ReturnT.SUCCESS;
    }

    @XxlJob("rankSyncInsertDataBZR")
    @GetMapping("rankSyncInsertDataBZR")
    public ReturnT<String> rankSyncInsertDataBZR(String params) {

        commonRankSyncInsertData.syncInsertData("", RankPlatformType.BZR);

        return ReturnT.SUCCESS;
    }

    /**
     * 大搜新榜指数数据同步(周一下午四点)
     */
    @XxlJob("DsRankSyncFirst")
    public ReturnT<String> dsRankSyncFirst(String params) {
        final LocalDateTime now = LocalDateTime.now();
        final LocalDateTime lastMonDay = LocalDate.now().atStartOfDay().with(DayOfWeek.MONDAY).minusWeeks(1);

        xhsRankSync.newSync(lastMonDay, now);

        return ReturnT.SUCCESS;
    }

    /**
     * 大搜新榜指数数据同步(周二十二点)
     */
    @XxlJob("DsRankSyncSecond")
    public ReturnT<String> dsRankSyncSecond(String params) {
        final LocalDateTime now = LocalDateTime.now();
        final LocalDateTime lastMonDay = LocalDate.now().atStartOfDay().with(DayOfWeek.MONDAY).minusWeeks(1);

        xhsRankSync.newSync(lastMonDay, now);

        return ReturnT.SUCCESS;
    }

}
