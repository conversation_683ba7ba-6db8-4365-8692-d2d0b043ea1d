package cn.newrank.niop.data.common;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.biz.export.atsp.AbilityResult;
import cn.newrank.niop.data.biz.export.atsp.AtspSubmitResult;
import cn.newrank.niop.data.common.model.AtspSubmitResp;
import cn.newrank.niop.data.context.AppContext;
import cn.newrank.niop.model.NiopHeaders;
import cn.newrank.niop.sdk.common.Environment;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.newrank.niop.web.exception.BizExceptions.createBizException;

/**
 * <AUTHOR>
 * @since 2025/4/2 11:45:56
 */
@Slf4j
public class AtspClient {

    public static final Environment HOST = AppContext.isDev() ? Environment.PRODUCT_OUTSIDE : Environment.PRODUCT;
    public static final String SUBMIT_URL = HOST.getGatewayContextPath() + "open/atsp/task/submit";
    public static final String GET_RESULT_URL = HOST.getGatewayContextPath() + "open/atsp/task/result";
    public static final MediaType MEDIA_TYPE = MediaType.get("application/json");

    private static final int RETRIES = 3;

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(5))
            .readTimeout(Duration.ofSeconds(10))
            .writeTimeout(Duration.ofSeconds(10))
            .connectionPool(new ConnectionPool(40, 1, TimeUnit.MINUTES))
            .build();

    /**
     *
     * @param abilityId
     * @param param
     * @param appKey
     * @return
     */
    public static AtspSubmitResp submitTask(String abilityId,
                                              Map<String, Object> param,
                                              String appKey) {
        try (Response response = CLIENT.newCall(submitRequest(abilityId, param, appKey)).execute()) {
            final ResponseBody body = response.body();
            if (Objects.nonNull(body)) {
                final String bodyStr = body.string();
                JSONObject respJson = JSONObject.parseObject(bodyStr);
                return AtspSubmitResp.of(respJson);
            }
            throw createBizException(BizErr.REQUEST_ERROR, "Status: {}, body is null", response.code());
        } catch (Exception e) {
            log.warn("任务提交失败, abilityId: {}, param: {}, e: ", abilityId, JSONObject.toJSONString(param), e);
        }
        return AtspSubmitResp.failed();
    }

    public static AbilityResult getResult(String taskId, String appKey) {
        final String url = GET_RESULT_URL + "?taskId=" + taskId;
        final Request request = resultGetRequest(url,appKey);
        for (int i = 1; i <= RETRIES; i++) {
            try (Response response = CLIENT.newCall(request).execute()) {
                final ResponseBody body = response.body();
                if (Objects.nonNull(body)) {
                    return AbilityResult.of(body.string());
                }
            } catch (Exception e) {
                log.warn("能力结果查询失败, taskId: {}, e: ", taskId, e);
                ThreadUtil.sleep(300);
            }
        }
        throw createBizException(BizErr.REQUEST_ERROR, "能力结果查询多次失败, 请稍后重试");
    }

    private static Request resultGetRequest(String url, String appKey) {
        return new Request.Builder().url(url)
                .addHeader(NiopHeaders.APP_KEY, appKey)
                .get().build();
    }

    private static Request submitRequest(String abilityId, Map<String, Object> param, String appKey) {
        return new Request.Builder()
                .url(SUBMIT_URL)
                .addHeader(NiopHeaders.APP_KEY, appKey)
                .addHeader(NiopHeaders.ABILITY_ID, abilityId)
                .post(RequestBody.create(JSON.toJSONString(param), MEDIA_TYPE))
                .build();
    }
}
