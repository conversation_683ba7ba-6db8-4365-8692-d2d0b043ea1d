package cn.newrank.niop.data.biz.pojo.po;

import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:38
 */
@Data
public class DynamicInterfaceAuthPo {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private Timestamp gmtModified;

    /**
     *
     */
    private Timestamp gmtCreate;

    /**
     * 授权ID
     */
    private String authId;

    /**
     * 接口ID
     */
    private String interfaceId;

    /**
     * 应用ID
     */
    private String appId;


    /**
     * QPS 时间间隔
     */
    private Integer refreshPermits;

    /**
     * QPS 次数
     */
    private Integer refreshSeconds;

    public static DynamicInterfaceAuthPo of(DynamicInterfaceAuth dynamicInterfaceAuth) {
        DynamicInterfaceAuthPo authPo = new DynamicInterfaceAuthPo();

        authPo.setAuthId(dynamicInterfaceAuth.getAuthId());
        authPo.setInterfaceId(dynamicInterfaceAuth.getInterfaceId());
        authPo.setAppId(dynamicInterfaceAuth.getAppId());
        authPo.setRefreshPermits(dynamicInterfaceAuth.getRefreshPermits());
        authPo.setRefreshSeconds(dynamicInterfaceAuth.getRefreshSeconds());

        return authPo;

    }

    public DynamicInterfaceAuth toDto() {
        DynamicInterfaceAuth dynamicInterfaceAuth = new DynamicInterfaceAuth();
        dynamicInterfaceAuth.setAppId(this.appId);
        dynamicInterfaceAuth.setAuthId(this.authId);
        dynamicInterfaceAuth.setGmtCreate(this.gmtCreate.toLocalDateTime());
        dynamicInterfaceAuth.setGmtModified(this.gmtModified.toLocalDateTime());
        dynamicInterfaceAuth.setInterfaceId(this.interfaceId);
        dynamicInterfaceAuth.setRefreshPermits(this.refreshPermits);
        dynamicInterfaceAuth.setRefreshSeconds(this.refreshSeconds);
        return dynamicInterfaceAuth;
    }

}