package cn.newrank.niop.data.biz.dataclear.controller;

import cn.newrank.iam.annotation.NkCheckPermission;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.param.*;
import cn.newrank.niop.data.biz.dataclear.pojo.vo.DataCleanRuleSearchVo;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanRuleService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@RequestMapping("date-clear")
public class DataCleanRuleController {

    private final DataCleanRuleService dataCleanRuleService;


    public DataCleanRuleController(DataCleanRuleService dataCleanRuleService) {
        this.dataCleanRuleService = dataCleanRuleService;

    }

    /**
     * 新建配置
     *
     * @param dataCleanRuleCreate 配置
     * @return id
     */
    @NkCheckPermission("dc:edit")
    @PostMapping("create")
    public String create(@Valid @RequestBody DataCleanRuleCreateQuery dataCleanRuleCreate) {
        return dataCleanRuleService.create(dataCleanRuleCreate);
    }


    /**
     * 通过数据源id查询
     *
     * @param dataCleanPageQuery 分页
     * @return 分页
     * search
     */
    @GetMapping("search")
    public PageView<DataCleanRuleSearchVo> page(@Valid DataCleanPageQuery dataCleanPageQuery) {
        return dataCleanRuleService.page(dataCleanPageQuery);
    }

    /**
     * 更新
     *
     * @param cleanRuleUpadteQuery 更新
     * @return 成功
     */
    @NkCheckPermission("dc:edit")
    @PostMapping("update")
    public boolean update(@Valid @RequestBody CleanRuleUpadteQuery cleanRuleUpadteQuery) {
        return dataCleanRuleService.update(cleanRuleUpadteQuery);
    }

    /**
     * 模糊查询
     *
     * @param fuzzyQuery 模糊查询
     * @return 配置
     */
    @GetMapping("fuzzy-search")
    public List<DataCleanRuleCreate> fuzzySearch(@Valid DataCleanFuzzyQuery fuzzyQuery) {
        return dataCleanRuleService.fuzzyQuery(fuzzyQuery);
    }

    /**
     * 删除
     *
     * @param cbDelete 删除
     * @return 成功
     */
    @NkCheckPermission("dc:edit")
    @PostMapping("delete")
    public boolean delete(@Valid @RequestBody DataCleanDeleteQuery cbDelete) {
        return dataCleanRuleService.delete(cbDelete.getRuleId());
    }


    @GetMapping("get")
    public DataCleanRuleCreate get(@NotBlank(message = "规则id不能为空") String ruleId) {
        return dataCleanRuleService.get(ruleId);
    }

}
