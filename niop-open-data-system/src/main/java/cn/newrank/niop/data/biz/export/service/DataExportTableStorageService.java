package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import java.util.List;

/**
 * 导数表格存储服务
 *
 * <AUTHOR>
 */
public interface DataExportTableStorageService {

    /**
     * 翻页获取表格存储数据
     *
     * @param taskId 任务id
     * @param uniqueId 唯一id
     * @return 表格存储数据
     */
    List<DaTaExportTableStorage> queryTableStorageContents(String taskId, String uniqueId);

    /**
     * 是否存在对应的数据
     *
     * @param taskIds 任务id列表
     * @return 是否存在
     */
    Boolean existsData(List<String> taskIds);

}
