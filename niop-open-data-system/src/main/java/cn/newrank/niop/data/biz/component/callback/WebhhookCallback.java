package cn.newrank.niop.data.biz.component.callback;

import cn.newrank.niop.data.biz.callback.event.CbTriggerEvent;
import cn.newrank.niop.data.biz.callback.pojo.CbData;
import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;
import cn.newrank.niop.data.biz.callback.pojo.CbStatus;
import cn.newrank.niop.data.biz.callback.service.CbDataService;
import cn.newrank.niop.data.biz.callback.service.CbHttpTaskService;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.Hash;
import cn.newrank.niop.data.util.Ids;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.Headers;
import org.apache.commons.codec.digest.Sha2Crypt;
import org.springframework.context.ApplicationEventPublisher;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/24 19:12
 */
public class WebhhookCallback implements Callback {

    private static final Pattern CLEAN_HASH_PATTERN = Pattern.compile("[^a-zA-Z0-9]");
    private final ConfigProperties configProperties;
    private final CbHttpTaskService cbHttpTaskService;
    private final CbDataService cbDataService;
    private final ApplicationEventPublisher eventPublisher;
    private final String cbId;
    private final CallbackRequest callbackRequest;
    private final WebhookCbScheduler webhookCbScheduler;
    private long nextTriggerTime = System.currentTimeMillis() - 1;

    public WebhhookCallback(ConfigProperties configProperties,
                            CbHttpTaskService cbHttpTaskService,
                            CbDataService cbDataService,
                            ApplicationEventPublisher eventPublisher) {
        this.configProperties = configProperties;
        this.cbId = configProperties.getString(ConfigKey.ID);
        this.callbackRequest = new CallbackRequest(configProperties.getCheckedString(ConfigKey.URL),
                getHeaders(configProperties));
        this.cbHttpTaskService = cbHttpTaskService;
        this.cbDataService = cbDataService;
        this.eventPublisher = eventPublisher;
        this.webhookCbScheduler = new WebhookCbScheduler(configProperties, cbHttpTaskService, cbDataService
                , callbackRequest);
    }

    private static Headers getHeaders(ConfigProperties configProperties) {
        final Object headers = configProperties.getValue(ConfigKey.HEADERS);
        if (headers == null) {
            return Headers.of();
        }
        final Headers.Builder builder = new Headers.Builder();
        JSONObject.parseObject(JSONObject.toJSONString(headers)).forEach((k, v) -> builder.add(k, v.toString()));

        return builder.build();
    }

    static String generateCid(CallbackRedirect callbackRedirect) {
        final String sourceType = callbackRedirect.getSourceType();
        final String sourceId = callbackRedirect.getSourceId();

        final String source = sha256(sourceId, sourceType, 4);
        final String sourceKey = callbackRedirect.getSourceKey();
        return sha256(sourceKey, source, 32);
    }

    private static String sha256(String data, String salt, int len) {
        salt = "$5$" + salt;

        String sha = "";
        while (true) {
            final String hash = Sha2Crypt.sha256Crypt(data.getBytes(), salt);
            final String cleanedHash = CLEAN_HASH_PATTERN.matcher(hash).replaceAll("");

            sha += cleanedHash.substring(1);
            if (sha.length() >= len) {
                return sha.substring(sha.length() - len);
            }

            data = cleanedHash;
        }
    }

    @Override
    public String getUUID() {
        return Hash.sha256(configProperties.getCheckedString(ConfigKey.URL));
    }

    @Override
    public ConfigProperties getConfig() {
        return configProperties;
    }

    @Override
    public boolean isActive() {
        //return callbackRequest.ping();
        return true;
    }

    @Override
    public boolean callback(String data) {
        callbackRequest.request(data);
        return true;
    }

    @Override
    public boolean callback(Collection<CallbackRedirect> callbackRedirects) {
        if (callbackRedirects == null || callbackRedirects.isEmpty()) {
            return true;
        }

        final Map<String, CbHttpTask> tasks = new HashMap<>();
        final Map<String, CbData> dataList = new HashMap<>();
        for (CallbackRedirect redirect : callbackRedirects) {
            final String cid = generateCid(redirect);

            final Date partition = Date.valueOf(LocalDate.now());
            // 任务
            final CbHttpTask task = toTask(redirect);
            // 唯一标识
            task.setCid(cbId + cid);
            task.setPartition(partition);
            tasks.putIfAbsent(task.getCid(), task);

            // 数据
            final CbData data = toData(redirect);
            data.setCid(cid);
            data.setPartition(partition);
            dataList.putIfAbsent(data.getCid(), data);
        }

        cbDataService.saveBatch(new ArrayList<>(dataList.values()));
        cbHttpTaskService.saveBatch(new ArrayList<>(tasks.values()));

        sendTriggerEvent(cbId);
        return true;
    }

    @Override
    public List<CallbackRedirect> callbackReturnError(Collection<CallbackRedirect> callbackRedirects) {
        List<CallbackRedirect> errorList = new ArrayList<>();
        try {
            if (!this.callback(callbackRedirects)) {
                errorList.addAll(callbackRedirects);
            }
        } catch (Exception e) {
            errorList.addAll(callbackRedirects);
        }
        return errorList;
    }

    private void sendTriggerEvent(String cbId) {
        final long now = System.currentTimeMillis();
        if (now >= nextTriggerTime) {
            eventPublisher.publishEvent(CbTriggerEvent.of(cbId));
            nextTriggerTime = now + 1000;
        }
    }

    private CbData toData(CallbackRedirect redirect) {
        final CbData data = new CbData();

        data.setData(redirect.toPayloadJSONString());
        final Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        data.setGmtModified(now);
        data.setGmtCreate(now);

        return data;
    }

    @Override
    public boolean callback(CallbackRedirect callbackRedirect) {
        return callback(List.of(callbackRedirect));
    }

    @Override
    public void close() {
        stopScheduler();
    }

    private CbHttpTask toTask(CallbackRedirect callbackRedirect) {
        final CbHttpTask task = new CbHttpTask();

        final Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        task.setNextCallbackTime(now);
        task.setGmtCreate(now);
        task.setGmtModified(now);
        task.setSourceId(callbackRedirect.getSourceId());
        task.setSourceKey(callbackRedirect.getSourceKey());
        task.setSourceType(callbackRedirect.getSourceType());
        task.setStatus(CbStatus.RUNNING);
        task.setRetryTimes(0);
        task.setCbId(cbId);

        return task;
    }

    public void startScheduler() {
        webhookCbScheduler.start();
    }

    public void stopScheduler() {
        webhookCbScheduler.stop();
    }
}
