package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.RankHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/6 12:56
 */
@Service
public class GzhRankHistorySync extends RankHistorySync {

    static final String RANK_QUERY = """
            GET weixin_rank_week/_search
            {
              "size": 10000,
              "_source": [
                "uid",
                "articleCount",
                "articlePreLike",
                "articleClicksCount",
                "articleLikesCount",
                "log1pMark",
                "rankDate"
              ],
              "query": {
                "bool": {
                  "must": [
                    {
                      "terms": {
                        "uid":  <foreach collection="uids" item="uid" open="[" separator="," close="]">
                                    #{uid}
                                </foreach>
                      }
                    },
                    {
                      "range": {
                        "log1pMark": {
                          "gt": 0
                        }
                      }
                    }
                  ]
                }
              },
              "sort": [
                {
                  "uid": {
                    "order": "desc"
                  }
                }
              ]
            }
            """;
    protected Datasource datasource;

    protected GzhRankHistorySync(RedissonClient redissonClient,
                                 CommonEsService commonEsService,
                                 DsConfigManager dsConfigManager) {
        super("gzh", redissonClient, commonEsService);
        this.datasource = EsFactory.DEFAULT.create(dsConfigManager.chooseGzhEsConfig());
    }

    @Override
    protected Map<String, JSONObject> rankIndexes(List<String> ids) {
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(RANK_QUERY)
                .addParam("uids", ids);
        final JSONObject resp = datasource.query(queryBuilder).data();
        final List<JSONObject> items = resp.getJSONObject("hits")
                .getJSONArray("hits")
                .toJavaList(JSONObject.class)
                .stream()
                .map(json -> json.getJSONObject("_source"))
                .toList();
        final Map<String, JSONObject> map = new HashMap<>();
        for (JSONObject item : items) {
            map.compute(item.getString("uid"), (uid, rank) -> {
                if (rank == null) {
                    return item;
                }

                if (DateTimeUtil.toDateTime(rank.getString("rankDate"))
                        .isBefore(DateTimeUtil.toDateTime(item.getString("rankDate")))) {
                    return item;
                }

                return rank;
            });
        }

        return map;
    }

    @Override
    protected EsEntity castOf(JSONObject rank, String docId) {
        final GzhRankHistoryUpdate update = new GzhRankHistoryUpdate();
        update.setIndexId(docId);
        update.setLastWeekAwemeCount(rank.getLong("articleCount"));
        update.setLastWeekLikeCount(rank.getLong("articlePreLike"));
        update.setLastWeekReadCount(rank.getLong("articleClicksCount"));
        update.setLastWeekWatchCount(rank.getLong("articleLikesCount"));
        update.setNrIndexWeek(rank.getDouble("log1pMark"));
        update.setNrIndexWeekDate(rank.getString("rankDate"));

        return update;
    }

    @Data
    public static class GzhRankHistoryUpdate implements EsEntity {
        Long lastWeekAwemeCount;
        Long lastWeekLikeCount;
        Long lastWeekReadCount;
        Long lastWeekWatchCount;
        Double nrIndexWeek;
        String nrIndexWeekDate;
        String indexId;

        @Override
        public String docId() {
            return indexId;
        }
    }
}
