package cn.newrank.niop.data.biz.export.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.NetUtil;
import cn.newrank.niop.data.biz.export.atsp.AtspRequest;
import cn.newrank.niop.data.biz.export.atsp.AtspSubmitResult;
import cn.newrank.niop.data.biz.export.atsp.SubmitResultType;
import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import cn.newrank.niop.data.biz.export.excel.ExportExcelReader;
import cn.newrank.niop.data.biz.export.factory.DataExportResultFileHandlerFactory;
import cn.newrank.niop.data.biz.export.handler.DataExportResultFileHandler;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import cn.newrank.niop.data.biz.export.service.DataExportResultFileService;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import com.alibaba.fastjson2.JSON;
import com.aliyun.oss.model.OSSObject;
import com.xxl.job.core.log.XxlJobLogger;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Component
public class DataExportTaskProcessor {

    private final DataExportTaskService dataExportTaskService;

    private final DataExportSubtaskService dataExportSubtaskService;

    private final OssService ossService;

    private final RedissonClient redissonClient;

    private static final ThreadPoolTaskExecutor SUBTASKS_EXECUTOR = new ThreadPoolTaskExecutor();

    private final DataExportResultFileHandlerFactory exportResultFileHandlerFactory;

    private final DataExportResultFileService dataExportResultFileService;

    private static final int BATCH_SIZE = 200;

    public DataExportTaskProcessor(DataExportTaskService dataExportTaskService,
                                   DataExportSubtaskService dataExportSubtaskService,
                                   OssService ossService,
                                   RedissonClient redissonClient,
                                   DataExportResultFileHandlerFactory exportResultFileHandlerFactory,
                                   DataExportResultFileService dataExportResultFileService) {
        this.dataExportTaskService = dataExportTaskService;
        this.dataExportSubtaskService = dataExportSubtaskService;
        this.ossService = ossService;
        this.redissonClient = redissonClient;
        this.exportResultFileHandlerFactory = exportResultFileHandlerFactory;
        this.dataExportResultFileService = dataExportResultFileService;
    }

    static {
        SUBTASKS_EXECUTOR.setCorePoolSize(4);
        SUBTASKS_EXECUTOR.setMaxPoolSize(4);
        SUBTASKS_EXECUTOR.setQueueCapacity(0);
        SUBTASKS_EXECUTOR.setKeepAliveSeconds(60);
        SUBTASKS_EXECUTOR.setAllowCoreThreadTimeOut(true);
        SUBTASKS_EXECUTOR.setThreadNamePrefix("export-subtasks-thread-");
        SUBTASKS_EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        SUBTASKS_EXECUTOR.initialize();
    }

    public void batchInitSubtasks() {
        final List<DataExportTask> tasks = dataExportTaskService.listRunningExportTasksByStatus(DataExportTaskStatus.PARAM_FILE_UPLOADED);
        if (CollUtil.isEmpty(tasks)) {
            return;
        }

        for (DataExportTask task : tasks) {
            try {
                // 确保事务生效
                ((DataExportTaskProcessor) AopContext.currentProxy()).initSubtask(task);
            } catch (Exception e) {
                log.warn("导数任务初始化异常, taskId: {}, e: ", task.getExportTaskId(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void initSubtask(DataExportTask task) {
        // 之前保存的任务数
        final int prevSavedSubtaskNum = dataExportSubtaskService.countSubtasks(task.getExportTaskId());

        try (OSSObject ossObject = ossService.getObjectFromUrl(task.getTaskParamFile());
             ExportExcelReader reader = new ExportExcelReader(ossObject.getObjectContent())) {

            Integer dataCount = reader.forEachRead(100, dataList -> {
                List<DataExportSubtask> subtasks = dataList.stream()
                    .map(d -> DataExportSubtask.init(task, d))
                    .toList();
                // 批量保存子任务
                dataExportSubtaskService.batchInsert(subtasks);
            });

            // 已保存的子任务数
            int savedSubtaskNum = dataExportSubtaskService.countSubtasks(task.getExportTaskId());
            // 如果保存前和保存后的任务数一致, 说明有重复的参数无法添加, 直接进入下一步
            if (dataCount == savedSubtaskNum || prevSavedSubtaskNum == savedSubtaskNum) {
                dataExportTaskService.taskInitialized(task.getId(), savedSubtaskNum);
            }
        } catch (Exception e) {
            throw new IllegalStateException("Init subtask failed, e: ", e);
        }
    }

    public void batchSubmitSubtasks() {
        List<DataExportTaskExecutionDTO> tasks = dataExportTaskService.listRunningExportTaskDTOByStatus(DataExportTaskStatus.TASK_INITIALIZED);

        // 删除正在执行的任务
        tasks.removeIf(t -> redissonClient.getBucket(DataExportConstant.EXPORT_TASK_SUBMITTING_KEY + t.getExportTaskId()).isExists());

        if (CollUtil.isEmpty(tasks)) {
            return;
        }

        // 最多只取 4 个任务
        List<DataExportTaskExecutionDTO> exportTasks = CollUtil.sub(tasks, 0, 4);
        XxlJobLogger.log("导数子任务待提及, 任务数: {}", exportTasks.size());

        final CountDownLatch countDownLatch = new CountDownLatch(exportTasks.size());
        final String localhost = NetUtil.getLocalhostStr();

        for (DataExportTaskExecutionDTO task : exportTasks) {
            SUBTASKS_EXECUTOR.execute(() -> {
                final String exportTaskId = task.getExportTaskId();
                final String targetId = task.getTargetId();

                // 提交任务前先设置缓存
                final RBucket<String> bucket = redissonClient.getBucket(DataExportConstant.EXPORT_TASK_SUBMITTING_KEY + exportTaskId);
                try {
                    // 如果缓存设置失败, 说明已经被其他机器抢先执行了
                    if (!bucket.setIfAbsent(localhost, Duration.ofMinutes(10))) {
                        log.info("{} 导数子任务提交已被抢占, 执行方: {}", exportTaskId, bucket.get());
                        return;
                    }

                    List<DataExportSubtask> subtasks = dataExportSubtaskService.listNotSubmittedSubtasks(exportTaskId, BATCH_SIZE);

                    // 是否终止后续提交, 终止表示有异常情况
                    boolean abortSubmission = false;
                    for (DataExportSubtask subtask : subtasks) {
                        try {
                            final Map<String, Object> paramMap = JSON.parseObject(subtask.getParamJson(), Map.class);
                            AtspSubmitResult submitResult = AtspRequest.submitTask(targetId, paramMap);
                            final SubmitResultType resultType = submitResult.getResultType();
                            switch (resultType) {
                                case SUCCEED -> dataExportSubtaskService.subtaskSubmitted(subtask.getId(), submitResult.getTaskId());
                                // 参数异常, 任务直接失败
                                case INVALID_PARAM -> {
                                    dataExportSubtaskService.subtaskFailed(subtask.failed(submitResult));
                                    dataExportTaskService.taskFailed(task.getId());
                                    abortSubmission = true;
                                }
                                default -> {
                                    dataExportSubtaskService.subtaskFailed(subtask.failed(submitResult));
                                    // 需要暂停任务
                                    if (resultType.isPaused()) {
                                        dataExportTaskService.taskPaused(task.getId());
                                        abortSubmission = true;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("导数子任务提交异常, 导数源id: {}, param: {}, e: ", targetId, subtask.getParamJson(), e);
                        }

                        if (abortSubmission) {
                            break;
                        }
                    }

                    // 提交没有失败且子任务全部提交完成
                    if (!abortSubmission && dataExportSubtaskService.isFinishedSubmitSubtasks(exportTaskId)) {
                        dataExportTaskService.taskRunning(task.getId());
                    }
                } finally {
                    bucket.deleteAsync();
                    countDownLatch.countDown();
                }
            });
        }

        try {
            if (!countDownLatch.await(5, TimeUnit.MINUTES)) {
                log.warn("导数子任务提交已超时, 超时时间 5 分钟");
            }
        } catch (InterruptedException e) {
            log.error("导数子任务提交等待异常, e: ", e);
            Thread.currentThread().interrupt();
        }

    }

    public void monitorTasksCompletionStatus() {
        final List<DataExportTask> runningTasks = dataExportTaskService.listRunningExportTasksByStatus(DataExportTaskStatus.TASK_RUNNING);

        for (DataExportTask runningTask : runningTasks) {
            final String exportTaskId = runningTask.getExportTaskId();
            final DataExportSubtaskExecutionInfo executionInfo = dataExportSubtaskService.getSubtaskExecutionInfo(exportTaskId);

            // 有任务未完成
            if (!executionInfo.isFinished()) {
                continue;
            }

            dataExportTaskService.taskFinished(executionInfo);
        }
    }

    public void batchHandleTaskResultsDelivery() {
        final List<DataExportTaskExecutionDTO> tasks = dataExportTaskService.listRunningExportTaskDTOByStatus(DataExportTaskStatus.SUCCEED);

        for (DataExportTaskExecutionDTO task : tasks) {
            final String exportTaskId = task.getExportTaskId();
            final DataExportResultFileHandler resultFileHandler = exportResultFileHandlerFactory.getResultFileHandler(task.getDeliveryType());
            try {
                final boolean succeed = resultFileHandler.generateAndUploadResultFiles(exportTaskId, dataExportResultFileService::save);
                if (succeed) {
                    dataExportTaskService.taskResultFileReady(task.getId());
                }
            } catch (Exception e) {
                log.warn("导数任务文件生成异常, exportTaskId: {}, e: ", exportTaskId, e);
            }
        }
    }

}
