package cn.newrank.niop.data.biz.dataclear.controller;

import cn.newrank.iam.annotation.NkCheckPermission;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskPageQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanTaskUpdateStatus;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanTaskService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@RequestMapping("date-clear-log")
public class DataCleanTaskController {

    private final DataCleanTaskService dataCleanTaskService;


    public DataCleanTaskController(DataCleanTaskService dataCleanTaskService) {
        this.dataCleanTaskService = dataCleanTaskService;

    }

    /**
     * 通过规则id查询规则下面的任务
     *
     * @param dataCleanPageQuery 分页
     * @return 分页
     * search
     */
    @GetMapping("search")
    public PageView<DataCleanTask> page(@Valid DataCleanTaskPageQuery dataCleanPageQuery) {
        return dataCleanTaskService.page(dataCleanPageQuery);
    }


    @PostMapping("update")
    @NkCheckPermission("dc:edit")
    public boolean update(@Valid @RequestBody DataCleanTaskUpdateStatus dataCleanTaskUpdateStatus) {
        return dataCleanTaskService.updateStatus(dataCleanTaskUpdateStatus);
    }


}
