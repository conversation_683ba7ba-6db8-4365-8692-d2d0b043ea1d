package cn.newrank.niop.data.biz.pojo.po;


import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.enums.CbType;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;

/**
 * 回调信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 10:09
 */
@Data
public class CbConfigPo {
    /**
     * 负责人
     */
    String maintainers;
    /**
     *
     */
    private Integer id;
    /**
     * 回调策略id
     */
    private String cbId;
    /**
     * 回调唯一键
     */
    String uuid;
    /**
     * 回调类型
     */
    private CbType type;
    /**
     * 回调名称
     */
    private String name;
    /**
     * 回调来源
     */
    private String source;
    /**
     * 回调配置信息
     */
    private String config;
    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;


    public CbConfig toDto() {
        final CbConfig cbConfig = new CbConfig();

        cbConfig.setCbId(this.cbId);
        cbConfig.setUuid(this.uuid);
        cbConfig.setType(this.type);
        cbConfig.setName(this.name);
        cbConfig.setSource(this.source == null ? "default" : this.source);

        cbConfig.setConfig(ConfigProperties.of(config));
        cbConfig.setGmtCreate(this.gmtCreate);
        cbConfig.setGmtModified(this.gmtModified);
        cbConfig.setMaintainers(
                StringUtils.isBlank(maintainers)
                        ? new ArrayList<>()
                        : JSON.parseArray(maintainers, String.class)
        );


        return cbConfig;
    }

}