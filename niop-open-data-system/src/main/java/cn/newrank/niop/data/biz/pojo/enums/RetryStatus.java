package cn.newrank.niop.data.biz.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

/**
 * 重试状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 9:55
 */
public enum RetryStatus implements BizEnum {
    ERROR("-1", "ERROR"),
    PROCESS("1", "PROCESS"),
    SUCCESS("2", "SUCCESS"),
    ;

    final String code;
    final String description;

    RetryStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

    public Integer getIntDbCode() {
        return Integer.valueOf(code);
    }

}
