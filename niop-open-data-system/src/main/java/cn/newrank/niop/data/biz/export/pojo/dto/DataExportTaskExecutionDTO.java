package cn.newrank.niop.data.biz.export.pojo.dto;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import lombok.Data;

@Data
public class DataExportTaskExecutionDTO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 导数id
     */
    private String exportId;

    /**
     * 导数类型
     */
    private DataExportType exportType;

    /**
     * 交付类型
     */
    private DataExportDeliveryType deliveryType;

    /**
     * 导数源id
     */
    private String targetId;

    /**
     * 导数任务id
     */
    private String exportTaskId;

    /**
     * 任务状态
     */
    private DataExportTaskStatus taskStatus;

    /**
     * 参数总数
     */
    private Integer paramTotalNum;

    /**
     * 成功数
     */
    private Integer succeedTotalNum;

}
