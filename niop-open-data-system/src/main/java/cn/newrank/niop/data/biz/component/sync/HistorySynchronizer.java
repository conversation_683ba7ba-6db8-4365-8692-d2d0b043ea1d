package cn.newrank.niop.data.biz.component.sync;

import cn.newrank.niop.data.biz.component.sync.model.State;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据同步器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/31 15:22
 */
public interface HistorySynchronizer extends AutoCloseable {

    /**
     * 获取同步器名称
     *
     * @return String
     */
    String getName();

    /**
     * 新建同步任务
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param partitionInterval 时间分区
     * @return true: 新建成功，false: 新建失败
     */
    boolean newSync(LocalDateTime startTime, LocalDateTime endTime, Duration partitionInterval);

    /**
     * 新建同步任务
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true: 新建成功，false: 新建失败
     */
    boolean newSync(LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 同步调度
     */
    void schedule();


    /**
     * 获取同步进度
     *
     * @return 进度
     * @param <C> 游标类型
     */
    <C extends Cursor<?>> List<C> getProgress();

    /**
     * 控制同步状态
     *
     * @param key 同步任务标识
     * @return true: 成功
     */
    boolean operate(String key, State operation);

    /**
     * 新建同步任务
     *
     * @param start 开始游标
     * @param end 结束游标
     * @return true: 新建成功，false: 新建失败
     */
    boolean newSync(String start, String end);
}
