package cn.newrank.niop.data.biz.biz.xhs.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.xhs.enums.XhsSourceDataTypeEnum;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusHoloMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsUserLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusClassify;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusSaveHolo;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsUserLdm;
import cn.newrank.niop.data.biz.component.biz.CbConfigManager;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小红书作品存储ldm服务
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
@Service
public class XhsOpusSaveServiceImpl implements StorageBizService<XhsOpusFromMulti> {

    private final XhsOpusLmMapper xhsOpusLmMapper;
    private final XhsUserLmMapper xhsUserLmMapper;
    private final XhsOpusHoloMapper xhsOpusHoloMapper;
    private final KafkaCallback xhsOpusClassifyAcqKafka;

    public XhsOpusSaveServiceImpl(XhsOpusLmMapper xhsOpusLmMapper,
                                  XhsUserLmMapper xhsUserLmMapper,
                                  XhsOpusHoloMapper xhsOpusHoloMapper,
                                  CbConfigManager cbConfigManager) {
        this.xhsOpusLmMapper = xhsOpusLmMapper;
        this.xhsUserLmMapper = xhsUserLmMapper;
        this.xhsOpusHoloMapper = xhsOpusHoloMapper;
        this.xhsOpusClassifyAcqKafka = new KafkaCallback(cbConfigManager.apiCallBackConfig("niop_dc_xhs_dapan_opus_classify_prod"));
    }

    @Override
    public XhsOpusFromMulti castOf(JSONObject item) {
        if (Objects.isNull(item)) {
            return null;
        }
        return XhsOpusFromMulti.getXhsOpusFromMultiFromAcq(item);
    }

    @Override
    public void storeBatch(List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        final List<XhsOpusFromMulti> filterList = list.stream().filter(Objects::nonNull).toList();
        // 根据作品Id分组
        final Map<String, List<XhsOpusFromMulti>> newOpusIdMap = getMapByOpusId(filterList);
        // 查存在作品，算法已保证同一作品在同一个分区消费
        final Map<String, XhsOpusFromMulti> existOpusMap = getExitOpusMap(filterList);
        final List<XhsOpusFromMulti> saveList = new ArrayList<>();
        // 批量合并数据
        for (String opusId : newOpusIdMap.keySet()) {
            final List<XhsOpusFromMulti> newOpusList = newOpusIdMap.get(opusId);
            final XhsOpusFromMulti existOpus = existOpusMap.get(opusId);
            saveList.add(mergeOpuses(existOpus, newOpusList));
        }
        // 批量保存合并值
        if (CollectionUtil.isNotEmpty(saveList)) {
            xhsOpusLmMapper.saveBatch(saveList);
            acqOpusClassify(filterList, saveList);
        }
        // 存日志
        storeHolo(XhsOpusSaveHolo.convertSaveHoloList(filterList));
    }

    /**
     * 对于不存在二级分类的数据 推送到kafka 调用能力采集
     *
     * @param list 作品列表
     */
    private void acqOpusClassify(List<XhsOpusFromMulti> sourceList, List<XhsOpusFromMulti> list) {
        try {
            //来自于话题的作品数据
            Set<String> fromTopicList = sourceList.stream()
                    .filter(this::isAbilityTopicSource)
                    .map(XhsOpusFromMulti::getOpusId)
                    .collect(Collectors.toSet());
            if (CollUtil.isEmpty(fromTopicList)) {
                return;
            }
            //从合并后的数据里过滤 来着话题的作品 切 分类为空
            list.stream().filter(this::classifyIsEmpty)
                    .filter(opus -> fromTopicList.contains(opus.getOpusId()))
                    .forEach(opus -> {
                        XhsOpusClassify xoc = XhsOpusClassify.build(opus);
                        xhsOpusClassifyAcqKafka.callback(JSON.toJSONString(xoc));
                    });
        } catch (Exception e) {
            log.error("opus_classify:", e);
        }
    }

    /**
     * 分类信息为空
     *
     * @param opus 作品
     * @return 是、否
     */
    private boolean classifyIsEmpty(XhsOpusFromMulti opus) {
        String v1 = opus.getNoteCounterTypeV1();
        String v2 = opus.getNoteCounterTypeV2();
        return CharSequenceUtil.isBlank(v1) || CharSequenceUtil.isBlank(v2);
    }

    /**
     * 来源于话题作品能力
     *
     * @param opus 作品
     * @return 是、否
     */
    private boolean isAbilityTopicSource(XhsOpusFromMulti opus) {
        return XhsSourceDataTypeEnum.ABILITY_TOPIC == XhsSourceDataTypeEnum.getByDbCode(opus.getSource());
    }


    /**
     * 合并数据
     *
     * @param existOpus   存在数据
     * @param newOpusList 新数据
     * @return 合并后的数据
     */
    private XhsOpusFromMulti mergeOpuses(XhsOpusFromMulti existOpus, List<XhsOpusFromMulti> newOpusList) {
        if (CollectionUtil.isEmpty(newOpusList)) {
            return null;
        }
        final Map<String, List<XhsOpusFromMulti>> sourceMap = getMapBySource(newOpusList);
        final XhsOpusFromMulti mergeOpus = Objects.isNull(existOpus) ? new XhsOpusFromMulti() : existOpus;
        final XhsOpusFromMulti.SourceTag sourceTag = getSourceTag(mergeOpus);
        for (Map.Entry<String, List<XhsOpusFromMulti>> entry : sourceMap.entrySet()) {
            XhsSourceDataTypeEnum source = XhsSourceDataTypeEnum.getByDbCode(entry.getKey());
            //更新来源信息
            sourceTag.refresh(source);
            if (XhsSourceDataTypeEnum.isOpusDetail(source) && Objects.isNull(mergeOpus.getLdGmtCreate())) {
                //更新作品详情初次写入的时间
                mergeOpus.setLdGmtCreate(DateUtil.now());
            }
            mergeByDataType(mergeOpus, source, entry.getValue());
        }
        mergeOpus.setLdGmtModify(DateUtil.now());
        mergeOpus.setSourceTag(JSON.toJSONString(sourceTag));
        return mergeOpus;
    }

    private XhsOpusFromMulti.SourceTag getSourceTag(XhsOpusFromMulti mergeOpus) {
        String sourceRag = mergeOpus.getSourceTag();
        return StrUtil.isNotBlank(sourceRag)
                ? JSON.parseObject(sourceRag, XhsOpusFromMulti.SourceTag.class)
                : new XhsOpusFromMulti.SourceTag();
    }

    /**
     * 根据数据源分组
     *
     * @param list 数据
     * @return 分组
     */
    private static @NotNull Map<String, List<XhsOpusFromMulti>> getMapByOpusId(List<XhsOpusFromMulti> list) {
        return list.stream().collect(Collectors.groupingBy(XhsOpusFromMulti::getOpusId));
    }


    /**
     * 根据数据源分组
     *
     * @param list 数据
     * @return 分组
     */
    private static @NotNull Map<String, List<XhsOpusFromMulti>> getMapBySource(List<XhsOpusFromMulti> list) {
        return list.stream().collect(Collectors.groupingBy(XhsOpusFromMulti::getSource));
    }


    /**
     * 根据数据类型存储
     *
     * @param mergeOpus    已存在的作品信息或new的空实例
     * @param list         数据
     * @param dataTypeEnum 数据类型
     */
    public void mergeByDataType(XhsOpusFromMulti mergeOpus,
                                XhsSourceDataTypeEnum dataTypeEnum,
                                List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list) || Objects.isNull(dataTypeEnum)) {
            return;
        }
        switch (dataTypeEnum) {
            case AWEME_BASE:
                mergeAwemeBaseDataBatch(mergeOpus, list);
                break;
            case AWEME_ACCOUNT:
                mergeAwemeAccount(mergeOpus, list);
                break;
            case AWEME_FIRST_ACQ_DETAIL:
                mergeAwemeFirstAcqDetail(mergeOpus, list);
                break;
            case AWEME_FIRST_ACQ_ABNORMAL:
                mergeAwemeFirstAcqAbnormal(mergeOpus, list);
                break;
            case ABILITY_TOPIC:
                mergeTopicOpus(mergeOpus, list);
            default:
                break;
        }
    }


    /**
     * 合并 AwemeFirstAcqAbnormal
     *
     * @param mergeOpus 合并对象
     * @param list      待合并数据
     */
    private void mergeAwemeFirstAcqAbnormal(XhsOpusFromMulti mergeOpus, List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // anaTime排序
        list.sort(Comparator.comparing(opus -> DateTimeUtil.toDateTime(opus.getAnaTime())));
        if (Strings.isNotBlank(mergeOpus.getAnaTime())) {
            final LocalDateTime oldAnaTime = DateTimeUtil.toDateTime(mergeOpus.getAnaTime());
            list.forEach(opus -> {
                final LocalDateTime newAnaTime = DateTimeUtil.toDateTime(opus.getAnaTime());
                opus.mergeIfNewExist(XhsOpusFromMulti.AWEME_FIRST_ACQ_ABNORMAL_FIELDS, mergeOpus);
                // anaTime更新互动数 >= 则更新
                if (oldAnaTime.equals(newAnaTime) || newAnaTime.isAfter(oldAnaTime)) {
                    opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
                }
                opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
            });
            return;
        }
        list.forEach(opus -> {
            opus.mergeIfNewExist(XhsOpusFromMulti.AWEME_FIRST_ACQ_ABNORMAL_FIELDS, mergeOpus);
            opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
            opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
        });
    }


    /**
     * 合并 aweme_account
     *
     * @param mergeOpus 待合并的 opus
     * @param list      待合并的 opus 列表
     */
    private void mergeAwemeAccount(XhsOpusFromMulti mergeOpus, List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        final XhsOpusFromMulti newOpus = list.get(list.size() - 1);
        newOpus.mergeIfNewExist(XhsOpusFromMulti.AWEME_ACCOUNT_FIELDS, mergeOpus);


        // 写入用户表，用于话题去重
        final String dateTime = LocalDateTime.now().format(DatePattern.NORM_DATETIME_FORMATTER);
        xhsUserLmMapper.storeBatch(list.stream()
                .filter(opus -> Strings.isNotBlank(opus.getUid()))
                .map(opus -> new XhsUserLdm().setUserId(opus.getUid()).setUpdateTime(dateTime))
                .toList());
    }


    /**
     * 合并AwemeFirstAcqDetail
     *
     * @param mergeOpus 合并的opus
     * @param list      待合并的opus
     */
    private void mergeAwemeFirstAcqDetail(XhsOpusFromMulti mergeOpus, List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 拿最早的那个
        list.sort(Comparator.comparing(opus -> DateTimeUtil.toDateTime(opus.getFirstDetailAnaTime())));
        final XhsOpusFromMulti newOpus = list.get(0);
        newOpus.mergeIfNewExist(XhsOpusFromMulti.AWEME_FIRST_ACQ_DETAIL_FIELDS, mergeOpus);
        newOpus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
    }


    /**
     * 合并AwemeBase
     *
     * @param mergeOpus 待合并的opus
     * @param list      待合并的opus列表
     */
    private void mergeAwemeBaseDataBatch(XhsOpusFromMulti mergeOpus, List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // anaTime排序
        list.sort(Comparator.comparing(opus -> DateTimeUtil.toDateTime(opus.getAnaTime())));
        if (Strings.isNotBlank(mergeOpus.getAnaTime())) {
            final LocalDateTime oldAnaTime = DateTimeUtil.toDateTime(mergeOpus.getAnaTime());
            list.forEach(opus -> {
                final LocalDateTime newAnaTime = DateTimeUtil.toDateTime(opus.getAnaTime());
                opus.mergeIfNewExist(XhsOpusFromMulti.AWEME_BASE_FIELDS, mergeOpus);
                // anaTime更新互动数 >= 则更新
                if (oldAnaTime.equals(newAnaTime) || newAnaTime.isAfter(oldAnaTime)) {
                    opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
                }
                opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
            });
            return;
        }
        list.forEach(opus -> {
            opus.mergeIfNewExist(XhsOpusFromMulti.AWEME_BASE_FIELDS, mergeOpus);
            opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
            opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
        });
    }

    /**
     * 获取库内作品Map
     *
     * @param list 作品
     * @return Map
     */
    private @NotNull Map<String, XhsOpusFromMulti> getExitOpusMap(List<XhsOpusFromMulti> list) {
        final Set<String> opusIds = list.stream().map(XhsOpusFromMulti::getOpusId).collect(Collectors.toSet());
        return Optional.ofNullable(xhsOpusLmMapper.getOpusExistBatch(new ArrayList<>(opusIds)))
                .map(items -> items.stream()
                        .collect(Collectors.toMap(XhsOpusFromMulti::getOpusId, Function.identity())))
                .orElse(Map.of());
    }


    /**
     * 合并话题作品
     *
     * @param mergeOpus 合并的opus
     * @param list      待合并的opus
     */
    public void mergeTopicOpus(XhsOpusFromMulti mergeOpus, List<XhsOpusFromMulti> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // anaTime排序
        list.sort(Comparator.comparing(opus -> DateTimeUtil.toDateTime(opus.getAnaTime())));
        if (Strings.isNotBlank(mergeOpus.getAnaTime())) {
            final LocalDateTime oldAnaTime = DateTimeUtil.toDateTime(mergeOpus.getAnaTime());
            list.forEach(opus -> {
                final LocalDateTime newAnaTime = DateTimeUtil.toDateTime(opus.getAnaTime());
                opus.mergeIfNewExist(XhsOpusFromMulti.TOPIC_UPDATE_FIELDS, mergeOpus);
                // anaTime更新互动数
                if (oldAnaTime.equals(newAnaTime) || newAnaTime.isAfter(oldAnaTime)) {
                    opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
                }
                opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
            });
            return;
        }
        list.forEach(opus -> {
            opus.mergeIfNewExist(XhsOpusFromMulti.TOPIC_UPDATE_FIELDS, mergeOpus);
            opus.mergeIfNewExist(XhsOpusFromMulti.INTERACTIVE_FIELDS, mergeOpus);
            opus.mergeIfOldNull(XhsOpusFromMulti.NOT_UPDATE_FIELDS, mergeOpus);
        });
    }


    /**
     * 存holo
     *
     * @param storeHolo holo列表
     */
    public void storeHolo(List<XhsOpusSaveHolo> storeHolo) {
        try {
            xhsOpusHoloMapper.storeBatch(storeHolo);
        } catch (Exception e) {
            log.error("【xhs-holo-store】写入失败 error:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
