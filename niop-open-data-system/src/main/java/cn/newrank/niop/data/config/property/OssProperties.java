package cn.newrank.niop.data.config.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/24 12:52
 */
@Data
@ConfigurationProperties(prefix = "newrank.oss")
public class OssProperties {
    public static final String PUBLIC_HOST = "oss-cn-hangzhou.aliyuncs.com";
    public static final String INTERNAL_HOST = "oss-cn-hangzhou-internal.aliyuncs.com";
    /**
     * bucket
     */
    private String bucket = "niop";
    /**
     * accessKey
     */
    private String ak;
    /**
     * secretKey
     */
    private String sk;

    /**
     * 基础路径
     */
    private String basePath = "data/";
}
