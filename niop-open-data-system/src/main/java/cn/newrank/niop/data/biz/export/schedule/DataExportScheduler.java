package cn.newrank.niop.data.biz.export.schedule;

import cn.newrank.niop.data.biz.export.processor.DataExportTaskProcessor;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

@Component
public class DataExportScheduler {

    private final DataExportTaskProcessor exportTaskProcessor;

    public DataExportScheduler(DataExportTaskProcessor exportTaskProcessor) {
        this.exportTaskProcessor = exportTaskProcessor;
    }

    @XxlJob("initExportSubtasks")
    public ReturnT<String> initExportSubtasks(String params) {
        exportTaskProcessor.batchInitSubtasks();
        return ReturnT.SUCCESS;
    }

    @XxlJob("batchSubmitSubtasks")
    public ReturnT<String> batchSubmitSubtasks(String params) {
        exportTaskProcessor.batchSubmitSubtasks();
        return ReturnT.SUCCESS;
    }

    @XxlJob("monitorTasksCompletionStatus")
    public ReturnT<String> monitorTasksCompletionStatus(String params) {
        exportTaskProcessor.monitorTasksCompletionStatus();
        return ReturnT.SUCCESS;
    }

    @XxlJob("handleTaskResultsDelivery")
    public ReturnT<String> handleTaskResultsDelivery(String params) {
        exportTaskProcessor.batchHandleTaskResultsDelivery();
        return ReturnT.SUCCESS;
    }

}
