package cn.newrank.niop.data.biz.biz.xhs.pojo.source;

import cn.newrank.nrcore.json.JsonField;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
public class XhsDpFirstDetailAnaTime {

    @JsonField("id")
    private String opusId;

    @JsonField("first_detail_ana_time")
    private String firstDetailAnaTime;

    public static XhsDpFirstDetailAnaTime parse(JSONObject item){
        XhsDpFirstDetailAnaTime data = new XhsDpFirstDetailAnaTime();
        data.setOpusId(item.getString("id"));
        data.setFirstDetailAnaTime(item.getString("first_detail_ana_time"));
        return  data;
    }
}
