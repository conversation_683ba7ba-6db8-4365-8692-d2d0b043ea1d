package cn.newrank.niop.data.biz.export.mapper;

import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface DataExportResultFileMapper {

    /**
     * 保存结果文件
     *
     * @param resultFile 结果文件
     * @return 是否成功
     */
    boolean insert(DataExportResultFile resultFile);

    /**
     * 获取结果文件列表
     *
     * @param exportTaskId 导数任务id
     * @return 结果文件列表
     */
    List<DataExportResultFile> listResultFiles(@Param("exportTaskId") String exportTaskId);

}
