package cn.newrank.niop.data.biz.export.mapper;

import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportSubtaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface DataExportSubtaskMapper {

    /**
     * 批量插入导数子任务
     *
     * @param subtasks 导数子任务
     * @return 新增数量
     */
    int batchInsert(@Param("subtasks") List<DataExportSubtask> subtasks);

    /**
     * 根据导数任务id统计子任务数量
     *
     * @param exportTaskId 导数任务id
     * @return 子任务数量
     */
    int countSubtasks(@Param("exportTaskId") String exportTaskId);

    /**
     * 查询未提交的子任务列表
     *
     * @param exportTaskId 导数任务id
     * @param batchSize 查询数量
     * @return 子任务列表
     */
    List<DataExportSubtask> listNotSubmittedSubtasks(@Param("exportTaskId") String exportTaskId,
                                                     @Param("batchSize") int batchSize);

    /**
     * 获取子任务信息
     *
     * @param exportTaskId 导数任务id
     * @param resultTaskId 结果任务id
     * @return 导数子任务
     */
    DataExportSubtask getSubtask(@Param("exportTaskId") String exportTaskId,
                                 @Param("resultTaskId") String resultTaskId);

    /**
     * 统计未提交的子任务数量
     *
     * @param exportTaskId 导数任务id
     * @return 未提交子任务数量
     */
    Integer countNotSubmittedSubtasks(@Param("exportTaskId") String exportTaskId);

    /**
     * 子任务已提交
     *
     * @param id 主键id
     * @param resultTaskId 结果任务id
     */
    void subtaskSubmitted(@Param("id") Integer id,
                          @Param("resultTaskId") String resultTaskId);

    /**
     * 子任务失败
     *
     * @param subtask 子任务失败信息
     */
    void subtaskFailed(DataExportSubtask subtask);

    /**
     * 批量更新子任务信息
     *
     * @param subtasks 待更新子任务列表
     * @return 影响行数
     */
    int batchUpdateSubtasks(@Param("subtasks") List<DataExportSubtask> subtasks);

    /**
     * 获取子任务执行状态信息
     *
     * @param exportTaskId 导数任务id
     * @return 子任务执行状态信息
     */
    DataExportSubtaskExecutionInfo getSubtaskExecutionInfo(@Param("exportTaskId") String exportTaskId);

    /**
     * 通过游标查询子任务列表
     *
     * @param exportTaskId 导数任务id
     * @param cursor 游标id
     * @param limit 获取条数
     * @return 子任务列表
     */
    List<DataExportSubtask> listSubtasksByCursor(@Param("exportTaskId") String exportTaskId,
                                                 @Param("cursor") int cursor,
                                                 @Param("limit") int limit);

    /**
     * 获取结果任务id列表
     *
     * @param exportTaskId 导数任务id
     * @return 结果任务id列表
     */
    List<String> listResultTaskIds(@Param("exportTaskId") String exportTaskId);

    /**
     * 分页搜索导数子任务
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 分页数据
     */
    Page<DataExportSubtask> page(@Param("query") DataExportSubtaskPageQuery query, Page<DataExportSubtask> page);

    /**
     * 更新子任务结果为空
     *
     * @param resultTaskId 结果任务id
     */
    void emptyData(@Param("resultTaskId") String resultTaskId);

    /**
     * 重置子任务
     *
     * @param exportTaskId 导数任务id
     * @param resultTaskId 结果任务id
     * @return 是否成功
     */
    boolean resetSubtask(@Param("exportTaskId") String exportTaskId,
                         @Param("resultTaskId") String resultTaskId);

}
