package cn.newrank.niop.data.util;

import cn.hutool.core.util.IdUtil;
import lombok.experimental.UtilityClass;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@UtilityClass
public final class Base64Codec {

    private static final Charset CHARSET = StandardCharsets.UTF_8;
    int SALT_LEN = 4;

    public static String encode(String text) {
        return new String(Base64.getEncoder().encode(text.getBytes(CHARSET)), CHARSET);
    }

    public static String decode(String encodeText) {
        return new String(Base64.getDecoder().decode(encodeText.getBytes(CHARSET)), CHARSET);
    }

    public static String encodePw(String text) {
        final String salt = IdUtil.fastSimpleUUID().substring(0, SALT_LEN);
        return salt + encode(text + salt);
    }


    public static String decodePw(String encodeText) {
        return decode(encodeText.substring(SALT_LEN)).replace(encodeText.substring(0, SALT_LEN), "");
    }

}