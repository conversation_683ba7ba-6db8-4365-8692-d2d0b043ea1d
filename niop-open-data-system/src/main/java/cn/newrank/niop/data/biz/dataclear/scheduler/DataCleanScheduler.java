package cn.newrank.niop.data.biz.dataclear.scheduler;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanTask;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanRuleCreateQuery;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanRuleService;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanTaskService;
import cn.newrank.niop.data.biz.dataclear.service.impl.TaskDataCleanTaskExecuteService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.ExecutionException;


@Component
@RestController
@RequestMapping("datetest")
public class DataCleanScheduler {
    private final DataCleanTaskService dataCleanTaskService;
    private final DataCleanRuleService dataCleanRuleService;
    private final TaskDataCleanTaskExecuteService taskDataCleanTaskExecuteService;
    private final ThreadPoolTaskExecutor dataClearTaskExecutor;

    public DataCleanScheduler(DataCleanTaskService dataCleanTaskService,
                              DataCleanRuleService dataCleanRuleService,
                              TaskDataCleanTaskExecuteService taskDataCleanTaskExecuteService,
                              @Qualifier("dataClearTaskExecutor") ThreadPoolTaskExecutor dataClearTaskExecutor) {
        this.dataCleanTaskService = dataCleanTaskService;
        this.dataCleanRuleService = dataCleanRuleService;
        this.taskDataCleanTaskExecuteService = taskDataCleanTaskExecuteService;
        this.dataClearTaskExecutor = dataClearTaskExecutor;
    }

    @XxlJob("DataCleanTaskInsert")
    @GetMapping("DataCleanTaskInsert")
    public ReturnT<String> DataCleanTaskInsert(String params) {
        //每个小时，拿出所有的可执行任务
        int cursor = 0;
        while (true) {
            List<DataCleanRuleCreate> dataCleanRuleCreates = dataCleanRuleService.listExecuteTask(cursor);
            if (dataCleanRuleCreates.isEmpty()) break;
            dataCleanRuleCreates.forEach(item -> {
                DataCleanRuleCreateQuery dataCleanRuleSearchVo = new DataCleanRuleCreateQuery();
                BeanUtils.copyProperties(item, dataCleanRuleSearchVo);
                dataCleanTaskService.create(dataCleanRuleSearchVo);
            });
            cursor = dataCleanRuleCreates.get(dataCleanRuleCreates.size() - 1).getId();
        }
        return ReturnT.SUCCESS;
    }


    // todo 取消xxljob
    //  @XxlJob("TakeDataCleanTaskExecute")
    @GetMapping("search")
    public ReturnT<String> TaskDataCleanTaskExecute() {

        if (dataClearTaskExecutor.getActiveCount() >= 5) {
            XxlJobLogger.log("核心线程数已经占满");
            return ReturnT.SUCCESS;
        }
        //扫描任务表，获得5个待执行任务
        List<DataCleanTask> dataCleanTasks = dataCleanTaskService.listWaitingTaskAndLock();
        if (dataCleanTasks.isEmpty()) {
            return ReturnT.SUCCESS;
        }
        //放入线程池中去执行
        try {
            taskDataCleanTaskExecuteService.submitTask(dataCleanTasks);
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }
}
