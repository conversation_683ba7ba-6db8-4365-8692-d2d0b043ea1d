package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.dao.mapper.DynamicInterfaceAuthMapper;
import cn.newrank.niop.data.biz.pojo.po.DynamicInterfaceAuthPo;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import cn.newrank.niop.util.U;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/13 15:48
 */
@Component
public class DynamicInterfaceAuthDao {

    private final DynamicInterfaceAuthMapper dynamicInterfaceAuthMapper;
    private final RedissonClient redissonClient;

    public DynamicInterfaceAuthDao(DynamicInterfaceAuthMapper dynamicInterfaceAuthMapper,
                                   RedissonClient redissonClient) {
        this.dynamicInterfaceAuthMapper = dynamicInterfaceAuthMapper;
        this.redissonClient = redissonClient;
    }

    public void save(DynamicInterfaceAuth interfaceAuth) {
        final DynamicInterfaceAuthPo authPo = DynamicInterfaceAuthPo.of(interfaceAuth);

        if (has(authPo.getInterfaceId(), authPo.getAppId())) {
            throw createDbError("接口权限(InterfaceId: {}, APPID: {})已存在", authPo.getInterfaceId()
                    , authPo.getAppId());
        }

        try {
            dynamicInterfaceAuthMapper.insert(authPo);

            getCachedInterfaceAuth(authPo.getInterfaceId(), authPo.getAppId()).delete();
        } catch (Exception e) {
            throw createDbError(e, "保存接口权限失败");
        }
    }

    private boolean has(String interfaceId, String appId) {
        return dynamicInterfaceAuthMapper.count(interfaceId, appId) > 0;
    }

    public boolean delete(String authId) {
        final DynamicInterfaceAuth interfaceAuth = get(authId);
        if (interfaceAuth == null) {
            return false;
        }

        final boolean deleted = dynamicInterfaceAuthMapper.delete(authId) > 0;

        getCachedInterfaceAuth(interfaceAuth.getInterfaceId(), interfaceAuth.getAppId()).delete();

        return deleted;
    }

    public boolean update(DynamicInterfaceAuth interfaceAuth) {
        final DynamicInterfaceAuthPo authPo = DynamicInterfaceAuthPo.of(interfaceAuth);

        final DynamicInterfaceAuth dbAuth = get(authPo.getInterfaceId(), authPo.getAppId());

        if (dbAuth != null && !dbAuth.getAuthId().equals(authPo.getAuthId())) {
            throw createParamError("接口权限(InterfaceId: {}, AppId: {})已存在", authPo.getInterfaceId()
                    , authPo.getAppId());
        }

        try {
            final boolean updated = dynamicInterfaceAuthMapper.update(authPo) > 0;

            getCachedInterfaceAuth(authPo.getInterfaceId(), authPo.getAppId()).delete();

            return updated;
        } catch (Exception e) {
            throw createDbError(e, "更新接口权限(ID: {})失败", authPo.getAuthId());
        }
    }

    public List<DynamicInterfaceAuth> list(String interfaceId) {
        return U.toList(dynamicInterfaceAuthMapper.list(interfaceId), DynamicInterfaceAuthPo::toDto);
    }

    public DynamicInterfaceAuth get(String authId) {
        final DynamicInterfaceAuthPo authPo = dynamicInterfaceAuthMapper.get(authId);
        if (authPo == null) {
            return null;
        }

        return authPo.toDto();
    }

    public DynamicInterfaceAuth get(String interfaceId, String appId) {
        final RBucket<String> cachedInterfaceAuth = getCachedInterfaceAuth(interfaceId, appId);
        if (cachedInterfaceAuth.isExists()) {
            return DynamicInterfaceAuth.ofJSONString(cachedInterfaceAuth.get());
        }

        final DynamicInterfaceAuthPo authPo = dynamicInterfaceAuthMapper.getByInterfaceIdAndAppId(interfaceId, appId);
        if (authPo == null) {
            cachedInterfaceAuth.set("");
            cachedInterfaceAuth.expire(Duration.ofDays(15));

            return null;
        }

        final DynamicInterfaceAuth interfaceAuth = authPo.toDto();

        cachedInterfaceAuth.set(interfaceAuth.toJSONString());
        cachedInterfaceAuth.expire(Duration.ofDays(15));

        return interfaceAuth;
    }

    private RBucket<String> getCachedInterfaceAuth(String interfaceId, String appId) {
        return redissonClient.getBucket("dynamic:interface:auth:" + interfaceId + ":" + appId);
    }
}
