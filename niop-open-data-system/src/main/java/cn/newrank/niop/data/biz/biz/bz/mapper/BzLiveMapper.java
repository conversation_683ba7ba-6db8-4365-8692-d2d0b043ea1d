package cn.newrank.niop.data.biz.biz.bz.mapper;

import cn.newrank.niop.data.biz.biz.bz.pojo.BzLive;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 11:12
 */
@Mapper
public interface BzLiveMapper {

    void saveAll(@Param("items") List<BzLive> items);

    void update(BzLive bzLive);

    List<BzLive> listCalculateLives();

    List<BzLive> list(@Param("sampleIds") List<String> sampleIds);
}




