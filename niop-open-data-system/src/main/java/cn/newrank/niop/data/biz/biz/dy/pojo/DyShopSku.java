package cn.newrank.niop.data.biz.biz.dy.pojo;

import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopSkuDTO;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_shop_sku
 */
@Data
@Accessors(chain = true)
public class DyShopSku implements Serializable {
    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;

    /**
     * 商品id(logic_id)
     */
    private String productId;

    /**
     *
     */
    private String skuLongId;

    /**
     *
     */
    private String skuPrice;

    /**
     *
     */
    private Long skuStockNum;

    /**
     *
     */
    private String skuId;

    /**
     *
     */
    private String skuExtra;

    /**
     *
     */
    private String specsName;

    /**
     *
     */
    private String specsItems;

    /**
     *
     */
    private String pic;

    /**
     * 数据来源
     */
    private String deviceName;

    /**
     * 分区位点
     */
    private String partitionOffset;


    private static final long serialVersionUID = 1L;


    public static DyShopSku createItem(DyShopSkuDTO dto) {
        return new DyShopSku()
                .setProductId(dto.getProductId())
                .setSkuLongId(dto.getSkuLongId())
                .setSkuPrice(dto.getSkuPrice())
                .setSkuStockNum(dto.getSkuStockNum())
                .setSkuId(dto.getSkuId())
                .setSkuExtra(dto.getSkuExtra())
                .setSpecsName(JSON.toJSONString(dto.getSpecsName()))
                .setSpecsItems(JSON.toJSONString(dto.getSpecsItems()))
                .setPic(dto.getPic())
                .setDeviceName(dto.getDeviceName())
                .setPartitionOffset(dto.getPartitionOffset());
    }
}