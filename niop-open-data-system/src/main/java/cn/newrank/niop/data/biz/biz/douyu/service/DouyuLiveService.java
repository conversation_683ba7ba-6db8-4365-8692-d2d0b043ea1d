package cn.newrank.niop.data.biz.biz.douyu.service;

import cn.newrank.niop.data.biz.biz.douyu.mapper.DouyuLiveMapper;
import cn.newrank.niop.data.biz.biz.douyu.pojo.DouyuLive;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 斗鱼直播
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 14:07
 */
@Service
public class DouyuLiveService implements StorageBizService<DouyuLive> {

    private final DouyuLiveMapper douyuLiveMapper;

    public DouyuLiveService(DouyuLiveMapper douyuLiveMapper) {
        this.douyuLiveMapper = douyuLiveMapper;
    }

    @Override
    public void storeBatch(List<DouyuLive> items) {
        douyuLiveMapper.saveAll(items);
    }

    @Override
    public DouyuLive castOf(JSONObject item) {
        final JSONObject data = item.getJSONObject("data");
        final DouyuLive douyuLive = data.to(DouyuLive.class);


        douyuLive.setSampleId(item.getString("sampleId"));
        douyuLive.setSampleStatus(item.getString("sampleStatus"));
        douyuLive.setUpdateTime(item.getLongValue("updateTime"));

        return douyuLive;
    }

    public List<DouyuLive> listCalculateLives() {
        return douyuLiveMapper.listCalculateLives();
    }

    public void updateCalculateProps(DouyuLive live) {
        douyuLiveMapper.update(live);
    }


    @Override
    public List<DouyuLive> list(List<String> identifiers) {
        return douyuLiveMapper.list(identifiers);
    }
}
