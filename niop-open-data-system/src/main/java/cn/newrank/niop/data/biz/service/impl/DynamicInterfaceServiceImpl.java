package cn.newrank.niop.data.biz.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.dao.DynamicInterfaceAuthDao;
import cn.newrank.niop.data.biz.dao.DynamicInterfaceDao;
import cn.newrank.niop.data.biz.dao.DynamicInterfaceSchemaDao;
import cn.newrank.niop.data.biz.manager.ConsoleManager;
import cn.newrank.niop.data.biz.pojo.dto.ApiPastParam;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.service.DatasourceService;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.biz.service.DynamicInterfaceService;
import cn.newrank.niop.data.common.TraceCreator;
import cn.newrank.niop.data.common.ds.*;
import cn.newrank.niop.data.common.entity.DynamicInterface;
import cn.newrank.niop.data.common.entity.DynamicInterfaceAuth;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import cn.newrank.niop.data.common.model.Arg;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.niop.util.U;
import cn.newrank.niop.web.model.PageView;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.MD5Utils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

import static cn.newrank.niop.util.U.toList;
import static cn.newrank.niop.util.U.toSet;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:24
 */
@Service
public class DynamicInterfaceServiceImpl implements DynamicInterfaceService {
    public static final String API_HUB_AIO_PROD = "https://open.newrank.cn/api/open/data/api-hub/aio/";
    public static final String API_HUB_AIO_TEST = "http://test.open.newrank.cn/api/open/data/api-hub/aio/";
    private static final String API_PARAMS_QUERY_TEMPLATE = "* and api_id: %s and status: 200";
    private static final String API_REQUEST_LOGSTORE = "niop-dc-api-access-log";
    private static final String SLS_REQUEST_PARAMS_FILED = "request_params";
    private final DynamicInterfaceDao dynamicInterfaceDao;
    private final DatasourceService datasourceService;
    private final DsConfigService dsConfigService;
    private final SystemConfig systemConfig;
    private final DynamicInterfaceAuthDao dynamicInterfaceAuthDao;
    private final ConsoleManager consoleManager;
    private final DynamicInterfaceSchemaDao dynamicInterfaceSchemaDao;
    private final TraceCreator traceCreator;

    public DynamicInterfaceServiceImpl(DynamicInterfaceDao dynamicInterfaceDao,
                                       DatasourceService datasourceService,
                                       DsConfigService dsConfigService,
                                       SystemConfig systemConfig,
                                       DynamicInterfaceAuthDao dynamicInterfaceAuthDao,
                                       ConsoleManager consoleManager,
                                       DynamicInterfaceSchemaDao dynamicInterfaceSchemaDao, TraceCreator traceCreator) {
        this.dynamicInterfaceDao = dynamicInterfaceDao;
        this.datasourceService = datasourceService;
        this.dsConfigService = dsConfigService;
        this.systemConfig = systemConfig;
        this.dynamicInterfaceAuthDao = dynamicInterfaceAuthDao;
        this.consoleManager = consoleManager;
        this.dynamicInterfaceSchemaDao = dynamicInterfaceSchemaDao;
        this.traceCreator = traceCreator;
    }

    private static JSONObject getParams(JSONObject record) {
        JSONObject param = new JSONObject();
        String paramsJson = record.getString(SLS_REQUEST_PARAMS_FILED);
        if (Objects.nonNull(paramsJson)) {
            param = JSON.parseObject(paramsJson);
        }
        return param;
    }

    private static @NotNull String getRequestTime(JSONObject record) {
        String time = record.get("time").toString();
        if (StrUtil.isNotBlank(time)) {
            return DateTimeUtil.format(DateTimeUtil.timeStampToDateTime(Long.parseLong(time)));
        }
        return Strings.EMPTY;
    }

    private void slsStoreDebug(HttpServletRequest request,
                               DynamicInterfaceQueryDebug queryDebug,
                               DynamicInterface dynamicInterface,
                               long currentMillis) {
        MutateBuilder mutateBuilder = traceCreator.sls.newMutateBuilder();
        final String ip = request.getHeader("x-real-ip");
        mutateBuilder.collection(API_REQUEST_LOGSTORE)
                .addParam("dc_id", dynamicInterface.getDcId())
                .addParam("template", dynamicInterface.getQuery())
                .addParam("template_md5", MD5Utils.encodeHexString(dynamicInterface.getQuery().getBytes()))
                .addParam("api_id", queryDebug.getInterfaceId())
                .addParam("environment", SystemConfig.getConfig().getEnvironment().name())
                .addParam("url", request.getRequestURI().replace(request.getContextPath(), ""))
                .addParam("time", currentMillis)
                .addParam("app_id", request.getHeader("niop-app-id"))
                .addParam("http_host", request.getHeader("x-forwarded-host"))
                .addParam("remote_ip", StringUtils.isNotBlank(ip) ? ip : request.getHeader("remoteip"))
                .addParam("request_body_bytes", request.getContentLengthLong())
                .addParam("request_params", JSON.toJSONString(queryDebug.getDebugParams()))
                .addParam("timing", System.currentTimeMillis() - currentMillis)
                .addParam("status", 200);
        traceCreator.sls.mutate(mutateBuilder);
    }

    @Override
    public String create(DynamicInterfaceCreate interfaceCreate) {
        final DynamicInterface dynamicInterface = interfaceCreate.toDto();

        if (!dsConfigService.hasConfig(interfaceCreate.getDcId())) {
            throw createParamError("数据源(ID: {})不存在", interfaceCreate.getDcId());
        }

        dynamicInterface.setInterfaceId(Ids.create(DynamicInterface.ID_LENGTH));

        dynamicInterfaceDao.save(dynamicInterface);

        return dynamicInterface.getInterfaceId();
    }

    @Override
    public Resp queryPreview(@Valid DynamicInterfaceQueryPreview interfaceQueryPreview) {
        final String interfaceId = interfaceQueryPreview.getInterfaceId();
        final DynamicInterface dynamicInterface = dynamicInterfaceDao.get(interfaceId);
        if (dynamicInterface == null) {
            throw createParamError("接口(ID: {})不存在", interfaceId);
        }
        final Datasource datasource = datasourceService.getDatasource(dynamicInterface.getDcId());

        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(interfaceQueryPreview.getQuery())
                .enablePreview();

        final List<Arg> args = dynamicInterface.getArgs();
        if (args != null) {
            args.forEach(arg -> queryBuilder.addParam(arg.getName(), arg.getValue()));
        }

        return datasource.query(queryBuilder);
    }

    @Override
    public Resp parsedQueryPreview(DynamicInterfaceParsedQueryPreview interfaceQueryPreview) {
        final String interfaceId = interfaceQueryPreview.getInterfaceId();
        final DynamicInterface dynamicInterface = dynamicInterfaceDao.get(interfaceId);
        if (dynamicInterface == null) {
            throw createParamError("接口(ID: {})不存在", interfaceId);
        }
        final Datasource datasource = datasourceService.getDatasource(dynamicInterface.getDcId());

        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(interfaceQueryPreview.getQuery())
                .enablePreview();

        final List<Arg> args = dynamicInterface.getArgs();
        if (args != null) {
            args.forEach(arg -> queryBuilder.addParam(arg.getName(), arg.getValue()));
        }

        String dataSchema = interfaceQueryPreview.getDataSchema();
        if (StrUtil.isBlank(dataSchema)) {
            return datasource.query(queryBuilder);
        }

        return datasource.query(queryBuilder).mapping(dataSchema);
    }

    @Override
    public Resp debug(HttpServletRequest request, DynamicInterfaceQueryDebug queryDebug) {
        final long currentMillis = System.currentTimeMillis();
        final String interfaceId = queryDebug.getInterfaceId();
        final DynamicInterface dynamicInterface = dynamicInterfaceDao.get(interfaceId);

        if (dynamicInterface == null) {
            throw createParamError("接口(ID: {})不存在", interfaceId);
        }

        final Datasource datasource = datasourceService.getDatasource(dynamicInterface.getDcId());

        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(dynamicInterface.getQuery());

        final List<Arg> args = dynamicInterface.getArgs();
        if (args != null) {
            final Map<String, Object> invokeParams = queryDebug.getDebugParams();
            args.forEach(arg -> {
                Object value = invokeParams.get(arg.getName());
                if (value == null) {
                    value = arg.getValue();
                }

                if (Boolean.TRUE.equals(arg.getRequired()) && value == null) {
                    throw createParamError("参数(name: {})不能为空", arg.getName());
                }

                queryBuilder.addParam(arg.getName(), value);
            });
        }
        // 参数映射
        Resp result = queryBySchema(interfaceId, datasource, queryBuilder);
        if (Objects.isNull(result)) {
            result = datasource.query(queryBuilder);
        }
        // sls日志
        if (Objects.nonNull(result)) {
            slsStoreDebug(request, queryDebug, dynamicInterface, currentMillis);
        }
        return result;
    }

    private Resp queryBySchema(String interfaceId, Datasource datasource, QueryBuilder queryBuilder) {
        DynamicInterfaceSchema interfaceSchema = dynamicInterfaceSchemaDao.getByInterfaceId(interfaceId);
        if (Objects.nonNull(interfaceSchema)) {
            String dataSchema = Optional.of(interfaceSchema)
                    .map(DynamicInterfaceSchema::getDataSchema)
                    .orElse("");
            if (!Strings.isNullOrEmpty(dataSchema)) {
                // 是否开启映射
                Boolean enableMapping = interfaceSchema.getEnableMapping();
                enableMapping = Optional.ofNullable(enableMapping).orElse(false);
                if (enableMapping) {
                    return datasource.query(queryBuilder).mapping(dataSchema);
                }
            }
        }
        return null;
    }

    @Override
    public List<ApiPastParam> listPastParams(String interfaceId) {
        final String dsl = String.format(API_PARAMS_QUERY_TEMPLATE, interfaceId);
        LocalDateTime now = LocalDateTime.now();
        String startTime = now.minusMonths(1).format(DatePattern.NORM_DATETIME_FORMATTER);
        String endTime = now.format(DatePattern.NORM_DATETIME_FORMATTER);
        final QueryBuilder template = traceCreator.sls.newQueryBuilder()
                .template(dsl)
                .addParam(SlsFactory.Sls.SLS_PARAM_LOGSTORE, API_REQUEST_LOGSTORE)
                .addParam(SlsFactory.Sls.SLS_PARAM_START, startTime)
                .addParam(SlsFactory.Sls.SLS_PARAM_END, endTime)
                .addParam(QueryBuilder.PAGE_QUERY_PARAM_PAGE, 1)
                .addParam(QueryBuilder.PAGE_QUERY_PARAM_SIZE, 20);
        // 查询sls日志
        final SlsFactory.Sls.QueryResp resp = traceCreator.sls.query(template);
        if (Objects.isNull(resp) || resp.getRecords().isEmpty()) {
            return Collections.emptyList();
        }
        return resp.getRecords()
                .stream()
                .map(record -> ApiPastParam.create(
                        interfaceId,
                        getParams(record),
                        getRequestTime(record))
                ).toList();
    }

    @Override
    public Resp createDebug(DynamicInterfaceCreateDebug createDebug) {
        final Datasource datasource = datasourceService.getDatasource(createDebug.getDcId());

        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(createDebug.getQuery());

        final Map<String, Object> debugParams = createDebug.getDebugParams();
        if (debugParams != null) {
            debugParams.forEach(queryBuilder::addParam);
        }

        return datasource.query(queryBuilder);
    }

    @Override
    public boolean update(DynamicInterfaceUpdate interfaceUpdate) {
        return dynamicInterfaceDao.update(interfaceUpdate.toDto());
    }

    @Override
    public boolean delete(DynamicInterfaceDelete interfaceDelete) {
        dynamicInterfaceDao.delete(interfaceDelete.getInterfaceId());
        return true;
    }


    @Override
    public boolean updateTags(DynamicInterfaceTagUpdate tagUpdate) {
        return dynamicInterfaceDao.updateTags(tagUpdate.getInterfaceId(), tagUpdate.getTags());
    }

    @Override
    public PageView<DynamicInterface> page(DynamicInterfacePageQuery pageQuery) {
        final PageView<DynamicInterface> page = dynamicInterfaceDao.page(pageQuery);

        final Map<String, DsConfig> dsConfigMap = dsConfigService.map(toSet(page.getRecords(), DynamicInterface::getDcId));

        page.forEach(dynamicInterface -> {
            dynamicInterface.setPath(getPath(dynamicInterface));
            final DsConfig dsConfig = dsConfigMap.get(dynamicInterface.getDcId());

            if (dsConfig != null) {
                dynamicInterface.setDsName(dsConfig.getName());
                dynamicInterface.setDsType(dsConfig.getType());
            }
        });

        return page;
    }

    @Override
    public DynamicInterface get(String interfaceId) {
        final DynamicInterface dynamicInterface = dynamicInterfaceDao.get(interfaceId);
        if (dynamicInterface == null) {
            return null;
        }

        dynamicInterface.setPath(getPath(dynamicInterface));
        final DsConfig config = dsConfigService.getConfig(dynamicInterface.getDcId());
        if (config != null) {
            dynamicInterface.setDsName(config.getName());
            dynamicInterface.setDsType(config.getType());
        }

        return dynamicInterface;
    }

    @NotNull
    private String getPath(DynamicInterface dynamicInterface) {
        if (systemConfig.isProduct()) {
            return API_HUB_AIO_PROD + dynamicInterface.getInterfaceId();
        } else {
            return API_HUB_AIO_TEST + dynamicInterface.getInterfaceId();
        }
    }

    @Override
    public String createAuth(DynamicInterfaceAuthCreate authCreate) {
        final DynamicInterfaceAuth interfaceAuth = authCreate.toDto();

        checkAuthParam(interfaceAuth);

        interfaceAuth.setAuthId(U.randUUID(DynamicInterfaceAuth.ID_LENGTH));

        dynamicInterfaceAuthDao.save(interfaceAuth);

        return interfaceAuth.getAuthId();
    }

    @Override
    public boolean deleteAuth(String authId) {
        return dynamicInterfaceAuthDao.delete(authId);
    }

    @Override
    public boolean updateAuth(DynamicInterfaceAuthUpdate authUpdate) {
        final DynamicInterfaceAuth interfaceAuth = authUpdate.toDto();

        checkAuthParam(interfaceAuth);


        return dynamicInterfaceAuthDao.update(interfaceAuth);
    }

    private void checkAuthParam(DynamicInterfaceAuth interfaceAuth) {
        final String interfaceId = interfaceAuth.getInterfaceId();
        if (get(interfaceId) == null) {
            throw createParamError("接口(ID: {})不存在", interfaceId);
        }

        if (!consoleManager.hasApp(interfaceAuth.getAppId())) {
            throw createParamError("应用(ID: {})不存在", interfaceAuth.getAppId());
        }
    }

    @Override
    public List<DynamicInterfaceAuth> listAuth(String interfaceId) {
        final List<DynamicInterfaceAuth> auths = dynamicInterfaceAuthDao.list(interfaceId);
        if (auths == null) {
            return new ArrayList<>();
        }

        final Map<String, App> mapApp = consoleManager.mapApp(toList(auths, DynamicInterfaceAuth::getAppId));
        auths.forEach(auth -> {
            final App app = mapApp.get(auth.getAppId());
            if (app != null) {
                auth.setAppName(app.getAppName());
            }
        });

        return auths;
    }

    @Override
    public DynamicInterfaceAuth getAuth(String authId) {
        final DynamicInterfaceAuth interfaceAuth = dynamicInterfaceAuthDao.get(authId);
        if (interfaceAuth == null) {
            return null;
        }

        final App app = consoleManager.getApp(interfaceAuth.getAppId());
        if (app != null) {
            interfaceAuth.setAppName(app.getAppName());
        }

        return interfaceAuth;
    }

    @Override
    public DynamicInterfaceAuth getAuth(String interfaceId, String appId) {
        return dynamicInterfaceAuthDao.get(interfaceId, appId);
    }
}




