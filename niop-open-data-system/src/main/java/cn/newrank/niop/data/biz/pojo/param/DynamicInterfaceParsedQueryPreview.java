package cn.newrank.niop.data.biz.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:26
 */
@Data
public class DynamicInterfaceParsedQueryPreview {
    /**
     * 数据源ID
     */
    @NotBlank(message = "接口ID(interfaceId)不能为空")
    private String interfaceId;
    /**
     * 查询语句
     */
    @NotBlank(message = "查询语句(query)不能为空")
    private String query;
    /**
     * 数据解析Schema
     */
    @NotBlank(message = "数据解析Schema(dataSchema)不能为空")
    private String dataSchema;

}