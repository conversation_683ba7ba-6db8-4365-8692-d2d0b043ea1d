package cn.newrank.niop.data.biz.biz.tiktok.pojo;

import lombok.Data;

import java.sql.Timestamp;

/**
 * tiktok用户表
 *
 * @TableName ds_dwd_tiktok_user
 */
@Data
public class TiktokHoloUser {
    /**
     * user id
     */
    private String uid;

    /**
     * 账号
     */
    private String account;

    /**
     * 昵称
     */
    private String nickname;

    /**
     *
     */
    private String secUid;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 签名
     */
    private String signature;

    /**
     * 地域
     */
    private String region;

    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 关注数
     */
    private Long followingNum;

    /**
     * 作品数
     */
    private Long opusNum;

    /**
     * 获赞数
     */
    private Long likeNum;

    /**
     * 入库时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    public static TiktokHoloUser of(TiktokAccount account) {
        final TiktokHoloUser holoUser = new TiktokHoloUser();

        holoUser.setAccount(account.getAccount());
        holoUser.setAvatar(account.getAvatar());
        holoUser.setFansNum(account.getFansNum());
        final Long followNum = account.getFollowNum();
        holoUser.setFollowingNum(followNum == null ? 0 : followNum);
        holoUser.setLikeNum(account.getLikeNum());
        holoUser.setNickname(account.getNickname());
        holoUser.setOpusNum(account.getOpusNum());
        holoUser.setRegion(account.getRegion());
        holoUser.setSecUid(account.getSecUid());
        holoUser.setUid(account.getUid());

        return holoUser;
    }
}