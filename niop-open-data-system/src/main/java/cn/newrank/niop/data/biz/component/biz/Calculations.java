package cn.newrank.niop.data.biz.component.biz;

import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 16:31
 */
@UtilityClass
public final class Calculations {

    static final ConcurrentMap<Class<?>, List<String>> CALCULATION_FIELDS = new ConcurrentHashMap<>();


    public static List<String> getCalculationFields(Object object) {
        if (object == null) {
            return List.of();
        }

        return CALCULATION_FIELDS.computeIfAbsent(object.getClass(), clazz -> {
            final Field[] fields = clazz.getDeclaredFields();

            final List<String> names = new ArrayList<>();
            for (Field field : fields) {
                final Calculation calculation = field.getAnnotation(Calculation.class);
                if (calculation != null) {
                    names.add(field.getName());
                }
            }

            return names.stream().toList();
        });
    }
}
