package cn.newrank.niop.data.biz.biz.xhs.service.impl;

import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusClassifyAbility;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/8/12 15:09
 */
@Service
public class XhsClassifyAbilityServiceImpl implements StorageBizService<XhsOpusClassifyAbility> {

    private static final int SUCCESS_STATUS = 1;

    private final XhsOpusLmMapper xhsOpusLmMapper;

    public XhsClassifyAbilityServiceImpl(XhsOpusLmMapper xhsOpusLmMapper) {
        this.xhsOpusLmMapper = xhsOpusLmMapper;
    }

    @Override
    public void storeBatch(List<XhsOpusClassifyAbility> items) {
        List<XhsOpusFromMulti> list = items.stream().filter(Objects::nonNull).map(item -> {
            XhsOpusFromMulti opus = new XhsOpusFromMulti();
            opus.setOpusId(item.getOpusId());
            opus.setNoteCounterTypeV1(item.getNoteCounterTypeV1());
            opus.setNoteCounterTypeV2(item.getNoteCounterTypeV2());
            return opus;
        }).toList();
        if (list.isEmpty()) {
            return;
        }
        xhsOpusLmMapper.batchUpdateClassify(list);
    }

    @Override
    public XhsOpusClassifyAbility castOf(JSONObject item) {
        int status = item.getIntValue("status");
        if (SUCCESS_STATUS != status) {
            return null;
        }
        String paramsJson = item.getString("params");
        JSONObject params = JSON.parseObject(paramsJson);
        String opusId = params.getString("opusId");
        XhsOpusClassifyAbility result = new XhsOpusClassifyAbility();
        result.setOpusId(opusId);

        JSONObject data = item.getJSONObject("data");
        result.setNoteCounterTypeV1(data.getString("type_v1"));
        result.setNoteCounterTypeV2(data.getString("type_v2"));
        return result;
    }
}
