package cn.newrank.niop.data.biz.data.ark.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2025/8/27 14:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ObjData extends StorageEntity {
    /**
     * 对象id
     */
    private String objId;
    /**
     * 对象名称
     */
    private String objName;
    /**
     * 数据详情
     */
    private String dataDetail;
    /**
     * 维度id
     */
    private String dimensionId;
    /**
     * 任务id
     */
    private String acqTaskId;
    /**
     * 采集时间
     */
    private String acqTime;
    /**
     * 采集耗时
     */
    private int acqTiming;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;

    @Override
    public String identifier() {
        return objId + acqTaskId;
    }
}
