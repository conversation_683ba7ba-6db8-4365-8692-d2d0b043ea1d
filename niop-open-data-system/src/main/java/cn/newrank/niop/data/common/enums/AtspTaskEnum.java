package cn.newrank.niop.data.common.enums;

import cn.newrank.niop.web.model.BizEnum;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/10 11:29:49
 */
@AllArgsConstructor
public enum AtspTaskEnum implements BizEnum {

    /**
     * 任务状态
     */
    SUCCESS("0", "成功"),
    FAILED("-1", "失败"),
    TIMEOUT("-2", "超时"),

    ATSP_QUEUE("1104", "排队中"),
    ATSP_RUNNING("1101", "执行中"),
    ATSP_FAILED("1102", "执行失败"),
    ATSP_TIMEOUT("1105", "执行超时"),
    ;

    private final String code;
    private final String description;

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

    public static AtspTaskEnum of(String code) {
        for (AtspTaskEnum value : values()) {
            if (value.getDbCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
