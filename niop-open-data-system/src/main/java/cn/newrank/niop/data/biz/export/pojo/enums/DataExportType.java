package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.web.model.BizEnum;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

public enum DataExportType implements BizEnum {

    /**
     * 导数类型
     */
    ABILITY("1", "能力"),
    HUB("2", "综合服务"),
    ;

    private final String code;
    private final String description;

    DataExportType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DataExportType parseByCode(String code) {
        if (StrUtil.isNotBlank(code)) {
            for (DataExportType type : values()) {
                if (type.getDbCode().equals(code)) {
                    return type;
                }
            }
        }
        throw createParamError("不支持的导数类型");
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

}
