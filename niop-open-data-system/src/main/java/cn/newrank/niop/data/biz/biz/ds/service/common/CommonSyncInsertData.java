package cn.newrank.niop.data.biz.biz.ds.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.ds.mapper.DsMapper;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.CollectCount;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.po.DsEsSyncRecordPo;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBiz;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizServiceContext;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncEntity;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.*;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.newrank.niop.data.util.EsUtil.HITS;
import static cn.newrank.niop.data.util.EsUtil.SORT;
import static cn.newrank.niop.data.util.Iterables.toList;
import static cn.newrank.niop.data.util.Iterables.toMap;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class CommonSyncInsertData {

    public static final String ACCOUNT_LIST_QUERY = """
            GET /%s/_search
            {
                "size": 500,
                "_source": %s,
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "%s": {
                                        "gte": "%s"
                                    }
                                }
                            }
                        ]
                    }
                },
                "sort": [
                  {
                    "%s": {
                      "order": "desc"
                    }
                  }
                ]
            }
            """;

    public static final String ACCOUNT_LIST_SEARCH_AFTER_QUERY = """
            GET /%s/_search
            {
                "size": 500,
                "_source": %s,
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "%s": {
                                        "gte": "%s"
                                    }
                                }
                            }
                        ]
                    }
                },
                "search_after": %s,
                "sort": [
                  {
                    "%s": {
                      "order": "desc"
                    }
                  }
                ]
            }
            """;

    private final EsFactory esFactory;
    private final Datasource phbMainMysql;
    private final Datasource dSEs;
    private final CommonEsService commonEsService;
    private final DsMapper dsMapper;
    private final SyncBizServiceContext syncBizServiceContext;

    public CommonSyncInsertData(DsConfigManager dsConfigManager, DsMapper dsMapper, SyncBizServiceContext syncBizServiceContext, CommonEsService commonEsService) {
        this.commonEsService = commonEsService;
        this.esFactory = EsFactory.DEFAULT;
        this.phbMainMysql = MySQLFactory.DEFAULT.create(dsConfigManager.chooseDsMySQLConfig());
        this.dSEs = EsFactory.DEFAULT.create(commonEsService.getRestClient());
        this.dsMapper = dsMapper;
        this.syncBizServiceContext = syncBizServiceContext;
    }

    public void syncInsertData(String lastSyncTime, PlatformType platformType) {
        XxlJobLogger.log("大搜es开始同步平台{}", platformType);
        if (StrUtil.isBlank(lastSyncTime)) {
            final DsEsSyncRecordPo dsEsSyncRecordPo = dsMapper.get(platformType);

            if (null == dsEsSyncRecordPo) {
                XxlJobLogger.log("请检查同步时间，首次同步需要进行手动添加db记录");
                return;
            }

            lastSyncTime = dsEsSyncRecordPo.getLastSyncTime();
        }

        if (StrUtil.isBlank(lastSyncTime) || !DateTimeUtil.isValidDateFormat(lastSyncTime, DatePattern.NORM_DATETIME_PATTERN)) {
            XxlJobLogger.log("同步时间有误，请检查(params:{})", lastSyncTime);
            return;
        }

        final SyncBiz syncBiz = SyncBiz.ofJSONValue(platformType.getDbCode());
        final SyncBizService<? extends SyncEntity> syncBizService = syncBizServiceContext.getService(syncBiz);

        if (!syncBizService.isStart(lastSyncTime)) {
            XxlJobLogger.log("同步时间未到，不执行(params:{})", lastSyncTime);
            return;
        }

        try (final EsFactory.Es es = esFactory.create(syncBizService.getConfig())) {
            DateTime nowSyncTime = DateUtil.beginOfDay(DateUtil.date()).offset(DateField.HOUR_OF_DAY, 16);
            // 格式化日期时间
            String nowSyncTimeStr = DateUtil.formatDateTime(nowSyncTime);

            // 将上次的同步时间向前推二天
            DateTime parse = DateUtil.parse(lastSyncTime);

            String twoDaysAgoSyncTime = DateUtil.formatDateTime(parse.offset(DateField.DAY_OF_MONTH, -1));

            Resp query = es.query(
                    es.newQueryBuilder()
                            .template(
                                    String.format(ACCOUNT_LIST_QUERY,
                                            syncBizService.getIndexName(),
                                            syncBizService.getSourceFields(),
                                            syncBizService.getRangField(),
                                            twoDaysAgoSyncTime,
                                            syncBizService.getSortField()
                                    )
                            )
            );
            Resp.DataView dataView = query.getDataView();

            int total = 0;
            while (true) {
                if (dataView instanceof EsFactory.Es.RespImpl.EsDataView esDataView) {
                    JSONObject resultObj = esDataView.resp();

                    final List<DataCenterEsMetaData> dataList = syncBizService.parseData(resultObj, DataCenterEsMetaData.class);
                    if (CollUtil.isEmpty(dataList)) {
                        break;
                    }

                    // 同步到数据中心es
                    commonEsService.upsert(dataList);
                    total += dataList.size();

                    // searchAfter查询
                    JSONArray jsonArray = resultObj.getJSONObject(HITS).getJSONArray(HITS);
                    JSONArray sort = jsonArray.getJSONObject(jsonArray.size() - 1).getJSONArray(SORT);

                    query = es.query(
                            es.newQueryBuilder().template(
                                    String.format(ACCOUNT_LIST_SEARCH_AFTER_QUERY,
                                            syncBizService.getIndexName(),
                                            syncBizService.getSourceFields(),
                                            syncBizService.getRangField(),
                                            twoDaysAgoSyncTime,
                                            sort,
                                            syncBizService.getSortField()
                                    ))
                    );

                    dataView = query.getDataView();

                    XxlJobLogger.log("大搜es数据-[({})平台数据]已经同步了: {}", platformType, total);
                }
            }

            // 更新同步时间(每天下午4点)
            dsMapper.update(syncBiz.getJson(), nowSyncTimeStr);
            XxlJobLogger.log("大搜es数据-[({})平台数据]同步成功, total: {}", platformType, total);
        } catch (Exception e) {
            log.error("大搜es数据-[{}平台增量数据]同步失败, e: ", syncBiz.getJson(), e);
            XxlJobLogger.log("大搜es数据-[{}平台增量数据]同步失败, e: ", syncBiz.getJson(), e);
        }
    }

    public static final String ACCOUNT_COLLECT_COUNT_QUERY = """
            SELECT
                id,
                account_id,
                collect_count,
                platform
            FROM nr_main_account_search_all_collect
            WHERE gmt_modified >= #{gmtModified}
            AND id > #{cursor}
            ORDER BY id
            LIMIT 1000
            """;

    static final String ACCOUNT_QUERY_DSL = """
            GET search_nr_account/_search
            {
              "size": 1000,
              "_source": [
                "index_id",
                "account_id",
                "platform_type"
              ],
              "query": {
                "bool": {
                  "must": [
                    {
                      "terms": {
                        "account_id": <foreach collection="accountIds" item="accountId" open="[" separator="," close="]">
                                  #{accountId}
                              </foreach>
                      }
                    }
                  ]
                }
              }
            }
            """;

    /**
     * 同步账号收藏数到有度主站es(全平台)
     * 每天进行增量同步
     */
    public void accountCollectCountSync(String gmtModified) {
        if (StrUtil.isBlank(gmtModified)) {
            // 获取昨天开始日期
            gmtModified = DateTimeUtil.format(LocalDate.now().minusDays(1).atStartOfDay());
        }
        try {
            int total = 0;
            long cursor = 0;
            while (true) {
                final QueryBuilder queryBuilder = phbMainMysql.newQueryBuilder()
                        .template(ACCOUNT_COLLECT_COUNT_QUERY)
                        .addParam("cursor", cursor)
                        .addParam("gmtModified", gmtModified);

                final JSONArray items = phbMainMysql.query(queryBuilder).data();
                List<JSONObject> collectCounts = items.toList(JSONObject.class);
                if (collectCounts.isEmpty()) {
                    return;
                }
                cursor = collectCounts.get(collectCounts.size() - 1).getLong("id");

                // 查询唯一键
                QueryBuilder dsQueryBuilder = dSEs.newQueryBuilder()
                        .template(ACCOUNT_QUERY_DSL)
                        .addParam("accountIds", toList(collectCounts, item -> item.getString("account_id")));
                final JSONObject resp = dSEs.query(dsQueryBuilder).data();

                final List<JSONObject> accountItems = resp.getJSONObject("hits")
                        .getJSONArray("hits")
                        .toJavaList(JSONObject.class)
                        .stream()
                        .map(json -> json.getJSONObject("_source"))
                        .toList();

                Map<String, String> map = toMap(accountItems,
                        item -> item.getString("platform_type") + item.getString("account_id"),
                        item -> item.getString("index_id")
                );

                List<CollectCount> exists = new ArrayList<>(collectCounts.size());
                for (JSONObject c : collectCounts) {
                    String key = c.getString("platform") + c.getString("account_id");

                    if (map.containsKey(key)) {
                        exists.add(CollectCount.of(map.get(key), c.getInteger("collect_count")));
                    }
                }

                if (CollUtil.isNotEmpty(exists)) {
                    commonEsService.update(exists);
                    total += exists.size();
                    XxlJobLogger.log("大搜es数据-[账号收藏数]已经同步了: {}", total);
                }
            }
        } catch (Exception e) {
            log.error("大搜es数据-[账号收藏数]同步失败, e: ", e);
            XxlJobLogger.log("大搜es数据-[账号收藏数]同步失败, e: ", e);
        }
    }
}
