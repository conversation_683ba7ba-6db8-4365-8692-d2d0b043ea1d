package cn.newrank.niop.data.biz.biz.dy.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @TableName niop_data_biz_dy_buyin_shop_info
 */
@Data
@Accessors(chain = true)
public class DyBuyinShopInfo implements Serializable {
    /**
     *
     */
    private String gmtModified;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String productId;

    /**
     *
     */
    private String promotionId;

    /**
     *
     */
    private String secShopId;

    /**
     *
     */
    private String price;

    /**
     *
     */
    private String oldPrice;

    /**
     *
     */
    private Long saleNum;

    /**
     *
     */
    private String shopId;

    /**
     *
     */
    private String shopName;

    /**
     *
     */
    private String deviceName;

    /**
     *
     */
    private String partitionOffset;

    private static final long serialVersionUID = 1L;

}