package cn.newrank.niop.data.biz.biz.xhs.service;

import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/5 13:55
 */
public abstract class AbstractSyncService {





    /**
     * 同步历史记录
     *
     * @param cursor 游标
     * @return 是否成功
     */
    public boolean syncHistory(@Nullable String cursor) {
        return false;
    }


    /**
     * 同步新记录
     *
     * @param startTime 开始时间
     * @return 是否成功
     */
    public boolean syncNewRecords(@Nullable String startTime) {
        if (StringUtils.isBlank(startTime)) {
            startTime = DateTimeUtil.format(LocalDate.now().minusDays(1));
        }

        return true;
    }
}
