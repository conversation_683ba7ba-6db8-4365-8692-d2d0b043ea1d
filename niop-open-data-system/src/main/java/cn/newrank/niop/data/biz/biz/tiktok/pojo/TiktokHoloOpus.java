package cn.newrank.niop.data.biz.biz.tiktok.pojo;

import cn.newrank.niop.data.util.DateTimeUtil;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 20:22
 */
@Data
public class TiktokHoloOpus {
    /**
     * 收藏数
     */
    private Long collectNum;

    /**
     * 评论数
     */
    private Long commentNum;

    /**
     * 封面
     */
    private String cover;
    /**
     * 点赞数
     */
    private Long likeNum;
    /**
     * 作品id
     */
    private String opusId;

    /**
     * 发布时间
     */
    private Timestamp publishTime;

    /**
     * 分享数
     */
    private Long shareNum;
    /**
     * uid
     */
    private String uid;

    /**
     * 在看数
     */
    private Long viewNum;

    /**
     * 分区键
     */
    private String ds;

    public static TiktokHoloOpus of(TiktokOpus tiktokOpus) {
        final TiktokHoloOpus opus = new TiktokHoloOpus();

        final LocalDateTime pubTime = tiktokOpus.getPublishTime().toLocalDateTime();
        opus.setDs(DateTimeUtil.format(pubTime, "yyyyMM"));
        opus.setCollectNum(tiktokOpus.getCollectNum());
        opus.setCommentNum(tiktokOpus.getCommentNum());
        opus.setCover(tiktokOpus.getCover());
        opus.setLikeNum(tiktokOpus.getLikeNum());
        opus.setOpusId(tiktokOpus.getOpusId());
        opus.setPublishTime(Timestamp.valueOf(pubTime));
        opus.setShareNum(tiktokOpus.getShareNum());
        opus.setUid(tiktokOpus.getUid());
        opus.setViewNum(tiktokOpus.getViewNum());

        return opus;
    }
}
