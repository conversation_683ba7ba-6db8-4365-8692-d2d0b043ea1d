package cn.newrank.niop.data.biz.biz.ds.service.wb;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.WbEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_WB_RANK_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WbRankInsertDataSyncService implements SyncBizService<WbEsMetaData> {


    private final DsConfigManager dsConfigManager;

    public WbRankInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseWbEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_weibo_week_rank";
    }


    @Override
    public String getRangField() {
        return "week_index";
    }


    @Override
    public String getRangIndex() {
        return "rank_date";
    }

    @Override
    public String getUniqueIndex() {
        return MAIN_WB_RANK_FIELD_MAPPING.get("account_id");
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_WB_RANK_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<WbEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), WbEsMetaData.class);
        });
    }

    @Override
    public List<WbEsMetaData> convertData(List<WbEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (WbEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();
            for (Map.Entry<String, String> entry : MAIN_WB_RANK_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }

            mainMap.put("platform_type", "weibo");


            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.WEIBO.getDbCode() + "_" + mainMap.get("account_id"));
            }

            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), WbEsMetaData.class);
    }


}
