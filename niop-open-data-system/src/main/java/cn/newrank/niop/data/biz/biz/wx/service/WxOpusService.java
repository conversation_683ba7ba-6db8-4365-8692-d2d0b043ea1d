package cn.newrank.niop.data.biz.biz.wx.service;

import cn.newrank.niop.data.biz.biz.wx.mapper.WxOpusMapper;
import cn.newrank.niop.data.biz.biz.wx.pojo.WxOpus;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/14 10:47
 */
@Service
public class WxOpusService implements StorageBizService<WxOpus> {

    private final WxOpusMapper wxOpusMapper;

    public WxOpusService(WxOpusMapper wxOpusMapper) {
        this.wxOpusMapper = wxOpusMapper;
    }

    @Override
    public void storeBatch(List<WxOpus> items) {
        wxOpusMapper.insertBatch(items);
    }

    @Override
    public List<WxOpus> list(List<String> identifiers) {
        return wxOpusMapper.list(identifiers);
    }

    @Override
    public WxOpus castOf(JSONObject item) {
        final Object topics = item.getJSONObject("data").remove("topics");
        final WxOpus wxOpus = StorageBizService.format(WxOpus.class, item);

        wxOpus.setTopics(topics == null ? null : JSON.toJSONString(topics));

        return wxOpus;
    }
}
