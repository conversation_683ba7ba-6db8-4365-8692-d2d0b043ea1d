package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;


import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpSubmitSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 监测话题表
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
public interface XhsExpScheduleMapper {

    /**
     * 获取话题调度器
     *
     * @return 调度器列表
     */
    List<XhsExpSubmitSchedule> listSchedules();


    /**
     * 更新话题调度器
     *
     * @param id           id
     * @param lastExecTime 最后执行时间
     * @return 更新结果
     */
    boolean updateScheduleExecTime(@Param("id") Long id,
                                   @Param("lastExecTime") LocalDateTime lastExecTime);

}




