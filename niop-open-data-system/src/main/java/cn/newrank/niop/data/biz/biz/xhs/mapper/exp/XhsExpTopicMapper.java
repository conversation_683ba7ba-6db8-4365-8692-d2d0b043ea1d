package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;


import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监测话题表
 * <AUTHOR>
 * @since 2025/3/11
 */
@Mapper
public interface XhsExpTopicMapper {

    /**
     * 批量存储
     * @param items 存储对象
     * @return 存储结果
     */
    boolean storeBatch(@Param("items") List<XhsExpTopic> items);

    /**
     * 根据权重获取需要存储的数据
     * @param weight 权重
     * @param cursor 游标
     * @param size 数量
     * @return  存储对象
     */
    List<XhsExpTopic> listByWeight(@Param("weight") Integer weight,
                                   @Param("cursor") Long cursor,
                                   @Param("size") Integer size);

    /**
     * 更新topic权重
     * @param topicId 话题
     * @param topicWeight 话题权重
     * @return 更新结果
     */
    boolean updateTopicWeight(@Param("topicId") String topicId,
                              @Param("weight") Integer topicWeight);


    /**
     * 根据topicId获取topicWeight
     * @param topicId topicId
     * @return topicWeight
     */
    Integer getTopicWeight(@Param("topicId") String topicId);

    /**
     * 根据topicId获取topicWeight
     * @param topicIds topicId
     * @return topicWeight
     */
    List<String> listExistTopicIds(@Param("items") List<String> topicIds);


}




