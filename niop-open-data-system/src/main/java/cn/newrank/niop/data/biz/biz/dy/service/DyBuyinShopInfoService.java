package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinShopInfoMapper;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyBuyinShopInfoSourceDataMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShopInfo;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyBuyinShopInfoSourceData;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @createTime 2024/10/29
 * @description
 */
@Service
@RequiredArgsConstructor
public class DyBuyinShopInfoService {

    private final DyBuyinShopInfoMapper buyinShopVerifyMapper;

    private final DyBuyinShopInfoSourceDataMapper buyinShopInfoSourceDataMapper;

    private static final Pattern COVER_PATTERN = Pattern.compile("(?<=https://p3-aio.ecombdimg.com/obj/ecom-shop-material/)[A-Za-z0-9]+");

    public void shopSkuData(JSONObject json, String deviceName) {
        String partitionOffset = json.getString("kafka_partition") + "_" + json.getString("kafka_offset");
        JSONObject dataJson = json.getJSONObject("json_details").getJSONObject("data");
        if (dataJson == null || dataJson.isEmpty()) {
            return;
        }
        String productId = dataJson.getString("product_id");
        String promotionId = dataJson.getString("promotion_id");
        JSONObject modelJson = dataJson.getJSONObject("model");
        JSONObject productJson = modelJson.getJSONObject("product");
        if (productJson == null || productJson.isEmpty()) {
            return;
        }
        //获取商品信息
        JSONObject productBaseJson = productJson.getJSONObject("product_base");
        if (productBaseJson == null || productBaseJson.isEmpty()) {
            return;
        }
        String secShopId = handleCoverSign(productBaseJson.getString("cover"));
        Long saleNum = productJson.getJSONObject("product_sales").getJSONObject("product_label").getLong("sales_num");
        JSONObject priceJson = productJson.getJSONObject("product_price").getJSONObject("price_label");
        String price = priceJson.getString("price");
        String oldPrice = priceJson.getString("old_price");
        //获取店铺信息
        JSONObject shopJson = modelJson.getJSONObject("shop");
        String shopId = shopJson.getString("shop_id");
        String shopName = shopJson.getJSONObject("shop_base").getString("shop_name");
        buyinShopVerifyMapper.insertOne(new DyBuyinShopInfo()
                .setProductId(productId)
                .setPromotionId(promotionId)
                .setSecShopId(secShopId)
                .setPrice(price)
                .setOldPrice(oldPrice)
                .setSaleNum(saleNum)
                .setShopId(shopId)
                .setShopName(shopName)
                .setDeviceName(deviceName)
                .setPartitionOffset(partitionOffset)
        );
        //保存原始数据
        buyinShopInfoSourceDataMapper.insertOne(new DyBuyinShopInfoSourceData()
                .setProductId(productId)
                .setPartitionOffset(partitionOffset)
                .setSourceData(json.toJSONString()));
    }

    private static String handleCoverSign(String coverUrl) {
        if (StrUtil.isBlank(coverUrl)) {
            return "";
        }
        Matcher matcher = COVER_PATTERN.matcher(coverUrl);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }
}
