package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.data.biz.export.constant.DataExportConstant;
import lombok.Data;

/**
 * 能力完成任务信息, 不包含结果数据
 *
 * <AUTHOR>
 */
@Data
public class AbilityFinishTask {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 能力id
     */
    private String abilityId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 业务code
     */
    private Integer bizCode;

    /**
     * 业务消息
     */
    private String bizMsg;

    /**
     * 任务状态
     *
     * [1.成功 -1.失败 -2.超时]
     */
    private Integer status;

    /**
     * 完成时间
     */
    private String finishTime;

    /**
     * 是否是导数应用的任务
     *
     * @return 是否导数应用的任务
     */
    public boolean fromExportApp() {
        return DataExportConstant.EXPORT_APP_ID.equals(this.getAppId());
    }

    /**
     * 任务状态是否成功
     *
     * @return 状态是否成功
     */
    public boolean statusSucceed() {
        return 1 == this.getStatus();
    }

}
