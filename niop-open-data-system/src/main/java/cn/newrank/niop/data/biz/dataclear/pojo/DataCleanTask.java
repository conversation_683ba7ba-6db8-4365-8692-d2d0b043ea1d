package cn.newrank.niop.data.biz.dataclear.pojo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DataCleanTask {
    /**
     * 任务id
     */
    private String taskId;

    /**
     * 数据源id
     */
    private String dcId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 对外展示执行sql
     */
    private String sql;

    /**
     * 真实每次去执行sql
     */
    private String realSql;

    /**
     * 耗时
     */
    private int duration;

    /**
     * 任务状态，-3:暂停中 -2：丢弃 -1：失败，0：待执行，1：完成，2：执行中
     */
    private int taskStatus;

    /**
     * 实际影响行数
     */
    private int realDeleteNum;

    /**
     * 预计影响行数
     */
    private int predictDeleteNum;

    /**
     * 下次执行时间（单次+10s）
     */
    private LocalDateTime nextExecTime;

    /**
     * 规则id
     */
    private String ruleId;

}
