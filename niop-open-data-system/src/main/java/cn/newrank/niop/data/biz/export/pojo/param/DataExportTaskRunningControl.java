package cn.newrank.niop.data.biz.export.pojo.param;

import cn.newrank.nrcore.web.validator.OneOf;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class DataExportTaskRunningControl {

    /**
     * 导数任务id
     */
    @NotBlank(message = "导数任务id不能为空")
    private String exportTaskId;

    /**
     * 运行状态
     */
    @OneOf(values = "0|1", message = "运行状态参数异常")
    private String runningStatus;

}
