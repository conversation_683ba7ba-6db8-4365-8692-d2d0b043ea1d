package cn.newrank.niop.data.biz.biz.xhs.mapper.exp;

import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1 10:20:07
 */
@Mapper
public interface XhsExpAtspTaskMapper {


    /**
     * 创建表
     * @param partition 分区
     * @param execDate 执行日期
     * @return 创建结果
     */
    boolean createTable(@Param("partition") String partition,
                        @Param("execDate") LocalDate execDate);


    /**
     * 删除表
     * @param partition 分区
     * @return 删除结果
     */
    boolean dropTable(@Param("partition") String partition);

    /**
     * 创建任务
     * @param items 任务列表
     * @param partition 分区
     * @return 创建结果
     */
    boolean storeBatch(@Param("items") List<XhsExpAtspTask> items,
                       @Param("partition") String partition);

    /**
     * 获取未提交的任务数量
     * @return 任务数量
     */
    Integer countNotSubmitTask();

    /**
     * 获取未提交的任务
     * @param size 大小
     * @return 任务列表
     */
    List<XhsExpAtspTask> listNotSubmitTask(@Param("size") Integer size);

    /**
     * 获取未提交的任务
     * @param size 大小
     * @return 任务列表
     */
    List<XhsExpAtspTask> listNotSubmitTaskByCursor(@Param("cursor") String cursor,
                                                   @Param("size") Integer size);

    /**
     * 批量更新任务
     * @param task 任务
     * @return 更新结果
     */
    boolean updateTaskStatus(@Param("task") XhsExpAtspTask task, @Param("taskId") String taskId);


    /**
     * 批量根据任务ID获取任务
     * @param taskIds 任务ID
     * @return 任务
     */
    List<XhsExpAtspTask> getTasksByIds(@Param("taskIds") List<String> taskIds);


    /**
     * 获取已翻页数
     * @return 任务数量
     */
    Integer getParentCurrentPageNum(@Param("parentId") String parentId);

    /**
     * 获取未完成任务
     * @param size 大小
     * @return 任务列表
     */
    List<XhsExpAtspTask> listRunningTask(@Param("execDate")LocalDate execDate,
                                         @Param("cursor") String cursor,
                                         @Param("size") Integer size);

    List<XhsExpAtspTask> listTasks(@Param("execDate")LocalDate execDate);
}
