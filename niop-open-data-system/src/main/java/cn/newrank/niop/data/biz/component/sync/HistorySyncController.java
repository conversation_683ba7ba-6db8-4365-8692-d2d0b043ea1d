package cn.newrank.niop.data.biz.component.sync;

import cn.newrank.niop.data.biz.component.sync.model.StateUpdate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/5 13:41
 */
@Validated
@RestController
@RequestMapping("history-sync")
public class HistorySyncController {

    private final HistorySyncContext historySyncContext;

    public HistorySyncController(HistorySyncContext historySyncContext) {
        this.historySyncContext = historySyncContext;
    }

    /**
     * 新增同步
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param name      同步名称
     * @return 结果
     */
    @GetMapping("newSync/{name}")
    public boolean newSync(LocalDateTime startTime,
                           LocalDateTime endTime,
                           Integer hours,
                           String start,
                           String end,
                           @PathVariable String name) {
        if (startTime == null || endTime == null) {
            return historySyncContext.getSynchronizer(name).newSync(start, end);
        }else {
            if (hours == null) {
                return historySyncContext.getSynchronizer(name).newSync(startTime, endTime);
            } else {
                return historySyncContext.getSynchronizer(name).newSync(startTime, endTime, Duration.ofHours(hours));
            }
        }
    }

    /**
     * 调度
     *
     * @param name 名称
     */
    @GetMapping("schedule/{name}")
    public void schedule(@PathVariable String name) {
        historySyncContext.getSynchronizer(name).schedule();
    }


    /**
     * 获取名称
     *
     * @return 同步器名称
     */
    @GetMapping("names")
    public List<String> getNames() {
        return historySyncContext.getNames();
    }


    @GetMapping("processes/{name}")
    public List<Cursor<?>> getProcesses(@PathVariable String name) {
        return historySyncContext.getSynchronizer(name).getProgress();
    }


    /**
     * 更新状态
     *
     * @param stateUpdate 更新状态
     * @return 结果
     */
    @PostMapping("operate")
    public boolean update(@RequestBody StateUpdate stateUpdate) {
        return historySyncContext.getSynchronizer(stateUpdate.getName())
                .operate(stateUpdate.getKey(), stateUpdate.getState());
    }

}
