package cn.newrank.niop.data.util;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 基于Lambda 的公用创建者模式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/11/25 17:41
 */
public final class Builders<T> {
    private final Supplier<T> constructor;
    private Consumer<T> setters;

    private Builders(Supplier<T> constructor) {
        if (constructor == null) {
            throw new NullPointerException("constructor == null");
        }
        this.constructor = constructor;
    }

    /**
     * 工厂方法
     *
     * @param constructor 构造方法
     * @param <T>         实体类型
     * @return builder
     */
    public static <T> Builders<T> of(Supplier<T> constructor) {
        return new Builders<>(constructor);
    }

    /**
     * 工厂方法
     *
     * @param entity 实体
     * @param <T>    实体类型
     * @return builder
     */
    public static <T> Builders<T> of(T entity) {
        return new Builders<>(() -> entity);
    }

    /**
     * 属性设置
     *
     * @param setter setter 方法
     * @param value  值
     * @param <U>    属性类型
     * @return builder
     */
    public <U> Builders<T> with(BiConsumer<T, U> setter, U value) {
        if (setters == null) {
            setters = entity -> setter.accept(entity, value);
        } else {
            setters = setters.andThen(entity -> setter.accept(entity, value));
        }
        return this;
    }

    public Builders<T> with(Consumer<T> setter) {
        if (setter == null) {
            return this;
        }
        if (this.setters == null) {
            this.setters = setter;
        } else {
            this.setters = this.setters.andThen(setter);
        }
        return this;
    }

    /**
     * 执行构造
     *
     * @return 实体
     */
    public T build() {
        final T entity = constructor.get();
        setters.accept(entity);
        return entity;
    }
}
