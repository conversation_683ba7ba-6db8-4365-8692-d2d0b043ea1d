package cn.newrank.niop.data.biz.biz.ks.pojo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/28 15:42
 */
@Data
public class HistorySync implements Comparable<HistorySync>{
    public static final int UNSTART = 0;
    public static final int RUNNING = 1;
    public static final int FINISHED = 2;
    String startTime;
    String endTime;
    String scrollId;
    /**
     * 0:未开始 1:进行中 2:已完成
     */
    int status = UNSTART;
    int total;
    int completed;
    String address;
    String error;

    public String getKey() {
        return startTime + "~" + endTime;
    }

    public boolean isRunning() {
        return status == RUNNING;
    }

    public boolean isParsed() {
        return isRunning() && StringUtils.isBlank(address);
    }

    @Override
    public int compareTo(@NotNull HistorySync o) {
        return StringUtils.compare(getKey(), o.getKey());
    }
}
