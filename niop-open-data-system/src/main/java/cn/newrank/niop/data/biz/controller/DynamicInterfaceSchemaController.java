package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.data.biz.pojo.param.*;
import cn.newrank.niop.data.biz.service.DynamicInterfaceSchemaService;
import cn.newrank.niop.data.common.entity.DynamicInterfaceSchema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 15:55
 */

@Validated
@RestController
@RequestMapping("dynamic-interface/schema")
@RequiredArgsConstructor
public class DynamicInterfaceSchemaController {
    private final DynamicInterfaceSchemaService dynamicInterfaceSchemaService;

    /**
     * 保存动态接口Schema
     */
    @PostMapping("save")
    public String save(@Valid @RequestBody DynamicInterfaceSchemaSave interfaceSchemaSave) {
        return dynamicInterfaceSchemaService.save(interfaceSchemaSave);
    }

    /**
     * 获取动态接口Schema
     *
     * @param interfaceSchemaId 接口SchemaID
     * @return 接口
     */
    @GetMapping("get")
    public DynamicInterfaceSchema get(@NotBlank(message = "接口SchemaID(interfaceId)不能为空") String interfaceId) {
        return dynamicInterfaceSchemaService.get(interfaceId);
    }

}
