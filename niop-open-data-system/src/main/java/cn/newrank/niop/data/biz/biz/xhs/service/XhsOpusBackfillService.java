package cn.newrank.niop.data.biz.biz.xhs.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusHoloMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsUserLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpHoloOpusMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.nrcore.json.JsonParser;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 小红书作品存储ldm服务
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
@Service
public class XhsOpusBackfillService {

    private final XhsOpusLmMapper xhsOpusLmMapper;
    private final XhsUserLmMapper xhsUserLmMapper;
    private final XhsOpusHoloMapper xhsOpusHoloMapper;
    private final XhsExpHoloOpusMapper xhsExpHoloOpusMapper;
    private final RedissonClient redissonClient;
    private final EsFactory.Es xhsEs;


    public XhsOpusBackfillService(XhsOpusLmMapper xhsOpusLmMapper,
                                  XhsUserLmMapper xhsUserLmMapper,
                                  XhsOpusHoloMapper xhsOpusHoloMapper,
                                  XhsExpHoloOpusMapper xhsExpHoloOpusMapper,
                                  RedissonClient redissonClient,
                                  DsConfigManager dsConfigManager) {
        this.xhsOpusLmMapper = xhsOpusLmMapper;
        this.xhsUserLmMapper = xhsUserLmMapper;
        this.xhsOpusHoloMapper = xhsOpusHoloMapper;
        this.xhsExpHoloOpusMapper = xhsExpHoloOpusMapper;
        this.redissonClient = redissonClient;
        this.xhsEs = EsFactory.DEFAULT.create(dsConfigManager.chooseLmEsConfig());
    }

    public static final String ES_DATA = """
            GET xhs.dim_opus.idx_search/_search
                {
                    "size": #{size},
                        "query": {
                    "bool": {
                        "must": [
                        {
                            "term": {
                            "poi_id": {
                                "value": ""
                            }
                        }
                        }
                  ]
                    }
                },
                    "_source": "opus_id",
                        "sort": [
                    {
                        "opus_id": {
                        "order": "asc"
                    }
                    }
              ],
                    "search_after": [#{searchAfter}]
                }
            """;

    private List<XhsOpusFromMulti> getOpus(String cursor) {
        if (Strings.isBlank(cursor)) {
            cursor = "";
        }
        JSONObject object = xhsEs.query(xhsEs.newQueryBuilder().template(ES_DATA)
                .addParam("size", 1000)
                .addParam("searchAfter", cursor)).data();
        return EsUtil.listHitsToEntity(object, json -> {
            JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
            if (Objects.isNull(sourceObj)) {
                return null;
            }
            return JsonParser.parseObject(sourceObj, XhsOpusFromMulti.class);
        });
    }


    public void fillLdmOpus(String param) {
        XxlJobLogger.log("开始");
        RBucket<String> cacheCursor = redissonClient.getBucket("xhs:opus:sync:fill:cursor");
        RBucket<String> cacheDsTime = redissonClient.getBucket("xhs:opus:sync:fill:dsTime");
        String cursor = "";
        int count = 0;
        while (true) {
            List<XhsOpusFromMulti> list = getOpus(cursor);
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            cursor = list.get(list.size() - 1).getOpusId();
            cacheCursor.set(cursor);
            count += list.size();
            XxlJobLogger.log("进度 {},cursor:{}", count, cursor);
            List<String> ids = list.stream().map(XhsOpusFromMulti::getOpusId).toList();
            xhsOpusLmMapper.fillPoiNull(ids);
        }
        XxlJobLogger.log("完毕");
        cacheDsTime.set("");
        cacheCursor.set("");
    }
}
