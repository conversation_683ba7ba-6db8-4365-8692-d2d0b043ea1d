package cn.newrank.niop.data.util;

import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;

public class TriggerUtil {

    /**
     * 下次指数退避的触发时间
     * @param triggerTimes 触发次数
     * @param triggerDelayTimes 触发延迟次数
     * @param defaultTriggerSeconds 默认触发时间
     */
    public static @NotNull LocalDateTime nextExpBackTime(int triggerTimes, int triggerDelayTimes, int defaultTriggerSeconds) {
        int intervalSeconds = triggerTimes < triggerDelayTimes
                ? defaultTriggerSeconds
                : (int) Math.pow(2, (triggerTimes - triggerDelayTimes)) * defaultTriggerSeconds;
        return LocalDateTime.now().plusSeconds(intervalSeconds);
    }

}
