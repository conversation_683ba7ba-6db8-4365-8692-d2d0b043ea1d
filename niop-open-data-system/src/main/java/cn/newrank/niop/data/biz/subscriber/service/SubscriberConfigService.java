package cn.newrank.niop.data.biz.subscriber.service;

import cn.newrank.niop.data.biz.subscriber.pojo.dto.CbEnableParamSubCache;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigCreate;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigDelete;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigPageQuery;
import cn.newrank.niop.data.biz.subscriber.pojo.param.SubscriberConfigUpdate;
import cn.newrank.niop.web.model.PageView;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SubscriberConfigService {


    /**
     * 创建订阅配置
     *
     * @param configCreate 配置信息
     * @return 配置ID
     */
    String create(SubscriberConfigCreate configCreate);

    /**
     * 获取订阅配置
     *
     * @param subscriberId 订阅ID
     * @return 订阅配置
     */
    SubscriberConfig get(String subscriberId);

    /**
     * 更新订阅配置
     *
     * @param configUpdate 配置信息
     * @return 是否更新成功
     */
    boolean update(SubscriberConfigUpdate configUpdate);

    /**
     * 获取订阅配置列表
     *
     * @param pageQuery 查询参数
     * @return 订阅配置列表
     */
    PageView<SubscriberConfig> page(SubscriberConfigPageQuery pageQuery);

    /**
     * 根据订阅源类型和订阅源ID搜索回调ID列表
     *
     * @param sourceType 订阅源类型
     * @param sourceId   订阅源ID
     * @param appId
     * @return 回调ID列表
     */
    List<String> searchCbIds(SubSourceType sourceType, String sourceId, String appId);


    /**
     * 根据订阅源类型和订阅源ID
     *
     * @param sourceType 订阅源类型
     * @param sourceId   订阅源ID
     * @param appId      应用ID
     * @return 回调列表
     */
    List<CbEnableParamSubCache> searchCdEnableParam(SubSourceType sourceType, String sourceId, String appId);

    /**
     * 删除订阅关系
     *
     * @param subscriberId
     * @return boolean
     * <AUTHOR>
     * @date 2024/8/6 上午10:59
     */
    boolean delete(SubscriberConfigDelete subscriberId);

    /**
     * 根据回调ID获取订阅关系列表
     *
     * @param cbId       回调ID
     * @param sourceName 数据源
     * @param appName    应用名称
     * @return 订阅关系列表
     */
    List<SubscriberConfig> list(String cbId, String sourceName, String appName);
}

