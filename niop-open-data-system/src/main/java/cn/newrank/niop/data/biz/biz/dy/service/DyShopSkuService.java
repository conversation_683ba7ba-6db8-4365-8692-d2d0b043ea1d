package cn.newrank.niop.data.biz.biz.dy.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.newrank.niop.data.biz.biz.dy.mapper.DyShopSkuMapper;
import cn.newrank.niop.data.biz.biz.dy.pojo.DyShopSku;
import cn.newrank.niop.data.biz.biz.dy.pojo.dto.DyShopSkuDTO;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024/10/17 
 * @description 
 */
@Service
@RequiredArgsConstructor
public class DyShopSkuService {


    private final DyShopSkuMapper shopSkuMapper;


    public void shopSkuData(JSONObject item, String deviceName) {
        String productId = StrUtil.isNotBlank(item.getString("logic_id")) ? item.getString("logic_id") : item.getString("product_id");
        String partitionOffset = item.getString("kafka_partition") + "_" + item.getString("kafka_offset");

        JSONObject jsonDetails = item.getJSONObject("json_details");
        JSONArray specsList = jsonDetails.getJSONArray("specs");
        List<String> specsName = new ArrayList<>();
        List<JSONObject> specsItems = new ArrayList<>();
        specsList.forEach(specsJson -> {
            JSONObject jsonData = (JSONObject) specsJson;
            specsName.add(jsonData.getString("name"));
            JSONArray specItems = jsonData.getJSONArray("spec_items");
            if (CollUtil.isNotEmpty(specItems)) {
                specItems.forEach(specItemjson -> {
                    JSONObject json = (JSONObject) specItemjson;
                    JSONObject jsonResult = new JSONObject();
                    jsonResult.put("id", json.getString("id"));
                    jsonResult.put("name", json.getString("name"));
                    specsItems.add(jsonResult);
                });
            }
        });
        JSONObject pic = jsonDetails.getJSONObject("pic");

        JSONObject skusListInfo = jsonDetails.getJSONObject("skus");
        List<DyShopSkuDTO> skuList = skusListInfo.keySet().stream().map(skuLongId -> {
            JSONObject skuInfo = skusListInfo.getJSONObject(skuLongId);
            return new DyShopSkuDTO()
                    .setPartitionOffset(partitionOffset)
                    .setDeviceName(deviceName)
                    .setProductId(productId)
                    .setSkuLongId(skuLongId)
                    .setSkuPrice(skuInfo.getString("price"))
                    .setSkuStockNum(skuInfo.getLong("stock_num"))
                    .setSkuId(skuInfo.getString("sku_id"))
                    .setSkuExtra(skuInfo.getString("sku_extra"))
                    .setSpecsName(specsName)
                    .setSpecsItems(specsItems)
                    .setPic(pic == null ? "" : JSONObject.toJSONString(pic));
        }).toList();
        List<DyShopSku> resultList = skuList.stream().map(DyShopSku::createItem).toList();
        if (CollUtil.isNotEmpty(resultList)) {
            shopSkuMapper.batchSave(resultList);
        }

    }


}
