package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.DsConfigCreate;
import cn.newrank.niop.data.biz.pojo.param.DsConfigUpdate;
import cn.newrank.niop.data.biz.pojo.param.DsFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.DsPageQuery;
import cn.newrank.niop.web.model.PageView;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:41
 */
public interface DsConfigService {


    /**
     * 创建数据源配置
     *
     * @param dsConfigCreate 数据源配置信息
     * @return 数据源配置id
     */
    String create(DsConfigCreate dsConfigCreate);

    /**
     * 获取数据源配置
     *
     * @param dcId 数据源配置id
     * @return 数据源配置
     */
    DsConfig getConfig(String dcId);

    /**
     * 判断数据源配置是否存在
     *
     * @param dcId 数据源配置id
     * @return 是否存在
     */
    boolean hasConfig(String dcId);

    /**
     * 更新数据源
     *
     * @param dsConfigUpdate 数据源配置信息
     * @return 是否成功
     */
    boolean update(DsConfigUpdate dsConfigUpdate);

    /**
     * 删除数据源配置
     *
     * @param dcId 数据源配置id
     * @return 是否成功
     */
    boolean delete(String dcId);

    /**
     * 模糊查询数据源配置
     *
     * @param fuzzyQuery 模糊查询条件
     * @return 数据源配置列表
     */
    List<DsConfig> fuzzyQuery(DsFuzzyQuery fuzzyQuery);

    /**
     * 校验数据源配置id
     *
     * @param dcId 数据源配置id
     */
    void checkDcId(String dcId);

    /**
     * 获取所有数据源配置
     *
     * @return 数据源配置列表
     */
    List<DsConfig> listAll();

    /**
     * 分页查询数据源配置
     *
     * @param pageQuery 分页查询条件
     * @return 数据源配置列表
     */
    PageView<DsConfig> page(DsPageQuery pageQuery);

    /**
     * 获取数据源配置集合
     *
     * @param dcIds 数据源配置id集合
     * @return 数据源配置集合
     */
    Map<String, DsConfig> map(Collection<String> dcIds);

    /**
     * 根据传入的type获取数据源配置集合
     *
     * @param types
     * @return List<DsConfig>
     * @date 2025/1/16 10:34
     */
    List<DsConfig> findByTypes(Collection<String> types);
}
