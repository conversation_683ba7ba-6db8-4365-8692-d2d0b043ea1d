package cn.newrank.niop.data.biz.export.excel;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ListUtils;
import com.google.common.collect.Maps;
import java.io.ByteArrayInputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import org.apache.commons.io.IOUtils;

public class ExportExcelReader implements Closeable {

    private final ExcelReader excelReader;

    private final InputStream inputStream;

    public ExportExcelReader(InputStream inputStream) throws IOException {
        this.inputStream = new ByteArrayInputStream(IOUtils.toByteArray(inputStream));
        this.excelReader = EasyExcelFactory.read(this.inputStream).build();
        this.inputStream.reset();
    }

    public Integer getRowTotal() throws IOException {
        final RowCountListener rowCounter = new RowCountListener();
        EasyExcelFactory.read(this.inputStream, rowCounter).sheet().doRead();
        this.inputStream.reset();
        return rowCounter.getRowCount();
    }

    public List<Map<String, String>> viewData(int dataSize) throws IOException {
        final DataViewerListener viewerListener = new DataViewerListener(dataSize);
        EasyExcelFactory.read(this.inputStream, viewerListener).sheet().doRead();
        this.inputStream.reset();
        return viewerListener.getData();
    }

    public Integer forEachRead(int batchSize, Consumer<List<Map<String, ?>>> consumer) throws IOException {
        final BatchReadListener readListener = new BatchReadListener(batchSize, consumer);
        EasyExcelFactory.read(this.inputStream, readListener).sheet().doRead();
        this.inputStream.reset();
        return readListener.getDataCount();
    }

    private static class BatchReadListener extends AnalysisEventListener<Map<Integer, String>> {

        /**
         * 每批读取的数据量
         */
        private final int batchSize;

        private Map<Integer, String> headMap;

        private Set<Integer> keySet;

        /**
         * 缓存数据列表
         */
        private final List<Map<String, ?>> cacheDataList;

        /**
         * 读取的数据量
         */
        private final AtomicInteger readDataCount = new AtomicInteger(0);

        private final Consumer<List<Map<String, ?>>> consumer;

        public BatchReadListener(int batchSize, Consumer<List<Map<String, ?>>> consumer) {
            this.batchSize = Math.max(10, batchSize);
            this.cacheDataList = ListUtils.newArrayListWithExpectedSize(batchSize);
            this.consumer = consumer;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            this.headMap = headMap;
            this.keySet = headMap.keySet();
        }

        @Override
        public void invoke(Map<Integer, String> map, AnalysisContext context) {
            final Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();

            Map<String, Object> rowMap = Maps.newHashMapWithExpectedSize(keySet.size());
            keySet.forEach(i -> rowMap.put(headMap.get(i), getCellValue(cellMap.get(i))));

            this.cacheDataList.add(rowMap);
            if (this.cacheDataList.size() >= batchSize) {
                this.consume();
                this.cacheDataList.clear();
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 保证剩下的数据被消费
            if (CollUtil.isNotEmpty(this.cacheDataList)) {
                this.consume();
            }
        }

        public Integer getDataCount() {
            return readDataCount.get();
        }

        private void consume() {
            this.consumer.accept(this.cacheDataList);
            // 记录消费的数据量
            readDataCount.addAndGet(this.cacheDataList.size());
        }

        private Object getCellValue(Cell cell) {
            if (cell instanceof ReadCellData<?> readCellData) {
                return switch (readCellData.getType()) {
                    case STRING -> readCellData.getStringValue();
                    case NUMBER -> readCellData.getNumberValue().longValue();
                    case BOOLEAN -> readCellData.getBooleanValue();
                    default -> readCellData.getData();
                };
            }

            throw new IllegalArgumentException("Unsupported cell type: " + cell.getClass().getName());
        }

    }

    private static class DataViewerListener extends AnalysisEventListener<Map<Integer, String>> {

        private final int MAX_DATA_SIZE;

        private Map<Integer, String> headMap;

        private final List<Map<String, String>> data = new ArrayList<>(20);

        public DataViewerListener(int maxDataSize) {
            this.MAX_DATA_SIZE = maxDataSize;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            this.headMap = headMap;
        }

        @Override
        public void invoke(Map<Integer, String> map, AnalysisContext context) {
            if (data.size() < MAX_DATA_SIZE) {
                Set<Integer> keySet = headMap.keySet();
                Map<String, String> rowMap = Maps.newHashMapWithExpectedSize(keySet.size());
                keySet.forEach(i -> rowMap.put(headMap.get(i), map.get(i)));
                data.add(rowMap);
            }
        }

        public List<Map<String, String>> getData() {
            return this.data;
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // do nothing
        }

        @Override
        public boolean hasNext(AnalysisContext context) {
            return data.size() < MAX_DATA_SIZE;
        }

    }

    private static class RowCountListener extends AnalysisEventListener<Map<Integer, String>> {

        private final AtomicInteger rowCount = new AtomicInteger(0);

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            if (CollUtil.isNotEmpty(data)) {
                rowCount.incrementAndGet();
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // do nothing
        }

        public Integer getRowCount() {
            return this.rowCount.get();
        }

    }

    @Override
    public void close() throws IOException {
        this.excelReader.finish();
        this.inputStream.close();
    }

}
