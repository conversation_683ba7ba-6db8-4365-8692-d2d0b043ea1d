package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.CollectionPageQuery;
import cn.newrank.niop.data.biz.pojo.param.ColumnQuery;
import cn.newrank.niop.data.biz.pojo.param.DatasourceConnectionTest;
import cn.newrank.niop.data.biz.service.DatasourceService;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.common.RedisEnableManager;
import cn.newrank.niop.data.common.ds.*;
import cn.newrank.niop.data.common.event.DatasourceConfigRefreshTopic;
import cn.newrank.niop.web.model.PageView;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 数据源服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 15:23
 */
@Service
@Log4j2
public class DatasourceServiceImpl implements DatasourceService {

    protected final ConcurrentMap<String/*dcId*/, Datasource> datasourceCache;
    private final DsConfigService dsConfigService;
    private final RedisEnableManager redisEnableManager;

    public DatasourceServiceImpl(DsConfigService dsConfigService,
                                 RedissonClient redissonClient,
                                 DatasourceConfigRefreshTopic datasourceConfigRefreshTopic) {
        this.datasourceCache = new ConcurrentHashMap<>(256);
        this.dsConfigService = dsConfigService;
        this.redisEnableManager = new RedisEnableManager(redissonClient, "ds");
        datasourceConfigRefreshTopic.addListener((channel, msg) -> {
            try {
                log.info("数据源配置(dcId: {})被修改，将关闭连接", msg.getDcId());
                final Datasource datasource = datasourceCache.remove(msg.getDcId());
                if (datasource != null) {
                    datasource.close();
                }
            } catch (Exception ignore) {
                //
            }
        });
    }


    @Override
    public boolean testConnection(DatasourceConnectionTest connectionTest) {
        final DatasourceFactory factory = connectionTest.getType().getFactory();

        final DsConfig dsConfig = connectionTest.toDto();

        try (final Datasource datasource = factory.create(dsConfig.getConfig())) {
            if (datasource.isActive()) {
                return true;
            } else {
                throw datasource.getCause();
            }
        }
    }

    @Override
    public PageView<Collection> pageCollection(CollectionPageQuery pageQuery) {
        final List<Collection> collections = getDatasource(pageQuery.getDcId()).getCollections();
        final String schemaName = pageQuery.getSchemaName();
        final String keyword = pageQuery.getKeyword();
        final List<Collection> items = collections.stream()
                .sorted(Comparator.comparing(Collection::getName))
                .filter(collection -> collection.isNameMatch(keyword))
                .filter(collection -> StringUtils.isBlank(schemaName) || schemaName.equals(collection.getSchemaName()))
                .toList();

        final List<Collection> records = items
                .stream()
                .skip((long) pageQuery.getPageSize() * (pageQuery.getPageNum() - 1))
                .limit(pageQuery.getPageSize())
                .toList();

        final int total = items.size();
        return new PageView<>(records, total / pageQuery.getPageSize() + 1, total);
    }

    @Override
    public List<Collection> getChildren(String dcId, String collection) {
        final Datasource datasource = getDatasource(dcId);
        return datasource.getChildren(collection)
                .stream()
                .sorted(Comparator.comparing(Collection::getName))
                .toList();
    }

    @Override
    public List<String> querySchemaName(String dcId) {
        final List<Collection> collections = getDatasource(dcId).getCollections();
        return collections.stream()
                .map(Collection::getSchemaName)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> querytableName(String dcId, String schemaNameToFilter) {
        final List<Collection> collections = getDatasource(dcId).getCollections();
        return collections.stream()
                .filter(collection -> schemaNameToFilter.equals(collection.getSchemaName()))
                .map(Collection::getName)
                .collect(Collectors.toList());
    }

    @Override
    public List<Column> getColumns(ColumnQuery columnQuery) {
        return getDatasource(columnQuery.getDcId()).getColumns(columnQuery.getCollection())
                .stream()
                .sorted(Comparator.comparing(Column::getName))
                .toList();
    }

    @Override
    public Resp.PageView queryPreview(String dcId, String collection) {
        final Datasource datasource = getDatasource(dcId);
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .collection(collection)
                .enablePreview();

        return datasource.query(queryBuilder).getPageView();
    }

    @Override
    public Datasource getDatasource(String dcId) {
        return datasourceCache.computeIfAbsent(dcId, id -> {
            if (redisEnableManager.isEnable(dcId)) {
                final DsConfig config = dsConfigService.getConfig(dcId);
                if (config == null) {
                    throw createParamError("数据源配置(dcId: {})不存在", dcId);
                }

                final DatasourceFactory factory = config.getType().getFactory();
                final Datasource datasource = factory.create(config.getConfig());

                if (datasource.isActive()) {
                    return datasource;
                } else {
                    log.warn("数据源配置(dcId: {})连接失败, e: ", dcId, datasource.getCause());
                    datasource.close();
                }

                redisEnableManager.disable(dcId, Duration.ofMinutes(1));
            }


            throw createParamError("数据源配置(id: {})连接失败", dcId);
        });

    }

    @Override
    public Resp query(String dcId, String query, Map<String, Object> args) {
        final Datasource datasource = getDatasource(dcId);
        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(query);

        if (args != null) {
            args.forEach(queryBuilder::addParam);
        }

        return datasource.query(queryBuilder);
    }
}
