package cn.newrank.niop.data.biz.dataclear.pojo.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


@Data
public class CleanRuleUpadteQuery implements CleanRuleQuery {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则id
     */
    @NotBlank
    private String ruleId;

    /**
     * 数据源id
     */
    private String dcId;

    /**
     * 表名
     */
    private String tableName;
    /**
     * schemaName
     */
    private String schemaName;

    /**
     * 执行表达式（小时级）
     */
    private String cron;

    /**
     * 过滤条件
     */
    private String filterCondition;

    /**
     * 负责人，逗号分割
     */
    private String principal;

    /**
     * 规则状态，1：开启，0：关闭
     */
    private int ruleStatus;

}