package cn.newrank.niop.data.biz.component.biz;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.config.SystemConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/30 16:20
 */
@Component
public class DsConfigManager {
    private final SystemConfig systemConfig;

    public DsConfigManager(SystemConfig systemConfig) {
        this.systemConfig = systemConfig;
    }

    /**
     * niop lindorm 数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseNiopLmConfig() {
        if (systemConfig.isProduct()) {
            return key -> switch (key) {
                case ADDRESS -> "ld-bp15hsj7j8rjot7a6-proxy-lindorm-vpc.lindorm.aliyuncs.com:33060";
                case USERNAME -> "niop_dc";
                case PASSWORD -> "qaBKqwfG0#P5tuEr";
                case DATABASE -> "niop_slr";
                default -> throw new IllegalStateException("Unexpected value: " + key);
            };
        }

        return key -> switch (key) {
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "*************:33060";
                } else {
                    yield "ld-bp15hsj7j8rjot7a6-proxy-lindorm-vpc.lindorm.aliyuncs.com:33060";
                }
            }
            case DATABASE -> "niop_slr_test";
            case USERNAME -> "yd_api_dev";
            case PASSWORD -> "7ZzJ2s8F@DLF$%Kc";
            default -> throw new IllegalStateException("Unexpected value: " + key);
        };
    }

    /**
     * xhs holo数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseXhsHoloConfig() {
        if (systemConfig.isDevelop()) {
            return key -> switch (key) {
                case ADDRESS -> "hgprecn-cn-x0r3cckur004-cn-hangzhou.hologres.aliyuncs.com:80";
                case USERNAME -> "LTAI4GJNHjpFYPAuarc7EtVt";
                case PASSWORD -> "******************************";
                case DATABASE -> "xhs";
                default -> throw new IllegalArgumentException("未知的配置");
            };
        }
        return key -> switch (key) {
            case ADDRESS -> "hgprecn-cn-x0r3cckur004-cn-hangzhou-vpc-st.hologres.aliyuncs.com:80";
            case USERNAME -> "LTAI5t9yvi1AbDBryb3RhanD";
            case PASSWORD -> "******************************";
            case DATABASE -> "xhs";
            default -> throw new IllegalArgumentException("未知的配置");
        };
    }

    /**
     * 有数holo数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseYsHoloConfig() {
        final DsConfig config = new DsConfig();

        config.setConfig(key -> switch (key) {
            case USERNAME -> "LTAI5tByMANjbdQ7n5LMFgs5";
            case PASSWORD -> "******************************";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "hgprecn-cn-pe337iks9002-cn-hangzhou.hologres.aliyuncs.com:80";
                } else {
                    yield "hgprecn-cn-pe337iks9002-cn-hangzhou-vpc-st.hologres.aliyuncs.com:80";
                }
            }
            case DATABASE -> "youzhuan_kol";
            default -> null;
        });

        return config.getConfig();
    }

    /**
     * 分析主站Es数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseDsEsConfig() {
        // ES大搜-只有内网环境可调用
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> "es-cn-x0r3fh6e3000o9u0y.elasticsearch.aliyuncs.com:9200";
            default -> null;
        };
    }

    /**
     * 商桥MySQL数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseDsMySQLConfig() {
        return key -> switch (key) {
            case USERNAME -> "g_dml_uoaf3o";
            case PASSWORD -> "PNZ7P1UNBZNSN44R";
            case ADDRESS -> "rm-bp1r4k6t6l91pshid681.mysql.rds.aliyuncs.com:3306";
            case DATABASE -> "phb_main";
            default -> null;
        };
    }

    /**
     * 公众号Es数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseGzhEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-4590ylnnc00020t9z.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-4590ylnnc00020t9z.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    /**
     * 抖音Es数据库
     *
     * @return 配置
     */
    public ConfigProperties chooseDyEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-2r42zdk71000isxmo.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-2r42zdk71000isxmo.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseBzEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-nwy34dd600002kvrj.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-nwy34dd600002kvrj.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseLmEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "root";
            case PASSWORD -> "UYdgnUkZwIiw";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "ld-bp1e25h77kx2tl94o-proxy-search-pub.lindorm.aliyuncs.com:30070";
                } else {
                    yield "ld-bp1e25h77kx2tl94o-proxy-search-vpc.lindorm.aliyuncs.com:30070";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseKsEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-zvp2ayevz002tkn1z.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-zvp2ayevz002tkn1z.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseXhsEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "xhs-search.public.elasticsearch.newrank.cn:9200";
                } else {
                    yield "xhs-search.elasticsearch.newrank.cn:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseWbEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-i6h2y72fz000nh9g5.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-i6h2y72fz000nh9g5.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseSphEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "elastic_youdu";
            case PASSWORD -> "BjkNydJqo6";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-9lb33ligg000lgmh1.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-9lb33ligg000lgmh1.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    /**
     * 有度的open的大搜
     *
     * @return 配置
     */
    public ConfigProperties chooseEsConfig() {
        return key -> switch (key) {
            case USERNAME -> "niop_dc";
            case PASSWORD -> "x3Cxl8dnlH0dut8Y";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-cfn3vu6rj000pcbzy.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-cfn3vu6rj000pcbzy.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseAdHoloKolConfig() {
        return key -> switch (key) {
            case USERNAME -> "adholo_kol";
            case PASSWORD -> "ok#bTG-R7sY5K";
            case ADDRESS -> {
                if (systemConfig.isDevelop()) {
                    yield "es-cn-7pp2nfzgz000it7xw.public.elasticsearch.aliyuncs.com:9200";
                } else {
                    yield "es-cn-7pp2nfzgz000it7xw.elasticsearch.aliyuncs.com:9200";
                }
            }
            default -> null;
        };
    }

    public ConfigProperties chooseDyLmConfig() {
        return chooseLmNrDefault("dy");
    }

    public ConfigProperties chooseXhsLmConfig() {
        return chooseLmNrDefault("xhs");
    }

    public ConfigProperties chooseWxLmConfig() {
        return chooseLmNrDefault("wx");
    }

    public ConfigProperties chooseLmNrDefault(String db) {
        final String targetDb = systemConfig.isProduct() ? db : db + "_test";
        final String targetAddress = systemConfig.isDevelop()
                ? "*************:33160"
                : "ld-bp1e25h77kx2tl94o-proxy-lindorm-vpc.lindorm.aliyuncs.com:33060";

        return key -> switch (key) {
            case USERNAME -> "niop_dc";
            case PASSWORD -> "6SyadWMfr2!1ElqJ";
            case ADDRESS -> targetAddress;
            case DATABASE -> targetDb;
            default -> null;
        };
    }
}
