package cn.newrank.niop.data.biz.biz.ds.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.DataCenterEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.RankPlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBiz;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizServiceContext;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncEntity;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cn.newrank.niop.data.util.EsUtil.HITS;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class CommonRankSyncHistoryInsertData {
    //先拿没有rankdata的，拿出其account的id，再去库里查询对应的
    public static final String OPEN_ACCOUNT = """
            GET search_nr_account_20241015/_search?scroll=10m
                    {
                      "size": 500,
                      "query": {
                        "bool": {
                          "must": [
                            {
                              "term": {
                                "platform_type": {
                                  "value": #{platform}
                                }
                              }
                            }
                          ],
                          "must_not": [
                            {
                              "exists": {
                                "field": "nr_index_week"
                              }
                            }
                          ]
                        }
                      },
                      "_source": [
                        "account_id"
                      ]
                    }
            """;

    public static final String ACCOUNT_LIST_SEARCH_AFTER_QUERY = """
            GET  /_search/scroll
            {
                "scroll" : "5m",
                "scroll_id" :  "%s"
            }
            """;

    public static final String TARGET_PLATFORM = """
            GET /%s/_search
              {"size": 1,
               "_source": %s,
                "query": {
                  "term": {
                    "%s": {
                      "value": "%s"
                    }
                  }
                }
                , "sort": [
                  {
                    "%s": {
                      "order": "desc"
                    }
                  }
                ]
              }
            """;


    private final EsFactory esFactory;
    private final SyncBizServiceContext syncBizServiceContext;
    private final CommonEsService commonEsService;
    protected final Datasource dsEs;

    public CommonRankSyncHistoryInsertData(SyncBizServiceContext syncBizServiceContext, CommonEsService commonEsService) {
        this.esFactory = EsFactory.DEFAULT;
        this.syncBizServiceContext = syncBizServiceContext;
        this.commonEsService = commonEsService;
        this.dsEs = EsFactory.DEFAULT.create(commonEsService.getRestClient());
    }

    public void syncInsertData(RankPlatformType platformType) {
        XxlJobLogger.log("大搜es开始同步平台{}", platformType);
        final SyncBiz syncBiz = SyncBiz.ofJSONValue(platformType.getDbCode());
        final SyncBizService<? extends SyncEntity> syncBizService = syncBizServiceContext.getService(syncBiz);
        try (final EsFactory.Es es = esFactory.create(syncBizService.getConfig())) {
            String platform = platformType.getDbCode().split("r")[0];

            Resp query = dsEs.query(
                    dsEs.newQueryBuilder()
                            .template(OPEN_ACCOUNT)
                            .addParam("platform", platform)
            );
            JSONObject resultObj = query.data();

            int total = 0;
            while (true) {
                String scrollId = resultObj.getString(EsUtil.SCROLL_ID);

                List<String> accountIds = EsUtil.listHitsToEntity(resultObj, json -> {
                    JSONObject sourceObj = json.getJSONObject(EsUtil.SOURCE);
                    return sourceObj.getString("account_id");
                });
                if (CollUtil.isEmpty(accountIds)) {
                    XxlJobLogger.log("大搜es数据-[({})平台数据]同步成功, total: {}", platformType, total);
                    break;
                }

                List<DataCenterEsMetaData> dataLists = new ArrayList<>();
                // 依次去查询周榜中区查询
                for (String accountId : accountIds) {
                    Resp query1 = es.query(
                            es.newQueryBuilder()
                                    .template(
                                            String.format(TARGET_PLATFORM,
                                                    syncBizService.getIndexName(),
                                                    syncBizService.getSourceFields(),
                                                    syncBizService.getUniqueIndex(),
                                                    accountId,
                                                    syncBizService.getRangField()
                                            )
                                    )
                    );
                    JSONObject resultObj1 = query1.data();
                    if (resultObj1.getJSONObject(HITS).getInteger("total") == 0) {
                        continue;
                    }
                    List<DataCenterEsMetaData> dataList = syncBizService.parseData(resultObj1, DataCenterEsMetaData.class);
                    dataLists.addAll(dataList);
                }

                if (CollUtil.isNotEmpty(dataLists)) {
                    // 同步到数据中心es
                    commonEsService.upsert(dataLists);
                    total += dataLists.size();
                    XxlJobLogger.log("大搜es数据-[({})平台数据]同步成功, total: {}", platformType, total);
                }

                Resp scrollQueryResp = dsEs.query(
                        dsEs.newQueryBuilder()
                                .template(String.format(ACCOUNT_LIST_SEARCH_AFTER_QUERY, scrollId))
                );
                resultObj = scrollQueryResp.data();
            }
        }
    }
}
