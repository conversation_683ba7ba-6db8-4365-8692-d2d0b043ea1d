package cn.newrank.niop.data.biz.biz.ks.mapper;

import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:53
 */
@Mapper
@DS("ks")
public interface LmKsOpusMapper {

    void storeBasicBatch(@Param("items") List<LmKsOpus> items);

    void storeExtBatch(@Param("items") List<LmKsOpus> items);

    void deleteBatch(@Param("items") List<LmKsOpus> items);

    List<LmKsOpus> list(@Param("ids") List<String> ids);

    List<LmKsOpus> listNonSync(@Param("next") String next);

    void updateSync(@Param("items") List<LmKsOpus> lmKsOpuses);

    void storeCategoryBatch(@Param("items") List<LmKsOpus> items);

    void storeHistory(@Param("items") List<LmKsOpus> items);

    /**
     * 获取存在的作品id列表
     *
     * @param photoIds 作品id列表
     * @return 存在的作品id列表
     */
    List<String> listExistsPhotoId(@Param("ids") List<String> photoIds);
}




