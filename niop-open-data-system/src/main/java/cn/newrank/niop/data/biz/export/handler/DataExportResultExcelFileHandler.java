package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.excel.ExportExcelWriter;
import cn.newrank.niop.data.biz.export.factory.DataExportResultHandlerFactory;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportConsumeTaskResult;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportDeliveryType;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import com.alibaba.fastjson2.JSONObject;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class DataExportResultExcelFileHandler extends BaseDataExportFileHandler {

    public DataExportResultExcelFileHandler(OssService ossService,
                                            DataExportResultHandlerFactory resultHandlerFactory,
                                            DataExportTaskService dataExportTaskService) {
        super(ossService, resultHandlerFactory, dataExportTaskService);
    }

    @Override
    public DataExportDeliveryType getFileDeliveryType() {
        return DataExportDeliveryType.EXCEL;
    }

    @Override
    public Boolean generateAndUploadResultFiles(String exportTaskId, Consumer<FileUploadResult> uploadResultHandler) throws IOException {
        final DataExportResultHandler dataExportResultHandler = getResultHandler(exportTaskId);

        JSONObject sampleJson = dataExportResultHandler.getSample(exportTaskId).firstData();
        Set<String> header = handleFileHeader(sampleJson.keySet());

        int fileNum = 1;
        while (true) {
            final File file = createResultFile(exportTaskId, fileNum);

            DataExportConsumeTaskResult consumeResult;
            try (ExportExcelWriter writer = new ExportExcelWriter(file, header)) {
                consumeResult = dataExportResultHandler.forEachConsume(exportTaskId, results -> {
                    handleResults(results);
                    writer.write(results.getDataList());
                });
            }

            // 上传生成的文件
            final FileUploadResult uploadResult = uploadResultFile(exportTaskId, file);

            try {
                if (!uploadResult.succeed()) {
                    return false;
                }

                uploadResultHandler.accept(uploadResult);

                // 如果数据写完了
                if (consumeResult.isEnd()) {
                    return true;
                }
                // 继续写入下一个文件
                fileNum++;
            } finally {
                Files.deleteIfExists(file.toPath());
            }
        }
    }

    @Override
    public File createResultFile(String exportTaskId, Integer fileNumber) throws IOException {
        final File file = new File(getFileName(exportTaskId, fileNumber));
        Files.deleteIfExists(file.toPath());
        return file;
    }

    @Override
    public String getFileName(String exportTaskId, Integer fileNumber) {
        return exportTaskId + String.format("-result%s.xlsx", Objects.nonNull(fileNumber) ? fileNumber : "");
    }

}
