package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.factory.DataExportResultHandlerFactory;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportFileType;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import cn.newrank.niop.data.util.Md5Util;
import com.alibaba.fastjson2.JSONObject;
import java.io.File;
import java.util.LinkedHashSet;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public abstract class BaseDataExportFileHandler implements DataExportResultFileHandler {

    private static final Logger log = LoggerFactory.getLogger(BaseDataExportFileHandler.class);

    protected final OssService ossService;

    protected final DataExportResultHandlerFactory resultHandlerFactory;

    protected final DataExportTaskService dataExportTaskService;

    protected BaseDataExportFileHandler(OssService ossService,
                                        DataExportResultHandlerFactory resultHandlerFactory,
                                        DataExportTaskService dataExportTaskService) {
        this.ossService = ossService;
        this.resultHandlerFactory = resultHandlerFactory;
        this.dataExportTaskService = dataExportTaskService;
    }

    @Override
    public DataExportResultHandler getResultHandler(String exportTaskId) {
        final DataExportTaskExecutionDTO executionInfo = dataExportTaskService.getExportTaskExecutionInfo(exportTaskId);
        return this.resultHandlerFactory.getResultHandler(executionInfo.getExportType());
    }

    @Override
    public Set<String> handleFileHeader(Set<String> header) {
        Set<String> updatedHeader = new LinkedHashSet<>(header);
        updatedHeader.add("taskId");
        return updatedHeader;
    }

    @Override
    public void handleResults(ExportResult results) {
        final String resultTaskId = results.getResultTaskId();
        // 额外添加字段
        for (JSONObject jsonObject : results.getDataList()) {
            jsonObject.put("taskId", resultTaskId);
        }
    }

    @Override
    public FileUploadResult uploadResultFile(String exportTaskId, File file) {
        try {
            String fileUrl = ossService.multipartUploadFile(DataExportFileType.TASK_RESULT.getFileDir(), file);
            final String md5 = Md5Util.md5(file);
            return FileUploadResult.of(exportTaskId, fileUrl, file.getName(), md5);
        } catch (Exception e) {
            log.error("{} 导数任务结果文件上传异常, e: ", exportTaskId, e);
        }
        return FileUploadResult.failed(exportTaskId);
    }

}
