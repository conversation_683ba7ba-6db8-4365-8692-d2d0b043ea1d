package cn.newrank.niop.data.biz.component.biz;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;

import java.util.List;

/**
 * 业务的存储服务， {@link StorageBiz} 管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 10:02
 */
public interface StorageBizService<T extends StorageEntity> {

    static <T extends SampleVersionEntity> T format(Class<T> clazz, JSONObject item) {
        final JSONObject data = item.getJSONObject("data");
        final T t = data.to(clazz);

        t.setSampleId(item.getString("sampleId"));
        t.setSampleStatus(item.getString("sampleStatus"));
        t.setUpdateTime(item.getLongValue("updateTime"));

        return t;
    }

    /**
     * 获取数据
     *
     * @param identifier 标识
     * @return 数据
     */
    default T get(String identifier) {
        final List<T> ts = list(List.of(identifier));

        if (ts == null || ts.isEmpty()) {
            return null;
        }

        return ts.get(0);
    }


    /**
     * 存储数据
     *
     * @param items 数据
     */
    void storeBatch(List<T> items);

    /**
     * 存储数据
     *
     * @param item 数据
     */
    default void store(StorageEntity item) {
        if (item != null) {
            storeBatch((List<T>) List.of(item));
        }
    }

    /**
     * 类型转换
     *
     * @param item 原始数据
     * @return 目标数据
     */
    T castOf(JSONObject item);

    default T castWithPrintException(JSONObject item) {
        try {
            return castOf(item);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 批量获取数据
     *
     * @param identifiers 标识
     * @return 数据
     */
    default List<T> list(List<String> identifiers) {
        throw new IllegalArgumentException("未实现");
    }

    default void storeJSONBatch(List<JSONObject> records) {
        if (records == null) {
            return;
        }

        storeBatch(records.stream().map(this::castWithPrintException).toList());
    }
}
