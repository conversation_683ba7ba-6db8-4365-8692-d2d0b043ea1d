package cn.newrank.niop.data.biz.callback.event;

import cn.newrank.niop.data.biz.callback.service.AbstractNacosServiceRefresher;
import cn.newrank.niop.data.biz.component.callback.Callback;
import cn.newrank.niop.data.biz.component.callback.WebhhookCallback;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.biz.pojo.enums.CbType;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.service.CbConfigService;
import cn.newrank.niop.data.util.IPv4;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.naming.listener.NamingEvent;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RMap;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 触发事件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/29 9:29
 */
@Log4j2
@Component
public class CbEventListener extends AbstractNacosServiceRefresher {

    final RMap<String, String> cbServerAddress;
    final CallbackService callbackService;
    final CbConfigService cbConfigService;
    final String localAddress;
    final RTopic scheduleEventTopic;

    public CbEventListener(RedissonClient redissonClient,
                           CallbackService callbackService,
                           CbConfigService cbConfigService,
                           ServerProperties serverProperties) {
        this.cbServerAddress = redissonClient.getMap("cb_server_address");
        this.callbackService = callbackService;
        this.cbConfigService = cbConfigService;
        this.localAddress = IPv4.local() + ":" + serverProperties.getPort();
        this.scheduleEventTopic = redissonClient.getTopic("cb_schedule_event");
        this.scheduleEventTopic.addListener(String.class, (channel, msg) -> {
            try {
                final CbScheduleEvent cbScheduleEvent = JSON.parseObject(msg, CbScheduleEvent.class);
                processScheduleEvent(cbScheduleEvent);
            } catch (Exception e) {
                log.error("schedule event parse error", e);
            }
        });
    }

    /**
     * 触发事件监听
     *
     * @param event 事件
     */
    @EventListener
    public void onCbTriggerEvent(CbTriggerEvent event) {
        if (!isInitialized()) {
            return;
        }
        final CbConfig cbConfig = cbConfigService.get(event.getCbId());
        if (cbConfig == null) {
            return;
        }
        if (cbConfig.getType() != CbType.WEBHOOK) {
            return;
        }

        final String serverAddress = cbServerAddress.compute(event.getCbId(), (cbId, address) -> {
            if (address != null) {
                if (address.equals(localAddress)) {
                    return address;
                }

                if (getServerAddresses().contains(address)) {
                    return address;
                }
            }

            final String newAddress = chooseAvailableAddress();
            log.info("change address {} to {} for {}", address == null ? "" : address, newAddress, event.getCbId());
            return newAddress;
        });

        onScheduleEvent(CbScheduleEvent.of(event.getCbId(), serverAddress));
    }

    /**
     * 发布调度事件,可以暂停或者转移调度
     *
     * @param scheduleEvent 事件
     */
    @EventListener
    public void onScheduleEvent(CbScheduleEvent scheduleEvent) {
        cbServerAddress.put(scheduleEvent.getCbId(), scheduleEvent.getServerAddress());
        scheduleEventTopic.publish(JSON.toJSONString(scheduleEvent));
    }

    /**
     * 调度事件监听, redis广播，每台服务器都会监听到，只有本地的才开启调度，否则关闭
     *
     * @param event 事件
     */
    private void processScheduleEvent(CbScheduleEvent event) {
        if (!isInitialized()) {
            return;
        }

        final Callback callback = callbackService.get(event.getCbId());
        if (callback instanceof WebhhookCallback webhhookCallback) {
            if (localAddress.equals(event.getServerAddress())) {
                webhhookCallback.startScheduler();
            } else {
                webhhookCallback.stopScheduler();
            }
        }
    }

    private String chooseAvailableAddress() {
        final List<String> serverAddresses = getServerAddresses();
        if (serverAddresses.size() == 1) {
            return localAddress;
        }

        final Set<Map.Entry<String, String>> entries = cbServerAddress.readAllEntrySet();
        final Map<String, Integer> map = new HashMap<>();
        for (String serverAddress : serverAddresses) {
            map.put(serverAddress, 0);
        }

        for (Map.Entry<String, String> entry : entries) {
            if (serverAddresses.contains(entry.getValue())) {
                map.compute(entry.getValue(), (k, v) -> v == null ? 1 : v + 1);
            }
        }
        return map.entrySet()
                .stream()
                .min(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(localAddress);
    }


    @Override
    protected void refresh(NamingEvent event) {
        log.info("Nacos refresh ...");
        final List<String> serverAddresses = getServerAddresses();

        cbServerAddress.forEach((cbId, address) -> {
            if (serverAddresses.contains(address)) {
                return;
            }

            onCbTriggerEvent(CbTriggerEvent.of(cbId));
        });
    }
}
