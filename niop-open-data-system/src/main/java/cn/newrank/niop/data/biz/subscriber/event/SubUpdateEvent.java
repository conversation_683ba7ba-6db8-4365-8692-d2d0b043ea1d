package cn.newrank.niop.data.biz.subscriber.event;

import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/13 16:31
 */
@Data
public class SubUpdateEvent {

    String sourceType;
    String sourceId;

    public static SubUpdateEvent of(SubSourceType sourceType, String sourceId) {
        final SubUpdateEvent updateEvent = new SubUpdateEvent();

        updateEvent.setSourceType(sourceType.getJsonValue());
        updateEvent.setSourceId(sourceId);

        return updateEvent;

    }
}
