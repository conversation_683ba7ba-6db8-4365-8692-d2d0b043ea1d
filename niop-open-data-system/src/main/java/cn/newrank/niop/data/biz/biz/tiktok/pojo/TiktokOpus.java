package cn.newrank.niop.data.biz.biz.tiktok.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/18 13:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TiktokOpus extends SampleVersionEntity {
    /**
     * 账号
     */
    private String account;

    /**
     * 地址
     */
    private String address;

    /**
     * 收藏数
     */
    private Long collectNum;

    /**
     * 评论数
     */
    private Long commentNum;

    /**
     * 封面
     */
    private String cover;

    /**
     * 描述
     */
    private String description;

    /**
     * 时长
     */
    private Long duration;

    /**
     * 点赞数
     */
    private Long likeNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 作品id
     */
    private String opusId;

    /**
     * 发布时间
     */
    private Timestamp publishTime;

    /**
     * sec uid
     */
    private String secUid;

    /**
     * 分享数
     */
    private Long shareNum;

    /**
     * 签名
     */
    private String signature;

    /**
     * uid
     */
    private String uid;

    /**
     * 在看数
     */
    private Long viewNum;


    @Override
    public String identifier() {
        return opusId;
    }
}