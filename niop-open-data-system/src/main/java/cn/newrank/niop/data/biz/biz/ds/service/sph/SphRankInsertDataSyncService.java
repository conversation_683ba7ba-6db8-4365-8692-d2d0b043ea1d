package cn.newrank.niop.data.biz.biz.ds.service.sph;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.SphEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_SPH_RANK_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class SphRankInsertDataSyncService implements SyncBizService<SphEsMetaData> {

    private final DsConfigManager dsConfigManager;

    public SphRankInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseSphEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_week_index_rank";
    }


    @Override
    public String getRangField() {
        return "week_index";
    }


    @Override
    public String getRangIndex() {
        return "rank_date";
    }

    @Override
    public String getUniqueIndex() {
        return MAIN_SPH_RANK_FIELD_MAPPING.get("account_id");
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_SPH_RANK_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        return Iterables.toList(sourceFields, field -> "\"" + field + "\"");
    }


    @Override
    public List<SphEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), SphEsMetaData.class);
        });
    }

    @Override
    public List<SphEsMetaData> convertData(List<SphEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (SphEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();

            for (Map.Entry<String, String> entry : MAIN_SPH_RANK_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }
            mainMap.put("platform_type", "sph");

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", "sph" + "_" + mainMap.get("account_id"));
            }
            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), SphEsMetaData.class);
    }
}
