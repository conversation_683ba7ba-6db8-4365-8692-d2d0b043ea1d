package cn.newrank.niop.data.biz.subscriber.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 订阅源类型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 10:55
 */
public enum SubSourceType implements BizEnum {
    SAMPLE("sample", "样本中心"),
    SCHEDULER("scheduler", "工作流"),
    SERVICE_HUB("serviceHub", "综合服务"),
    ABILITY_RAW_RESULT("abilityRawResult", "能力原始结果"),
    ABILITY_PARSED_RESULT("abilityParsedResult", "能力解析结果"),
    ABILITY_RAW_RESULT_OLD("abilityRawResultOld", "能力原始结果(旧版)"),
    ABILITY_PARSED_RESULT_OLD("abilityParsedResultOld", "能力解析结果(旧版)"),
    DATA_ARK("dataArk", "数据方舟"),
    ;

    final String value;
    final String desc;

    SubSourceType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SubSourceType ofJSONValue(String json) {
        for (SubSourceType type : values()) {
            if (type.value.equals(json)) {
                return type;
            }
        }

        throw createParamError("SubSourceType(value: {}) not found", json);
    }

    @Override
    public String getDescription() {
        return desc;
    }

    @Override
    public String getJsonValue() {
        return value;
    }

    @Override
    public String getDbCode() {
        return value;
    }
}
