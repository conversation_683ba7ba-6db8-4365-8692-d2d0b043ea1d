package cn.newrank.niop.data.biz.pojo.dto;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/19 10:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StorageResult {
    String identifier;
    int version;
    long updateTime;
    String data;


    public static StorageResult of(QueriedLog log) {
        final LogItem item = log.GetLogItem();
        return JSONObject.parseObject(item.ToJsonString(), StorageResult.class);
    }
}
