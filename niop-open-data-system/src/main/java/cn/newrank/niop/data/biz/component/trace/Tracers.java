package cn.newrank.niop.data.biz.component.trace;

import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanBuilder;
import io.opentelemetry.api.trace.Tracer;
import lombok.Getter;
import lombok.experimental.UtilityClass;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/11 9:25
 */
@UtilityClass
public class Tracers {
    public static final String CALLBACK_KAFKA = "CALLBACK_KAFKA";
    public static final String CALLBACK_WEBHOOK = "CALLBACK_WEBHOOK";
    @Getter
    private static final Tracer cbTracer;
    static {
       cbTracer = GlobalOpenTelemetry.getTracer("cn.newrank.callback"
                , "1.0.0");
    }

    public Span getKafkaCbSpan(CallbackRedirect callbackRedirect, String cbId) {
        final SpanBuilder spanBuilder = cbTracer.spanBuilder(CALLBACK_KAFKA);

        final ConsumerRecord<String, String> consumerRecord = callbackRedirect.getConsumerRecord();
        if (consumerRecord != null) {
            spanBuilder
                    .setAttribute("kafka.send.topic", consumerRecord.topic())
                    .setAttribute("kafka.send.partition", consumerRecord.partition())
                    .setAttribute("kafka.send.offset", consumerRecord.offset())
                    .setAttribute("kafka.key", consumerRecord.key());
        }

        spanBuilder
                .setAttribute("cb.id", cbId)
                .setAttribute("cb.source.id", callbackRedirect.getSourceId())
                .setAttribute("cb.source.type", callbackRedirect.getSourceType())
                .setAttribute("cb.source.key", callbackRedirect.getSourceKey());

        if (callbackRedirect instanceof CallbackRetry callbackRetry) {
            spanBuilder.setAttribute("cb.times", callbackRetry.getRetryNums());
        }else {
            spanBuilder.setAttribute("cb.times", 1);
        }
        return spanBuilder.startSpan();
    }

    public static Span getWebhookCbSpan(CbHttpTask task, String cbId, String url) {
        final SpanBuilder spanBuilder = cbTracer.spanBuilder(CALLBACK_WEBHOOK);

        spanBuilder
                .setAttribute("cb.id", cbId)
                .setAttribute("cb.source.id", task.getSourceId())
                .setAttribute("cb.source.type", task.getSourceType())
                .setAttribute("cb.source.key", task.getSourceKey())
                .setAttribute("http.url", url)
                .setAttribute("cb.times", task.getRetryTimes() + 1);

        return spanBuilder.startSpan();
    }
}
