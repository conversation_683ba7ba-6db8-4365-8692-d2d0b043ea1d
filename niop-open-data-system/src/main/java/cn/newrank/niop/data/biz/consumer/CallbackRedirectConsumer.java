package cn.newrank.niop.data.biz.consumer;

import cn.newrank.niop.common.Environment;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.config.property.KafkaProperties;
import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.StreamSupport;

/**
 * 回调结果重试以及存储消费者
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/29 17:26
 */
@Log4j2
public class CallbackRedirectConsumer extends CommonConsumer {


    public CallbackRedirectConsumer(KafkaProperties.Config config,
                                    Environment environment,
                                    StorageService storageService,
                                    CallbackService callbackService,
                                    ThreadPoolExecutor executor) {
        super(config, environment, new CallbackRedirectConsumeService(storageService, callbackService), executor);
    }

    public static class CallbackRedirectConsumeService implements ConsumeService {
        private final StorageService storageService;
        private final CallbackService callbackService;

        public CallbackRedirectConsumeService(StorageService storageService, CallbackService callbackService) {
            this.storageService = storageService;
            this.callbackService = callbackService;
        }

        @Override
        public boolean consume(ConsumerRecords<String, String> consumerRecords) {
            final List<CallbackRedirect> redirects = StreamSupport.stream(consumerRecords.spliterator(), false)
                    .map(consumerRecord -> {

                        final CallbackRedirect callbackRedirect = JSON.parseObject(consumerRecord.value(),
                                CallbackRedirect.class);

                        callbackRedirect.setConsumerRecord(consumerRecord);

                        return callbackRedirect;
                    })
                    .toList();


            final List<CallbackRedirect> callbacks = new ArrayList<>(consumerRecords.count());
            final List<CallbackRedirect> storages = new ArrayList<>(consumerRecords.count());
            for (CallbackRedirect redirect : redirects) {
                final String storageBiz = redirect.getStorageBiz();
                if (StringUtils.isNotBlank(storageBiz)) {
                    storages.add(redirect);
                }

                if (redirect.isCb()) {
                    callbacks.add(redirect);
                }
            }

            storageService.store(storages);
            callbackService.callback(callbacks);
            return true;
        }
    }

}
