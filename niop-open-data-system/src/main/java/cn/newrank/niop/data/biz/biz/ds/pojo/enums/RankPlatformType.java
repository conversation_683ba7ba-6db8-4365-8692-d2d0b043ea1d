package cn.newrank.niop.data.biz.biz.ds.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * 平台类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum RankPlatformType implements BizEnum {
    DYR("dyr", "抖音榜单"),
    GZHR("gzhr", "公众号榜单"),
    SPHR("sphr", "视频号榜单"),
    WBR("wbr", "微博榜单"),
    KSR("ksr", "ks榜单"),
    BZR("bzr", "bz榜单"),
    ;

    final String code;
    final String description;

    RankPlatformType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static RankPlatformType getByCode(boolean expression, String code) {
        for (RankPlatformType platformType : values()) {
            if (platformType.getDbCode().equals(code)) {
                return platformType;
            }
        }
        if (expression) {
            throw createParamError("未知PlatformType类型-{}", code);
        }
        return null;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }
}
