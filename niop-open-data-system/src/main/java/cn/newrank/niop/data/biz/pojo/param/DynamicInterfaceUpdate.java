package cn.newrank.niop.data.biz.pojo.param;

import cn.newrank.niop.data.common.entity.DynamicInterface;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 9:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DynamicInterfaceUpdate extends DynamicInterfaceCreate {

    @NotBlank(message = "接口ID(interfaceId)不能为空")
    String interfaceId;


    @Override
    public DynamicInterface toDto() {
        final DynamicInterface dynamicInterface = super.toDto();

        dynamicInterface.setInterfaceId(interfaceId);

        return dynamicInterface;
    }
}
