package cn.newrank.niop.data.biz.dataclear.config;

import cn.newrank.niop.data.biz.dataclear.scheduler.DataCleanScheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;

import java.util.concurrent.ScheduledFuture;

@Configuration
public class DateCleanSchedulerConfig {
    @Autowired
    DataCleanScheduler dataCleanScheduler;

    @Bean
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(3); // 设置线程池大小
        scheduler.initialize();
        return scheduler;
    }

    @Bean
    public ScheduledFuture<?> scheduleMinuteTask(@Qualifier("threadPoolTaskScheduler") ThreadPoolTaskScheduler scheduler) {
        Runnable minuteTask = () -> {
            dataCleanScheduler.TaskDataCleanTaskExecute();
        };
        // 每秒执行一次，使用Cron表达式
        String cronExpression = "0/1 * * * * ?";
        return scheduler.schedule(minuteTask, new CronTrigger(cronExpression));
    }
}