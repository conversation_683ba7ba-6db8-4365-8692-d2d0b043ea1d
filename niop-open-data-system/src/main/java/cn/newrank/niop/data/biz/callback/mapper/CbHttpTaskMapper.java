package cn.newrank.niop.data.biz.callback.mapper;

import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/27 15:01
 */
@Mapper
public interface CbHttpTaskMapper {

    void insertBatch(@Param("tasks") List<CbHttpTask> tasks, @Param("partition") String partition);


    void updateBatch(@Param("tasks") List<CbHttpTask> tasks, @Param("partition") String partition);

    List<CbHttpTask> listRunning(@Param("cbId") String cbId,
                                 @Param("startTime") Timestamp startTime,
                                 @Param("taskCount") int taskCount,
                                 @Param("partition") String partition);

    void createTable(@Param("partition") String partition,
                     @Param("startDate") Date startDate,
                     @Param("endDate") Date endDate);

    void dropTable(String partition);

    List<String> queryTables();

    Date queryCleanEndPartition();

    Date queryStartPartition(@Param("cbId") String cbId);
}




