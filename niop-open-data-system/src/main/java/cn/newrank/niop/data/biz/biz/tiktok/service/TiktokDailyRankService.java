package cn.newrank.niop.data.biz.biz.tiktok.service;

import cn.newrank.niop.data.biz.biz.tiktok.mapper.TiktokDailyRankMapper;
import cn.newrank.niop.data.util.DateTimeUtil;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/31 10:31
 */
@Service
public class TiktokDailyRankService {
    private final TiktokDailyRankMapper tiktokDailyRankMapper;

    public TiktokDailyRankService(TiktokDailyRankMapper tiktokDailyRankMapper) {
        this.tiktokDailyRankMapper = tiktokDailyRankMapper;
    }

    public void calculate(LocalDate rankDate) {
        tiktokDailyRankMapper.startCalculate(DateTimeUtil.format(rankDate, "yyyyMM"),
                Date.valueOf(rankDate),
                Date.valueOf(rankDate.minusDays(1)),
                Date.valueOf(rankDate.plusDays(1)));
    }

    public void clear(LocalDate rankDate) {
        tiktokDailyRankMapper.clear(DateTimeUtil.format(rankDate, "yyyyMM"), Date.valueOf(rankDate));
    }
}
