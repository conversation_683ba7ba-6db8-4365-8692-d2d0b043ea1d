package cn.newrank.niop.data.biz.subscriber.pojo.vo;

import cn.newrank.niop.console.biz.project.pojo.dto.App;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SendStrategy;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.Tag;
import cn.newrank.niop.web.model.EnumVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/18 11:28
 */
@Data
public class SubscriberConfigVo {
    /**
     * 订阅者id
     */
    private String subscriberId;

    /**
     * 应用id
     */
    private List<App> apps;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 回调源id
     */
    private String cbId;
    /**
     * 回调源名称
     */
    private String cbName;

    /**
     * 源id
     */
    private String sourceId;
    /**
     * 源名称
     */
    private String sourceName;

    /**
     * 源类型
     */
    private EnumVo sourceType;

    /**
     * 是否启用参数(针对v1能力结果)
     */
    private Boolean enableParams;
    /**
     * 发送策略
     */
    private SendStrategy sendStrategy;

    /**
     * 标签
     */
    private List<Tag> tags;
    /**
     * 负责人
     */
    private List<String> maintainers;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public static SubscriberConfigVo fromDto(SubscriberConfig subscriberConfig) {
        final SubscriberConfigVo configVo = new SubscriberConfigVo();

        configVo.setApps(subscriberConfig.getApps());
        configVo.setCbId(subscriberConfig.getCbId());
        configVo.setCbName(subscriberConfig.getCbName());
        configVo.setGmtCreate(subscriberConfig.getGmtCreate());
        configVo.setGmtModified(subscriberConfig.getGmtModified());
        configVo.setMaintainers(subscriberConfig.getMaintainers());
        configVo.setSourceId(subscriberConfig.getSourceId());
        configVo.setSourceName(subscriberConfig.getSourceName());
        configVo.setSourceType(EnumVo.of(subscriberConfig.getSourceType()));
        configVo.setEnableParams(subscriberConfig.getEnableParams());
        configVo.setSubscriberId(subscriberConfig.getSubscriberId());
        configVo.setSendStrategy(subscriberConfig.getSendStrategy());
        configVo.setTags(subscriberConfig.getTags());

        return configVo;
    }
}
