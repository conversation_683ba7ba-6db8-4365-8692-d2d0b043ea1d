package cn.newrank.niop.data.biz.biz.dy.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName niop_data_biz_dy_buyin_room_source_data
 */
@Data
@Accessors(chain = true)
public class DyBuyinRoomSourceData implements Serializable {
    /**
     * 
     */
    private String gmtModified;

    /**
     * 
     */
    private String gmtCreate;

    /**
     * 
     */
    private String roomId;

    /**
     * 
     */
    private String partitionOffset;

    /**
     * 
     */
    private String sourceData;

    private static final long serialVersionUID = 1L;
}