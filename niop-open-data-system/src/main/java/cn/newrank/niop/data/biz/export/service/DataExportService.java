package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.biz.export.pojo.param.DataExportCreate;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportPageQuery;
import cn.newrank.niop.data.biz.export.pojo.po.DataExport;
import cn.newrank.niop.web.model.PageView;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;

public interface DataExportService {

    /**
     * 新建导数
     *
     * @param exportCreate 导数参数
     * @return 导数信息
     */
    DataExport create(DataExportCreate exportCreate);

    /**
     * 分页搜索导数信息
     *
     * @param pageQuery 搜索参数
     * @return 分页数据
     */
    PageView<DataExport> page(DataExportPageQuery pageQuery);

    /**
     * 获取导数模板文件
     *
     * @param exportId 导数id
     * @return 模板文件
     */
    ResponseEntity<FileSystemResource> getTemplateFile(String exportId);

}
