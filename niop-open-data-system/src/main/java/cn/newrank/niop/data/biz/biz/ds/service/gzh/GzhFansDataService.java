package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.newrank.niop.data.biz.biz.ds.pojo.dto.GzhAccountFansData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.Iterables;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.TEST_YOU_DU_MAIN_ES_INDEX_20241015;
import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.YOU_DU_MAIN_ES_INDEX_20241015;

/**
 * 大搜Es-公众号粉丝数据同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class GzhFansDataService implements StorageBizService<GzhAccountFansData> {

    private static final String GZH_DATA_TYPE = "gzh_fans_data";
    private final RestHighLevelClient storageEsClient;
    private final String index;

    public GzhFansDataService(RestHighLevelClient storageEsClient, SystemConfig systemConfig) {
        this.storageEsClient = storageEsClient;
        this.index = systemConfig.isProduct() ? YOU_DU_MAIN_ES_INDEX_20241015 : TEST_YOU_DU_MAIN_ES_INDEX_20241015;
    }


    @Override
    public void storeBatch(List<GzhAccountFansData> items) {
        List<GzhAccountFansData> noNullItems = Iterables.collectPart(items, Objects::nonNull);

        final BulkRequest bulkRequest = new BulkRequest(index);
        for (GzhAccountFansData item : noNullItems) {
            final UpdateRequest updateRequest = new UpdateRequest();
            updateRequest
                    .id(item.getIndexId())
                    .doc(EsCodec.serialize(item), XContentType.JSON);
            updateRequest.upsert(EsCodec.serialize(item), XContentType.JSON);

            bulkRequest.add(updateRequest);
        }

        if (bulkRequest.numberOfActions() == 0) {
            return;
        }

        try {
            storageEsClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("[GzhFansDataService] e: ", e);
        }
    }

    @Override
    public GzhAccountFansData castOf(JSONObject item) {
        final String dataType = item.getString("data_type");
        if (GZH_DATA_TYPE.equals(dataType)) {
            JSONObject detailObj = item.getJSONObject("json_details");
            final String platformType = detailObj.getString("platform_type");
            if (PlatformType.GZH.getDbCode().equals(platformType)) {
                return EsCodec.deserialize(detailObj.toJSONString(), GzhAccountFansData.class);
            }
        }
        return null;
    }
}
