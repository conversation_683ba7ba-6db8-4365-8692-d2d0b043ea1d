package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.component.callback.Callback;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.pojo.param.CallbackConnectTest;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/16 11:45
 */
public interface CallbackService {

    SubscriberConfigService getSubscriberConfigService();

    /**
     * 测试连接
     *
     * @param connectTest 连接
     * @return true 连接成功
     */
    boolean testConnect(CallbackConnectTest connectTest);

    /**
     * 获取回调
     *
     * @param cbId 回调id
     * @return 回调
     */
    Callback get(String cbId);

    /**
     * 回调
     *
     * @param cbId 回调id
     * @param data 回调数据
     * @return true 回调成功
     */
    boolean callback(String cbId, String data);

    /**
     * 回调
     *
     * @param callbacks 回调数据
     */
    @Deprecated
    void callback(List<CallbackRedirect> callbacks);

    /**
     * 回调
     *
     * @param cbId       回调id
     * @param redirects 回调数据
     */
    List<CallbackRetry> callback(String cbId, Collection<CallbackRedirect> redirects);

    /**
     * 重试队列
     *
     * @param retryList 回调重试
     */
    void sendRetryQueue(List<CallbackRetry> retryList);

}
