package cn.newrank.niop.data.biz.export.pojo.enums;

import cn.newrank.niop.web.model.BizEnum;

public enum DataExportFileType implements BizEnum {

    /**
     * 导数文件类型
     */
    TASK_PARAM("task_param", "任务参数文件", "export/param/"),
    TASK_RESULT("task_result", "任务结果文件", "export/result/"),
    ;

    private final String code;
    private final String description;
    private final String fileDir;

    DataExportFileType(String code, String description, String fileDir) {
        this.code = code;
        this.description = description;
        this.fileDir = fileDir;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

    public String getFileDir() {
        return fileDir;
    }

}
