package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.biz.export.pojo.dto.DataExportParamPreviewResult;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportSubtaskExecutionInfo;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportTaskExecutionDTO;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportTaskStatus;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportParamFilePreview;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskRunningControl;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskSubmit;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportTask;
import cn.newrank.niop.web.model.PageView;
import java.util.List;

public interface DataExportTaskService {

    /**
     * 任务参数文件预览
     *
     * @param filePreview 文件预览参数
     * @return 文件预览结果
     */
    DataExportParamPreviewResult taskFilePreview(DataExportParamFilePreview filePreview);

    /**
     * 提交导数任务
     *
     * @param taskSubmit 导数任务提交参数
     * @return 导数任务id
     */
    String submitExportTask(DataExportTaskSubmit taskSubmit);

    /**
     * 分页搜索导数任务信息
     *
     * @param pageQuery 搜索参数
     * @return 分页数据
     */
    PageView<DataExportTask> page(DataExportTaskPageQuery pageQuery);

    /**
     * 更新导数任务运行状态
     *
     * @param runningControl 运行状态控制
     * @return 导数任务当前运行状态
     */
    DataExportRunningStatus updateRunningStatus(DataExportTaskRunningControl runningControl);

    /**
     * 根据任务状态查询运行中的导数任务列表
     *
     * @param taskStatus 任务状态
     * @return 运行中的导数任务列表
     */
    List<DataExportTask> listRunningExportTasksByStatus(DataExportTaskStatus taskStatus);

    /**
     * 根据任务状态查询运行中的导数任务执行信息列表
     *
     * @param taskStatus 任务状态
     * @return 运行中的导数任务执行信息
     */
    List<DataExportTaskExecutionDTO> listRunningExportTaskDTOByStatus(DataExportTaskStatus taskStatus);

    /**
     * 根据导数任务id获取执行信息
     *
     * @param exportTaskId 导数任务id
     * @return 任务执行信息
     */
    DataExportTaskExecutionDTO getExportTaskExecutionInfo(String exportTaskId);

    /**
     * 更新导数任务状态为初始化完成
     *
     * @param id 主键id
     * @param paramTotalNum 参数总条数
     * @return 是否成功
     */
    boolean taskInitialized(Integer id, Integer paramTotalNum);

    /**
     * 暂停导数任务
     *
     * @param id 主键id
     */
    void taskPaused(Integer id);

    /**
     * 导数任务失败
     *
     * @param id 主键id
     */
    void taskFailed(Integer id);

    /**
     * 导数任务运行中
     *
     * @param id 主键id
     */
    void taskRunning(Integer id);

    /**
     * 导数任务结果文件准备好
     *
     * @param id 主键id
     */
    void taskResultFileReady(Integer id);

    /**
     * 导数任务完成
     *
     * @param subtaskExecutionInfo 子任务执行信息
     */
    void taskFinished(DataExportSubtaskExecutionInfo subtaskExecutionInfo);

    /**
     * 导数任务id
     *
     * @param exportTaskId 导数任务id
     * @return 文件结果列表
     */
    List<DataExportResultFile> listResultFiles(String exportTaskId);

}
