package cn.newrank.niop.data.biz.export.controller;

import cn.newrank.niop.data.biz.export.pojo.enums.DataExportFileType;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportRunningStatus;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportParamFilePreview;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskPageQuery;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskRunningControl;
import cn.newrank.niop.data.biz.export.pojo.param.DataExportTaskSubmit;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportFileSignatureVo;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportParamPreviewResultVo;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportResultFileVo;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportTaskRunningStatusVo;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportTaskSubmitVo;
import cn.newrank.niop.data.biz.export.pojo.vo.DataExportTaskVo;
import cn.newrank.niop.data.biz.export.service.DataExportTaskService;
import cn.newrank.niop.data.biz.oss.service.OssService;
import cn.newrank.niop.web.model.PageView;
import jakarta.validation.constraints.NotBlank;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/export-task")
public class DataExportTaskController {

    private final OssService ossService;

    private final DataExportTaskService dataExportTaskService;

    public DataExportTaskController(OssService ossService,
                                    DataExportTaskService dataExportTaskService) {
        this.ossService = ossService;
        this.dataExportTaskService = dataExportTaskService;
    }

    /**
     * 获取参数文件提交签名
     *
     * @return 签名信息
     */
    @GetMapping("/param-file/signature/get")
    public DataExportFileSignatureVo getParamSubmitSignature() {
        return DataExportFileSignatureVo.buildBy(ossService.getSignature(DataExportFileType.TASK_PARAM.getFileDir(), Duration.ofMinutes(10)));
    }

    /**
     * 预览参数文件
     *
     * @param filePreview 预览参数
     * @return 预览文件数据
     */
    @PostMapping("/param-file/preview")
    public DataExportParamPreviewResultVo previewParamFile(@Validated @RequestBody DataExportParamFilePreview filePreview) {
        return DataExportParamPreviewResultVo.buildBy(dataExportTaskService.taskFilePreview(filePreview));
    }

    /**
     * 提交导数任务
     *
     * @param taskSubmit 导数任务参数
     * @return 提交结果
     */
    @PostMapping("/submit")
    public DataExportTaskSubmitVo submitTask(@Validated @RequestBody DataExportTaskSubmit taskSubmit) {
        return DataExportTaskSubmitVo.of(dataExportTaskService.submitExportTask(taskSubmit));
    }

    /**
     * 分页查询导数任务
     *
     * @param query 查询参数
     * @return 分页导数任务数据
     */
    @GetMapping("/page")
    public PageView<DataExportTaskVo> page(@Validated DataExportTaskPageQuery query) {
        return dataExportTaskService.page(query).convert(DataExportTaskVo::buildBy);
    }

    /**
     * 导数任务更新运行状态
     *
     * @param runningControl 运行状态控制参数
     * @return 当前运行状态
     */
    @PostMapping("/running-status/update")
    public DataExportTaskRunningStatusVo exportTaskRunningStatusUpdate(@Validated @RequestBody DataExportTaskRunningControl runningControl) {
        DataExportRunningStatus currentStatus = dataExportTaskService.updateRunningStatus(runningControl);
        return DataExportTaskRunningStatusVo.buildBy(runningControl.getExportTaskId(), currentStatus);
    }

    /**
     * 获取结果文件列表
     *
     * @param exportTaskId 导数任务id
     * @return 结果文件列表
     */
    @GetMapping("/result/files")
    public List<DataExportResultFileVo> listResultFiles(@NotBlank(message = "导数任务id不能为空") String exportTaskId) {
        List<DataExportResultFile> resultFiles = dataExportTaskService.listResultFiles(exportTaskId);
        return resultFiles.stream().map(DataExportResultFileVo::buildBy).filter(Objects::nonNull).toList();
    }

}
