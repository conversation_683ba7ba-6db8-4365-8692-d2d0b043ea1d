package cn.newrank.niop.data.biz.biz.txhx.pojo;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 腾讯互选-视频号-样本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/3 15:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TxhxSphSample extends StorageEntity {
    /**
     * 达人id
     */
    private String appId;

    /**
     *
     */
    private String viewId;

    /**
     * 近 10 条视频的平均互动量
     */
    private String avgInteractionCount;

    /**
     * 近 10 条视频的平均点赞量
     */
    private String avgLikeCount;

    /**
     * 近 10 条视频的平均播放量
     */
    private String avgReadCount;

    /**
     * 预期cpm
     */
    private String expectedCpm;

    /**
     * 互动率
     */
    private String interactionRate;

    /**
     * 60s以上视频报价
     */
    private String longVideoPrice;

    /**
     * 播放中位数
     */
    private String medianReadCount;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 完播率
     */
    private String playFinishRate;

    /**
     * 近期报价变价
     */
    private String recentChangedPrice;

    /**
     * 1-60s视频报价
     */
    private String shortVideoPrice;

    @Override
    public String identifier() {
        return appId;
    }
}