package cn.newrank.niop.data.biz.callback.service;

import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.biz.callback.pojo.CdTaskConfig;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.consumer.CallbackRetry;
import cn.newrank.niop.data.biz.service.CallbackService;
import cn.newrank.niop.data.biz.subscriber.pojo.enums.SubSourceType;
import cn.newrank.niop.data.biz.subscriber.service.SubscriberConfigService;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.MySQLFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class CommonRedirectService {

    public static final String RESULT_QUERY = """
            SELECT * FROM `niop_ability_result`
            WHERE `task_id` IN
                <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            """;

    public static final String PARAMS_QUERY = """
            SELECT
                task_id,
                params
            FROM `ability_task_info`
            WHERE `task_id` IN
                <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            """;

    protected final Datasource datasource;
    protected final CallbackService callbackService;
    protected final SubscriberConfigService subscriberConfigService;

    public CommonRedirectService(DsConfigManager dsConfigManager,
                                 CallbackService callbackService,
                                 SubscriberConfigService subscriberConfigService) {
        this.datasource = MySQLFactory.DEFAULT.create(dsConfigManager.chooseNiopLmConfig());
        this.callbackService = callbackService;
        this.subscriberConfigService = subscriberConfigService;
    }

    public CdTaskConfig buildCbIdTaskIdMap(Map<String, CallbackRedirect> redirectMap) {
        CdTaskConfig config = CdTaskConfig.of();

        // 一个能力下的一个应用只能配置原始结果、解析两个结果回调
        for (CallbackRedirect redirect : redirectMap.values()) {
            final String taskId = redirect.getSourceKey();
            final String abilityId = redirect.getSourceId();
            final String appId = redirect.getAppId();

            // 回调结果类型 + abilityId批量 回调结果类型 + abilityId + appId归类 内存缓存 暂不考虑批量
            // 新版本
            subscriberConfigService.searchCbIds(SubSourceType.ABILITY_PARSED_RESULT, abilityId, appId)
                    .forEach(cbId -> config.addCbWithParsed(cbId, taskId));
            subscriberConfigService.searchCbIds(SubSourceType.ABILITY_RAW_RESULT, abilityId, appId)
                    .forEach(cbId -> config.addCbWithRaw(cbId, taskId));

            // 旧版本
            subscriberConfigService.searchCbIds(SubSourceType.ABILITY_RAW_RESULT_OLD, abilityId, appId)
                    .forEach(cbId -> config.addCbOldWithRaw(cbId, taskId));
            subscriberConfigService.searchCbIds(SubSourceType.ABILITY_PARSED_RESULT_OLD, abilityId, appId)
                    .forEach(cbId -> config.addCbOldWithParsed(cbId, taskId));
            // 是否启用参数
            subscriberConfigService.searchCdEnableParam(SubSourceType.ABILITY_RAW_RESULT_OLD, abilityId, appId)
                    .forEach(cbEnableParams -> {
                        config.setCdOldEnableParamWithRaw(cbEnableParams.getCbId(), cbEnableParams.getEnableParams());
                    });
            subscriberConfigService.searchCdEnableParam(SubSourceType.ABILITY_PARSED_RESULT_OLD, abilityId, appId)
                    .forEach(cbEnableParams -> {
                        config.setCdOldEnableParamWithParsed(cbEnableParams.getCbId(), cbEnableParams.getEnableParams());
                    });
        }

        return config;
    }


    public Map<String, JSONObject> getResults(List<String> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return Map.of();
        }

        final QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(RESULT_QUERY)
                .addParam("taskIds", taskIds);
        final JSONArray rs = datasource.query(queryBuilder).data();
        if (rs == null || rs.isEmpty()) {
            return Map.of();
        }

        return rs.toJavaList(JSONObject.class)
                .stream()
                .collect(Collectors.toMap(item -> item.getString("task_id"), Function.identity()));
    }

    public Map<String, JSONObject> getParams(List<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return Map.of();
        }

        QueryBuilder queryBuilder = datasource.newQueryBuilder()
                .template(PARAMS_QUERY)
                .addParam("taskIds", taskIds);
        JSONArray params = datasource.query(queryBuilder).data();
        if (CollUtil.isEmpty(params)) {
            return Map.of();
        }

        return params.toJavaList(JSONObject.class)
                .stream()
                .collect(Collectors.toMap(item -> item.getString("task_id"), Function.identity()));

    }

    public void callback(String cbId, List<CallbackRedirect> retries,
                         Map<String /*taskId*/, CallbackRedirect> redirectMap,
                         List<CallbackRetry> retryList) {
        // 回调业务异常重试
        List<CallbackRetry> retryListWithRaw = callbackService.callback(cbId, new ArrayList<>(retries));
        if (CollUtil.isEmpty(retryListWithRaw)) {
            return;
        }
        for (CallbackRetry retry : retryListWithRaw) {
            // 这里的retry是处理过的 需要拿原始的redirect初始化retry
            CallbackRedirect redirect = redirectMap.get(retry.getSourceKey());
            CallbackRetry copyRetry = CallbackRetry.of(redirect, retry.getErrorInfo());
            retryList.add(copyRetry);
        }
    }

}
