package cn.newrank.niop.data.biz.component.sls;


import cn.hutool.core.collection.CollUtil;
import cn.newrank.niop.data.config.property.AliyunProperties;
import com.aliyun.openservices.aliyun.log.producer.LogProducer;
import com.aliyun.openservices.aliyun.log.producer.ProducerConfig;
import com.aliyun.openservices.aliyun.log.producer.ProjectConfig;
import com.aliyun.openservices.aliyun.log.producer.errors.ProducerException;
import com.aliyun.openservices.log.common.LogItem;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.Closeable;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:52
 */
@Component
@Log4j2
public class SlsProducer implements Closeable {


    final LogProducer logProducer;
    private final AliyunProperties aliyunProperties;


    public SlsProducer(AliyunProperties aliyunProperties) {
        this.aliyunProperties = aliyunProperties;
        final ProducerConfig producerConfig = new ProducerConfig();
        producerConfig.setIoThreadCount(32);
        producerConfig.setBatchCountThreshold(50);
        producerConfig.setMaxBlockMs(1000);
        this.logProducer = new LogProducer(producerConfig);

        final ProjectConfig projectConfig = new ProjectConfig(AliyunProperties.SLS_PROJECT
                , AliyunProperties.getSlsHost()
                , aliyunProperties.getAk()
                , aliyunProperties.getSk());

        logProducer.putProjectConfig(projectConfig);
    }


    public void send(LogItem log) {
        if (log == null) {
            return;
        }

        doSendBatch(Collections.singletonList(log));
    }

    public void sendBatch(List<LogItem> logs) {
        if (logs == null || logs.isEmpty()) {
            return;
        }

        CollUtil.split(logs, 10).forEach(this::doSendBatch);
    }

    private void doSendBatch(List<LogItem> logs) {
        try {
            logProducer.send(AliyunProperties.SLS_PROJECT, aliyunProperties.getLogstore(), logs, result -> {
                if (!result.isSuccessful()) {
                    log.error("日志上传失败,code: {}, msg: {}", result.getErrorCode(),
                            result.getErrorMessage());
                }
            });
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("上传日志中断, e: ", e);
        } catch (ProducerException e) {
            log.warn("上传日志异常, e:", e);
        }
    }


    @Override
    public void close() throws IOException {
        try {
            logProducer.close();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("关闭日志上传失败, e: ", e);
        } catch (ProducerException e) {
            log.warn("关闭日志上传异常, e:", e);
        }
    }
}
