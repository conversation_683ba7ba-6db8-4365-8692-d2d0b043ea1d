package cn.newrank.niop.data.biz.controller;

import cn.newrank.niop.data.biz.pojo.dto.Dictionary;
import cn.newrank.niop.data.biz.pojo.param.ColumnAliasesUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryRemarkUpdate;
import cn.newrank.niop.data.biz.pojo.param.DictionaryTagUpdate;
import cn.newrank.niop.data.biz.service.DictionaryService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/8 11:17
 */
@Validated
@RestController
@RequestMapping("dictionary")
public class DictionaryController {

    private final DictionaryService dictionaryService;

    public DictionaryController(DictionaryService dictionaryService) {
        this.dictionaryService = dictionaryService;
    }


    /**
     * 开始同步数据源下的字典信息
     *
     * @param dcId 数据源ID
     * @return 是否成功
     */
    @GetMapping("start-sync-ds")
    public boolean startSyncDs(String dcId) {
        return dictionaryService.startDsSync(dcId);
    }


    /**
     * 编辑字典的标签
     *
     * @return 是否成功
     */
    @PostMapping("tags/update")
    public boolean editTags(@Valid @RequestBody DictionaryTagUpdate tagUpdate) {
        return dictionaryService.updateTags(tagUpdate);
    }

    /**
     * 编辑字典的备注
     *
     * @return 是否成功
     */
    @PostMapping("remark/update")
    public boolean editRemark(@Valid @RequestBody DictionaryRemarkUpdate remarkUpdate) {
        return dictionaryService.updateRemark(remarkUpdate);
    }

    /**
     * 编辑字典的列别名
     *
     * @return 是否成功
     */
    @PostMapping("column-aliases/update")
    public boolean startSync(@Valid @RequestBody ColumnAliasesUpdate columnAliasesUpdate) {
        return dictionaryService.updateColumnAliases(columnAliasesUpdate);
    }

    /**
     * 获取字典信息
     *
     * @param dicId 字典ID
     * @return 字典信息
     */
    @GetMapping("get")
    public Dictionary get(@NotBlank(message = "字典ID(dicId)不能为空") String dicId) {
        return dictionaryService.get(dicId);
    }

    /**
     * 获取字典信息
     *
     * @param dcId       数据源ID
     * @param collection 集合名
     * @return 字典信息
     */
    @GetMapping("get-by-collection")
    public Dictionary getByCollection(String dcId, String collection) {
        return dictionaryService.get(dcId, collection);
    }

    /**
     * 搜索字典信息
     */
    @GetMapping("search")
    public List<Dictionary> search(String remark) {
        return dictionaryService.search(remark);
    }
}
