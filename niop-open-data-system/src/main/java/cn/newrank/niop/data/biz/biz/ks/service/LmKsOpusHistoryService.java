package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.biz.biz.ks.mapper.LmKsOpusMapper;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.StorageBizService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基础数据存储
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/24 14:50
 */
@Service
@Log4j2
public class LmKsOpusHistoryService implements StorageBizService<LmKsOpus> {

    private final LmKsOpusMapper lmKsOpusMapper;

    public LmKsOpusHistoryService(LmKsOpusMapper lmKsOpusMapper) {
        this.lmKsOpusMapper = lmKsOpusMapper;
    }


    @Override
    public void storeBatch(List<LmKsOpus> items) {
        if (items.isEmpty()) {
            return;
        }
        List<String> ids = items.stream().map(LmKsOpus::getPhotoId).toList();
        List<String> list = lmKsOpusMapper.listExistsPhotoId(ids);
        Set<String> photoIds = new HashSet<>(list);
        //过滤 更新
        List<LmKsOpus> needUpdate = items.stream()
                .filter(opus -> photoIds.contains(opus.getPhotoId()))
                .toList();
        if (needUpdate.isEmpty()) {
            return;
        }
        lmKsOpusMapper.storeHistory(needUpdate);
    }


    @Override
    public LmKsOpus castOf(JSONObject item) {
        LmKsOpus ksOpus = new LmKsOpus();
        ksOpus.setPhotoId(item.getString("aweme_id"));
        ksOpus.setLdGmtCreate(item.getString("acq_time"));
        return ksOpus;
    }
}
