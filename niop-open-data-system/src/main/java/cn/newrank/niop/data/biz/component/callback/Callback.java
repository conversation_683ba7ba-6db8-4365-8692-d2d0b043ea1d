package cn.newrank.niop.data.biz.component.callback;

import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.common.ConfigProperties;

import java.io.Closeable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/15 19:55
 */
public interface Callback extends Closeable {
    /**
     * 获取回调配置信息
     *
     * @return 配置
     */
    ConfigProperties getConfig();

    /**
     * 获取唯一标识
     *
     * @return 唯一标识
     */
    String getUUID();
    /**
     * 是否可用
     *
     * @return 是否可用
     */
    boolean isActive();

    /**
     * 回调数据
     *
     * @param data 数据
     * @return 是否成功
     */
    boolean callback(String data);

    /**
     * 回调转发数据
     *
     * @param callbackRedirect 转发数据
     * @return 是否成功
     */
    boolean callback(CallbackRedirect callbackRedirect);

    /**
     * 回调转发数据
     *
     * @param callbackRedirects 转发数据
     * @return 是否成功
     */
    boolean callback(Collection<CallbackRedirect> callbackRedirects);

    /**
     * 回调转发数据并返回错误的回调
     * 实现该方法需要确保异常回调被处理和返回
     *
     * @param callbackRedirects 转发数据
     * @return 是否成功
     */
    List<CallbackRedirect> callbackReturnError(Collection<CallbackRedirect> callbackRedirects);

    /**
     * 关闭
     */
    @Override
    void close();
}

