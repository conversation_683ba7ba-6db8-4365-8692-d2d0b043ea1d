package cn.newrank.niop.data.biz.pojo.po;

import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.enums.DsType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:42
 */
@Data
public class DatasourceConfigPo {
    private Integer id;

    /**
     * 数据源配置id
     */
    private String dcId;

    /**
     * 数据源类型
     */
    private DsType type;

    /**
     * 数据源配置名称
     */
    private String name;

    /**
     * 数据源配置
     */
    private String config;

    /**
     * 任务并发数
     */
    private Integer concurrencyPermit;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public DsConfig toDto() {
        final DsConfig dsConfig = new DsConfig();
        dsConfig.setDcId(this.dcId);
        dsConfig.setName(this.name);
        dsConfig.setType(type);
        dsConfig.setConfig(ConfigProperties.of(this.config));
        dsConfig.setConcurrencyPermit(this.concurrencyPermit);
        dsConfig.setGmtCreate(this.gmtCreate);
        dsConfig.setGmtModified(this.gmtModified);

        return dsConfig;
    }
}