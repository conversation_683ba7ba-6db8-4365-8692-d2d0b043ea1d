package cn.newrank.niop.data.biz.export.handler;

import cn.newrank.niop.data.biz.export.pojo.dto.DataExportConsumeTaskResult;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public interface DataExportResultHandler {

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    DataExportType getDataSourceType();

    /**
     * 根据导数任务id获取查询接口
     *
     * @param exportTaskId 任务id
     * @return 查询接口
     */
    DataExportResultFetcher currentResultFetcher(String exportTaskId);

    /**
     * 每次获取子任务数
     *
     * @return 条数
     */
    int fetchBatchSize();

    /**
     * 循环消费结果数据
     *
     * @param exportTaskId 导数任务id
     * @param resultsConsumer 结果消费者
     * @return 消费结果
     */
    DataExportConsumeTaskResult forEachConsume(String exportTaskId, Consumer<ExportResult> resultsConsumer);

    /**
     * 获取一个样本数据
     *
     * @param exportTaskId 导数任务id
     * @return 样本数据
     */
    ExportResult getSample(String exportTaskId);

}
