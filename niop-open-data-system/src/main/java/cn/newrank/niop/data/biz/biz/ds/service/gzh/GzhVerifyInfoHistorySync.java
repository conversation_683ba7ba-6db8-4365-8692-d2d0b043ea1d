package cn.newrank.niop.data.biz.biz.ds.service.gzh;

import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.biz.biz.ds.service.common.temp.VerifyInfoHistorySync;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import com.alibaba.fastjson2.JSONObject;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static cn.hutool.core.text.CharSequenceUtil.EMPTY;
import static cn.hutool.core.text.CharSequenceUtil.nullToDefault;


/**
 * [gzh认证信息]同步历史数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class GzhVerifyInfoHistorySync extends VerifyInfoHistorySync {

    static final String START_QUERY = """
            GET weixin_user/_search?scroll=15m
            {
              "_source": [
                  "id",
                  "certifiedText",
                  "verifyStatus",
                  "verifyType"
              ],
              "size": 1500,
              "query": {
                "bool": {
                  "should": [
                    { "exists": { "field": "verifyStatus" } },
                    { "exists": { "field": "verifyType" } },
                    { "exists": { "field": "certifiedText" } }
                  ],
                  "minimum_should_match": 1
                }
              }
            }
            """;

    protected GzhVerifyInfoHistorySync(RedissonClient redissonClient,
                                       CommonEsService commonEsService,
                                       DsConfigManager dsConfigManager) {
        super(PlatformType.GZH.getDbCode(), dsConfigManager.chooseGzhEsConfig(), redissonClient, commonEsService);
    }

    @Override
    protected String startQuery() {
        return START_QUERY;
    }

    @Override
    protected EsEntity castOf(JSONObject sourceObj) {
        if (Objects.isNull(sourceObj)) {
            return null;
        }

        final VerifyInfoUpdate update = new VerifyInfoUpdate();
        update.setIndexId(PlatformType.GZH.getDbCode() + "_" + sourceObj.get("id"));
        update.setVerifyInfo(nullToDefault(sourceObj.getString("certifiedText"), EMPTY));
        update.setVerifyTypeV1(nullToDefault(sourceObj.getString("verifyStatus"), EMPTY));
        update.setVerifyTypeV2(nullToDefault(sourceObj.getString("verifyType"), EMPTY));

        return update;
    }
}
