package cn.newrank.niop.data.biz.dataclear.mapper;

import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.param.CleanRuleUpadteQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanFuzzyQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanPageQuery;
import cn.newrank.niop.data.biz.dataclear.pojo.param.DataCleanRuleCreateQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface DataClearMapper {

    void insert(DataCleanRuleCreateQuery DataCleanRuleCreate);

    int delete(String ruleId);

    DataCleanRuleCreate get(String ruleId);

    int update(CleanRuleUpadteQuery cleanRuleUpadteQuery);

    List<DataCleanRuleCreate> fuzzyQuery(@Param("fuzzyQuery") DataCleanFuzzyQuery fuzzyQuery);

    Page<DataCleanRuleCreate> page(@Param("pageQuery") DataCleanPageQuery pageQuery,
                                   Page<DataCleanRuleCreate> mybatisPlusPage);

    List<DataCleanRuleCreate> list(@Param("cursor") Integer cursor);

//    List<String> listCbIds();
}




