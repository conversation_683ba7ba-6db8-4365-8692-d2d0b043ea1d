package cn.newrank.niop.data.biz.biz.dy.pojo;

import lombok.Data;

import java.sql.Timestamp;


/**
 * 抖音商单数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DyOpus {
    /**
     * 作品id
     */
    private String awemeId;

    /**
     * 作品标题
     */
    private String title;

    /**
     * 发布时间
     */
    private Timestamp publishTime;

    /**
     * 1：指派 4：投稿 133：招募 134：星广联投 0：其他(包括空)
     */
    private Integer eType;

    /**
     * 作品合作品牌ID
     */
    private String brandId;

    /**
     * 作品合作品牌名称
     */
    private String brandName;

    /**
     * 达人uID
     */
    private String bloggerUid;

    /**
     * 作品更新状态-源（0：待更新，1：已完成）
     */
    private Integer sUpdateStatus;

    /**
     * 作品更新时间-源
     */
    private Timestamp sUpdateTime;

    /**
     * 播放数
     */
    private Integer playNum;

    /**
     * 点赞数
     */
    private Integer likeNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 分享数
     */
    private Integer shareNum;

    /**
     * 收藏数
     */
    private Integer collectNum;

    private Timestamp createTime;
    private Timestamp updateTime;

}