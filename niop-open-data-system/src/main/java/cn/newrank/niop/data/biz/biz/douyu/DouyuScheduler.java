package cn.newrank.niop.data.biz.biz.douyu;

import cn.newrank.niop.data.biz.biz.douyu.pojo.DouyuLive;
import cn.newrank.niop.data.biz.biz.douyu.service.DouyuLiveService;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.service.StorageService;
import cn.newrank.niop.data.common.lock.DistributeLock;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 18:56
 */
@Log4j2
@Component
public class DouyuScheduler {

    private final DouyuLiveService douyuLiveService;
    private final StorageService storageService;

    public DouyuScheduler(DouyuLiveService douyuLiveService, StorageService storageService) {
        this.douyuLiveService = douyuLiveService;
        this.storageService = storageService;
    }


    @DistributeLock(abortException = false)
    @Scheduled(cron = "0 0/5 * * * ?")
    public void run() {
        String failedSampleId = null;
        while (true) {
            final List<DouyuLive> lives = douyuLiveService.listCalculateLives();
            if (lives.isEmpty()) {
                return;
            }

            for (DouyuLive live : lives) {
                if (StringUtils.equals(failedSampleId, live.getSampleId())) {
                   return;
                }

                try {
                    calculate(live);
                } catch (Exception e) {
                    log.error("计算失败，e: ", e);
                    failedSampleId = live.getSampleId();
                }
            }
        }
    }

    private void calculate(DouyuLive live) {
        final LinkedList<StorageHistory.History> histories =
                storageService.getHistory(StorageBiz.DOUYU_LIVE, live.getSampleId()).getHistories();

        final int total = histories.size() + 1;

        final Timestamp endTime = live.getEndTime();
        final Timestamp startTime = live.getStartTime();
        if (endTime != null && startTime != null) {
            final Duration between = Duration.between(startTime.toLocalDateTime()
                    , endTime.toLocalDateTime());

            // 直播
            live.setDuration(between.toSeconds());
        }


        long maxHot = live.getHot() == null ? 0L : live.getHot();
        long hotSum = maxHot;
        for (StorageHistory.History history : histories) {
            final DouyuLive douyuLive = history.getData().to(DouyuLive.class);

            final Long hot = douyuLive.getHot();
            if (hot == null) {
                continue;
            }

            if (hot > maxHot) {
                maxHot = hot;
            }

            hotSum += hot;
        }

        live.setMaxHot(maxHot);
        live.setAvgHot(new BigDecimal(hotSum).divide(new BigDecimal(total), 2, RoundingMode.HALF_UP));

        douyuLiveService.updateCalculateProps(live);
    }
}
