package cn.newrank.niop.data.biz.export.factory;

import cn.newrank.niop.data.biz.export.handler.DataExportResultFetcher;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportResultSource;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.util.Pair;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DataExportResultFetcherFactory implements ApplicationContextAware {

    private final Map<Pair<DataExportType, DataExportResultSource>, DataExportResultFetcher> fetcherMap = new HashMap<>(8);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        final Map<String, DataExportResultFetcher> resultFetcherMap = applicationContext.getBeansOfType(DataExportResultFetcher.class);

        for (Map.Entry<String, DataExportResultFetcher> fetcher : resultFetcherMap.entrySet()) {
            DataExportResultFetcher value = fetcher.getValue();
            final Pair<DataExportType, DataExportResultSource> keyPair = new Pair<>(value.getDataSourceType(), value.getResultSource());
            this.fetcherMap.put(keyPair, value);
        }
    }

    public DataExportResultFetcher getResultFetcher(Pair<DataExportType, DataExportResultSource> key) {
        return Optional.ofNullable(this.fetcherMap.get(key))
            .orElseThrow(() -> new IllegalArgumentException("未实现的结果查询类型: " + key.getObject1().getDescription() + ", " + key.getObject2().getDescription()));
    }

}
