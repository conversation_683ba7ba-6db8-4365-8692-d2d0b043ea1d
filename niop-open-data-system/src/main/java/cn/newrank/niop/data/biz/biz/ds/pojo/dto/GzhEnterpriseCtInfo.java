package cn.newrank.niop.data.biz.biz.ds.pojo.dto;

import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公众号企业认证信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GzhEnterpriseCtInfo extends StorageEntity {

    private String indexId;
    private String anaTime;
    private String enterpriseVerifyInfo;
    private String verifyInfo;

    @Override
    public String identifier() {
        return indexId;
    }
}
