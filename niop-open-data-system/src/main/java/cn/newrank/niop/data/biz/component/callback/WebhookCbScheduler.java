package cn.newrank.niop.data.biz.component.callback;

import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.biz.callback.pojo.CbData;
import cn.newrank.niop.data.biz.callback.pojo.CbHttpTask;
import cn.newrank.niop.data.biz.callback.pojo.CbStatus;
import cn.newrank.niop.data.biz.callback.service.CbDataService;
import cn.newrank.niop.data.biz.callback.service.CbHttpTaskService;
import cn.newrank.niop.data.biz.component.trace.Tracers;
import cn.newrank.niop.data.common.ConfigKey;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.DateTimeUtil;
import cn.newrank.niop.data.util.TriggerUtil;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BooleanSupplier;
import java.util.function.Function;

import static cn.newrank.niop.data.util.Iterables.toMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/9/29 10:10
 */
@Log4j2
public class WebhookCbScheduler {

    private static final ThreadPoolExecutor IO_EXECUTOR;
    private static final ThreadPoolExecutor SCHEDULE_EXECUTOR;
    /**
     * 默认重试间隔
     */
    private static final int DEFAULT_RETRY_SECONDS = 30;
    private static final int MAX_RETRY_TIMES = 16;
    private static final int START_DELAY_TIMES = 8;

    static {
        IO_EXECUTOR = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                400,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new NamedThreadFactory("cb-executor", true),
                (r, executor) -> {
                    log.error("回调IO线程超过400,当前任务切换至调度线程执行, 及时关注");
                    if (!executor.isShutdown()) {
                        r.run();
                    }
                });
        SCHEDULE_EXECUTOR = new ThreadPoolExecutor(
                0,
                100,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new NamedThreadFactory("cb-scheduler", true),
                (r, executor) -> log.error("调度线程超过100, 调度被抛弃"));
    }

    private final InvokeCoordinator coordinator;
    private final CbHttpTaskService cbHttpTaskService;
    private final CbDataService cbDataService;
    private final String cbId;
    private final CallbackRequest callbackRequest;


    public WebhookCbScheduler(ConfigProperties configProperties,
                              CbHttpTaskService cbHttpTaskService,
                              CbDataService cbDataService,
                              CallbackRequest callbackRequest) {
        int refreshPermits = configProperties.getInt(ConfigKey.REFRESH_PERMITS);
        int refreshSeconds = configProperties.getInt(ConfigKey.REFRESH_SECONDS);
        this.cbId = configProperties.getString(ConfigKey.ID);
        this.coordinator = new InvokeCoordinator(refreshPermits, refreshSeconds, cbId, cbHttpTaskService::queryStartPartition);
        this.callbackRequest = callbackRequest;
        this.cbHttpTaskService = cbHttpTaskService;
        this.cbDataService = cbDataService;
    }


    private static @NotNull LocalDateTime nextCallbackTime(int retryTimes) {
        return TriggerUtil.nextExpBackTime(retryTimes, START_DELAY_TIMES, DEFAULT_RETRY_SECONDS);
    }

    private void execute() throws InterruptedException {
        final Args args = coordinator.args();
        final List<CbHttpTask> tasks = cbHttpTaskService.listRunning(cbId, args.queryTime(),
                args.pullCount(), args.tablePartition());
        if (tasks.isEmpty()) {
            coordinator.emptyInvoke();
            return;
        }

        final List<String> cIds = tasks.stream().map(CbHttpTask::getDataCid).toList();
        final Map<String, CbData> dataMap = toMap(cbDataService.list(cIds), CbData::getCid, Function.identity());
        // 任务量级
        final CountDownLatch countDownLatch = new CountDownLatch(tasks.size());
        for (CbHttpTask task : tasks) {
            coordinator.invoke(() -> {
                final Span span = Tracers.getWebhookCbSpan(task, cbId, callbackRequest.getUrl());
                try {
                    task.setRetryTimes(task.getRetryTimes() + 1);

                    // 执行
                    callbackRequest.request(dataMap.get(task.getDataCid()).getData());

                    task.setStatus(CbStatus.SUCCESS);
                    task.setErrorMsg("");

                    span.setStatus(StatusCode.OK);

                    return true;
                } catch (Exception e) {
                    task.setErrorMsg(e.getMessage());
                    if (task.getRetryTimes() >= MAX_RETRY_TIMES) {
                        task.setStatus(CbStatus.ERROR);
                    } else {
                        task.setNextCallbackTime(Timestamp.valueOf(nextCallbackTime(task.getRetryTimes())));
                    }
                    span.recordException(e)
                            .setStatus(StatusCode.ERROR);

                    return false;
                } finally {
                    task.setGmtModified(Timestamp.valueOf(LocalDateTime.now()));
                    countDownLatch.countDown();
                    span.end();
                }
            });
        }

        countDownLatch.await();

        cbHttpTaskService.update(tasks, args.tablePartition());
    }


    public void start() {
        SCHEDULE_EXECUTOR.execute(() -> {
            if (coordinator.start()) {
                log.info("开始执行回调(ID: {})调度任务, startPartition: {}...", cbId, coordinator.getTablePartition());
                while (coordinator.next()) {
                    try {
                        execute();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("调度任务执行异常中断", e);
                    } catch (Exception e) {
                        log.error("调度任务执行异常", e);
                        ThreadUtil.sleep(5000);
                    }
                }

                log.info("结束执行回调(ID: {})调度任务...", cbId);
            }
        });
    }

    public void stop() {
        coordinator.stop();
    }

    public record Args(Timestamp queryTime, int pullCount, String tablePartition) {
    }

    /**
     * 调度器 start → next → stop
     */
    public static class InvokeCoordinator {

        /**
         * 调度状态
         */
        private final AtomicBoolean running = new AtomicBoolean(false);
        private final Queue<InvokeMessage> invokeMessages;
        private final int qps;
        private final String cbId;
        private final Function<String, LocalDate> startPartitionQuery;
        /**
         * 调度任务数量
         */
        int taskPullCount;
        int lastThreads;
        /**
         * 空闲次数
         */
        private int idleTimes;
        /**
         * 线程信号量
         */
        private Semaphore threadPermits;
        /**
         * 调度时间
         */
        private LocalDateTime invokeTime;
        /**
         * 上次回调日期
         */
        private LocalDate lastCallbackDate;

        public InvokeCoordinator(int refreshPermits, int refreshSeconds, String cbId, Function<String, LocalDate> startPartitionQuery) {
            this.cbId = cbId;
            this.qps = sub(refreshPermits, refreshSeconds);
            this.invokeMessages = new ArrayBlockingQueue<>(100);
            this.startPartitionQuery = startPartitionQuery;
        }

        private static int sub(int n, int m) {
            if (m == 0) {
                return 1;
            }
            if (n % m == 0) {
                return n / m;
            }
            return n / m + 1;
        }

        public Args args() {
            return new Args(Timestamp.valueOf(invokeTime), taskPullCount, getTablePartition());
        }

        private String getTablePartition() {
            return DateTimeUtil.dayOfPartition(lastCallbackDate);
        }

        /**
         * 开始调度
         */
        public boolean start() {
            if (running.compareAndSet(false, true)) {
                this.lastCallbackDate = startPartitionQuery.apply(cbId);
                return true;
            }

            return false;
        }

        /**
         * 调度是否正在运行
         */
        public boolean isRunning() {
            return running.get();
        }

        /**
         * 结束调度
         */
        public void stop() {
            running.set(false);
        }

        /**
         * 执行任务
         *
         * @param runnable 任务执行， true: 成功， false: 失败
         */
        public void invoke(BooleanSupplier runnable) throws InterruptedException {
            idleTimes = 0;
            threadPermits.acquire();
            IO_EXECUTOR.execute(() -> {
                final long startMills = System.currentTimeMillis();
                boolean success = false;
                try {
                    success = runnable.getAsBoolean();
                } finally {
                    // 保存近一百个调用记录
                    synchronized (invokeMessages) {
                        if (invokeMessages.size() >= 100) {
                            invokeMessages.poll();
                        }

                        invokeMessages.offer(new InvokeMessage(success, System.currentTimeMillis() - startMills));
                    }

                    threadPermits.release();
                }
            });
        }

        /**
         * 空执行
         */
        public void emptyInvoke() {
            final LocalDate newStartPartition = startPartitionQuery.apply(cbId);
            if (!lastCallbackDate.equals(newStartPartition)) {
                lastCallbackDate = newStartPartition;
            }

            if (idleTimes++ > 60) {
                ThreadUtil.sleep(15000);
            } else {
                ThreadUtil.sleep(1000);
            }
        }

        /**
         * 下一轮执行
         */
        public boolean next() {
            if (!isRunning()) {
                return false;
            }

            this.invokeTime = LocalDateTime.now();
            double avgTiming = invokeMessages.stream()
                    .mapToLong(InvokeMessage::timing)
                    .average()
                    .orElse(0);

            // 根据耗时-分配线程数以及拉取任务数
            final int threads;
            final int speed;
            if (avgTiming > 1000) {
                threads = 10;
                this.taskPullCount = 20;
            } else {
                if (avgTiming == 0) {
                    threads = 5;
                    this.taskPullCount = 20;
                } else {
                    // 1000ms = 900 + 100 pull task ms
                    speed = (int) (900 / avgTiming) + 1;
                    threads = Math.min(20, qps / speed + 1);
                    this.taskPullCount = Math.min(threads * speed * 3, 100);
                }
            }

            this.threadPermits = new Semaphore(threads);
            if (lastThreads != threads) {
                log.info("{}分配线程数: {}->{}, 拉取任务数: {}, avgTiming: {}", cbId, lastThreads, threads,
                        taskPullCount, avgTiming);
                lastThreads = threads;
            }
            return true;
        }

        public record InvokeMessage(boolean success, long timing) {
        }
    }
}
