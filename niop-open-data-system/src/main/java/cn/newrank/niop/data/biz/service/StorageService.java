package cn.newrank.niop.data.biz.service;

import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.component.biz.StorageEntity;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.pojo.dto.StorageResult;
import cn.newrank.niop.data.biz.pojo.param.StorageHistoryPageQuery;
import cn.newrank.niop.data.biz.pojo.param.StorageQuery;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;

/**
 *
 */
public interface StorageService {

    /**
     * 获取数据, 0 标识最新版本
     *
     * @param storageBiz 存储业务
     * @param identifier 标识
     * @param version    版本
     * @return 数据
     */
    StorageResult get(StorageBiz storageBiz, String identifier, int version);

    /**
     * 获取历史数据
     *
     * @param storageBiz 存储业务
     * @param identifier 标识
     * @return 数据
     */
    StorageHistory getHistory(StorageBiz storageBiz, String identifier);

    /**
     * 获取数据列表
     *
     * @param storageQuery 查询参数
     * @return 数据列表
     */
    List<? extends StorageEntity> listLastedEntities(StorageQuery storageQuery);

    /**
     * 获取历史数据列表
     *
     * @param pageQuery 查询参数
     * @return 数据列表
     */
    StorageHistory getHistories(StorageHistoryPageQuery pageQuery);

    /**
     * 批量存储数据
     *
     * @param storageBiz 存储业务
     * @param records    数据
     */
    void storeBatch(StorageBiz storageBiz, List<JSONObject> records);

    /**
     * 批量存储数据
     *
     * @param storages 数据
     */
    void store(List<CallbackRedirect> storages);
}
