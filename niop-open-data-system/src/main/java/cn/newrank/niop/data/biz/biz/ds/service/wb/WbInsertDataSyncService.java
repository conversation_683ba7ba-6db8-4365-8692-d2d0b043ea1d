package cn.newrank.niop.data.biz.biz.ds.service.wb;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.WbEsMetaData;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.strategy.SyncBizService;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.util.EsCodec;
import cn.newrank.niop.data.util.EsUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.newrank.niop.data.biz.biz.ds.pojo.constants.DsConstant.MAIN_WB_FIELD_MAPPING;
import static cn.newrank.niop.data.util.EsUtil.SOURCE;
import static cn.newrank.niop.data.util.Iterables.toList;


/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Log4j2
@Service
public class WbInsertDataSyncService implements SyncBizService<WbEsMetaData> {


    private final DsConfigManager dsConfigManager;

    public WbInsertDataSyncService(DsConfigManager dsConfigManager) {
        this.dsConfigManager = dsConfigManager;
    }

    @Override
    public ConfigProperties getConfig() {
        return dsConfigManager.chooseWbEsConfig();
    }

    @Override
    public String getIndexName() {
        return "search_wb_user";
    }

    @Override
    public String getSortField() {
        return "uid";
    }

    @Override
    public String getRangField() {
        return "ana_time";
    }

    @Override
    public List<String> getSourceFields() {
        final List<String> sourceFields = new ArrayList<>();

        for (Map.Entry<String, String> entry : MAIN_WB_FIELD_MAPPING.entrySet()) {
            sourceFields.add(entry.getValue());
        }
        return toList(sourceFields, field -> "\"" + field + "\"");
    }

    @Override
    public List<WbEsMetaData> castOf(JSONObject item) {
        return EsUtil.listHitsToEntity(item, json -> {
            final JSONObject sourceObj = json.getJSONObject(SOURCE);
            return EsCodec.deserialize(JSON.toJSONString(sourceObj), WbEsMetaData.class);
        });
    }

    @Override
    public List<WbEsMetaData> convertData(List<WbEsMetaData> dataList) {
        // 字段转换
        List<Map<String, Object>> mainMapList = new ArrayList<>();
        for (WbEsMetaData data : dataList) {
            Map<String, Object> mainMap = new HashMap<>();
            for (Map.Entry<String, String> entry : MAIN_WB_FIELD_MAPPING.entrySet()) {
                mainMap.put(entry.getKey(), data.get(entry.getValue()));
            }

            mainMap.put("platform_type", PlatformType.WEIBO.getDbCode());

            if (Objects.nonNull(data.getString("label_desc"))) {
                String[] split = data.getString("label_desc").split(",");
                mainMap.put("account_tag", split);
            } else {
                mainMap.put("account_tag", null);

            }

            if (mainMap.containsKey("account_id")) {
                mainMap.put("index_id", PlatformType.WEIBO.getDbCode() + "_" + mainMap.get("account_id"));
            }
            if (mainMap.containsKey("account_name")) {
                mainMap.put("account_name_pinyin", PinyinUtil.getPinyin(String.valueOf(mainMap.get("account_name")), ""));
            }

            // 移除空字段
            mainMap.values().removeIf(Objects::isNull);

            mainMapList.add(mainMap);
        }
        return EsCodec.deserializeToList(JSON.toJSONString(mainMapList), WbEsMetaData.class);
    }


}
