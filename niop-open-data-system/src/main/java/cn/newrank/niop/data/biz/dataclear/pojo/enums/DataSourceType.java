package cn.newrank.niop.data.biz.dataclear.pojo.enums;

public enum DataSourceType {
    POSTGRES("Postgres",
            "DELETE FROM %s \n" +
                    "WHERE ctid IN (\n" +
                    "    SELECT ctid\n" +
                    "    FROM (\n" +
                    "        SELECT ctid\n" +
                    "        FROM %s TABLESAMPLE SYSTEM (1)\n" +
                    "        WHERE %s\n" +
                    "        ORDER BY ctid\n" +
                    "        LIMIT 500\n" +
                    "    ) AS subquery\n" +
                    ");"
    ),
    MYSQL("MySQL", "delete  from %s where %s LIMIT 500");

    private final String type;

    private final String dsl;

    DataSourceType(String type, String dsl) {
        this.type = type;
        this.dsl = dsl;
    }

    // 可选：根据字符串获取枚举实例的方法
    public static DataSourceType fromString(String type) {
        for (DataSourceType dataSourceType : DataSourceType.values()) {
            if (dataSourceType.getType().equalsIgnoreCase(type)) {
                return dataSourceType;
            }
        }
        throw new IllegalArgumentException("Unknown data source type: " + type);
    }


    public String getType() {
        return type;
    }

    public String getDsl() {
        return dsl;
    }
}
