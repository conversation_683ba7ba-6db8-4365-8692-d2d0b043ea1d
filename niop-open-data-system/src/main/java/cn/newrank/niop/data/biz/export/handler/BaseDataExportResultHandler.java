package cn.newrank.niop.data.biz.export.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.newrank.niop.data.biz.export.factory.DataExportResultFetcherFactory;
import cn.newrank.niop.data.biz.export.pojo.dto.DataExportConsumeTaskResult;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportResultSource;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportType;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import cn.newrank.niop.data.biz.export.service.DataExportSubtaskService;
import cn.newrank.niop.data.biz.export.service.DataExportTableStorageService;
import cn.newrank.niop.data.util.Pair;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xxl.job.core.log.XxlJobLogger;
import java.time.Duration;
import java.util.List;
import java.util.function.Consumer;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public abstract class BaseDataExportResultHandler implements DataExportResultHandler {

    protected final DataExportSubtaskService subtaskService;

    protected final Cache<String, Integer> consumeCursorCache = Caffeine.newBuilder()
                                                                  .maximumSize(100)
                                                                  .expireAfterAccess(Duration.ofMinutes(10))
                                                                  .build();

    protected BaseDataExportResultHandler(DataExportSubtaskService subtaskService) {
        this.subtaskService = subtaskService;
    }

    @Override
    public int fetchBatchSize() {
        return 200;
    }

    @Override
    public ExportResult getSample(String exportTaskId) {
        int cursor = 0;

        final DataExportResultFetcher resultFetcher = currentResultFetcher(exportTaskId);

        while (true) {
            List<DataExportSubtask> subtasks = subtaskService.listSubtasksByCursor(exportTaskId, cursor, 10);
            if (CollUtil.isEmpty(subtasks)) {
                break;
            }

            for (DataExportSubtask subtask : subtasks) {
                // 子任务的游标, 可能会翻页查很多数据
                String subtaskCursor = null;
                ExportResult exportResult;
                do {
                    exportResult = resultFetcher.queryResult(subtask.getResultTaskId(), subtaskCursor);
                    if (exportResult.isNotEmpty()) {
                        return exportResult;
                    }
                    // 游标移动
                    subtaskCursor = exportResult.nextCursor();
                } while (exportResult.hasMore());
            }
            // 游标移动
            cursor = CollUtil.getLast(subtasks).getId();
        }

        throw createParamError("获取样本结果为空, exportTaskId: " + exportTaskId);
    }

    @Override
    public DataExportConsumeTaskResult forEachConsume(String exportTaskId, Consumer<ExportResult> resultsConsumer) {
        int cursor = this.consumeCursorCache.get(exportTaskId, k -> 0);
        XxlJobLogger.log("导数任务结果开始循环消费, exportTaskId: {}, cursor: {}", exportTaskId, cursor);
        final DataExportConsumeTaskResult consumeTaskResult = DataExportConsumeTaskResult.of(exportTaskId);

        final DataExportResultFetcher resultFetcher = currentResultFetcher(exportTaskId);

        while (true) {
            List<DataExportSubtask> subtasks = subtaskService.listSubtasksByCursor(exportTaskId, cursor, fetchBatchSize());
            if (CollUtil.isEmpty(subtasks)) {
                break;
            }

            for (DataExportSubtask subtask : subtasks) {
                // 子任务的游标, 可能会翻页查很多数据
                String subtaskCursor = null;
                ExportResult exportResult;
                do {
                    exportResult = resultFetcher.queryResult(subtask.getResultTaskId(), subtaskCursor);
                    if (exportResult.isNotEmpty()) {
                        resultsConsumer.accept(exportResult);
                        consumeTaskResult.accumulateResultNum(exportResult.resultSize());
                    }
                    // 游标移动
                    subtaskCursor = exportResult.nextCursor();
                } while (exportResult.hasMore());
            }

            consumeTaskResult.accumulateTaskNum(subtasks.size());
            // 游标移动
            cursor = CollUtil.getLast(subtasks).getId();
            // 更新到缓存中
            this.consumeCursorCache.put(exportTaskId, cursor);


            XxlJobLogger.log("导数任务结果消费统计, 导数任务id: {}, 已消费任务数: {}, 已消费任务结果数: {}, 下次消费游标: {}", exportTaskId,
                consumeTaskResult.getConsumeTaskNum(),
                consumeTaskResult.getConsumeResultNum(), cursor);

            if (consumeTaskResult.reachMaxResultLimit()) {
                return consumeTaskResult;
            }
        }

        // 正常结束
        consumeTaskResult.end();
        XxlJobLogger.log("导数任务结果消费完成, 导数任务id: {}", exportTaskId);

        // 清除缓存
        this.consumeCursorCache.invalidate(exportTaskId);
        return consumeTaskResult;
    }

    @Override
    public DataExportResultFetcher currentResultFetcher(String exportTaskId) {
        final DataExportResultSource resultSource = existsInTableStorage(exportTaskId) ? DataExportResultSource.TABLE_STORE_NODE : DataExportResultSource.OPEN_QUERY;
        final Pair<DataExportType, DataExportResultSource> pair = new Pair<>(getDataSourceType(), resultSource);
        final DataExportResultFetcherFactory fetcherFactory = SpringUtil.getBean(DataExportResultFetcherFactory.class);
        return fetcherFactory.getResultFetcher(pair);
    }

    private boolean existsInTableStorage(String exportTaskId) {
        final List<String> resultTaskIds = subtaskService.listResultTaskIds(exportTaskId);
        if (CollUtil.isEmpty(resultTaskIds)) {
            return false;
        }
        final DataExportTableStorageService tableStorageService = SpringUtil.getBean(DataExportTableStorageService.class);
        return tableStorageService.existsData(resultTaskIds);
    }

}
