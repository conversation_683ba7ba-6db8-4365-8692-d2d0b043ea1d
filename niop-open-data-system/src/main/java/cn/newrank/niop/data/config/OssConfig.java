package cn.newrank.niop.data.config;


import cn.newrank.niop.data.config.property.OssProperties;
import cn.newrank.nrcore.web.oss.NrOssClient;
import cn.newrank.nrcore.web.oss.NrOssService;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/23 17:06
 */
@Configuration
@EnableConfigurationProperties(OssProperties.class)
public class OssConfig {

    private final OssProperties ossProperties;
    private final SystemConfig systemConfig;

    public OssConfig(OssProperties ossProperties, SystemConfig systemConfig) {
        this.ossProperties = ossProperties;
        this.systemConfig = systemConfig;
    }

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(getEndpoint(), ossProperties.getAk(), ossProperties.getSk());
    }

    @Bean
    public NrOssService niopOssService() {
        return NrOssClient.create(getEndpoint(), ossProperties.getAk(), ossProperties.getSk());
    }

    public String getAk() {
        return ossProperties.getAk();
    }

    public String getBucket() {
        return ossProperties.getBucket();
    }


    public String getEndpoint() {
        return systemConfig.isDevelop() ? OssProperties.PUBLIC_HOST : OssProperties.INTERNAL_HOST;
    }

    public String getBasePath() {
        return ossProperties.getBasePath();
    }

    public String getIntranetOssHost() {
        return "https://" + getBucket() + "." + OssProperties.INTERNAL_HOST;
    }

    public String getPublicOssHost() {
        return "https://" + getBucket() + "." + OssProperties.PUBLIC_HOST;
    }
}
