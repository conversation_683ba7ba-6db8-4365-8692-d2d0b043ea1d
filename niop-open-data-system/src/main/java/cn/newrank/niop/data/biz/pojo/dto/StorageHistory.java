package cn.newrank.niop.data.biz.pojo.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/19 13:45
 */
@Data
public class StorageHistory {
    /**
     * 标识符
     */
    String identifier;
    /**
     * 历史版本
     */
    LinkedList<History> histories = new LinkedList<>();

    /**
     * 总页数
     */
    long pages;

    /**
     * 总条数
     */
    long total;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class History {
        /**
         * 版本
         */
        Integer version;
        /**
         * 更新时间
         */
        Long updateTime;
        /**
         * 数据
         */
        JSONObject data;
    }
}
