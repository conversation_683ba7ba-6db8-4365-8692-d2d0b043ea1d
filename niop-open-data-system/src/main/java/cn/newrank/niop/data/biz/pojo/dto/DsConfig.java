package cn.newrank.niop.data.biz.pojo.dto;


import cn.newrank.niop.data.biz.pojo.po.DatasourceConfigPo;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.enums.DsType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:42
 */
@Data
public class DsConfig {
    public static final int ID_LENGTH = 5;
    /**
     * 数据源配置id
     */
    private String dcId;

    /**
     * 数据源类型
     */
    private DsType type;

    /**
     * 数据源配置名称
     */
    private String name;

    /**
     * 数据源配置
     */
    private ConfigProperties config;

    /**
     * 任务并发数
     */
    private Integer concurrencyPermit = 100000;

    /**
     *
     */
    private String gmtCreate;

    /**
     *
     */
    private String gmtModified;

    public DatasourceConfigPo toPo() {
        final DatasourceConfigPo configPo = new DatasourceConfigPo();
        configPo.setDcId(dcId);
        configPo.setType(type);
        configPo.setName(name);
        configPo.setConfig(config.toJSONString());
        configPo.setConcurrencyPermit(concurrencyPermit);

        return configPo;
    }
}