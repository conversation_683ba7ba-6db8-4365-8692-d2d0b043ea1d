package cn.newrank.niop.data.util;

import io.opentelemetry.api.internal.StringUtils;
import lombok.experimental.UtilityClass;

import java.net.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/19 17:12
 */
@UtilityClass
public class IPv4 {
    static String localHost;

    static {
        //aliyun dns
        localHost = getLocalHost("***************");
        if (StringUtils.isNullOrEmpty(localHost)) {
            //google dns
            localHost = getLocalHost("*******");
        }
        if (StringUtils.isNullOrEmpty(localHost)) {
            localHost = "127.0.0.1";
        }
    }

    private static String getLocalHost(String targetDns) {
        try (final DatagramSocket socket = new DatagramSocket()) {
            socket.connect(InetAddress.getByName(targetDns), 10002);
            if (socket.getLocalAddress() instanceof Inet4Address) {
                return socket.getLocalAddress().getHostAddress();
            }
        } catch (Exception ignore) {
        }
        return "";
    }

    /**
     * 获取本机IP
     *
     * @return IP
     */
    public static String local() {
        return localHost;
    }

}
