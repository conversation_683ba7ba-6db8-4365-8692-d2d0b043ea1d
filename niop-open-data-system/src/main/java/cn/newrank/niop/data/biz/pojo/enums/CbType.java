package cn.newrank.niop.data.biz.pojo.enums;

import cn.newrank.niop.data.biz.component.callback.CallbackFactory;
import cn.newrank.niop.data.biz.component.callback.WebhookCallbackFactory;
import cn.newrank.niop.data.biz.component.callback.KafkaCallbackFactory;
import cn.newrank.niop.web.model.BizEnum;

/**
 * 回调类型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/14 9:55
 */
public enum CbType implements BizEnum {
    KAFKA("KAFKA", "KAFKA", KafkaCallbackFactory.class),
    WEBHOOK("WEBHOOK", "WEBHOOK", WebhookCallbackFactory.class),
    ;

    final String code;
    final String description;
    final Class<? extends CallbackFactory> callbackFactoryClass;

    CbType(String code, String description, Class<? extends CallbackFactory> callbackFactoryClass) {
        this.code = code;
        this.description = description;
        this.callbackFactoryClass = callbackFactoryClass;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getJsonValue() {
        return code;
    }

    @Override
    public String getDbCode() {
        return code;
    }

    public Class<? extends CallbackFactory> getFactoryClass() {
        return callbackFactoryClass;
    }
}
