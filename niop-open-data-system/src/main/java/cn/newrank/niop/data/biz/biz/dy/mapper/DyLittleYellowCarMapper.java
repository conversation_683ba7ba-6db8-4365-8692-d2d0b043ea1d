package cn.newrank.niop.data.biz.biz.dy.mapper;

import cn.newrank.niop.data.biz.biz.dy.pojo.DyLittleYellowCar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【niop_data_biz_dy_little_yellow_car】的数据库操作Mapper
 * @createDate 2024-10-16 17:00:11
 * @Entity cn.newrank.niop.data.biz.biz.dy.pojo.DyLittleYellowCar
 */
@Mapper
public interface DyLittleYellowCarMapper {

    /**
     * 批量保存数据
     *
     * @param itemList
     */
    void batchSave(@Param("itemList") List<DyLittleYellowCar> itemList);


}




