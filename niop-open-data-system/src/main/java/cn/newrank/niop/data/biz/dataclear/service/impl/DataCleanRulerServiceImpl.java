package cn.newrank.niop.data.biz.dataclear.service.impl;

import cn.newrank.niop.data.biz.dao.DatasourceConfigDao;
import cn.newrank.niop.data.biz.dataclear.mapper.DataClearMapper;
import cn.newrank.niop.data.biz.dataclear.pojo.DataCleanRuleCreate;
import cn.newrank.niop.data.biz.dataclear.pojo.RuleStatus;
import cn.newrank.niop.data.biz.dataclear.pojo.param.*;
import cn.newrank.niop.data.biz.dataclear.pojo.vo.DataCleanRuleSearchVo;
import cn.newrank.niop.data.biz.dataclear.service.DataCleanRuleService;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.niop.web.model.PageView;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xkzhangsan.time.cron.CronExpressionUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.newrank.niop.data.biz.dataclear.utils.CronUtils.checkIsTimeBefore;
import static cn.newrank.niop.data.biz.dataclear.utils.CronUtils.validateCron;
import static cn.newrank.niop.util.U.toMap;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 *
 */
@Service
public class DataCleanRulerServiceImpl implements DataCleanRuleService, ApplicationEventPublisherAware {


    private final SqlService sqlService;
    private final DataClearMapper dataClearMapper;
    private final DatasourceConfigDao datasourceConfigDao;
    ApplicationEventPublisher eventPublisher;

    public DataCleanRulerServiceImpl(SqlService sqlService, DataClearMapper dataClearMapper, DatasourceConfigDao datasourceConfigDao) {
        this.sqlService = sqlService;
        this.dataClearMapper = dataClearMapper;
        this.datasourceConfigDao = datasourceConfigDao;
    }

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    @Override
    public DataCleanRuleCreate get(String ruleId) {
        return dataClearMapper.get(ruleId);
    }


    @Override
    public String create(DataCleanRuleCreateQuery dataCleanRuleCreate) {
        //检测corn表达式是否正确
        if (!CronExpressionUtil.isValidExpression(dataCleanRuleCreate.getCron())) {
            throw createParamError("cron表达式语法错误:{}", dataCleanRuleCreate.getCron());
        }
        if (!validateCron(dataCleanRuleCreate.getCron())) {
            throw createParamError("cron只支持创建小时级别以及以上的调度: ");
        }

        if (StringUtils.isNotBlank(dataCleanRuleCreate.getSchemaName())) {
            dataCleanRuleCreate.setTableName(dataCleanRuleCreate.getSchemaName() + "." + dataCleanRuleCreate.getTableName());
        }

        //检测语句是否正确
        sqlService.mysqlCheck(dataCleanRuleCreate);


        dataCleanRuleCreate.setRuleId(Ids.create(7));

        try {
            dataClearMapper.insert(dataCleanRuleCreate);
        } catch (DataIntegrityViolationException e) {
            // 处理唯一键冲突
            throw createParamError("出现唯一键冲突，一个表只能创建一个删除规则 ");
            // 可以在这里添加更多的处理逻辑，比如记录日志、返回错误信息等
        }


        String ruleId = dataCleanRuleCreate.getRuleId();

        //todo 新增去发送事件，调用redis入list表中
        //调用定时任务，去调用进入任务表
        publishCustomEventForCreate(dataCleanRuleCreate);


        return ruleId;
    }


    @Override
    public PageView<DataCleanRuleSearchVo> page(DataCleanPageQuery pageQuery) {
        final Page<DataCleanRuleCreate> mybatisPlusPage = pageQuery.toMybatisPlusPage();

        final Page<DataCleanRuleCreate> page = dataClearMapper.page(pageQuery, mybatisPlusPage);

        // 获取所有需要查询的dcId
        List<String> dcIds = page.getRecords().stream()
                .map(DataCleanRuleCreate::getDcId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
        //批量查询，并且存入map
        Map<String, DsConfig> dsConfigMap = toMap(datasourceConfigDao.list(new HashSet<>(dcIds)),
                DsConfig::getDcId, Function.identity());

        //
        List<DataCleanRuleSearchVo> collect = page.getRecords().stream()
                .map(i -> {
                    DataCleanRuleSearchVo vo = new DataCleanRuleSearchVo();
                    BeanUtils.copyProperties(i, vo);
                    if (StringUtils.isNotBlank(i.getDcId())) {
                        DsConfig dsConfig = dsConfigMap.get(i.getDcId());
                        if (dsConfig != null) {
                            vo.setDcName(dsConfig.getName());
                        }
                    }
                    return vo;
                }).collect(Collectors.toList());

        Page<DataCleanRuleSearchVo> dataCleanRuleSearchVoPage = new Page<>();

        dataCleanRuleSearchVoPage.setRecords(collect);
        dataCleanRuleSearchVoPage.setTotal(page.getTotal());
        dataCleanRuleSearchVoPage.setSize(page.getSize());
        dataCleanRuleSearchVoPage.setCurrent(page.getCurrent());


        return PageView.of(dataCleanRuleSearchVoPage);
    }


    @Override
    public boolean update(CleanRuleUpadteQuery cleanRuleUpadteQuery) {
        //检测corn表达式是否正确
        if (!CronExpressionUtil.isValidExpression(cleanRuleUpadteQuery.getCron())) {
            throw createParamError("cron表达式错误");
        }
        if (!validateCron(cleanRuleUpadteQuery.getCron())) {
            throw createParamError("cron只支持创建每小时以及以上的调度: " + cleanRuleUpadteQuery.getCron());
        }
        //支持pg的SchemaName
        if (StringUtils.isNotBlank(cleanRuleUpadteQuery.getSchemaName())) {
            cleanRuleUpadteQuery.setTableName(cleanRuleUpadteQuery.getSchemaName() + "." + cleanRuleUpadteQuery.getTableName());
        }
        //检测语句是否正确
        sqlService.mysqlCheck(cleanRuleUpadteQuery);

        publishCustomEventForUpdate(cleanRuleUpadteQuery);

        return dataClearMapper.update(cleanRuleUpadteQuery) > 0;
    }


    @Override
    public List<DataCleanRuleCreate> fuzzyQuery(DataCleanFuzzyQuery fuzzyQuery) {
        return dataClearMapper.fuzzyQuery(fuzzyQuery);
    }

    @Override
    public List<DataCleanRuleCreate> listExecuteTask(Integer cursor) {
        return dataClearMapper.list(cursor)
                .stream()
                .filter(item -> checkIsTimeBefore(item.getCron())).toList();
    }


    @Override
    public boolean delete(String ruleId) {
        CleanRuleDeleteQuery cleanRuleDeleteQuery = new CleanRuleDeleteQuery();
        cleanRuleDeleteQuery.setRuleId(ruleId);
        cleanRuleDeleteQuery.setRuleStatus(RuleStatus.DISABLED);
        publishCustomEventForDelete(cleanRuleDeleteQuery);
        return dataClearMapper.delete(ruleId) > 0;
    }

    //新建规则
    public void publishCustomEventForCreate(DataCleanRuleCreateQuery message) {
        eventPublisher.publishEvent(message);
    }

    //更新规则
    public void publishCustomEventForUpdate(CleanRuleUpadteQuery cleanRuleUpadteQuery) {
        eventPublisher.publishEvent(cleanRuleUpadteQuery);
    }

    public void publishCustomEventForDelete(CleanRuleDeleteQuery cleanRuleUpadteQuery) {
        eventPublisher.publishEvent(cleanRuleUpadteQuery);
    }

}




