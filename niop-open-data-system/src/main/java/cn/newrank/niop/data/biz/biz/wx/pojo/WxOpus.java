package cn.newrank.niop.data.biz.biz.wx.pojo;

import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import com.alibaba.fastjson2.JSONArray;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @TableName niop_data_biz_wx_opus
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WxOpus extends SampleVersionEntity {
    /**
     * 作品id
     */
    private String opusId;

    /**
     * 文章原始连接
     */
    private String oriUrl;

    /**
     * ip所在国家名称
     */
    private String countryName;

    /**
     * ip所在国家Id
     */
    private String countryId;

    /**
     * ip所在省份名称
     */
    private String provinceName;

    /**
     * 音乐地址
     */
    private String musicUrl;

    /**
     * 是否有广告
     */
    private Boolean isAds;

    /**
     * 备注信息
     */
    private String memo;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章内容详情
     */
    private String content;

    private String publishTime;

    /**
     * 文章内容图片地址
     */
    private String imageUrl;

    /**
     * 动态图地址
     */
    private List<String> gifs;

    /**
     * 文章总结内容
     */
    private String summary;

    /**
     * 文章详情所有图片地址
     */
    private List<String> images;

    /**
     * 作者名称
     */
    private String author;

    /**
     * 文章地址
     */
    private String url;

    /**
     * 是否是原创
     */
    private Boolean oriFlag;

    /**
     * 账户头像地址
     */
    private String avatar;

    /**
     * 账户姓名
     */
    private String nickname;

    /**
     * 账户唯一标识
     */
    private String biz;

    /**
     * 账户描述
     */
    private String description;

    /**
     * 头像大图地址
     */
    private String indexUrl;

    /**
     * 账户微信id
     */
    private String wxId;

    /**
     * 账户名称
     */
    private String account;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 发布位置
     */
    private String publishOrder;

    /**
     * 原创作者
     */
    private String oriAuthor;

    /**
     * 原文链接
     */
    private String sourceUrl;

    /**
     * 文章点赞数
     */
    private Integer likeNum;

    /**
     * 在看数
     */
    private Integer viewingNum;

    /**
     * 分享数
     */
    private Integer shareNum;

    /**
     * 文章阅读数
     */
    private Integer viewNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 文章标签
     */
    private List<String> tags;

    /**
     * 用户id
     */
    private String mid;

    /**
     * 文章话题
     */
    private String topics;

    @Override
    public String identifier() {
        return opusId;
    }
}