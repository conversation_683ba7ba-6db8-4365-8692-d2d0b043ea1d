package cn.newrank.niop.data.biz.dao.mapper;

import cn.newrank.niop.data.biz.pojo.po.CallbackRetryPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/5 17:50
 */
@Mapper
public interface CallbackRedirectRetryMapper {

    List<CallbackRetryPo> listByRetryStatus(@Param("retryStatus") Integer retryStatus,
                                          @Param("size") Integer size);

    void saveBatch(@Param("retryPoList") List<CallbackRetryPo> retryPoList);

    int updateBatch(@Param("retryPoList") List<CallbackRetryPo> retryPoList);

}




