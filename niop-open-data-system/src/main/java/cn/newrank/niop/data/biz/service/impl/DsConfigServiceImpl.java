package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.biz.dao.DatasourceConfigDao;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.DsConfigCreate;
import cn.newrank.niop.data.biz.pojo.param.DsConfigUpdate;
import cn.newrank.niop.data.biz.pojo.param.DsFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.DsPageQuery;
import cn.newrank.niop.data.biz.service.DsConfigService;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.DatasourceFactory;
import cn.newrank.niop.data.common.event.DatasourceConfigRefreshEvent;
import cn.newrank.niop.data.common.event.DatasourceConfigRefreshTopic;
import cn.newrank.niop.data.util.Ids;
import cn.newrank.niop.web.model.PageView;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static cn.newrank.niop.util.U.toMap;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 11:41
 */
@Service
public class DsConfigServiceImpl implements DsConfigService, ApplicationEventPublisherAware {

    private final DatasourceConfigDao datasourceConfigDao;
    private final DatasourceConfigRefreshTopic datasourceConfigRefreshTopic;
    ApplicationEventPublisher eventPublisher;

    public DsConfigServiceImpl(DatasourceConfigDao datasourceConfigDao,
                               DatasourceConfigRefreshTopic datasourceConfigRefreshTopic) {
        this.datasourceConfigDao = datasourceConfigDao;
        this.datasourceConfigRefreshTopic = datasourceConfigRefreshTopic;
    }

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    @Override
    public String create(DsConfigCreate configCreate) {
        final DsConfig config = configCreate.toDto();
        final DatasourceFactory factory = config.getType().getFactory();

        try (final Datasource datasource = factory.create(config.getConfig())) {
            if (datasource.isActive()) {
                config.setDcId(Ids.create(DsConfig.ID_LENGTH));
                datasourceConfigDao.save(config);

                //数据源更新事件
                final DatasourceConfigRefreshEvent refreshEvent = new DatasourceConfigRefreshEvent(config.getDcId());
                datasourceConfigRefreshTopic.emitEvent(refreshEvent);
                eventPublisher.publishEvent(refreshEvent);

                return config.getDcId();
            }

            throw createParamError("数据源链接不可用");
        }
    }

    @Override
    public DsConfig getConfig(String dcId) {
        return datasourceConfigDao.get(dcId);
    }

    @Override
    public boolean hasConfig(String dcId) {
        return getConfig(dcId) != null;
    }


    @Override
    public boolean update(DsConfigUpdate dsConfigUpdate) {
        final DsConfig config = dsConfigUpdate.toDto();

        final DatasourceFactory factory = config.getType().getFactory();
        try (final Datasource datasource = factory.create(config.getConfig())) {
            if (datasource.isActive()) {
                if (datasourceConfigDao.update(config)) {

                    // 数据源更新事件
                    datasourceConfigRefreshTopic.emitEvent(new DatasourceConfigRefreshEvent(config.getDcId()));

                    return true;
                } else {
                    return false;
                }
            }

            throw createParamError("数据源链接不可用");
        }
    }

    @Override
    public boolean delete(String dcId) {
        if (datasourceConfigDao.delete(dcId)) {
            datasourceConfigRefreshTopic.emitEvent(new DatasourceConfigRefreshEvent(dcId));

            return true;
        }
        return false;
    }

    @Override
    public List<DsConfig> fuzzyQuery(DsFuzzyQuery fuzzyQuery) {
        return datasourceConfigDao.fuzzyQuery(fuzzyQuery);
    }

    @Override
    public void checkDcId(String dcId) {
        if (getConfig(dcId) == null) {
            throw createParamError("数据源配置(id: {})不存在", dcId);
        }
    }

    @Override
    public List<DsConfig> listAll() {
        return datasourceConfigDao.listAll();
    }


    @Override
    public PageView<DsConfig> page(DsPageQuery pageQuery) {
        return datasourceConfigDao.page(pageQuery);
    }

    @Override
    public Map<String, DsConfig> map(Collection<String> dcIds) {
        return toMap(datasourceConfigDao.list(new HashSet<>(dcIds)), DsConfig::getDcId, Function.identity());
    }

    @Override
    public List<DsConfig> findByTypes(Collection<String> types) {
        return datasourceConfigDao.findByTypes(new HashSet<>(types));
    }


}




