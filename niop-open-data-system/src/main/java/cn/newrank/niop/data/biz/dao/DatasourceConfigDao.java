package cn.newrank.niop.data.biz.dao;

import cn.newrank.niop.data.biz.dao.mapper.DatasourceConfigMapper;
import cn.newrank.niop.data.biz.pojo.dto.DsConfig;
import cn.newrank.niop.data.biz.pojo.param.DsFuzzyQuery;
import cn.newrank.niop.data.biz.pojo.param.DsPageQuery;
import cn.newrank.niop.data.biz.pojo.po.DatasourceConfigPo;
import cn.newrank.niop.web.model.PageView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static cn.newrank.niop.data.util.Iterables.isEmpty;
import static cn.newrank.niop.data.util.Iterables.toList;
import static cn.newrank.niop.web.exception.BizExceptions.createDbError;
import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/8 14:00
 */
@Component
public class DatasourceConfigDao {
    private final DatasourceConfigMapper datasourceConfigMapper;

    public DatasourceConfigDao(DatasourceConfigMapper datasourceConfigMapper) {
        this.datasourceConfigMapper = datasourceConfigMapper;
    }

    public void save(DsConfig datasource) {
        final DatasourceConfigPo configPo = datasource.toPo();

        checkExisted(configPo);

        try {
            datasourceConfigMapper.insert(configPo);
        } catch (Exception e) {
            throw createDbError(e, "保存数据源配置失败");
        }
    }

    private void checkExisted(DatasourceConfigPo configPo) {
        final String name = configPo.getName();
        if (datasourceConfigMapper.getByName(name) != null) {
            throw createParamError("数据源配置名(name: {})已存在", name);
        }
    }

    public DsConfig get(String dcId) {
        final DatasourceConfigPo configPo = datasourceConfigMapper.get(dcId);

        return configPo == null ? null : configPo.toDto();
    }

    public boolean update(DsConfig dsConfig) {
        final DatasourceConfigPo configPo = dsConfig.toPo();
        final String dcId = dsConfig.getDcId();
        final DsConfig dbConfig = get(dcId);
        if (dbConfig == null) {
            throw createParamError("数据源配置(dcId: {})不存在", dcId);
        }

        if (StringUtils.equals(dbConfig.getName(), configPo.getName())) {
            configPo.setName(null);
        } else {
            checkExisted(configPo);
        }

        try {
            return datasourceConfigMapper.update(configPo) > 0;
        } catch (Exception e) {
            throw createDbError(e, "更新数据源配置(id: {})失败", configPo.getId());
        }
    }

    public boolean delete(String dcId) {
        return datasourceConfigMapper.delete(dcId) > 0;
    }

    public List<DsConfig> fuzzyQuery(DsFuzzyQuery fuzzyQuery) {
        return toList(datasourceConfigMapper.fuzzyQuery(fuzzyQuery), DatasourceConfigPo::toDto);
    }

    public List<DsConfig> findByTypes(Set<String> types) {
        return toList(datasourceConfigMapper.findByTypes(types), DatasourceConfigPo::toDto);
    }

    public List<DsConfig> listAll() {
        return toList(datasourceConfigMapper.listAll(), DatasourceConfigPo::toDto);
    }

    public PageView<DsConfig> page(DsPageQuery pageQuery) {
        return PageView.of(datasourceConfigMapper.page(pageQuery.toMybatisPlusPage(), pageQuery))
                .convert(DatasourceConfigPo::toDto);
    }

    public List<DsConfig> list(Set<String> dcIds) {
        if (isEmpty(dcIds)) {
            return new ArrayList<>();
        }
        return toList(datasourceConfigMapper.list(dcIds), DatasourceConfigPo::toDto);
    }
}
