package cn.newrank.niop.data.biz.biz.bz.pojo;

import cn.newrank.niop.data.biz.component.biz.Calculation;
import cn.newrank.niop.data.biz.component.biz.SampleVersionEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/11 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BzLive extends SampleVersionEntity implements Serializable {

    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 大航海观众
     */
    private Long guardNum;

    /**
     * 人气峰值/峰值热度
     */
    private Long hot;
    /**
     * 最高值(需要计算)
     */
    @Calculation("max(hot)")
    private Long maxHot;

    /**
     * 直播时长
     */
    @Calculation("(endTime - startTime) / 1000 (s)")
    private Long duration;
    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 点赞数
     */
    private Long totalLikeNum;
    /**
     * 平均热度
     */
    @Calculation("avg(hot)")
    private BigDecimal avgHot;
    /**
     * 直播状态
     */
    private String liveStatus;

    /**
     * 下播时间
     */
    private Timestamp endTime;

    /**
     * 开播时间
     */
    private Timestamp startTime;

    /**
     * 是否计算过
     */
    @Calculation("self")
    private Boolean calculated;

    private Long watchedNum;

    /**
     * 房间观众数
     */
    private Long audienceNum;

    /**
     *
     */
    @Calculation("max(audienceNum)")
    private Long maxAudienceNum;
    /**
     *
     */
    @Calculation("avg(audienceNum)")
    private Long avgAudienceNum;

}