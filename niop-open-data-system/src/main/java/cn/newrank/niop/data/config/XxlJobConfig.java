package cn.newrank.niop.data.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/24 14:25
 */
@Configuration
@Slf4j
@Profile({"test", "product"})
public class XxlJobConfig {

    @Value("${xxl.job.admin.address}")
    String adminAddresses;

    @Value("${xxl.job.accessToken}")
    String accessToken;

    @Value("${xxl.job.executor.appName}")
    String appName;

    @Value("${xxl.job.executor.port}")
    int port;

    @Value("${xxl.job.executor.logPath}")
    String logPath;

    @Value("${xxl.job.executor.logRetentionDays}")
    int logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setIp("");
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }
}
