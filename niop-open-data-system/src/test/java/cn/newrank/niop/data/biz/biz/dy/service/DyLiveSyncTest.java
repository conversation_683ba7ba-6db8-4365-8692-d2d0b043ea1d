package cn.newrank.niop.data.biz.biz.dy.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
class DyLiveSyncTest extends BaseTest {

    @Resource
    private DyLiveSync dyLiveSync;
    @Test
    void sync() {

        final Cursor<String> cursor = new Cursor<>();
        cursor.setStart("0");
        cursor.setEnd("7444761669550557978");
        for (int i = 0; i < 100; i++) {

            dyLiveSync.sync(cursor);
        }
    }
}