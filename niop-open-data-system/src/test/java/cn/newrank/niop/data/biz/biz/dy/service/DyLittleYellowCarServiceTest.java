package cn.newrank.niop.data.biz.biz.dy.service;

import cn.newrank.niop.data.BaseTest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

class DyLittleYellowCarServiceTest extends BaseTest {

    @Autowired
    private DyLittleYellowCarService littleYellowCarService;

    @Test
    public void test() {
        JSONObject jsonObject = JSON.parseObject(getStr());
        jsonObject.put("kafka_partition", 6);
        jsonObject.put("kafka_offset", 2432);
        littleYellowCarService.castOf(jsonObject);
    }


    public static String getStr() {
        try {
            return new String(Files.readAllBytes(Paths.get("C:\\Users\\<USER>\\Desktop\\json.txt")), "UTF-8");
            // Use the longString as needed
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }



}