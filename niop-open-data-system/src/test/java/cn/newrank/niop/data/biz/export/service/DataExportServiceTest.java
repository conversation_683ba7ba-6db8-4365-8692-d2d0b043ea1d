package cn.newrank.niop.data.biz.export.service;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.BaseTest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;

@Slf4j
class DataExportServiceTest extends BaseTest {

    @Resource
    private DataExportService dataExportService;

    @Test
    void testGetTemplateFile() {
        ResponseEntity<FileSystemResource> templateFile = dataExportService.getTemplateFile("7DBZ7L18");
        ThreadUtil.sleep(1000);
        log.info("done");
    }

}