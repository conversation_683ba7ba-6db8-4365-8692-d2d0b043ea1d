package cn.newrank.niop.data.biz.biz.xhs;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.xhs.mapper.XhsOpusLmMapper;
import cn.newrank.niop.data.biz.biz.xhs.mapper.exp.XhsExpAtspTaskMapper;
import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusFromMulti;
import cn.newrank.niop.data.biz.biz.xhs.pojo.exp.XhsExpAtspTask;
import cn.newrank.niop.data.biz.biz.xhs.service.exp.XhsExpTestService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/16 00:14:02
 */
public class XhsExpTaskTest extends BaseTest {

    @Autowired
    XhsExpTestService xhsExpTestService;
    @Autowired
    XhsExpAtspTaskMapper xhsExpAtspTaskMapper;
    @Autowired
    XhsOpusLmMapper xhsOpusLmMapper;

    /**
     * 批量保存测试数据
     */
    @Test
    public void saveTestData() {
        LocalDate now = LocalDate.of(2025, 4, 15);
        List<XhsExpAtspTask> list = xhsExpAtspTaskMapper.listTasks(now);
        List<List<XhsExpAtspTask>> lists = CollectionUtil.split(list, 100);
        for (List<XhsExpAtspTask> subList : lists) {
            for (XhsExpAtspTask item : subList) {
                xhsExpTestService.saveTestData(item.getTaskId(), now.atStartOfDay().plusHours(12), item.getParam());
            }
            ThreadUtil.sleep(1, TimeUnit.SECONDS);
        }
    }

    private static final int BATCH_SIZE = 2000; // 阿里规范：合理设置批处理大小

    @Test
    public void searchOpusLindorm() {
        String in = "C:\\Users\\<USER>\\Desktop\\新30wxlsx.csv";
        String out = "C:\\Users\\<USER>\\Desktop\\新30wxlsx-1.csv";
        List<OpusDataSource> opusIds = readOpusIds(in);
        List<List<OpusDataSource>> batchList = ListUtil.split(opusIds, BATCH_SIZE);
        List<OpusData> finalList = new ArrayList<>();
        for (List<OpusDataSource> batch : batchList) {
            List<OpusData> batchOpusData = batch.stream().map(opus -> {
                OpusData opusData = new OpusData();
                opusData.setOpusId(opus.getOpusId());
                //opusData.setInsertTime(opus.getInsertTime());
                return opusData;
            }).toList();
            processBatch(batchOpusData);
            finalList.addAll(batchOpusData);
        }
        writeNewCsv(out, finalList);
    }

    private void writeNewCsv(String outputPath, List<OpusData> dataList) {
        ExcelWriterBuilder writerBuilder = EasyExcel.write(outputPath, OpusData.class)
                .charset(StandardCharsets.UTF_8);
        writerBuilder.sheet("作品数据")
                .doWrite(dataList);
    }


    private void processBatch(List<OpusData> batch) {
        List<String> opusIds = batch.stream()
                .map(OpusData::getOpusId)
                .collect(Collectors.toList());
        Map<String, XhsOpusFromMulti> existMap = xhsOpusLmMapper.getOpusExistBatch(opusIds).stream()
                .collect(Collectors.toMap(XhsOpusFromMulti::getOpusId, Function.identity()));
        batch.forEach(data -> {
            final XhsOpusFromMulti opus = existMap.get(data.getOpusId());
            if (Objects.nonNull(opus)) {
                data.setIsExist("Y");
                data.setFirst_detail_anaTime(opus.getFirstDetailAnaTime());
                data.setType(opus.getType());
                return;
            }
            data.setIsExist("N");
        });
    }

    private List<OpusDataSource> readOpusIds(String filePath) {
        List<OpusDataSource> opus = new ArrayList<>();
        EasyExcel.read(filePath, new OpusIdListener(opus))
                .sheet()
                .doRead();
        return opus;
    }

    class OpusIdListener extends AnalysisEventListener<Map<Integer, String>> {
        private final List<OpusDataSource> opus;

        public OpusIdListener(List<OpusDataSource> opus) {
            this.opus = opus;
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            OpusDataSource opusDataSource = new OpusDataSource();
            opusDataSource.setOpusId(data.get(0));
            //opusDataSource.setInsertTime(data.get(1));
            opus.add(opusDataSource);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 处理完成
        }
    }

    @Test
    public void testGetOpusExistBatch() {
        XhsOpusFromMulti.SourceTag sourceTag = new XhsOpusFromMulti.SourceTag();
        sourceTag.setSourceList(Set.of("aweme_base"));
        sourceTag.setFromEs(0);
        sourceTag.setLastSourceModify("aweme_base");

        List<XhsOpusFromMulti> list = xhsOpusLmMapper.getOpusExistBatch(List.of("65190dd2000000002101dc0e", "6519112e000000002301beeb"));
        list.forEach(opusData -> {
            opusData.setLdGmtCreate(DateUtil.now());
            ThreadUtil.sleep(1, TimeUnit.SECONDS);
            opusData.setLdGmtModify(DateUtil.now());
            opusData.setSource("aweme_base");
            opusData.setSourceTag(JSON.toJSONString(sourceTag));
        });
        xhsOpusLmMapper.saveBatch(list);
        for (XhsOpusFromMulti opusData : list) {
            System.out.println(opusData.getOpusId());
        }
    }

    @Data
    public class OpusDataSource {
        @ExcelProperty("opusId")
        private String opusId;

        @ExcelProperty("insertTime")
        private String insertTime;
    }


    @Data
    public class OpusData {
        @ExcelProperty("opusId")
        private String opusId;

//        @ExcelProperty("insertTime")
//        private String insertTime;

        @ExcelProperty("first_detail_anaTime")
        private String first_detail_anaTime;

        @ExcelProperty("type")
        private String type;

        @ExcelProperty("is_exist")
        private String isExist;
    }
}
