package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.pojo.dto.Dictionary;
import cn.newrank.niop.data.biz.service.DictionaryService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import jakarta.annotation.Resource;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;

class DictionaryServiceImplTest extends BaseTest {

    @Resource
    RestHighLevelClient elasticsearchClient;
    @Resource
    DictionaryService dictionaryService;


    @Test
    void startSync() throws IOException {
        dictionaryService.startSync();
    }

    @Test
    void startDsSync() {
        dictionaryService.startDsSync("F5106");
    }

    @Test
    void test() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);
        final Dictionary dictionary = new Dictionary();
        dictionary.setCollection("xhs_account");
        dictionary.setDatabase("niop_data");
        dictionary.setDescription("描述");
        //dictionary.setDcId("IODAFNJ");
        //dictionary.setTags(List.of("xhs", "xhs_account"));

        final Dictionary.DictionaryColumn column = new Dictionary.DictionaryColumn();
        column.setColumnAliases(List.of("xx"));
        column.setColumnName("f_a");
        column.setColumnType("string");
        dictionary.setColumns(List.of(column));
        UpdateRequest updateRequest = new UpdateRequest("niop_dc_dictionary_test", null)
                .doc(objectMapper.writeValueAsString(dictionary), XContentType.JSON)
                .docAsUpsert(true);

        UpdateResponse updateResponse = elasticsearchClient.update(updateRequest, RequestOptions.DEFAULT);
    }
}