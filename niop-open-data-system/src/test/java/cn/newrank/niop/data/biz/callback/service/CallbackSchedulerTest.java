package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.util.Ids;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;
import static org.junit.jupiter.api.Assertions.*;

class CallbackSchedulerTest extends BaseTest {
    @Resource
    CallbackScheduler callbackScheduler;

    @Test
    void callbackTableManager() {
        callbackScheduler.callbackTableManager("");
    }


    @Test
    void test_resource_mutate() {
        final CbConfig cbConfig = new CbConfig();
        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> """
                    alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093,
                    alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093
                    """;
            case TOPIC -> "niop_open_resource_mutate_resource_test";
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> "niop_rs";
            case PASSWORD -> "aXy4T2B3nn4Dp3j1";
            default -> null;
        });

        try (final KafkaCallback kafkaCallback = new KafkaCallback(cbConfig.getConfig())) {
            if (!kafkaCallback.isActive()) {
                throw createParamError("not active");
            }
            kafkaCallback.callback(getBody());
        }
    }


    static String getBody() {
        return """
            {
             "biz": "sphJghCookie",
             "resource": {
                 "cookie": "ilinkst_CLjukLjZppQuEhDNBQAAAAAAAAAAAAAAAAAAGjAQQkqxKLEAamDtaOwz7COJsPRkHVSKzf_dFnP7jpZYNSF60vRKqhL8wrHU6BHcSs8iF2lsaW5rYXBwXzA2MDAwMDVmOTQzNWFkKMrbp7gG",
                 "nickname": "昵称",
                 "account": "findermcn_ASp/Yp3wbHQPqNcY7Ado+A=="
             }
         }
                """;
    }
}