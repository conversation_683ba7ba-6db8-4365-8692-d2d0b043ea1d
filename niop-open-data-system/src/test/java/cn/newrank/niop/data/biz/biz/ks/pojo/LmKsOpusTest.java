package cn.newrank.niop.data.biz.biz.ks.pojo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class LmKsOpusTest {

    @Test
    void copyIfExistNull() {
        final LmKsOpus ksOpus = new LmKsOpus();
        ksOpus.setPhotoId("123");
        ksOpus.setAnaDel(1);

        final LmKsOpus old = new LmKsOpus();
        old.setAnaDel(2);
        old.setMusic("mu");
        old.setAnaTags("tag");
        ksOpus.copyIfExistNull(old);

        Assertions.assertEquals(old.getAnaTags(), ksOpus.getAnaTags());
        Assertions.assertEquals(ksOpus.getAnaDel(), ksOpus.getAnaDel());
    }
}