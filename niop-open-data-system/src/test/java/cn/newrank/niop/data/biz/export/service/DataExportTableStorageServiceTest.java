package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.export.pojo.po.DaTaExportTableStorage;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class DataExportTableStorageServiceTest extends BaseTest {

    @Resource
    private DataExportTableStorageService dataExportTableStorageService;

    @Test
    void testQuery() {
        List<DaTaExportTableStorage> list = dataExportTableStorageService.queryTableStorageContents("aaaaaaaa8ikwk5mj67512d11000000000000036f", null);

        log.info("done");
    }

}