package cn.newrank.niop.data.biz.service.impl;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.pojo.dto.StorageHistory;
import cn.newrank.niop.data.biz.pojo.dto.StorageResult;
import jakarta.annotation.Resource;

import org.junit.jupiter.api.Test;



class StorageServiceImplTest extends BaseTest {

    @Resource
    StorageServiceImpl storageService;

    @Test
    void get() {
        final StorageResult storageResult = storageService.get(StorageBiz.TIKTOK_OPUS,
                "7376741655354805546",
                27);

        System.out.println(storageResult);
    }

    @Test
    void getV() {
        final StorageResult storageResult = storageService.get(StorageBiz.TIKTOK_OPUS,
                "7376741655354805546",
                39);

        System.out.println(storageResult);
    }


    @Test
    void getHistory() {
        final StorageHistory history = storageService.getHistory(StorageBiz.TIKTOK_OPUS,
                "7376741655354805546");

        System.out.println(history);
    }
}