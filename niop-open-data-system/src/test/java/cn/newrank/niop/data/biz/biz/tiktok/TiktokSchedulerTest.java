package cn.newrank.niop.data.biz.biz.tiktok;

import cn.newrank.niop.data.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

class TiktokSchedulerTest extends BaseTest {

    @Resource
    TiktokScheduler tiktokScheduler;

    @Test
    void syncOpus() {
        tiktokScheduler.syncOpus(LocalDate.of(2024, 7, 17));
    }

    @Test
    void syncAccount() {
        tiktokScheduler.syncAccount();
    }

    @Test
    void calculateTiktokRank() {
        tiktokScheduler.calculate(LocalDate.of(2024, 7, 17));
    }
}