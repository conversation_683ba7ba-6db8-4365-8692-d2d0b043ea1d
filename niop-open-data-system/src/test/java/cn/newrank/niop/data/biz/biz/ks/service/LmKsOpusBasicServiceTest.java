package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import cn.newrank.niop.data.common.ds.Datasource;
import cn.newrank.niop.data.common.ds.EsFactory;
import cn.newrank.niop.data.common.ds.Resp;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

@Log4j2
class LmKsOpusBasicServiceTest extends BaseTest {

    @Resource
    LmKsOpusBasicService lmKsOpusBasicService;
    @Resource
    DsConfigManager dsConfigManager;

    static long toMillis(LocalDate date) {
        LocalDateTime localDateTime = date.atStartOfDay();
        ZonedDateTime shanghaiZonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        return shanghaiZonedDateTime.toInstant().toEpochMilli();
    }




    @Test
    void test_diff() {
        try (
                final Datasource ksEs = EsFactory.DEFAULT.create(dsConfigManager.chooseKsEsConfig());
                final Datasource lmEs = EsFactory.DEFAULT.create(dsConfigManager.chooseLmEsConfig())
        ) {

            final LocalDate end = LocalDate.now();
            LocalDate start = LocalDate.of(2024, 4, 1);
            while (start.isBefore(end)) {
                final int originCount = countOriginalCount(ksEs, start);
                final int newCount = countNewCount(lmEs, start);
                if (originCount > newCount) {
                    log.info("{} -> 总量:{} - 已同步:{}  -diff: {}", start, originCount, newCount, originCount - newCount);
                }

                start = start.plusDays(1);
            }
        }
    }

    int countNewCount(Datasource datasource, LocalDate date) {
        final Resp resp = datasource.query(datasource.newQueryBuilder()
                .template("""
                        GET ks.dim_opus.search_index/_search/
                        {
                          "query": {
                            "bool": {
                              "filter": [
                                {
                                  "range": {
                                    "time": {
                                      "gte": #{startTime},
                                      "lte": #{endTime}
                                    }
                                  }
                                }
                              ]
                            }
                          },
                          "track_total_hits": true
                        }
                        """)
                .addParam("startTime", toMillis(date))
                .addParam("endTime", toMillis(date.plusDays(1)))
        );
        final JSONObject data = resp.data();
        return data.getJSONObject("hits").getJSONObject("total").getIntValue("value");
    }

    int countOriginalCount(Datasource datasource, LocalDate date) {
        final Resp resp = datasource.query(datasource.newQueryBuilder()
                .template("""
                        GET search_kuaishou_photo/_search
                        {
                          "query": {
                            "bool": {
                              "filter": [
                                {
                                  "range": {
                                    "time": {
                                      "gte": #{startTime},
                                      "lte": #{endTime}
                                    }
                                  }
                                }
                              ]
                            }
                          }
                        }
                        """)
                .addParam("startTime", DateTimeUtil.format(date.atStartOfDay()))
                .addParam("endTime", DateTimeUtil.format(date.plusDays(1).atStartOfDay()))
        );

        final JSONObject data = resp.data();
       return data.getJSONObject("hits").getInteger("total");
    }

    @Test
    void storeBatch() {
        final LmKsOpus ksOpus = new LmKsOpus();
        ksOpus.setMusic("""
                {
                  "audioUrls" : [ {
                    "cdn" : "ali2.a.yximgs.com",
                    "url" : "http://ali2.a.yximgs.com/kimg/ECMqIQoFaW1hZ2USAm1zGhRzb19xbVVwTTlOSExkUV92LmpwZw.heif"
                  } ],
                  "artist" : "韦礼安",
                  "name" : "如果可以 (电影\\"月老\\"主题曲)"
                }
                """);
        ksOpus.setPhotoId("11111");
        lmKsOpusBasicService.storeBatch(List.of(ksOpus));
    }


    @Test
    void test() {
        final Cursor<String> cursor = new Cursor<>();
        cursor.setStartTime("2024-05-01 00:00:00");
        cursor.setEndTime("2024-05-02 00:00:00");
        final int sync = lmKsOpusBasicService.sync(cursor);
    }


    @Test
    void test_sync() {
        final LmKsOpus ksOpus = lmKsOpusBasicService.castOf(data());
        lmKsOpusBasicService.storeBatch(List.of(ksOpus));
    }


    public JSONObject data() {
        return JSONObject.parseObject("""
                {
                	"supportType": 0,
                	"enableCoronaViewLater": true,
                	"isPromotion": 0,
                	"overrideCoverThumbnailType": 0,
                	"caption": "Ai眼中的上古正神，炎帝神农，黄帝轩辕，兵主蚩尤，火神祝融，水神共工，女娲，盘古，帝俊，伏羲#ChatGPT #Ai绘画 #我要上热榜",
                	"enableShareToStory": true,
                	"likeCount": 6568,
                	"type": 1,
                	"photoAreaInfo": {
                		"areaName": "发布于浙江"
                	},
                	"sourcePhotoPage": "p",
                	"cover": "http://p66-plat.wsukwai.com/upic/2024/07/20/20/BMjAyNDA3MjAyMDE0MTBfMzY3MDAzNjU0NF8xMzgyOTkwOTIxNDhfMl8z_480p_B58c38a7b7d2357c2830caef84c6c905a.heif?tag=1-**********-p-0-5967be10b83044ab-3d4b358efb604372&clientCacheKey=3xqnuyi8q788c7e_480p.heif&di=3ad6571c&bp=10001",
                	"mtype": "3",
                	"noNeedToRequestPLCApi": true,
                	"expTag": "1_a/2004106592855648706_p0",
                	"savePlayProgressStrategy": 0,
                	"photoReward": {
                		"rewardCount": 23,
                		"descNotClickText": "23 赞赏",
                		"favorGuideStrategy": 0,
                		"canFreeFavorAuthor": false
                	},
                	"sameFrame": {
                		"allow": true,
                		"availableDepth": 7
                	},
                	"baseInsertTime": "2024-12-26 05:53:55",
                	"hasVote": false,
                	"tags": [
                		{
                			"name": "Ai绘画",
                			"rich": true,
                			"id": "18022480",
                			"tag": "Ai绘画"
                		},
                		{
                			"name": "我要上热榜",
                			"rich": true,
                			"id": "110473",
                			"tag": "我要上热榜"
                		}
                	],
                	"commentCount": 352,
                	"following": 0,
                	"supportLandscapePlay": true,
                	"streamManifest": {
                		"adaptationSet": [
                			{
                				"duration": 22894,
                				"representation": [
                					{
                						"url": "http://k0u3ayd7y7ay12z.djvod.ndcimgs.com/ksc1/Mpj6u-838PzJpZZ-wZkAflag-Vc9bzHpVjbVAKuPio4VvwkZw__dtrUTpQzHp89WjfdNB-FMNDrzZnlK4PkTLwYpkV6fESUrc_WR6Kpx-CGztvbZhlcqpTJ7_qKLZ4v7.mp4?tag=1-**********-p-0-elen6cu8zk-9cfd7f327a076be6&provider=self&clientCacheKey=3xqnuyi8q788c7e_9d3d1a92&di=3ad6571c&bp=10001&ocid=100000311&tt=hd15&ss=vp"
                					},
                					{
                						"url": "http://k0u3ayd7y7ay12z.djvod.ndcimgs.com/ksc1/DWnrsn-jA3aDrltoQ70c1n83ylv2argdjucc375iu7bW45j1dAAyYMmluKp9X3GttFe8-ej3hzf_LNFRmo1tI2jXq7do_3p2XLcFfMBwnQ6nrvwIoYrjnxseb3vOxoZ0.mp4?tag=1-**********-p-0-pbti0pzqj3-d610c54be6b9721e&provider=self&clientCacheKey=3xqnuyi8q788c7e_9961d2e7&di=3ad6571c&bp=10001&ocid=100000311&tt=hd18&ss=vp"
                					}
                				]
                			}
                		],
                		"videoId": "4afcf338317b0e21"
                	},
                	"fansGroup": {
                		"enableFansGroupV2": false
                	},
                	"shareGuide": {
                		"photoShareGuide": true,
                		"guides": {
                			"default": {
                				"iconType": "wechat"
                			}
                		},
                		"minPlayDurationInSeconds": 15,
                		"textDisplayDurationInSeconds": 4,
                		"playTimes": 2
                	},
                	"syncTime": "2024-12-26 05:53:55",
                	"coronaVipInfo": {
                		"vipPhoto": false,
                		"enableFilmAd": false,
                		"filmAdInfo": {
                			"posId": 9777,
                			"pageId": 100014436,
                			"subPageId": 100017199
                		}
                	},
                	"adminTags": [],
                	"recoReason": "p0",
                	"searchTags": [
                		"ChatGPT",
                		"Ai绘画",
                		"我要上热榜"
                	],
                	"enableFullScreenPlay": false,
                	"headurls": [
                		{
                			"cdn": "p4.a.yximgs.com",
                			"url": "http://p4.a.yximgs.com/kimg/uhead/AB/2023/12/22/15/CjtCTWpBeU16RXlNakl4TlRRNU1qUmZNelkzTURBek5qVTBORjh5WDJoa05EWTBYekU1TlE9PV9zLmpwZxCVzNcv:200x200.heif"
                		},
                		{
                			"cdn": "p2.a.yximgs.com",
                			"url": "http://p2.a.yximgs.com/kimg/uhead/AB/2023/12/22/15/CjtCTWpBeU16RXlNakl4TlRRNU1qUmZNelkzTURBek5qVTBORjh5WDJoa05EWTBYekU1TlE9PV9zLmpwZxCVzNcv:200x200.heif"
                		}
                	],
                	"recreationSetting": 1,
                	"serverExpTag": "feed_photo|5225301608869608471|3670036544|1_a/2004106592855648706_p0",
                	"commentGuessSearch": {
                		"searchWordMD5": "12de773f13ac2c8f656b31548e8b0d87",
                		"displayPrefix": "大家都在搜：",
                		"searchWordUrl": "kwai://search?keyword=ai中国神话&sessionId=**********************************&source=search_entrance_comment_trending&extParams={\\"refer_photo_id\\":\\"138299092148\\",\\"adQuerySource\\":\\"\\",\\"biz_source\\":\\"cmt_gys\\",\\"statistic_flag\\":\\"0\\"}&alwaysUploadExtParams=true",
                		"searchWord": "ai中国神话",
                		"iconUrl": "https://ali2.a.kwimgs.com/udata/pkg/se-cdn/search_blue3x.png",
                		"sessionId": "**********************************"
                	},
                	"anaDel": 0,
                	"acqDate": "2024-12-26",
                	"musicDisk": {
                		"expand": false
                	},
                	"photoStatus": 0,
                	"timestamp": 1721477678408,
                	"forwardStatsParams": {
                		"et": "1_a/2004106592855648706_p0"
                	},
                	"usC": 0,
                	"usD": 1,
                	"collectCount": 1462,
                	"photoId": "5225301608869608471",
                	"ffCoverThumbnailUrls": [],
                	"updateTime": "2024-12-26 05:53:55",
                	"userId": 3670036544,
                	"usL": true,
                	"frameStyle": 0,
                	"coverThumbnailUrls": [],
                	"disclaimerMessage": "作品疑似AI合成，请谨慎甄别",
                	"flinkAwemeSyncTime": "2024-12-26 05:53:55",
                	"anaTags": [
                		"ChatGPT",
                		"Ai绘画",
                		"我要上热榜"
                	],
                	"photoTextLocationInfo": {
                		"leftRatio": 0.28361,
                		"widthRatio": 0.51112,
                		"topRatio": 0.49444,
                		"heightRatio": 0.42314
                	},
                	"displayTime": "",
                	"shareInfo": "userId=3x58jmh5gcbguke&photoId=3xqnuyi8q788c7e",
                	"feedLogCtx": {
                		"stExParams": "",
                		"logParams": "{}",
                		"stidContainer": "CkIxfDIwMDQxMDY1OTI4NTU2NDg3MDZ8cGhvdG86NTIyNTMwMTYwODg2OTYwODQ3MXx7InBnIjoicCJ9fHsiciI6MH0="
                	},
                	"danmakuInfo": {
                		"disablePost": true,
                		"disableReasonKey": "SERVER_DISABLE",
                		"paster": false,
                		"disableReasonTip": "该作品暂不支持弹幕功能",
                		"photoDanmakuGuide": false
                	},
                	"soundTrack": {
                		"audioUrls": [
                			{
                				"cdn": "ali2.a.yximgs.com",
                				"url": "http://ali2.a.yximgs.com/bs2/ost/MTM4Mjk5MDkyMTQ4XzM2NzAwMzY1NDQ.m4a"
                			}
                		],
                		"artist": "AI设计师",
                		"name": "AI设计师的作品原声",
                		"id": "5x4kbmg2xp3p2q4"
                	},
                	"profilePagePrefetchInfo": {
                		"profilePageType": 1
                	},
                	"plcFeatureEntryAbFlag": 0,
                	"showGrDetailPage": false,
                	"liked": 0,
                	"tagHashType": 1,
                	"coverType": "heif",
                	"shareCount": 364,
                	"plcHighPriorityThanBottomEntry": false,
                	"followShoot": {
                		"isLipsSyncPhoto": false
                	},
                	"forwardCount": 0,
                	"hated": 0,
                	"editInfo": {},
                	"userSex": "M",
                	"awemeAcqDataType": "photo_list",
                	"fastCommentType": 1,
                	"insertTime": "2024-12-26 05:53:55",
                	"anaTime": "2024-12-26 05:53:55",
                	"ownerCount": {
                		"photoPublic": 213,
                		"fans": 124261
                	},
                	"overrideCoverSize": {
                		"width": 1436,
                		"height": 1080
                	},
                	"isShareCountExp": false,
                	"mainMvUrls": [
                		{
                			"cdn": "alimov6.a.yximgs.com",
                			"url": "http://alimov6.a.yximgs.com/ksc1/iTa6CWvnWE4KbjUZy9gJmFPlugkSsxpg6P6Tl-V2M9lcW4SLNR-O8PHYWIhs_q8G3HqtESMIBCPCuq39Z8KV8tBEZ-V53ho9QbKP9IElzSQKLPuxMdwj4uD6uHm52UrJT6o5ffxQIDYJpEVL5sLY_1WqK7i8Jv9LuHfBah3DnNoaiQC-dgFuF6j8i9Zdd8KF.mp4?tag=1-**********-p-0-5967be10b83044ab-3d4b358efb604372&clientCacheKey=3xqnuyi8q788c7e_b.mp4&tt=b&di=3ad6571c&bp=10001"
                		},
                		{
                			"cdn": "v1.kwaicdn.com",
                			"url": "http://v1.kwaicdn.com/ksc1/iTa6CWvnWE4KbjUZy9gJmFPlugkSsxpg6P6Tl-V2M9lcW4SLNR-O8PHYWIhs_q8G3HqtESMIBCPCuq39Z8KV8tBEZ-V53ho9QbKP9IElzSQKLPuxMdwj4uD6uHm52UrJT6o5ffxQIDYJpEVL5sLY_1WqK7i8Jv9LuHfBah3DnNoaiQC-dgFuF6j8i9Zdd8KF.mp4?pkey=AAV_99Ycyq6uSCFCTwyUoue7bSo-4UrEQyx3ap4sqzT_bQT1jQRrxiKPK0oeIXNtVjKVpAl7nrRfrlYW0gA7guhpwkGDqF4au3HOwhMQk66Udz4iJfrZSfXZQ48tr8YdBFw&tag=1-**********-p-1-5967be10b83044ab-3d4b358efb604372&clientCacheKey=3xqnuyi8q788c7e_b.mp4&tt=b&di=3ad6571c&bp=10001"
                		}
                	],
                	"duration": 22800,
                	"recoTags": [],
                	"videoColdStartType": 0,
                	"screenType": "horizontal",
                	"overrideCoverThumbnailUrls": [
                		{
                			"cdn": "s2-11988.kwimgs.com",
                			"url": "http://s2-11988.kwimgs.com/kimg/EKrM1y8qJAoFcGhvdG8SA2RlZhoWMTM4Mjk5MDkyMTQ4X2NjY19vLmpwZw.heif"
                		},
                		{
                			"cdn": "s1-11988.kwimgs.com",
                			"url": "http://s1-11988.kwimgs.com/kimg/EKrM1y8qJAoFcGhvdG8SA2RlZhoWMTM4Mjk5MDkyMTQ4X2NjY19vLmpwZw.heif"
                		}
                	],
                	"viewCount": 259099,
                	"enableCoronaDetailPage": true,
                	"feedSwitches": {
                		"disableCommentLikeAnimation": false,
                		"disable61ActivityAnimation": true,
                		"enablePlayerPanel": false,
                		"enablePictureCommentForPhoto": true
                	},
                	"slideCommentEntryDisabled": false,
                	"comments": [],
                	"acqType": "photo_list",
                	"verified": false,
                	"extParams": {
                		"mtype": 3,
                		"color": "E2E2E2",
                		"w": 1316,
                		"sound": 22779,
                		"h": 720,
                		"interval": 30,
                		"video": 22800
                	},
                	"userName": "AI设计师",
                	"ptp": "",
                	"vpf": 0.5,
                	"commentInfo": {
                		"permissions": 63,
                		"commentCountSpecialEffect": true,
                		"commentIconType": 0
                	},
                	"operationExpTagDisplayInfo": {},
                	"originalPhotoId": "3xqnuyi8q788c7e",
                	"unlikeCount": 0,
                	"plcResponseTime": **********700,
                	"location": {},
                	"awemeAcqFrequencyType": "day_aweme_list",
                	"time": "2024-07-20 20:14:38",
                	"inDaily": 1
                }
                
                """);
    }
}