package cn.newrank.niop.data.biz.component.es;


import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import org.junit.jupiter.api.Test;

import java.util.List;

class EsTest {

    static EsFactory.Es createEs() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "youdu_api";
            case PASSWORD -> "di&^23saiSDFI*SDFIW7";
            case ADDRESS -> "es-cn-2r42p7jaa000cg6ng.public.elasticsearch.aliyuncs.com:9200";
            default -> null;
        };

        return new EsFactory.Es(config);
    }

    @Test
    void get_collections() {
        try (final EsFactory.Es datasource = createEs()) {
            final List<Collection> collections = datasource.getCollections();


            System.out.println(JSON.toJSONString(collections, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void get_children() {
        try (final EsFactory.Es datasource = createEs()) {
            final List<Collection> children = datasource.getChildren("api_sph_daily_account");

            System.out.println(JSON.toJSONString(children, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void getCollections() {
        final EsFactory.Es es = EsFactory.DEFAULT.create(key ->
                switch (key) {
                    case USERNAME -> "nr_read";
                    case PASSWORD -> "NrZr1tfGC2IzHGkl";
                    case ADDRESS -> "es-cn-cfn3vu6rj000pcbzy.public.elasticsearch.aliyuncs.com:9200";
                    default -> null;
                });
        try (final EsFactory.Es datasource = es) {
            final List<Collection> collections = datasource.getCollections();


            System.out.println(JSON.toJSONString(collections, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void getColumns() {
        final EsFactory.Es es = EsFactory.DEFAULT.create(key ->
                switch (key) {
                    case USERNAME -> "elastic_youdu";
                    case PASSWORD -> "BjkNydJqo6";
                    case ADDRESS -> "es-cn-x0r3fh6e3000o9u0y.public.elasticsearch.aliyuncs.com:9200";
                    default -> null;
                });

        try (final EsFactory.Es datasource = es) {
            final List<Column> columns = datasource.getColumns("search_nr_account_20231010");


            System.out.println(JSON.toJSONString(columns, JSONWriter.Feature.PrettyFormat));
        }
    }

    @Test
    void query() {
        try (final EsFactory.Es datasource = createEs()) {
            final String template = """
                    GET api_sph_daily_account/_search
                    {
                      "size": 2,
                      "query": {
                        "bool": {
                          "must": [
                            {
                              "term": {
                                "sync_date": {
                                  "value": :date
                                }
                              }
                            }
                          ]
                        }
                      },
                      "track_total_hits":true
                    }
                    """;
            final QueryBuilder queryBuilder = datasource.newQueryBuilder();
            queryBuilder.template(template)
                    .enablePreview();
            //.addParam("date", "2024-02-23");

            final Resp resp = datasource.query(queryBuilder);
            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}