package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import static cn.newrank.niop.web.exception.BizExceptions.createParamError;

class CallbackTest extends BaseTest {
    @Resource
    CallbackScheduler callbackScheduler;

    @Test
    void callbackTableManager() {
        callbackScheduler.callbackTableManager("");
    }


    @Test
    void test_resource_mutate() {
        final CbConfig cbConfig = new CbConfig();
        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> """
                 120.55.44.52:9093,118.31.2.220:9093,116.62.162.105:9093
                    """;
            case TOPIC -> "xhs_account_open_reflux_source";
            case PROTOCOL -> "PLAINTEXT";
            default -> null;
        });

        try (final KafkaCallback kafkaCallback = new KafkaCallback(cbConfig.getConfig())) {
            if (!kafkaCallback.isActive()) {
                throw createParamError("not active");
            }
            kafkaCallback.callback(getBody());
        }
    }


    static String getBody() {
        return """
                {
                    "data_type": "account_base_open_reflux",
                    "logic_id": "66a39bf7000000001d0336c8",
                    "gmt_create": "2025-07-02 09:37:05",
                    "json_details": {
                      "account_id": "66a39bf7000000001d0336c8",
                      "nickname": "小红薯681350A3",
                      "avatar": "https://sns-avatar-qc.xhscdn.com/avatar/645b7f4a9262aeee795c99e7.jpg?imageView2/2/w/80/format/jpg"
                    }
                }
                """;
    }
}