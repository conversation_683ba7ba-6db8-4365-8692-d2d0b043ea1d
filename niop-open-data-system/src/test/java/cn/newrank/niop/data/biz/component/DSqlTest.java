package cn.newrank.niop.data.biz.component;

import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.JDBC;
import cn.newrank.niop.data.common.ds.LanguageDriver;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver;
import org.apache.ibatis.session.Configuration;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/19 10:02
 */
public class DSqlTest {

    private static JDBC getJdbc() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "BASIC$niop_test";
            case PASSWORD -> "xppFZzX8mx6YN509";
            case ADDRESS -> "hgprecn-cn-n6w22gd3w007-cn-hangzhou.hologres.aliyuncs.com:80";
            case DATABASE -> "yd_api_test";
            default -> null;
        };
        return new HoloFactory().create(config);
    }

    public static void main(String[] args) {
        try (final JDBC jdbc = getJdbc()) {
            final Configuration configuration = new Configuration();
            final XMLLanguageDriver driver = new XMLLanguageDriver();
            // 动态生成 SQL 语句
            final String dynamicSql = """
                       {
                         "query": {
                           "bool": {
                             "filter": {
                               "range": {
                                 "time": {
                                   "gte": "2024-08-16 00:00:00",
                                   "lte": "2024-08-17 00:00:00"
                                 }
                               }
                             },
                             "minimum_should_match": 1,
                             "should": [
                              <if test="display != null">
                                   ${display}
                              </if>
                               {
                                 "term": {
                                   "awemeAcqDataType": {
                                     "value": "photo_detail"
                                   }
                                 }
                               }
                             ]
                           }
                         }
                       }
                    
                    """;
            final SqlSource sqlSource = driver.createSqlSource(configuration, dynamicSql, Map.class);


            final Map<String, Object> params = new HashMap<>();

            params.put("display", """
                    {
                     "term": {
                       "awemeAcqDataType": {
                         "value": "photo_list"
                       }
                     }
                    },
                    """);

            final BoundSql boundSql = sqlSource.getBoundSql(params);

            // 替换参数占位符
            final StringBuilder sb = new StringBuilder();
            final List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            int index = 0;
            for (char ch : boundSql.getSql().toCharArray()) {
                if (ch != '?') {
                    sb.append(ch);
                    continue;
                }

                if (parameterMappings.size() == index) {
                    throw new IllegalArgumentException("Parameter mapping count does not match actual parameters.");
                }

                final ParameterMapping parameterMapping = parameterMappings.get(index);
                final String propertyName = parameterMapping.getProperty();
                final Object additionalParameter = boundSql.getAdditionalParameter(propertyName);
                final Object value = additionalParameter != null
                        ? additionalParameter
                        : params.get(propertyName);

                final String valueStr = value != null ? "'" + escapeSql(value.toString()) + "'" : "NULL";
                sb.append(valueStr);

                index++;
            }


            System.out.println("Generated SQL: " + sb);

            //final NamedParameterJdbcTemplate jdbcTemplate = jdbc.availableJdbcTemplate();
            //final List<Map<String, Object>> maps = jdbcTemplate.queryForList(boundSql.getSql(), params);
        }
    }

    // 简单的 SQL 转义方法
    private static String escapeSql(String value) {
        return value.replace("'", "''");
    }

    @Test
    void test() {
        // 动态生成 SQL 语句
        final String dynamicSql = """
                    select * from ds_dwd_bz_e_user
                       <where>
                         mid = #{mid}
                            <if test="mids != null and mids.size > 0">
                                and id in
                                     <foreach collection="mids" item="id" open="(" separator="," close=")">
                                            #{id}
                                        </foreach>
                            </if>
                       </where>
                       limit :offset
                """;
        final Map<String, Object> params = new HashMap<>();
        //params.put("mid", "John");
        params.put("mids", List.of(1, 2, 3));
        final String sql = new LanguageDriver(value -> {
            if (value == null) {
                return "NULL";
            }

            if (value instanceof CharSequence) {
                return "'" + value + "'";
            }

            return value;
        }).process(dynamicSql, params);
        System.out.println(sql);
    }
}
