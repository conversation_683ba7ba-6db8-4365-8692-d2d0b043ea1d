package cn.newrank.niop.data.biz.biz.xhs.mapper;

import cn.newrank.niop.data.biz.biz.xhs.pojo.XhsOpusExpand;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class XhsOpusExpandMapperTest {

    @Resource
    private XhsOpusExpandMapper xhsOpusExpandMapper;

    @Test
    public void testBatchInsert() {
        String json = """
                {
                	"logic_id": "5f0316b0000000000101c4f6",
                	"data_type":"web_topic_search" ,
                	"normal_id": "5c3a5705000000000703bc58",
                	"gmt_create": "2025-08-26 14:12:11",
                	"json_details": {
                		"aweme_id": "5d511297000000002602332b",
                		"data_type": "web_topic_search",
                		"account_id": "59b3829850c4b4197d115edf",
                		"public_time": "2019-08-12 15:17:00",
                		"acq_time": "2025-08-26 14:33:40",
                		"ana_time": "2025-08-26 14:33:40",
                		"nickname": "运动薯",
                		"avatar": "https://sns-avatar-qc.xhscdn.com/avatar/613037363707e365c22b6b83.jpg?imageView2/2/w/80/format/jpg",
                		"cover_url": "https://sns-webpic.xhscdn.com/9641dd56-60bc-5f64-bf10-96d1a10b4c74?imageView2/2/w/540/format/jpg", 
                		"like_count": "3373",
                		"comment_count": "0",
                		"collect_count": "0", 
                		"aweme_type": "normal", 
                		"aweme_title": "＃我的运动日常|在这个夏天动起来吧！" 
                	}
                }
                """;
        XhsOpusExpand.Message message = JSON.parseObject(json).to(XhsOpusExpand.Message.class);
        XhsOpusExpand opus = message.getDetail();
        opus.setDataType(message.getDataType());

        xhsOpusExpandMapper.batchInsert(List.of(opus));
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme