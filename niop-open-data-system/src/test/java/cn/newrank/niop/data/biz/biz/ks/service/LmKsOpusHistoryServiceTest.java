package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.ks.pojo.LmKsOpus;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/22 15:31
 */
public class LmKsOpusHistoryServiceTest extends BaseTest {
    @Resource
    private LmKsOpusHistoryService lmKsOpusHistoryService;

    @Test
    public void testStoreHistory() {
        final List<LmKsOpus> items = new ArrayList<>();

        final LmKsOpus item = new LmKsOpus();
        item.setPhotoId("5188146899479288437");
        item.setGmtTime("2025-07-22 15:00:00");
        items.add(item);

        lmKsOpusHistoryService.storeBatch(items);
    }
}
