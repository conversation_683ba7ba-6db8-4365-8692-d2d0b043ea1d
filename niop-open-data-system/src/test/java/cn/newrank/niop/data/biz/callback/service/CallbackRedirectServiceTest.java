package cn.newrank.niop.data.biz.callback.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.callback.pojo.ConsumerRecordCarrier;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import jakarta.annotation.Resource;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CallbackRedirectServiceTest extends BaseTest {

    @Resource
    private CallbackRedirectService callbackRedirectService;

    @Test
    void redirectCallback() {
        List<CallbackRedirect> redirectList = new ArrayList<>();

        CallbackRedirect redirect = new CallbackRedirect();
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>("123", 0, 0, "", "");
        redirect.setConsumerRecord(consumerRecord);

        // 序列化载体
        ConsumerRecordCarrier carrier = ConsumerRecordCarrier.buildBy(consumerRecord);
        redirect.setConsumerRecordCarrier(carrier);

        redirect.setAppId("5LGL1W5S");
        redirect.setSourceKey("ooxdjowx8ikwk5mj673fea6100000000000000aa");
        redirect.setSourceId("RAJ98AK8");
        redirect.setPayload("{\n" +
                "    \"abilityId\": \"OOXDJOWX\",\n" +
                "    \"appId\": \"8IKWK5MJ\",\n" +
                "    \"bizCode\": 0,\n" +
                "    \"bizMsg\": \"成功\",\n" +
                "    \"finishTime\": \"2024-11-22 10:20:33.080\",\n" +
                "    \"sceneIds\": [\n" +
                "        \"Y1VBMM6J\"\n" +
                "    ],\n" +
                "    \"status\": 1,\n" +
                "    \"taskId\": \"ooxdjowx8ikwk5mj673fea6100000000000000aa\"\n" +
                "}");
        redirectList.add(redirect);
        callbackRedirectService.redirectCallback(redirectList);
    }

}