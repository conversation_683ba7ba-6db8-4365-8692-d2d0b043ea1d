package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.export.pojo.enums.DataExportSubtaskStatus;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportSubtask;
import jakarta.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class DataExportSubtaskServiceTest extends BaseTest {

    @Resource
    private DataExportSubtaskService dataExportSubtaskService;

    @Test
    void testBatchUpdate() {
        DataExportSubtask t1 = new DataExportSubtask();
        t1.setResultTaskId("tfndf5w65g2rjm2l673d5c8c000000000000000c");
        t1.setBizCode(0);
        t1.setBizMsg("成功1");
        t1.setSubtaskStatus(DataExportSubtaskStatus.SUCCEED);
        t1.setDataNum(1);
        t1.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));

        DataExportSubtask t2 = new DataExportSubtask();
        t2.setResultTaskId("tfndf5w65g2rjm2l673d5c8d0000000000000010");
        t2.setBizCode(0);
        t2.setBizMsg("成功2");
        t2.setSubtaskStatus(DataExportSubtaskStatus.SUCCEED);
        t2.setDataNum(2);
        t2.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));

        DataExportSubtask t3 = new DataExportSubtask();
        t3.setResultTaskId("tfndf5w65g2rjm2l673d5c8d0000000000000011");
        t3.setBizCode(0);
        t3.setBizMsg("成功3");
        t3.setSubtaskStatus(DataExportSubtaskStatus.SUCCEED);
        t3.setDataNum(3);
        t3.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));

        List<DataExportSubtask> subtasks = List.of(t1, t2, t3);
        dataExportSubtaskService.batchUpdateSubtasks(subtasks);
        log.info("更新完成");
    }

}