package cn.newrank.niop.data.biz.export.atsp;

import cn.newrank.niop.data.BaseTest;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class AtspRequestTest extends BaseTest {

    @Test
    void testSubmit() {
        AtspSubmitResult result = AtspRequest.submitTask("Z8NG7GRG", Map.of("uid", "3546389128677764"));
        log.info("taskId: {}", result.getTaskId());
    }

    @Test
    void testGetResult() {
        AbilityResult result = AtspRequest.getResult("tfndf5w65g2rjm2l673d879f0000000000000016");
        log.info("done");
    }

}