package cn.newrank.niop.data.biz.biz.ks.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.callback.service.CbHttpTaskService;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class KsNewSyncTest extends BaseTest {

    @Resource
    private KsNewSync ksNewSync;
    @Resource
    CbHttpTaskService taskService;

    @Test
    void sync() {
        final Cursor<String> cursor = new Cursor<>();
        taskService.createTable(LocalDate.now());
        for (int i = 0; i < 100; i++) {

            ksNewSync.sync(cursor);
        }

    }
}