package cn.newrank.niop.data.biz.export.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.export.pojo.po.DataExportResultFile;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class DataExportTaskServiceTest extends BaseTest {

    @Resource
    private DataExportTaskService dataExportTaskService;

    @Test
    void testListResultFiles() {
        List<DataExportResultFile> files = dataExportTaskService.listResultFiles("96cphtj6z6biitwt");
        log.info("done");
    }

}