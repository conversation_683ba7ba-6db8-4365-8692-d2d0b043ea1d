package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.dy.service.haihui.DyHaihuiBusinessOpusServiceImpl;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DyHaihuiSyncTest extends BaseTest {
    @Autowired
    DyHaihuiBusinessOpusServiceImpl dyHistorySync;
    @Test
    void sync() {
        final Cursor<String> cursor = new Cursor<>();
        final int sync = dyHistorySync.sync(cursor);
        System.out.println(sync);

        final int sync1 = dyHistorySync.sync(cursor);
        System.out.println(sync1);
    }
}