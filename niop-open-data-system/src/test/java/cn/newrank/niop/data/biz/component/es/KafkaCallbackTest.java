package cn.newrank.niop.data.biz.component.es;

import cn.newrank.niop.data.biz.biz.athm.pojo.AthmOpusList;
import cn.newrank.niop.data.biz.component.biz.StorageBiz;
import cn.newrank.niop.data.biz.component.callback.KafkaCallback;
import cn.newrank.niop.data.biz.consumer.CallbackRedirect;
import cn.newrank.niop.data.biz.pojo.dto.CbConfig;
import cn.newrank.niop.data.config.SystemConfig;
import cn.newrank.niop.data.config.property.SystemProperties;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/5/30 13:36
 */
class KafkaCallbackTest {


    @Test
    void test() {
        final CbConfig cbConfig = new CbConfig();
        final SystemProperties systemProperties = new SystemProperties();
        systemProperties.setActiveProfile("dev");
        new SystemConfig(systemProperties);

        cbConfig.setConfig(key -> switch (key) {
            case KAFKA_BOOTSTRAP_SERVERS -> "alikafka-pre-cn-4xl3io1a0002-1.alikafka.aliyuncs.com:9093," +
                    "alikafka-pre-cn-4xl3io1a0002-2.alikafka.aliyuncs.com:9093," +
                    "alikafka-pre-cn-4xl3io1a0002-3.alikafka.aliyuncs.com:9093";
            case TOPIC -> "test_niop_data_redirect_callback";
            case PROTOCOL -> "PLAINTEXT";
            case USERNAME -> "youdu_api_test";
            case PASSWORD -> "346a9987a07111ee955c0c42a1b7e906";
            default -> null;
        });

        try (final KafkaCallback kafkaCallback = new KafkaCallback(cbConfig.getConfig())) {
            if (!kafkaCallback.isActive()) {
                throw new RuntimeException("not active");
            }

            final CallbackRedirect callbackRedirect = new CallbackRedirect();
            callbackRedirect.setStorageBiz(StorageBiz.ATHM_OPUS_LIST.getJson());

            final AthmOpusList athmOpusList = new AthmOpusList();
            athmOpusList.setAvatar("");
            athmOpusList.setUid("xxuusndfa");
            athmOpusList.setOpusId("dshaldfhkajhdjk");
            athmOpusList.setContent("content");
            athmOpusList.setCover("cover");
            athmOpusList.setDescription("description");
            athmOpusList.setDuration(1);


            final String jsonString = JSON.toJSONString(callbackRedirect);

            kafkaCallback.callback(jsonString);
        }


    }


}
