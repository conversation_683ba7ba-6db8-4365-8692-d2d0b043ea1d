package cn.newrank.niop.data.biz.biz.ds.service.dy;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.sync.Cursor;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import static org.junit.jupiter.api.Assertions.*;

class DyHistorySyncTest extends BaseTest {
    @Resource
    DyHistorySync dyHistorySync;
    @Test
    void sync() {
        final Cursor<String> cursor = new Cursor<>();
        final int sync = dyHistorySync.sync(cursor);
        System.out.println(sync);

        final int sync1 = dyHistorySync.sync(cursor);
        System.out.println(sync1);
    }
}