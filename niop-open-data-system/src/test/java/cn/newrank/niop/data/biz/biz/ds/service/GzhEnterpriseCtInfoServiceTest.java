package cn.newrank.niop.data.biz.biz.ds.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.ds.pojo.dto.GzhEnterpriseCtInfo;
import cn.newrank.niop.data.biz.biz.ds.service.gzh.GzhEnterpriseCtInfoService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;


public class GzhEnterpriseCtInfoServiceTest extends BaseTest {

    @Resource
    private GzhEnterpriseCtInfoService gzhEnterpriseCtInfoService;

    @Test
    public void storeBatchTest() {
        final List<GzhEnterpriseCtInfo> items = new ArrayList<>();

        final GzhEnterpriseCtInfo item = new GzhEnterpriseCtInfo();
        item.setIndexId("gzh_442644");
        item.setAnaTime("2024-08-29 16:22:44");
        item.setEnterpriseVerifyInfo("南昌格鲁比实业有限公司");
        item.setVerifyInfo("微信认证:南昌格鲁比实业有限公司");
        items.add(item);

        gzhEnterpriseCtInfoService.storeBatch(items);
    }

}
