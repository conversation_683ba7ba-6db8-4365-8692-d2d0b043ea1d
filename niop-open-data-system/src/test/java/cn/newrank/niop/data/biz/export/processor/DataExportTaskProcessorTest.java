package cn.newrank.niop.data.biz.export.processor;

import cn.hutool.core.thread.ThreadUtil;
import cn.newrank.niop.data.BaseTest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class DataExportTaskProcessorTest extends BaseTest {

    @Resource
    private DataExportTaskProcessor taskProcessor;

    @Test
    void testInitSubtasks() {
        taskProcessor.batchInitSubtasks();
        ThreadUtil.sleep(1000);
        log.info("done");
    }

    @Test
    void testSubmitSubtasks() {
        taskProcessor.batchSubmitSubtasks();
        ThreadUtil.sleep(1000);
        log.info("done");
    }

    @Test
    void testMonitorTasks() {
        taskProcessor.monitorTasksCompletionStatus();
        ThreadUtil.sleep(1000);
        log.info("done");
    }

    @Test
    void testTaskResultsDelivery() {
        taskProcessor.batchHandleTaskResultsDelivery();
        ThreadUtil.sleep(1000);
        log.info("done");
    }

}