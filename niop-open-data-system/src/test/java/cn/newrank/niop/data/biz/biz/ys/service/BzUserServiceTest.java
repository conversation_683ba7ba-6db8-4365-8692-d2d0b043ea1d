package cn.newrank.niop.data.biz.biz.ys.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.common.ConfigProperties;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.JDBC;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;


class BzUserServiceTest extends BaseTest {

    public static final String S = """
                SELECT
                upper_mid,
                mcn_id,
                mcn_company_name,
                head_img,
                nickname,
                region_desc,
                gender_desc,
                platform_price_made,
                platform_price_implantation,
                platform_price_direct,
                platform_price_forward,
                sax_distributions,
                age_distributions,
                device_distributions,
                top_region_distributions,
                city_distributions,
                tag_profile,
                first_categories_profile,
                second_categories_profile
            FROM
                dws_bz_up_info
            WHERE
                is_business = TRUE and detail_gmt_modified >= '%s'
            ORDER BY
                upper_mid
            LIMIT 200 OFFSET %s;
            """;
    @Resource
    HoloFactory holoFactory;

    public static void main(String[] args) {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "LTAI5tByMANjbdQ7n5LMFgs5";
            case PASSWORD -> "******************************";
            case ADDRESS -> "hgprecn-cn-pe337iks9002-cn-hangzhou.hologres.aliyuncs.com:80";
            case DATABASE -> "youzhuan_kol";
            default -> null;
        };


        try (final JDBC holo = new HoloFactory().create(config)) {
            final NamedParameterJdbcTemplate jdbcTemplate = holo.availableJdbcTemplate();

            //final List<Map<String, Object>> maps = jdbcTemplate.queryForList(sql, new MapSqlParameterSource());
            //final Map<String, Object> map = maps.get(0);
            //map.forEach((k, v) -> {
            //    if (v == null) {
            //        System.out.println("null" + " " + k + ";");
            //    } else {
            //        System.out.println(v.getClass().getSimpleName() + " " + k + ";");
            //    }
            //});
        }
    }

    @Test
    void list() {
        final ConfigProperties config = key -> switch (key) {
            case USERNAME -> "LTAI5tByMANjbdQ7n5LMFgs5";
            case PASSWORD -> "******************************";
            case ADDRESS -> "hgprecn-cn-pe337iks9002-cn-hangzhou.hologres.aliyuncs.com:80";
            case DATABASE -> "youzhuan_kol";
            default -> null;
        };

        try (final JDBC holo = new HoloFactory().create(config)) {
            final NamedParameterJdbcTemplate jdbcTemplate = holo.getJdbcTemplate();

            //final List<Map<String, Object>> maps = jdbcTemplate.queryForList("select * from dws_bz_up_info order by detail_gmt_modified desc limit 10;");
            //final Map<String, Object> map = maps.get(0);
            //map.forEach((k,v)-> {
            //    if (v == null) {
            //        System.out.println("null" + " " + k + ";");
            //    }else {
            //        System.out.println(v.getClass().getSimpleName() + " " + k + ";");
            //    }
            //});
        }
    }
}