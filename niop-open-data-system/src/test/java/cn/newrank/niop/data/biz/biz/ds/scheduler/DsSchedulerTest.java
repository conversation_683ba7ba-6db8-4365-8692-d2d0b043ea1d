package cn.newrank.niop.data.biz.biz.ds.scheduler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.ds.mapper.DsMapper;
import cn.newrank.niop.data.biz.biz.ds.pojo.enums.PlatformType;
import cn.newrank.niop.data.biz.biz.ds.pojo.po.DsEsSyncRecordPo;
import cn.newrank.niop.data.util.DateTimeUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

public class DsSchedulerTest extends BaseTest {

    @Resource
    DsIncreScheduler dsScheduler;

    @Resource
    DsMapper dsMapper;

    @Test
    void startTimeOfLastWeekGetTest() {
        final LocalDate sundayOfLastWeek = DateTimeUtil.getSundayOfLastWeek(LocalDate.now());
        final String startTimeOfLastWeek = LocalDateTimeUtil.format(sundayOfLastWeek, DatePattern.NORM_DATETIME_PATTERN);
        System.out.println(startTimeOfLastWeek);
    }

    @Test
    void isValidDateFormatTest() {
        boolean validDateFormat = DateTimeUtil.isValidDateFormat("2023-01-01", DatePattern.NORM_DATETIME_PATTERN);
        System.out.println(validDateFormat);
    }

    @Test
    void pinyinTest() {
        String test1 = PinyinUtil.getPinyin("新榜有度", "");
        System.out.println(test1);

        String test2 = PinyinUtil.getPinyin("Es Product", "");
        System.out.println(test2);
    }

    @Test
    void insertTest() {
        DsEsSyncRecordPo po = new DsEsSyncRecordPo();
        po.setPlatformType(PlatformType.GZH);
        po.setLastSyncTime(DateUtil.now());
        dsMapper.insert(po);
    }

    @Test
    void updateLastSyncTimeTest() {
        dsMapper.update(PlatformType.GZH.getDbCode(), DateUtil.now());
    }

    @Test
    void getTest() {
        DsEsSyncRecordPo dsEsSyncRecordPo = dsMapper.get(PlatformType.GZH);
        System.out.println(JSON.toJSONString(dsEsSyncRecordPo));
    }

    @Test
    void collectCountSyncTest() {
        dsScheduler.accountCollectCountSync(null);
    }

}
