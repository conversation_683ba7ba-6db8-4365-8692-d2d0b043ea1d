package cn.newrank.niop.data.biz.biz.ds.service.bz;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.biz.ds.pojo.EsEntity;
import cn.newrank.niop.data.biz.biz.ds.service.common.CommonEsService;
import cn.newrank.niop.data.util.EsCodec;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class BzRankHistorySyncTest extends BaseTest {

    @Resource
    BzRankHistorySync bzRankHistorySync;

    @Resource
    CommonEsService commonEsService;

    @Test
    void rankIndexes() {
        final Map<String, JSONObject> rankIndexes = bzRankHistorySync.rankIndexes(List.of("1765934367"));

        final EsEntity entity = bzRankHistorySync.castOf(rankIndexes.get("1765934367"), "bili_1765934367");

        System.out.println(EsCodec.serialize(entity));
    }

   @Test
   void convertData() {
       final RankHistoryUpdate update = new RankHistoryUpdate();
       update.setLastWeekAwemeCount(11111L);

       commonEsService.update(List.of(update));
   }

   @Data
    public static class RankHistoryUpdate implements EsEntity {
        Long lastWeekAwemeCount;

        @Override
        public String docId() {
            return "gzh_5186285";
        }
    }

}