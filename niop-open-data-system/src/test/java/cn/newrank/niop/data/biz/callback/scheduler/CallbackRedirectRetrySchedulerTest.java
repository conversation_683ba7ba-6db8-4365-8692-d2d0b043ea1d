package cn.newrank.niop.data.biz.callback.scheduler;

import cn.newrank.niop.data.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CallbackRedirectRetrySchedulerTest extends BaseTest {

    @Resource
    private CallbackRedirectRetryScheduler callbackRedirectRetryScheduler;

    @Test
    void retryCallbackRedirectScheduler() {
        for (int i = 0; i < 20; i++) {
            callbackRedirectRetryScheduler.retryCallbackRedirectScheduler("");
        }
    }

}