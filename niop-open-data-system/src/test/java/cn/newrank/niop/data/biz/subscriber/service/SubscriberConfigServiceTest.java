package cn.newrank.niop.data.biz.subscriber.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.subscriber.pojo.dto.SubscriberConfig;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/1 11:59
 */
public class SubscriberConfigServiceTest extends BaseTest {

    @Resource
    SubscriberConfigService subscriberConfigService;

    @Test
    public void listTest() {
        List<SubscriberConfig> list = subscriberConfigService.list("DC74739C", null, null);

        System.out.println(list);
    }
}
