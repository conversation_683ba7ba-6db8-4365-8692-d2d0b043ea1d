package cn.newrank.niop.data.biz.oss.service;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.oss.pojo.dto.OssSignature;
import jakarta.annotation.Resource;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class OssServiceTest extends BaseTest {

    @Resource
    private OssService ossService;

    @Test
    void testGetSignature() {
        OssSignature signature = ossService.getSignature("param/", Duration.ofMinutes(10));
        log.info("done");
    }

}