package cn.newrank.niop.data.biz.biz.ds.service.xhs;

import cn.newrank.niop.data.BaseTest;
import cn.newrank.niop.data.biz.component.biz.DsConfigManager;
import cn.newrank.niop.data.common.ds.HoloFactory;
import cn.newrank.niop.data.common.ds.QueryBuilder;
import cn.newrank.niop.data.common.ds.Resp;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class XhsRankSyncTest extends BaseTest {

    @Resource
    DsConfigManager dsConfigManager;

    @Test
    public void testNewSync() {
        try (final HoloFactory.Holo holo = HoloFactory.DEFAULT.create(dsConfigManager.chooseXhsHoloConfig())) {

            final QueryBuilder queryBuilder = holo.newQueryBuilder()
                    .template("""
                            SELECT
                                count(*)
                            FROM
                                dwd_user_rank_week
                            WHERE
                            rank_date >= #{startTime}::TIMESTAMP
                                and newrank_index > 0
                            """)
                    .addParam("startTime", "2024-10-28");

            final Resp resp = holo.query(queryBuilder);
            System.out.println(JSON.toJSONString(resp, JSONWriter.Feature.PrettyFormat));
        }
    }


}